#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for ternary operator conversion functionality
"""

class MockConfigMatrixCalculatedField:
    """Mock class to test the ternary conversion methods"""
    
    def _convert_js_to_python(self, js_formula):
        """Convert JavaScript formula syntax to Python syntax"""
        if not js_formula:
            return js_formula

        python_formula = js_formula

        # Convert JavaScript operators to Python
        python_formula = python_formula.replace('===', '==')
        python_formula = python_formula.replace('!==', '!=')
        python_formula = python_formula.replace('&&', ' and ')
        python_formula = python_formula.replace('||', ' or ')

        # Convert JavaScript Math.min/max to Python min/max
        python_formula = python_formula.replace('Math.min(', 'min(')
        python_formula = python_formula.replace('Math.max(', 'max(')
        python_formula = python_formula.replace('Math.', '')

        # Convert JavaScript logical OR with default value (|| 0) to Python equivalent
        # Note: This needs to be after the general || conversion
        python_formula = python_formula.replace(' or 0', ' or 0')  # This is already correct

        # Convert ternary operator using a more robust approach
        python_formula = self._convert_ternary_operators(python_formula)

        return python_formula

    def _convert_ternary_operators(self, formula):
        """Convert JavaScript ternary operators to Python if-else expressions"""
        
        def convert_nested_ternary_manual(expr):
            """Convert nested ternary operators manually for the specific pattern"""
            expr = expr.strip()
            
            # Handle the specific pattern: condition1 ? true1 : (condition2 ? true2 : false2)
            # For the specific case we're dealing with, let's handle it manually
            
            # Look for the pattern: " ? " followed by something, then " : " followed by something with another " ? "
            import re
            
            # First, let's try to find the nested ternary pattern
            # Look for: condition ? true : (nested_condition ? nested_true : nested_false)
            
            # Find the first question mark
            first_question = expr.find('?')
            if first_question == -1:
                return expr
            
            # Find the colon after the first question mark
            first_colon = expr.find(':', first_question)
            if first_colon == -1:
                return expr
            
            # Check if there's a nested ternary in the false part
            false_part_start = first_colon + 1
            false_part = expr[false_part_start:].strip()
            
            # Look for nested ternary in the false part
            if false_part.startswith('(') and '?' in false_part and ':' in false_part:
                # Extract the parts
                condition1 = expr[:first_question].strip()
                true1 = expr[first_question + 1:first_colon].strip()
                
                # Remove the outer parentheses from the false part
                nested_expr = false_part[1:-1] if false_part.startswith('(') and false_part.endswith(')') else false_part
                
                # Find the nested ternary
                nested_question = nested_expr.find('?')
                nested_colon = nested_expr.find(':', nested_question)
                
                if nested_question != -1 and nested_colon != -1:
                    condition2 = nested_expr[:nested_question].strip()
                    true2 = nested_expr[nested_question + 1:nested_colon].strip()
                    false2 = nested_expr[nested_colon + 1:].strip()
                    
                    # Convert to Python if-else
                    python_expr = f"({true1} if ({condition1}) else ({true2} if ({condition2}) else {false2}))"
                    return python_expr
            
            # If no nested pattern found, try simple ternary
            condition = expr[:first_question].strip()
            true_part = expr[first_question + 1:first_colon].strip()
            false_part = expr[first_colon + 1:].strip()
            
            # Convert to Python if-else
            python_expr = f"({true_part} if ({condition}) else {false_part})"
            return python_expr
        
        # Only process if there are ternary operators
        if '?' in formula and ':' in formula:
            try:
                return convert_nested_ternary_manual(formula)
            except Exception as e:
                print(f"⚠️ Ternary conversion failed: {e}, using fallback")
                # Fallback to simple replacement
                formula = formula.replace(' ? ', ' if ')
                formula = formula.replace(' : ', ' else ')
                return formula
        
        return formula


def test_ternary_conversion():
    """Test the ternary operator conversion"""
    
    # Create mock instance
    calc_field = MockConfigMatrixCalculatedField()
    
    # Test case 1: Original problematic formula
    js_formula = "_CALCULATED_height_calculation_method === 'manual' ? Math.max(_CALCULATED_manual_left_height, _CALCULATED_manual_right_height) : (_CALCULATED_height_calculation_method === 'even' ? (parseFloat(bx_dbl_hinge_make_each_door_height_mm_even) || 0) : Math.max(parseFloat(bx_dbl_hinge_make_left_door_height_mm_uneven) || 0, parseFloat(bx_dbl_hinge_make_right_door_height_mm_uneven) || 0))"
    
    print("🔍 Testing ternary operator conversion...")
    print(f"Original JS formula: {js_formula}")
    print()
    
    # Convert to Python
    python_formula = calc_field._convert_js_to_python(js_formula)
    print(f"Converted Python formula: {python_formula}")
    print()
    
    # Test syntax validity
    try:
        compiled = compile(python_formula, '<string>', 'eval')
        print("✅ Syntax check passed - formula compiles successfully")
    except SyntaxError as e:
        print(f"❌ Syntax error: {e}")
        return False
    
    # Test with sample values
    test_context = {
        '_CALCULATED_height_calculation_method': 'manual',
        '_CALCULATED_manual_left_height': 100,
        '_CALCULATED_manual_right_height': 150,
        'bx_dbl_hinge_make_each_door_height_mm_even': '200',
        'bx_dbl_hinge_make_left_door_height_mm_uneven': '180',
        'bx_dbl_hinge_make_right_door_height_mm_uneven': '160',
        'parseFloat': lambda x: float(x) if x else 0,
        'max': max,
        'min': min
    }
    
    try:
        result = eval(compiled, test_context)
        print(f"✅ Runtime test passed - result: {result}")
        
        # Test different calculation methods
        print("\n🧪 Testing different calculation methods:")
        
        # Test manual method
        test_context['_CALCULATED_height_calculation_method'] = 'manual'
        result = eval(compiled, test_context)
        print(f"  Manual method: {result}")
        
        # Test even method
        test_context['_CALCULATED_height_calculation_method'] = 'even'
        result = eval(compiled, test_context)
        print(f"  Even method: {result}")
        
        # Test uneven method
        test_context['_CALCULATED_height_calculation_method'] = 'uneven'
        result = eval(compiled, test_context)
        print(f"  Uneven method: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Runtime error: {e}")
        return False


if __name__ == "__main__":
    success = test_ternary_conversion()
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n💥 Tests failed!") 