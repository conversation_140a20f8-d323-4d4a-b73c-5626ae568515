Project Summary
This project consists of two main Odoo modules:

Canbrax CPQ Pro: An advanced Configure, Price, Quote system for Canbrax Wholesale Screens that handles complex product configurations for security doors and screens. Key features include:
Advanced decision tree engine for complex dependencies
Dynamic SVG product visualization
Precise price calculation
BOM generation for manufacturing
Built with Odoo 18's OWL framework
Xero Connector: A bidirectional integration between Odoo and Xero accounting software using OAuth 2.0 REST API. This module synchronizes:
Contacts and contact groups
Accounts and taxes
Products and inventory
Invoices, bills, and payments
Both modules are designed to integrate seamlessly with Odoo's existing functionality while providing specialized features for their respective domains.