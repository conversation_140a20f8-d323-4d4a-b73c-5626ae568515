<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <record id="view_order_form_inherit" model="ir.ui.view">
		<field name="name">product.template.form.view.inherit</field>
		<field name="model">product.template</field>
		<field name="inherit_id" ref="product.product_template_only_form_view"/>
        <field name="priority" eval="20"/>
		<field name="arch" type="xml">
                <xpath expr="//field[@name='name']/.." position="after">

                   <label for="colour" string="Colour"/>
                    <h1>
                        <div class="d-flex">
                            <field class="text-break" name="colour"/>
                        </div>
                    </h1>


                   <h3>
                        <label for="default_code" string="Internal Reference"/>
                        <div class="d-flex">
                            <field class="text-break" name="default_code"/>
                        </div>
                    </h3>
                 
                </xpath>

                <xpath expr="//form[@name='Product Template']/sheet[@name='product_form']/notebook[1]/page[@name='inventory']/group[@name='inventory']/group[@name='group_lots_and_weight']/div[@name='volume']" position="after">
                    <label for="pc_circumference" string="PC Circumference"/>
                    <div class="o_row" name="pc_circumference" >
                        <field name="pc_circumference" string="PC Circumference" class="oe_inline" style="vertical-align:baseline"/> 
                        <span class="oe_inline">mm</span>
                    </div>


                </xpath>
        </field>
    </record>

    <record id="view_order_tree_inherit" model="ir.ui.view">
		<field name="name">product.template.tree.view.inherit</field>
		<field name="model">product.template</field>
		<field name="inherit_id" ref="product.product_template_tree_view"/>
        <field name="priority" eval="20"/>
		<field name="arch" type="xml">
                <xpath expr="//field[@name='default_code']" position="before">
                    <field name="colour" string="Colour"/>
                </xpath>
        </field>
    </record>

</odoo>
