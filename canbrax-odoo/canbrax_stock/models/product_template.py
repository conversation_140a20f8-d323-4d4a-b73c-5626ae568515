from odoo import api, fields, models, _

class ProductTemplate(models.Model):
    _inherit = 'product.template'

    colour = fields.Char('Colour')
    pc_circumference = fields.Float('PC Circumference (mm)')

    @api.depends("name", "colour")
    def _compute_display_name(self):
        for template in self:
            template.display_name = "{}{}".format(
                template.name,
                " - {}".format(template.colour) if template.colour else ""
            )
