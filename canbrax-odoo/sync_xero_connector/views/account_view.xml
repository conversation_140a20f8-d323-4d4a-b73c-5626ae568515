<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="xero_connector_view_tax_form" model="ir.ui.view">
        <field name="name">account.tax.form</field>
        <field name="model">account.tax</field>
        <field name="inherit_id" ref="account.view_tax_form"/>
        <field name="arch" type="xml">
            <field name="description" position="after">
                <field name="xero_tax_type" readonly="1" force_save="1"/>
            </field>
            <xpath expr="//notebook" position="inside">
                <page string="Tax Components">
                    <field name="componet_ids">
                        <list editable="top">
                            <field name="name" required="1"/>
                            <field name="rate" required="1"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <record id="xero_connector_view_account_form" model="ir.ui.view">
        <field name="name">account.form</field>
        <field name="model">account.account</field>
        <field name="inherit_id" ref="account.view_account_form"/>
        <field name="arch" type="xml">
            <field name="account_type" position="after">
                <field name="account_type_xero" class="oe_inline" required="1" string="Account type (Xero)" invisible='1'/>
                <field name="acc_id" class="oe_inline" invisible='1'/>
                <field name="use_as_inventory_in_xero"/>
            </field>
            <xpath expr="//notebook" position="inside">
                <page name="xero_desc" string="Xero Description">
                    <field name="xero_description" placeholder="Xero Description ...." nolabel="1"/>
                </page>
            </xpath>
        </field>
    </record>

    <record id="xero_connector_view_partner_bank_form" model="ir.ui.view">
        <field name="name">bank.account.form</field>
        <field name="model">res.partner.bank</field>
        <field name="inherit_id" ref="base.view_partner_bank_form"/>
        <field name="arch" type="xml">
            <field name="acc_number" position="replace">
                <field name="acc_number" placeholder="Account Number Format 000000-**********"/>
            </field>
        </field>
    </record>

    <record id="xero_connector_view_account_journal_form" model="ir.ui.view">
        <field name="name">account.journal.form</field>
        <field name="model">account.journal</field>
        <field name="inherit_id" ref="account.view_account_journal_form"/>
        <field name="arch" type="xml">
            <field name="account_control_ids" position="before">
                <field name="xero_account_id" domain="[('company_ids', 'in', company_id),('acc_id', '!=', False)]"/>
            </field>
            <xpath expr="//notebook/page/field[@name='inbound_payment_method_line_ids']/list/field[@name='payment_account_id']" position="attributes">
                <attribute name="context">{'is_payment_account': True, 'xero_account_id': True}</attribute>
            </xpath>
            <xpath expr="//notebook/page/field[@name='outbound_payment_method_line_ids']/list/field[@name='payment_account_id']" position="attributes">
                <attribute name="context">{'is_payment_account': True, 'xero_account_id': True}</attribute>
            </xpath>
        </field>
    </record>

    <record id="account.action_move_journal_line" model="ir.actions.act_window">
        <field name="context">{'default_move_type': 'entry', 'search_default_misc_filter':1, 'default_is_manual_journal': True, 'view_no_maturity': True}</field>
    </record>
</odoo>