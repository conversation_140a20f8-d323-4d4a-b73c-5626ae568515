<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="xero_connector_sale_order_form" model="ir.ui.view">
        <field name="name">sale.order.form</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <!-- <xpath expr="//header" position="inside">
                <button name="tax_calculation" type="object" string="Calculate Tax" />
            </xpath> -->
            <field name="date_order" position="after">
                <field name="line_amount_type" readonly="xero_quotes_number" invisible="not xero_quotes_number"/>
                <field name="xero_quotes_number" readonly="1"/>
            </field>
            <xpath expr="//notebook" position="inside">
                <page string="Xero" invisible="1">
                    <group string="Xero Data">
                        <field name="xero_quotes_id" class="oe_inline" readonly="1"/>
                    </group>
                </page>
            </xpath>
            <field name="price_subtotal" position="after">
                <field name="xero_order_line_id" invisible="1" readonly="1"/>
            </field>
        </field>
    </record>

    <record id="sale_order_tree_inherit" model="ir.ui.view">
        <field name="name">sale.order.tree (quotes)</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.sale_order_tree"/>
        <field name="arch" type="xml">
            <field name="user_id" position="after">
                <field name="xero_quotes_number" optional="hide"/>
            </field>
        </field>
    </record>

    <record id="view_sales_order_filter_inherit" model="ir.ui.view">
        <field name="name">sale.order.list.select</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_sales_order_filter"/>
        <field name="arch" type="xml">
            <field name="user_id" position="after">
                <field name="xero_quotes_number"/>
                <field name="xero_quotes_id"/>
            </field>
        </field>
    </record>

    <record id="action_saleorder_export_xero" model="ir.actions.server">
        <field name="name">Export In Xero</field>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="model_id" ref="model_sale_order"/>
        <field name="binding_model_id" ref="model_sale_order"/>
        <field name="code">
            records.action_export_order()
        </field>
    </record>

</odoo>