<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="xero_connector_move_tree" model="ir.ui.view">
        <field name="name">account.move.tree</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_invoice_tree"/>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="xero_invoice_number" invisible="move_type not in ['in_invoice', 'out_invoice','in_refund','out_refund']" />
            </field>
        </field>
    </record>

    <record id="xero_connector_move_form" model="ir.ui.view">
        <field name="name">account.move.form</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <button name="button_draft" position="attributes">
                <attribute name="invisible">not show_reset_to_draft_button or xero_invoice_number</attribute>
            </button>
            <xpath expr="//notebook" position="inside">
                <page string="Xero" invisible="1">
                    <group string="Xero Data">
                        <field name="xero_invoice_id" class="oe_inline" />
                    </group>
                </page>
            </xpath>
            <!-- <xpath expr="//header" position="inside">
                <button name="tax_calculation" type="object" string="Calculate Tax" invisible="not able_to_xero_export or move_type not in ['in_invoice', 'out_invoice','in_refund','out_refund'] or state != 'draft'"/>
            </xpath> -->
            <field name="invoice_date" position="after">
                <field name="xero_invoice_number" string="Invoice (Xero)" invisible="move_type not in ['in_invoice', 'out_invoice','in_refund','out_refund']"/>
                <field name="able_to_xero_export" invisible="move_type not in ['in_invoice', 'out_invoice','in_refund','out_refund']"/>
                <!-- <field name="line_amount_type" required="1" readonly="state != 'draft'" invisible="not able_to_xero_export or move_type not in ['in_invoice', 'out_invoice','in_refund','out_refund']"/> -->
                <field name="line_amount_type" required="1" readonly="xero_invoice_number" invisible="not xero_invoice_number or move_type not in ['in_invoice', 'out_invoice','in_refund','out_refund']"/>
            </field>
            <field name='price_subtotal' position='after'>
                <field name='xero_invoice_line_id' column_invisible='1'/>
            </field>
            <field name="journal_id" position="after">
                <field name="is_manual_journal" invisible="1" />
                <field name="xero_manual_journal_id" invisible="1"/>
            </field>
        </field>
    </record>

    <record id="view_account_payment_form_inherit" model="ir.ui.view">
        <field name="name">account.payment.form</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="account.view_account_payment_form"/>
        <field name="arch" type="xml">
            <field name="date" position="after">
                <field name="xero_payment_id" invisible="1"/>
            </field>
        </field>
    </record>

    <record id="account_invoice_filter_inherit_xero" model="ir.ui.view">
        <field name="name">account.move.select</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_account_invoice_filter"/>
        <field name="arch" type="xml">
            <filter name="draft" position="before">
                <filter name="not_exported" string="Not Exported in Xero" domain="[('xero_invoice_id','=',False)]"/>
                <separator/>
            </filter>
        </field>
    </record>

    <record id="action_invoice_export_xero" model="ir.actions.server">
        <field name="name">Export In Xero</field>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="model_id" ref="model_account_move"/>
        <field name="binding_model_id" ref="model_account_move"/>
        <field name="code">
            records.action_export_invoice()
        </field>
    </record>

    <record id="xero_view_account_payment_search" model="ir.ui.view">
        <field name="name">account.payment.search</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="account.view_account_payment_search"/>
        <field name="arch" type="xml">
            <field name="company_id" position="after">
                <filter string="Not Exported in Xero" domain="[('xero_payment_id', '=', False)]" name="not_exported"/>
                <separator/>
            </field>
        </field>
    </record>
</odoo>