<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="xero_connector_product_normal_form_view" model="ir.ui.view">
        <field name="name">product.form</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_normal_form_view"/>
        <field name="arch" type="xml">
            <field name="default_code" position="replace">
                <field name="default_code" required="1"/>
            </field>
            <field name="company_id" position="after">
                <field name="product_xero_company_ids" invisible="1">
                    <list>
                        <field name="company_id"/>
                        <field name="xero_item_id"/>
                    </list>
                </field>
            </field>
        </field>
    </record>

    <record id="xero_connector_product_template_only_form_view" model="ir.ui.view">
        <field name="name">product.template.form</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_only_form_view"/>
        <field name="arch" type="xml">
            <field name="default_code" position="replace">
                <field name="default_code" required="1"/>
            </field>
        </field>
    </record>

    <record id="action_product_export_xero" model="ir.actions.server">
        <field name="name">Export In Xero</field>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="model_id" ref="model_product_product"/>
        <field name="binding_model_id" ref="model_product_product"/>
        <field name="code">records.action_export_product()</field>
    </record>
</odoo>