<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.ui.view" id="view_mismatch_tree">
        <field name="name">view.mismatch.log.list</field>
        <field name="model">mismatch.log</field>
        <field name="arch" type="xml">
            <list create="0" edit="0">
                <field name="name"/>
                <field name="source_model"/>
                <field name="source_id"/>
                <field name="date"/>
                <field name="option"/>
                <field name="xero_account_id"/>
                <field name="company_id"/>
                <field name="description"/>
            </list>
        </field>
    </record>

    <record model="ir.ui.view" id="view_mismatch_form">
        <field name="name">view.mismatch.log.form</field>
        <field name="model">mismatch.log</field>
        <field name="arch" type="xml">
            <form create="0" edit="0">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="source_model"/>
                            <field name="source_id"/>
                        </group>
                        <group>
                            <field name="date"/>
                            <field name="option"/>
                            <field name="xero_account_id"/>
                        </group>
                    </group>
                    <field name="description"/>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_mismatch_log" model="ir.actions.act_window">
        <field name="name">Mismatch Log</field>
        <field name="res_model">mismatch.log</field>
        <field name="view_mode">list,form</field>
    </record>

    <record model="ir.ui.view" id="view_xero_account_form">
        <field name="name">view.xero.account.form</field>
        <field name="model">xero.account</field>
        <field name="arch" type="xml">
            <form string="Account">
                <sheet>
                    <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                    <group>
                        <group>
                            <field name="name" required='1'/>
                            <field name="company_id" widget="selection" required="1"/>
                        </group>
                        <group>
                            <field name="auth" invisible="1"/>
                            <!-- <field name="active" widget="boolean_toggle"/> -->
                        </group>
                    </group>
                    <group string="OAuth2 Details" >
                        <field name="id" invisible="1"/>
                        <field name="client_id" required="1"/>
                        <field name="client_secret" required="1"/>
                        <field name="callback_uri" required="1"/>
                        <field name="raw_url" widget="url" readonly="1" class="text-break" invisible="not raw_url"/>
                    </group>
                    <button name="generate_url_auth2" type="object" string="Generate OAuth2 URL" />
                    <group>
                        <field name="raw_uri" widget="url" invisible="not raw_url"/>
                        <field name="state" invisible="1"/>
                    </group>
                    <button name="authenticate" type="object" string="Authentication" invisible="not raw_uri"/>
                    <group>
                        <field name="auth" invisible="1"/>
                        <field name="xero_org_id" domain="[('xero_account_id', '=', id)]" options="{'no_create': 1, 'no_edit': 1}" invisible="not auth" required="auth"/>
                    </group>
                    <p class="oe_grey" >
                        <strong>Note</strong>: Follow these steps <br/>
                            1. Generate URL <br/>
                            2. Go to URL and Allow Access<br/>
                            3. After authorization the user will be redirected from Xero to the callback URI <br/>
                            4. Copy URL <br/>
                            5. Copied URL add in raw uri<br/>
                            6. Authentication <br/>
                            7. After Authentication, Select Xero Organization for Import/Export data. <br/>
                    </p>
                    <group>
                        <group>
                             <field name="contact_overwrite" widget="boolean_toggle"/>
                        </group>
                    </group>
                    <p class="oe_grey">
                        <strong>Note</strong>: By Enabling this option contacts with same name and email address will be overwritten automatically in Odoo as well as Xero side. <br/>
                    </p>
                    <group>
                        <field name="import_export_creditnotes" widget="radio" options="{'horizontal': True}"/>
                    </group>
                    <p class="oe_grey">
                        <strong>Note</strong>: You can either Import or Export Credit Note based on configuration.
                    </p>
                    <notebook>
                        <page string="Import Details">
                            <group>
                                <group>
                                    <field name="import_option" widget="radio" options="{'horizontal': True}" string="Option"/>
                                    <field name="inv_without_product" widget="boolean_toggle" string="If you want to import invoice without product tick this"/>
                                </group>
                                <group>
                                    <field name="tracked_category_id"/>
                                    <field name="untracked_category_id"/>
                                    <field name="last_create_journal_import_date" invisible="1"/>
                                </group>
                            </group>
                            <br/>
                            <br/>
                            <button name="import_currency" string="Currencies" type="object" class="px-3 btn"/>
                            <button name="import_tax" string="Taxes" type="object" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="import_account" string="Chart of Accounts" type="object" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="import_manual_journal" string="Manual Journals" type="object" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="import_bank_account" string="Bank Accounts" type="object" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="import_contact" string="Contacts" type="object" invisible="contact_overwrite == True" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="import_contact_overwrite" string="Contacts" type="object" invisible="not contact_overwrite" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="import_product" string="Products" type="object" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="import_quotes" string="Quotes" type="object" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="import_purchase_order" string="Purchase Order" type="object" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="import_invoice" string="Invoices/Bills" type="object" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="import_credit_notes" string="Credit/Debit Notes" type="object" invisible="import_export_creditnotes not in ['import']" style="margin-left:8px;" class="px-3 btn"/>
                        </page>
                        <page string="Export Details">
                            <separator string="Export"/>
                            <group>
                                <group>
                                    <field name="adjustment_account_id"/>
                                    <p class="oe_grey" colspan="2">
                                        <strong>Notes</strong>: Attachments can be exported to<br/>
                                        1. Customers/Vendors <br/>
                                        2. Customer Invoices/Vendor Bills <br/>
                                        3. Credit Notes/Refund <br/>
                                        4. Sales Order <br/>
                                        5. Purchase Order
                                    </p>
                                </group>
                                <group>
                                    <field name="export_disable" widget="boolean_toggle" string="Disable Export (Accounts &amp; Taxes)"/>
                                </group>
                            </group>
                            <br/>
                            <button name="export_currency" string="Currency" type="object" class="px-3 btn"/>
                            <button name="export_tax" string="Taxes" type="object" invisible="export_disable == True" class="px-3 btn"/>
                            <button name="export_account" string="Chart of Accounts" type="object" invisible="export_disable == True" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="export_bank_account" string="Bank Accounts" type="object" invisible="export_disable == True" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="export_contact" string="Contacts" type="object" invisible="contact_overwrite == True" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="export_contact_overwrite" string="Contacts" type="object" invisible="not contact_overwrite" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="export_product" string="Products" type="object" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="export_purchase_order" string="Purchase Order" type="object" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="export_quotes" string="Quotes" type="object" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="export_invoice" string="Invoices/Bills" type="object" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="export_payment" string="Invoice/Bill Payment" type="object" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="export_credit_notes" string="Credit/Debit Notes" type="object" invisible="import_export_creditnotes not in ['export']" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="export_credit_notes_payment" string="Credit/Debit Notes Payment" type="object" invisible="import_export_creditnotes not in ['export']" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="export_inventory_adjustments" string="Inventory Adjustments" type="object" style="margin-left:8px;" class="px-3 btn"/>
                            <button name="export_attachments" string="Attachments" type="object" style="margin-left:8px;" class="px-3 btn"/>
                        </page>
                        <page string="Accounting Details">
                            <group>
                                <group>
                                    <field name="customer_inv_journal_id" required="1" widget="selection" domain="[('type', '=', 'sale'), ('company_id', '=', company_id)]"/>
                                    <field name="miscellaneous_operations_journal_id" required="1" widget="selection" domain="[('type', '=', 'general'), ('company_id', '=', company_id)]"/>
                                </group>
                                <group>
                                    <field name="vendor_bill_journal_id" required="1" widget="selection" domain="[('type', '=', 'purchase'), ('company_id', '=', company_id)]"/>
                                </group>
                            </group>
                            <separator string="Payment Journal"/>
                            <field name="journal_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="code"/>
                                    <field name="type"/>
                                    <field name="company_id"/>
                                </list>
                            </field>
                        </page>
                        <page string="Xero Organization Details" >
                            <field name="xero_org_ids">
                                <list editable="top" create="0">
                                    <field name="name"/>
                                    <field name="xero_id"/>
                                    <field name="xero_tenant_id"/>
                                    <field name="xero_tenant_type"/>
                                    <field name="active" widget="boolean_toggle"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record model="ir.ui.view" id="view_xero_account_tree">
        <field name="name">view.xero.account.list</field>
        <field name="model">xero.account</field>
        <field name="arch" type="xml">
            <list string="Account">
                <field name="name"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </list>
        </field>
    </record>

    <record id="action_xero_account" model="ir.actions.act_window">
        <field name="name">Xero Account</field>
        <field name="path">xeroaccount</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">xero.account</field>
        <field name="view_mode">list,form</field>
    </record>
</odoo>