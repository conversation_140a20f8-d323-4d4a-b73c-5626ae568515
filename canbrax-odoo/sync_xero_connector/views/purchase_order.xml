<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="xero_connector_po_order_form" model="ir.ui.view">
        <field name="name">purchase.order.form</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <!-- <xpath expr="//header" position="inside">
                <button name="tax_calculation" type="object" string="Calculate Tax"/>
            </xpath> -->
            <field name="date_order" position="after">
                <!-- <field name="line_amount_type" readonly="state != 'draft'"/> -->
                <field name="line_amount_type" readonly="xero_po_number" invisible="not xero_po_number"/>
                <field name="xero_po_number" readonly="1"/>
            </field>
            <xpath expr="//notebook" position="inside">
                <page string="Xero" invisible="1">
                    <group string="Xero Data">
                        <field name="xero_po_id" readonly="1"/>
                    </group>
                </page>
            </xpath>
            <field name="price_subtotal" position="after">
                <field name="xero_order_line_id" invisible="1"/>
            </field>
            <xpath expr="//button[@name='action_create_invoice'][2]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='action_create_invoice'][1]" position="attributes">
                <attribute name="invisible">invoice_count != 0</attribute>
            </xpath>
        </field>
    </record>

    <record id="purchase_order_kpis_tree_inherit" model="ir.ui.view">
        <field name="name">purchase.order.inherit.purchase.order.tree</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_kpis_tree"/>
        <field name="arch" type="xml">
            <field name="origin" position="after">
                <field name="xero_po_number" optional="hide"/>
            </field>
        </field>
    </record>

    <record id="view_purchase_order_filter_inherit" model="ir.ui.view">
        <field name="name">request.quotation.select</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.view_purchase_order_filter"/>
        <field name="arch" type="xml">
            <field name="origin" position="after">
                <field name="xero_po_number"/>
                <field name="xero_po_id"/>
            </field>
        </field>
    </record>

    <record id="action_poorder_export_xero" model="ir.actions.server">
        <field name="name">Export In Xero</field>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="model_id" ref="model_purchase_order"/>
        <field name="binding_model_id" ref="model_purchase_order"/>
        <field name="code">
            records.action_export_order()
        </field>
    </record>

</odoo>