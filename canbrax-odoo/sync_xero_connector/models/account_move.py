# -*- coding: utf-8 -*-
# Part of Odoo. See COPYRIGHT & LICENSE files for full copyright and licensing details.

import datetime
import logging
import time
from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
from datetime import date
from odoo.tools import (
    date_utils,
    float_compare,
    float_is_zero,
    format_amount,
    format_date,
    formatLang,
    frozendict,
    get_lang,
    is_html_empty,
    sql
)
from odoo.tools.mail import (
    email_re,
    email_split,
    )

_logger = logging.getLogger(__name__)


class account_payment(models.Model):
    _inherit = "account.payment"

    xero_payment_id = fields.Char('Xero Payment ID', copy=False)


class AccountMove(models.Model):
    _inherit = 'account.move'

    xero_invoice_id = fields.Char('Xero Invoice ID', readonly=True, copy=False)
    xero_invoice_number = fields.Char('Xero Invoice Number', readonly=True, copy=False)
    line_amount_type = fields.Selection([('Exclusive', 'Exclusive'),
                                        ('Inclusive', 'Inclusive'),
                                        ('NoTax', 'NoTax'),
                                        ], 'Line Amount Type', default='Exclusive')
    xero_credit_note_allocation = fields.Boolean(string="Xero Credit Note Allocation")
    xero_manual_journal_id = fields.Char('Xero Manual Journal ID', readonly=True, copy=False)
    is_manual_journal = fields.Boolean(string='Is Manual Journal')
    able_to_xero_export = fields.Boolean(string="Able to Xero Export", default='True')

    @api.onchange('line_amount_type')
    def onchange_line_amount_type(self):
        context = dict(self.env.context)
        context.update({'check_move_validity': False,'line_amount_type': self.line_amount_type})
        self.env.context = context
        # .with_context({'check_move_validity': False,'line_amount_type': self.line_amount_type})
        # .with_context({'check_move_validity': False,'line_amount_type': self.line_amount_type})
        # .with_context({'check_move_validity': False,'line_amount_type': self.line_amount_type})
        # .with_context({'check_move_validity': False,'line_amount_type': self.line_amount_type})
        # .with_context({'check_move_validity': False,'line_amount_type': self.line_amount_type})
        self.invoice_line_ids._compute_name()
        self.invoice_line_ids._compute_account_id()
        self.invoice_line_ids._compute_product_uom_id()
        # self.invoice_line_ids._compute_price_unit()
        self.invoice_line_ids._compute_totals()
        if self.line_amount_type == 'NoTax':
            self.invoice_line_ids.with_context({'check_move_validity': False, 'line_amount_type': self.line_amount_type}).write({'tax_ids': [(6, 0, [])]})
        self._compute_tax_totals()

    @api.depends_context('lang')
    @api.depends(
        'invoice_line_ids.currency_rate',
        'invoice_line_ids.tax_base_amount',
        'invoice_line_ids.tax_line_id',
        'invoice_line_ids.price_total',
        'invoice_line_ids.price_subtotal',
        'invoice_payment_term_id',
        'partner_id',
        'currency_id',
    )
    def _compute_tax_totals(self):
        """ Computed field used for custom widget's rendering.
            Only set on invoices.
        """
        for move in self:
            if move.is_invoice(include_receipts=True):
                base_lines, _tax_lines = move.with_context({'line_amount_type': self.line_amount_type})._get_rounded_base_and_tax_lines()
                move.tax_totals = self.env['account.tax']._get_tax_totals_summary(
                    base_lines=base_lines,
                    currency=move.currency_id,
                    company=move.company_id,
                    cash_rounding=move.invoice_cash_rounding_id,
                )
                move.tax_totals['display_in_company_currency'] = (
                    move.company_id.display_invoice_tax_company_currency
                    and move.company_currency_id != move.currency_id
                    and move.tax_totals['has_tax_groups']
                    and move.is_sale_document(include_receipts=True)
                )
            else:
                # Non-invoice moves don't support that field (because of multicurrency: all lines of the invoice share the same currency)
                move.tax_totals = None


    def _prepare_product_base_line_for_taxes_computation(self, product_line):
        """ Convert an account.move.line having display_type='product' into a base line for the taxes computation.

        :param product_line: An account.move.line.
        :return: A base line returned by '_prepare_base_line_for_taxes_computation'.
        """
        self.ensure_one()
        is_invoice = self.is_invoice(include_receipts=True)
        sign = self.direction_sign if is_invoice else 1
        if is_invoice:
            rate = self.invoice_currency_rate
        else:
            rate = (abs(product_line.amount_currency) / abs(product_line.balance)) if product_line.balance else 0.0
        context = dict(self.env.context) or {}
        return self.env['account.tax']._prepare_base_line_for_taxes_computation(
            product_line,
            price_unit=product_line.price_unit if is_invoice else product_line.amount_currency,
            quantity=product_line.quantity if is_invoice else 1.0,
            discount=product_line.discount if is_invoice else 0.0,
            rate=rate,
            sign=sign,
            # special_mode=False if is_invoice else 'total_excluded',
            special_mode= context.get('line_amount_type') == 'Inclusive' and 'total_included' or 'total_excluded',
        )

    def tax_calculation(self):
        context = dict(self.env.context)
        context.update({'check_move_validity': False,'line_amount_type': self.line_amount_type})
        self.env.context = context

        # .with_context({'check_move_validity': False, 'line_amount_type': self.line_amount_type})
        # .with_context({'check_move_validity': False, 'line_amount_type': self.line_amount_type})
        # .with_context({'check_move_validity': False, 'line_amount_type': self.line_amount_type})
        # .with_context({'check_move_validity': False, 'line_amount_type': self.line_amount_type})
        # .with_context({'check_move_validity': False, 'line_amount_type': self.line_amount_type})
        # .with_context({'check_move_validity': False, 'line_amount_type': self.line_amount_type})
        self.invoice_line_ids._compute_name()
        self.invoice_line_ids._compute_account_id()
        self.invoice_line_ids._compute_product_uom_id()
        self.invoice_line_ids._compute_price_unit()
        self.invoice_line_ids._compute_totals()
        if self.line_amount_type == 'NoTax':
            self.invoice_line_ids.write({'tax_ids': [(6, 0, [])]})
        self._compute_tax_totals()
        # .with_context({'line_amount_type': self.line_amount_type})

    @api.model
    def default_get(self, default_fields):
        res = super(AccountMove, self).default_get(default_fields)
        tax_type = self.env['ir.config_parameter'].sudo().get_param('account.show_line_subtotals_tax_selection')
        if tax_type == 'tax_included':
            res['line_amount_type'] = 'Inclusive'
        elif tax_type == 'tax_excluded':
            res['line_amount_type'] = 'Exclusive'
        return res

    @api.model
    def get_views(self, views, options=None):
        res = super().get_views(views, options)
        if self._context.get('default_move_type') in ['out_receipt', 'in_receipt', 'entry']:
            for view in res['views'].values():
                for action in view.get('toolbar').get('action'):
                    if action.get('name') == 'Export In Xero':
                        view.get('toolbar').get('action').remove(action)
                        break
                break
        credit_note_option = self.env['xero.account'].search([('import_export_creditnotes', '=', 'import'),('company_id', 'in', self._context.get('allowed_company_ids', []))])
        if self._context.get('default_move_type') in ['out_refund','in_refund'] and credit_note_option:
            for view in res['views'].values():
                for action in view.get('toolbar').get('action'):
                    if action.get('name') == 'Export In Xero':
                        view.get('toolbar').get('action').remove(action)
                        break
                break
        return res

    def pay_creditnote(self, xero, partner, xero_invoice, invoice_id, type, company=False):
        for payment in xero_invoice.get('Allocations'):
            domain = [('move_id.xero_invoice_id', '=', payment.get('Invoice').get('InvoiceID')),
                      ('partner_id', '=', self.env['res.partner']._find_accounting_partner(partner).id),
                      ('reconciled', '=', False),
                      '|',
                      '&', ('amount_residual_currency', '!=', 0.0), ('currency_id', '!=', None),
                      '&', ('amount_residual_currency', '=', 0.0), '&', ('currency_id', '=', None), ('amount_residual', '!=', 0.0)]
            if invoice_id.move_type in ('out_invoice', 'in_refund'):
                domain.extend([('credit', '>', 0), ('debit', '=', 0)])
            else:
                domain.extend([('credit', '=', 0), ('debit', '>', 0)])
            lines = self.env['account.move.line'].search(domain)
            if len(lines) != 0:
                invoice_id.with_context({'allocation_amount': payment['Amount']}).js_assign_outstanding_line(lines.id)

    def pay_invoice(self, xero_account_id, xero, partner, xero_invoice, odoo_invoice, type, company=False):
        partner_pool = self.env['res.partner']
        currency_pool = self.env['res.currency']
        account_pool = self.env['account.account']
        xero_pool = self.env['xero.account']
        company_pool = self.env['res.company']
        payment_pool = self.env['account.payment']
        if partner and xero_invoice and odoo_invoice:
            invoice = odoo_invoice
            partner_id = partner_pool._find_accounting_partner(partner).id
            if xero_invoice.get('Payments'):
                for payment in xero_invoice.get('Payments'):
                    payment_line = payment_pool.search([('xero_payment_id', '=', payment.get('PaymentID'))])
                    currency_id = currency_pool.search([('name', '=', xero_invoice.get('CurrencyCode'))], limit=1)
                    if not currency_id:
                        currency_list = xero.currencies.all()
                        currency_pool.import_currency(currency_list, xero)
                        currency_id = currency_pool.search([('name', '=', xero_invoice.get('CurrencyCode'))], limit=1)

                    if payment.get('PaymentID'):
                        xero_payment_id = xero.payments.get(payment.get('PaymentID'))
                        payment_account = xero_payment_id[0].get('Account')
                        account = account_pool.search([('code', '=', payment_account.get('Code')), ('acc_id', '=', payment_account.get('AccountID'))],limit=1)
                        if not account:
                            account_list = xero.accounts.all()
                            account_pool.import_account(account_list, xero, xero_account_id, company=company, import_option='create')
                            account = account_pool.search([('code', '=', payment_account.get('Code')), ('acc_id', '=', payment_account.get('AccountID'))],limit=1)

                        xero_account = xero_pool.browse(xero_account_id)
                        payment_journal = False
                        for journal in xero_account.journal_ids:
                            if journal.inbound_payment_method_line_ids.payment_account_id.id == account.id or journal.outbound_payment_method_line_ids.payment_account_id.id == account.id:
                                payment_journal = journal
                                break
                        if not payment_journal:
                            company_id = company_pool.browse(company)
                            raise UserError(_("Please Create or Add 'Payment Journal' for account \'%s %s\' of company \'%s\'.")% (account.code, account.name, company_id.name))

                    if not payment_line:
                        date = payment.get('Date')
                        account_payment_vals = {
                                            # 'invoice_ids': [(6, 0, odoo_invoice.ids)],
                                            'amount': payment.get('Amount'),
                                            'currency_id': currency_id and currency_id.id or False,
                                            'partner_id': partner_id,
                                            'company_id': company,
                                            'journal_id': payment_journal and payment_journal.id or False,
                                            'date': date or fields.Date.today(),
                                            'partner_type': odoo_invoice.move_type in ('out_invoice', 'out_refund') and 'customer' or 'supplier',
                                            'xero_payment_id': payment.get('PaymentID') or False,
                                            'memo': odoo_invoice[0].name
                                            }
                        if type in ['out_invoice', 'in_refund']:
                            account_payment_vals.update({'reconciled_invoice_ids': [(6, 0, odoo_invoice.ids)],
                                                        'reconciled_invoices_count': len(odoo_invoice.ids),
                                                        'payment_type':invoice.move_type in ('out_invoice', 'in_refund') and 'inbound' or 'outbound',
                                                        'payment_method_id': self.env.ref('account.account_payment_method_manual_in').id})
                        elif type in ['in_invoice', 'out_refund']:
                            account_payment_vals.update({'reconciled_bill_ids': [(6, 0, odoo_invoice.ids)],
                                                        'reconciled_bills_count': len(odoo_invoice.ids),
                                                        'payment_type':invoice.move_type in ('in_invoice', 'out_refund') and 'outbound' or 'inbound',
                                                        'payment_method_id': self.env.ref('account.account_payment_method_manual_out').id})
                        account_payment = invoice.env['account.payment'].create([account_payment_vals])
                        if xero_invoice.get('CurrencyRate'):
                            account_payment.with_context({'CurrencyRate': payment.get('CurrencyRate')}).action_post()
                        else:
                            account_payment.action_post()
                        account_payment.action_validate()
                        domain = [('account_type', 'in', ('asset_receivable', 'liability_payable')),('reconciled', '=', False)]
                        # payment_lines = account_payment.line_ids.filtered_domain(domain)
                        payment_lines = account_payment.move_id.line_ids.filtered_domain(domain)
                        for line in invoice.line_ids.filtered(lambda l: l.account_type in ('asset_receivable', 'liability_payable')):
                            payment_lines |= line
                        payment_lines.filtered_domain([('reconciled', '=', False)]).reconcile()
                        self._cr.commit()

    def _get_reconciled_info_JSON_values(self):
        for move in self:
            reconciled_vals = []
            reconciled_partials = move.sudo()._get_all_reconciled_invoice_partials()
            for reconciled_partial in reconciled_partials:
                counterpart_line = reconciled_partial['aml']
                if counterpart_line.move_id.ref:
                    reconciliation_ref = '%s (%s)' % (counterpart_line.move_id.name, counterpart_line.move_id.ref)
                else:
                    reconciliation_ref = counterpart_line.move_id.name

                reconciled_vals.append({
                    'name': counterpart_line.name,
                    'journal_name': counterpart_line.journal_id.name,
                    'amount': reconciled_partial['amount'],
                    'currency_id': reconciled_partial['currency'].id,
                    'date': counterpart_line.date,
                    'partial_id': reconciled_partial['partial_id'],
                    'account_payment_id': counterpart_line.payment_id.id,
                    'payment_method_name': counterpart_line.payment_id.payment_method_line_id.name,
                    'move_id': counterpart_line.move_id.id,
                    'ref': reconciliation_ref,
                    'is_exchange': reconciled_partial['is_exchange'],
                    'amount_company_currency': formatLang(self.env, abs(counterpart_line.balance), currency_obj=counterpart_line.company_id.currency_id),
                    'amount_foreign_currency': formatLang(self.env, abs(counterpart_line.amount_currency), currency_obj=counterpart_line.currency_id) if counterpart_line.currency_id != counterpart_line.company_id.currency_id else False
                })
            return reconciled_vals

    def import_invoice(self, xero_account_id, invoice_list, xero, company=False, without_product=False, import_option=None, customer_inv_journal_id=False, vendor_bill_journal_id=False):
        inv_line_pool = self.env['account.move.line']
        inv_pool = self.env['account.move']
        tax_pool = self.env['account.tax']
        account_pool = self.env['account.account']
        product_pool = self.env['product.product']
        partner_pool = self.env['res.partner']
        currency_pool = self.env['res.currency']
        mismatch_log = self.env['mismatch.log']
        xero_account = self.env['xero.account'].browse(xero_account_id)
        for invoice in invoice_list:
            try:
                if invoice.get('Total') != 0.0:
                    invoice_res = self.search([('xero_invoice_id', '=', invoice.get('InvoiceID')), ('company_id', '=', company)], limit=1)
                    if invoice_res and import_option in ['update', 'both']:
                        if invoice_res[0].line_amount_type != invoice.get('LineAmountTypes'):
                            description = (_('You cannot change Line Amount Type %s to %s.', invoice_res[0].line_amount_type, invoice.get('LineAmountTypes')))
                            mismatch_log.create({'name': invoice_res[0].name,
                                                 'source_model': 'account.move',
                                                 'source_id': invoice_res[0].id,
                                                 'description': description,
                                                 'date': datetime.datetime.now(),
                                                 'option': 'import',
                                                 'xero_account_id': xero_account_id
                                                 })
                            continue

                        if invoice.get('Status') in ['VOIDED','DRAFT'] and invoice_res[0].state == 'posted':
                            invoice_res[0].button_draft()
                        if invoice.get('Status') in ['DELETED', 'VOIDED'] and invoice_res[0].state == 'draft':
                            invoice_res[0].button_cancel()
                        if invoice_res[0].state != 'cancel' and invoice_res[0].payment_state != 'paid':
                            if invoice_res[0].state == 'draft':
                                available_lines = []
                                flag = 0
                                if invoice:
                                    sub_tax = invoice.get('SubTotal') + invoice.get('TotalTax')
                                    if float("%.2f"%sub_tax) != invoice.get('Total'):
                                        flag = 1

                                    line_items = []
                                    for lines in invoice.get('LineItems'):
                                        available_lines.append(lines.get('LineItemID'))
                                        line = inv_line_pool.search([('xero_invoice_line_id', '=', lines.get('LineItemID')),('company_id', '=', company)])
                                        if flag == 1:
                                            line_amount = lines.get('LineAmount') - lines.get('TaxAmount')
                                            unit_amount = line_amount / lines.get('Quantity')
                                        else:
                                            line_amount = lines.get('LineAmount')
                                            unit_amount = lines.get('UnitAmount')

                                        tax_id = False
                                        if lines.get('TaxType'):
                                            tax_id = tax_pool.search([('xero_tax_type', '=', lines.get('TaxType')),('company_id', '=', company)], limit=1)

                                        acc = account_pool.search([('code', '=', lines.get('AccountCode')),('company_ids', 'in', company)])
                                        # if not acc and invoice.get('Type') == 'ACCREC':
                                        #     acc = self.env['ir.property']._get('property_account_income_categ_id', 'product.category')
                                        # elif not acc and invoice.get('Type') == 'ACCPAY':
                                        #     acc = self.env['ir.property']._get('property_account_expense_categ_id', 'product.category')
                                        if line:
                                            if lines.get('ItemCode'):
                                                if without_product:
                                                    vals = {'name': lines.get('Description') or 'Didn\'t specify',
                                                            'price_unit': abs(unit_amount) if unit_amount < 0 else unit_amount,
                                                            'company_id': company,
                                                            'tax_ids': [(6, 0, tax_id.ids)] if tax_id else [],
                                                            'quantity': -(lines.get('Quantity')) if unit_amount < 0 else lines.get('Quantity') or 1,
                                                            'discount': lines.get('DiscountRate') or 0.0,
                                                            'xero_invoice_line_id': lines.get('LineItemID'),
                                                            'move_id': invoice_res[0].id,
                                                            }
                                                    if acc:
                                                        if unit_amount:
                                                            vals.update({'account_id': acc and acc.id})
                                                        line.with_context({'check_move_validity': False, 'line_amount_type': invoice.get('LineAmountTypes')}).write(vals)
                                                        # line.with_context({'check_move_validity': False, 'line_amount_type': invoice.get('LineAmountTypes')})._compute_all_tax()

                                                elif not without_product:
                                                    product = product_pool.search([('default_code', '=', lines.get('ItemCode'))])
                                                    if not product:
                                                        raise UserError(_("Please First Import Product."))
                                                    for product_id in product:
                                                        inv_line_val = {'name': lines.get('Description') or product_id.description or 'Didn\'t specify',
                                                                        'price_unit': (abs(unit_amount) if unit_amount < 0 else unit_amount) or (abs(product_id.lst_price) if product_id.lst_price < 0 else product_id.lst_price),
                                                                        'tax_base_amount': (abs(unit_amount) if unit_amount < 0 else unit_amount) or (abs(product_id.lst_price) if product_id.lst_price < 0 else product_id.lst_price),
                                                                        'quantity': -(lines.get('Quantity')) if unit_amount < 0 or product_id.lst_price < 0 else lines.get('Quantity') or 1,
                                                                        'company_id': company,
                                                                        'product_id': product_id and product_id.id or False,
                                                                        'tax_ids': [(6, 0, tax_id.ids)] if tax_id else [],
                                                                        'discount': lines.get('DiscountRate') or 0.0,
                                                                        'xero_invoice_line_id': lines.get('LineItemID'),
                                                                        'move_id': invoice_res[0].id,
                                                                        }
                                                        if acc:
                                                            if unit_amount:
                                                                inv_line_val.update({'account_id': acc and acc.id})
                                                            line.with_context({'check_move_validity': False, 'line_amount_type': invoice.get('LineAmountTypes')}).write(inv_line_val)
                                                            # line.with_context({'check_move_validity': False, 'line_amount_type': invoice.get('LineAmountTypes')})._compute_all_tax()

                                            else:
                                                vals = {'name': lines.get('Description') or 'Didn\'t specify',
                                                        'price_unit': abs(unit_amount) if unit_amount < 0 else unit_amount or 0.0,
                                                        'company_id': company,
                                                        'tax_ids': [(6, 0, tax_id.ids)] if tax_id else [],
                                                        'quantity': -(lines.get('Quantity')) if unit_amount < 0 else lines.get('Quantity') or 1,
                                                        'discount': lines.get('DiscountRate') or 0.0,
                                                        'xero_invoice_line_id': lines.get('LineItemID'),
                                                        'move_id': invoice_res[0].id,
                                                        }
                                                if acc:
                                                    if unit_amount:
                                                        vals.update({'account_id': acc and acc.id})
                                                    line.with_context({'check_move_validity': False, 'line_amount_type': invoice.get('LineAmountTypes')}).write(vals)
                                                    # line.with_context({'check_move_validity': False, 'line_amount_type': invoice.get('LineAmountTypes')})._compute_all_tax()
                                        else:
                                            if lines.get('ItemCode'):
                                                if without_product:
                                                    vals = {'name': lines.get('Description') or 'Didn\'t specify',
                                                            'price_unit': abs(unit_amount) if unit_amount < 0 else unit_amount or 0.0,
                                                            'company_id': company,
                                                            'tax_ids': [(6, 0, tax_id.ids)] if tax_id else [],
                                                            'quantity': -(lines.get('Quantity')) if unit_amount < 0 else lines.get('Quantity') or 1,
                                                            'discount': lines.get('DiscountRate') or 0.0,
                                                            'xero_invoice_line_id': lines.get('LineItemID'),
                                                            'move_id': invoice_res[0].id,
                                                            }
                                                    if acc:
                                                        if unit_amount:
                                                            vals.update({'account_id': acc and acc.id})
                                                        line_items.append(vals)
                                                elif not without_product:
                                                    product = product_pool.search([('default_code', '=', lines.get('ItemCode'))])
                                                    if not product:
                                                        raise UserError(_("Please First Import Product."))
                                                    for product_id in product:
                                                        inv_line_val = {'name': lines.get('Description') or product_id.description or 'Didn\'t specify',
                                                                        'price_unit': (abs(unit_amount) if unit_amount < 0 else unit_amount) or (abs(product_id.lst_price) if product_id.lst_price < 0 else product_id.lst_price),
                                                                        'quantity': -(lines.get('Quantity')) if unit_amount < 0 or product_id.lst_price < 0 else lines.get('Quantity') or 1,
                                                                        'company_id': company,
                                                                        'product_id': product_id and product_id.id or False,
                                                                        'tax_ids': [(6, 0, tax_id.ids)] if tax_id else [],
                                                                        'discount': lines.get('DiscountRate') or 0.0,
                                                                        'xero_invoice_line_id': lines.get('LineItemID'),
                                                                        'move_id': invoice_res[0].id,
                                                                        }
                                                        if acc:
                                                            inv_line_val.update({'account_id': acc and acc.id})
                                                            line_items.append(inv_line_val)
                                            else:
                                                vals = {'name': lines.get('Description') or 'Didn\'t specify',
                                                        'price_unit': abs(unit_amount) if unit_amount < 0 else unit_amount or 0.0,
                                                        'company_id': company,
                                                        'tax_ids': [(6, 0, tax_id.ids)] if tax_id else [],
                                                        'quantity': -(lines.get('Quantity')) if unit_amount < 0 else lines.get('Quantity') or 1,
                                                        'discount': lines.get('DiscountRate') or 0.0,
                                                        'xero_invoice_line_id': lines.get('LineItemID'),
                                                        'move_id': invoice_res[0].id,
                                                        }
                                                if acc:
                                                    if unit_amount:
                                                        vals.update({'account_id': acc and acc.id})
                                                    line_items.append(vals)

                                    if line_items:
                                        line_ids = inv_line_pool.with_context({'check_move_validity': False, 'line_amount_type': invoice.get('LineAmountTypes')}).create(line_items)

                                    if invoice['Type'] == 'ACCREC':
                                        xero_type = 'out_invoice'
                                        sale_id = self.env['sale.order'].search(['|', '|', ('xero_quotes_number', '=', invoice_res.ref), ('xero_quotes_number', '=', invoice_res.xero_invoice_number), ('name', '=', invoice_res.ref)],limit=1)
                                        if sale_id and not sale_id.invoice_ids:
                                            for inv_line in invoice_res.invoice_line_ids:
                                                for sale_line in sale_id.order_line:
                                                    if sale_line.product_id == inv_line.product_id and sale_line.product_uom_qty == inv_line.quantity:
                                                        sale_line.invoice_lines = [(4, inv_line.id)]
                                            sale_id.invoice_ids = [(4, invoice_res.id)]
                                            sale_id.invoice_count = len(sale_id.invoice_ids)
                                    else:
                                        xero_type = 'in_invoice'
                                        purchase_id = self.env['purchase.order'].search(['|', '|', ('xero_po_number', '=', invoice_res.ref), ('xero_po_number', '=', invoice_res.xero_invoice_number),('name', '=', invoice_res.ref)], limit=1)
                                        if purchase_id and not purchase_id.invoice_ids:
                                            purchase_id.invoice_ids = [(4, invoice_res.id)]
                                            purchase_id.invoice_count = len(purchase_id.invoice_ids)

                                    partner = partner_pool.search(['|', ('company_id', '=', company), ('company_id', '=', False)]).filtered(lambda partner: partner.contact_xero_company_ids.filtered(lambda contact: contact.xero_contact_id == invoice.get('Contact').get('ContactID') and contact.company_id.id == company))
                                    if not partner:
                                        xero_account.import_contact_overwrite() if xero_account.contact_overwrite else xero_account.import_contact()
                                        partner = partner_pool.search(['|', ('company_id', '=', company), ('company_id', '=', False)]).filtered(lambda partner: partner.contact_xero_company_ids.filtered(lambda contact: contact.xero_contact_id == invoice.get('Contact').get('ContactID') and contact.company_id.id == company))

                                    currency_id = currency_pool.search([('name', '=', invoice.get('CurrencyCode'))], limit=1)

                                    vals = {
                                        'partner_id': partner and partner[0].id or False,
                                        'currency_id': currency_id and currency_id.id or False,
                                        'invoice_date': invoice.get('DateString'),
                                        'invoice_date_due': invoice.get('DueDateString'),
                                        'xero_invoice_id': invoice.get('InvoiceID'),
                                        'xero_invoice_number': invoice.get('InvoiceNumber'),
                                        'amount_tax': invoice.get('TotalTax'),
                                        'move_type': xero_type,
                                        'company_id': company,
                                        'line_amount_type': invoice.get('LineAmountTypes'),
                                        'ref': invoice.get('Reference') or '',
                                    }
                                    context = dict(self.env.context)
                                    context.update({'check_move_validity': False, 'line_amount_type': invoice.get('LineAmountTypes')})
                                    self.env.context = context
                                    invoice_res[0].write(vals)
                                    if context.get('check_move_validity'):
                                        del context['check_move_validity']
                                        self.env.context = context
                                    # invoice_res[0]._compute_amount()
                                    for invoice_lines in invoice_res[0].invoice_line_ids:
                                        if invoice_lines.xero_invoice_line_id not in available_lines:
                                            invoice_lines.unlink()
                                    container = {'records': invoice_res[0]}

                                    invoice_res[0].invoice_line_ids._compute_totals()
                                    # invoice_res[0].with_context({'check_move_validity': False, 'line_amount_type': invoice.get('LineAmountTypes')})._sync_invoice(container)
                                    invoice_res[0].with_context({'line_amount_type': invoice.get('LineAmountTypes')})._compute_tax_totals()
                                    invoice_res[0].with_context({'check_move_validity': False, 'line_amount_type': invoice.get('LineAmountTypes')})._sync_dynamic_lines(container)
                                    invoice_res[0].with_context({'check_move_validity': False, 'line_amount_type': invoice.get('LineAmountTypes')})._compute_currency_id()
                            if invoice.get('Status') == 'AUTHORISED' and invoice_res[0].state in ['draft', 'posted'] and invoice_res[0].invoice_line_ids:
                                if invoice_res[0].state == 'draft':
                                    context = dict(self.env.context)
                                    context.update({'CurrencyRate': invoice.get('CurrencyRate')})
                                    self.env.context = context
                                    invoice_res[0].action_post()
                                    if context.get('CurrencyRate'):
                                        del context['CurrencyRate']
                                        self.env.context = context
                                if invoice.get('AmountPaid') != 0.0 or invoice.get('Total') !=  invoice.get('AmountDue'):
                                    if invoice_res[0].move_type in ('out_invoice', 'out_refund') and invoice.get('Type') == 'ACCREC':
                                        self.pay_invoice(xero_account_id, xero, invoice_res[0].partner_id, invoice, invoice_res[0], type='out_invoice', company=company)
                                    else:
                                        self.pay_invoice(xero_account_id, xero, invoice_res[0].partner_id, invoice, invoice_res[0], type='out_invoice', company=company)
                            if invoice.get('Status') == 'PAID' and invoice_res[0].state in ['draft', 'posted'] and invoice_res[0].invoice_line_ids:
                                if invoice_res[0].state == 'draft':
                                    invoice_res[0].with_context({'CurrencyRate': invoice.get('CurrencyRate')}).action_post()
                                if invoice_res[0].move_type in ('out_invoice', 'out_refund') and invoice.get('Type') == 'ACCREC':
                                    self.pay_invoice(xero_account_id, xero, invoice_res[0].partner_id, invoice, invoice_res[0], type='out_invoice', company=company)
                                else:
                                    self.pay_invoice(xero_account_id, xero, invoice_res[0].partner_id, invoice, invoice_res[0], type='out_invoice', company=company)

                    elif not invoice_res and import_option in ['create', 'both']:
                        flag = 0
                        if invoice and invoice.get('Status') not in ['DELETED', 'VOIDED']:
                            partner = partner_pool.search(['|', ('company_id', '=', company), ('company_id', '=', False)]).filtered(lambda partner: partner.contact_xero_company_ids.filtered(lambda contact: contact.xero_contact_id == invoice.get('Contact').get('ContactID') and contact.company_id.id == company))
                            if not partner:
                                xero_account.import_contact_overwrite() if xero_account.contact_overwrite else xero_account.import_contact()
                                partner = partner_pool.search(['|', ('company_id', '=', company), ('company_id', '=', False)]).filtered(lambda partner: partner.contact_xero_company_ids.filtered(lambda contact: contact.xero_contact_id == invoice.get('Contact').get('ContactID') and contact.company_id.id == company))

                            invoice_lines = []
                            sub_tax = invoice.get('SubTotal') + invoice.get('TotalTax')
                            if float("%.2f"%sub_tax) != invoice.get('Total'):
                                flag = 1

                            for lines in invoice.get('LineItems'):
                                acc = account_pool.search([('code', '=', lines.get('AccountCode')),('company_ids', 'in', company)])
                                # if not acc and invoice.get('Type') == 'ACCREC':
                                #     acc = self.env['ir.property']._get('property_account_income_categ_id', 'product.category')
                                # elif not acc and invoice.get('Type') == 'ACCPAY':
                                #     acc = self.env['ir.property']._get('property_account_expense_categ_id', 'product.category')

                                if flag == 1:
                                    line_amount = lines.get('LineAmount') - lines.get('TaxAmount')
                                    unit_amount = line_amount / lines.get('Quantity')
                                else:
                                    line_amount = lines.get('LineAmount')
                                    unit_amount = lines.get('UnitAmount')

                                tax_id = False
                                if lines.get('TaxType'):
                                    tax_id = tax_pool.search([('xero_tax_type', '=', lines.get('TaxType')),('company_id', '=', company)])

                                if lines.get('ItemCode'):
                                    if without_product:
                                        vals = {'name': lines.get('Description') or 'Didn\'t specify',
                                                'price_unit': abs(unit_amount) if unit_amount < 0 else unit_amount or 0.0,
                                                'company_id': company,
                                                'tax_ids': [(6, 0, tax_id.ids)] if tax_id else [],
                                                'quantity': -(lines.get('Quantity')) if unit_amount < 0 else lines.get('Quantity') or 1,
                                                'discount': lines.get('DiscountRate') or 0.0,
                                                'xero_invoice_line_id': lines.get('LineItemID'),
                                                }
                                        if acc:
                                            if unit_amount:
                                                vals.update({'account_id': acc and acc.id})
                                            invoice_lines.append((0, 0, vals))

                                    elif not without_product:
                                        product = product_pool.search([('default_code', '=', lines.get('ItemCode'))])
                                        if not product:
                                            raise UserError(_("Please First Import Product."))
                                        for product_id in product:
                                            inv_line_val = {'name': lines.get('Description') or product_id.description or 'Didn\'t specify',
                                                            'price_unit': abs(unit_amount) if unit_amount < 0 else unit_amount or 0.0,
                                                            'company_id': company,
                                                            'quantity': -(lines.get('Quantity')) if unit_amount < 0 else lines.get('Quantity') or 1,
                                                            'product_id': product_id and product_id.id or False,
                                                            'tax_ids': [(6, 0, tax_id.ids)] if tax_id else [],
                                                            'discount': lines.get('DiscountRate') or 0.0,
                                                            'xero_invoice_line_id': lines.get('LineItemID'),
                                                            }
                                            if acc:
                                                if unit_amount:
                                                    inv_line_val.update({'account_id': acc.id})
                                                invoice_lines.append((0, 0, inv_line_val))
                                else:
                                    vals = {'name': lines.get('Description') or 'Didn\'t specify',
                                            'price_unit': abs(unit_amount) if unit_amount < 0 else unit_amount or 0.0,
                                            'company_id': company,
                                            'tax_ids': [(6, 0, tax_id.ids)] if tax_id else [],
                                            'quantity': -(lines.get('Quantity')) if unit_amount < 0 else lines.get('Quantity') or 1,
                                            'discount': lines.get('DiscountRate') or 0.0,
                                            'xero_invoice_line_id': lines.get('LineItemID'),
                                            }
                                    if acc:
                                        if unit_amount:
                                            vals.update({'account_id': acc and acc.id})
                                        invoice_lines.append((0, 0, vals))

                            if invoice['Type'] == 'ACCREC':
                                xero_type = 'out_invoice'
                            else:
                                xero_type = 'in_invoice'

                            currency_id = currency_pool.search([('name', '=', invoice.get('CurrencyCode'))])
                            if invoice.get('Type') == 'ACCREC':
                                journal_id = customer_inv_journal_id
                            elif invoice.get('Type') == 'ACCPAY':
                                journal_id = vendor_bill_journal_id
                            inv_default = {'partner_id': partner and partner.id or False,
                                           'currency_id': currency_id and currency_id[0].id or False,
                                           'company_id': company,
                                           'journal_id': journal_id and journal_id.id or False,
                                           'invoice_date': invoice.get('DateString'),
                                           'invoice_date_due': invoice.get('DueDateString'),
                                           'xero_invoice_id': invoice.get('InvoiceID'),
                                           'xero_invoice_number': invoice.get('InvoiceNumber'),
                                           'amount_tax': invoice.get('TotalTax'),
                                           'move_type': xero_type,
                                           'line_amount_type':invoice.get('LineAmountTypes'),
                                           'ref': invoice.get('Reference') or '',
                                           }
                            invoice_type = self.env.context.copy()
                            invoice_type.update({'move_type': xero_type, 'line_amount_type': invoice.get('LineAmountTypes')})
                            if invoice_lines:
                                inv_default.update({'invoice_line_ids': invoice_lines})
                            inv_id = inv_pool.with_context(invoice_type).create(inv_default)
                            inv_id._compute_amount()
                            container = {'records': inv_id}
                            inv_id.with_context({'check_move_validity': False, 'line_amount_type': invoice.get('LineAmountTypes')})._sync_dynamic_lines(container)
                            inv_id.with_context({'check_move_validity': False, 'line_amount_type': invoice.get('LineAmountTypes')})._compute_currency_id()
                            inv_id.with_context({'line_amount_type': invoice.get('LineAmountTypes')})._compute_tax_totals()

                            if xero_type == 'out_invoice':
                                sale_id = self.env['sale.order'].search(['|', '|', ('xero_quotes_number', '=', inv_id.ref), ('xero_quotes_number', '=', inv_id.xero_invoice_number), ('name', '=', inv_id.ref)],limit=1)
                                if sale_id and not sale_id.invoice_ids:
                                    for inv_line in inv_id.invoice_line_ids:
                                        for sale_line in sale_id.order_line:
                                            if sale_line.product_id == inv_line.product_id and sale_line.product_uom_qty == inv_line.quantity:
                                                sale_line.invoice_lines = [(4, inv_line.id)]
                                    sale_id.invoice_ids = [(4, inv_id.id)]
                                    sale_id.invoice_count = len(sale_id.invoice_ids)
                            else:
                                purchase_id = self.env['purchase.order'].search(['|', '|', ('xero_po_number', '=', inv_id.ref), ('xero_po_number', '=', inv_id.xero_invoice_number),('name', '=', inv_id.ref)], limit=1)
                                if purchase_id and not purchase_id.invoice_ids:
                                    purchase_id.invoice_ids = [(4, inv_id.id)]
                                    purchase_id.invoice_count = len(purchase_id.invoice_ids)
                            self._cr.commit()

                            if inv_id and invoice.get('Status') in ['AUTHORISED', 'PAID'] and inv_id.invoice_line_ids:
                                if inv_id.state == 'draft':
                                    inv_id.with_context({'CurrencyRate': invoice.get('CurrencyRate'), 'line_amount_type': invoice.get('LineAmountTypes')}).action_post()

                                if inv_id and invoice.get('Status') == 'AUTHORISED':
                                    if invoice.get('AmountPaid') != 0.0 or invoice.get('Total') !=  invoice.get('AmountDue'):
                                        if inv_id.move_type in ['out_invoice', 'out_refund']:
                                            self.pay_invoice(xero_account_id, xero, inv_id.partner_id, invoice, inv_id, type='out_invoice', company=company)
                                        else:
                                            self.pay_invoice(xero_account_id, xero, inv_id.partner_id, invoice, inv_id, type='out_invoice', company=company)
                                if inv_id and invoice.get('Status') == 'PAID':
                                    if inv_id.move_type in ['out_invoice', 'out_refund']:
                                        self.pay_invoice(xero_account_id, xero, inv_id.partner_id, invoice, inv_id, type='out_invoice', company=company)
                                    else:
                                        self.pay_invoice(xero_account_id, xero, inv_id.partner_id, invoice, inv_id, type='out_invoice', company=company)
            except Exception as e:
                raise UserError(_('%s') % e)

    def import_credit_notes(self, xero_account_id, credit_notes_list, xero, company=False, without_product=False, import_option=None, customer_inv_journal_id=False, vendor_bill_journal_id=False):
        partner_pool = self.env['res.partner']
        currency_pool = self.env['res.currency']
        account_pool = self.env['account.account']
        product_pool = self.env['product.product']
        # ir_property_pool = self.env['ir.property']
        tax_pool = self.env['account.tax']
        mismatch_log = self.env['mismatch.log']
        xero_account = self.env['xero.account'].browse(xero_account_id)
        for credit_note in credit_notes_list:
            try:
                InvoiceData = {}
                if credit_note.get('Total') != 0.0:
                    current_credit_note = self.search([('xero_invoice_id', '=', credit_note.get('CreditNoteID')), ('xero_invoice_number','=', credit_note.get('CreditNoteNumber')), ('company_id', '=', company)], limit=1)

                    if current_credit_note and current_credit_note.state == 'draft' and import_option in ['update', 'both']:
                        current_credit_note.invoice_line_ids.with_context({'check_move_validity': False}).unlink()
                    invoice_type = 'out_refund' if credit_note['Type'] == 'ACCRECCREDIT' else 'in_refund'
                    if current_credit_note.state == 'draft' or not current_credit_note:
                        customer = partner_pool.search(['|', ('company_id', '=', company), ('company_id', '=', False)]).filtered(lambda partner: partner.contact_xero_company_ids.filtered(lambda contact: contact.xero_contact_id == credit_note.get('Contact').get('ContactID') and contact.company_id.id == company))
                        if not customer:
                            xero_account.import_contact_overwrite() if xero_account.contact_overwrite else xero_account.import_contact()
                            customer = partner_pool.search(['|', ('company_id', '=', company), ('company_id', '=', False)]).filtered(lambda partner: partner.contact_xero_company_ids.filtered(lambda contact: contact.xero_contact_id == credit_note.get('Contact').get('ContactID') and contact.company_id.id == company))

                        if credit_note.get('Type') == 'ACCRECCREDIT':
                            journal_id = customer_inv_journal_id
                        elif credit_note.get('Type') == 'ACCPAYCREDIT':
                            journal_id = vendor_bill_journal_id
                        currency_id = currency_pool.search([('name', '=', credit_note.get('CurrencyCode'))], limit=1)
                        InvoiceData.update({'partner_id': customer.id or False,
                                            'currency_id': currency_id and currency_id.id or False,
                                            'invoice_date': credit_note.get('DateString'),
                                            'invoice_date_due': credit_note.get('DueDateString'),
                                            'xero_invoice_number': credit_note.get('CreditNoteNumber'),
                                            'xero_invoice_id': credit_note.get('CreditNoteID'),
                                            'amount_tax': credit_note.get('TotalTax'),
                                            'move_type': invoice_type,
                                            'journal_id': journal_id and journal_id.id or False,
                                            'company_id': company,
                                            'line_amount_type': credit_note.get('LineAmountTypes')})

                        invoice_lines = []
                        for line in credit_note.get('LineItems'):
                            product_id = False
                            account_id = account_pool.search([('code', '=', line.get('AccountCode')), ('company_ids', 'in', company)], limit=1)
                            if line.get('ItemCode'):
                                product_id = product_pool.search([('default_code', '=', line.get('ItemCode'))], limit=1)
                            if product_id:
                                if credit_note['Type'] == 'ACCRECCREDIT':
                                    account_id = product_id.property_account_income_id or product_id.categ_id.property_account_income_categ_id
                                else:
                                    account_id = product_id.property_account_expense_id or product_id.categ_id.property_account_expense_categ_id
                            # if not account_id and credit_note['Type'] == 'ACCRECCREDIT':
                            #     account_id = ir_property_pool._get('property_account_income_categ_id', 'product.category')
                            # elif not account_id and credit_note['Type'] == 'ACCPAYCREDIT':
                            #     account_id = ir_property_pool._get('property_account_expense_categ_id', 'product.category')

                            tax_id = False
                            if line.get('TaxType'):
                                tax_id = tax_pool.search([('xero_tax_type', '=', line.get('TaxType')), ('company_id', '=', company)], limit=1)

                            if line.get('ItemCode'):
                                if without_product:
                                    inv_line_data = {
                                            'name': line.get('Description') or 'Didn\'t specify',
                                            'price_unit': abs(line.get('UnitAmount')) if line.get('UnitAmount') < 0 else line.get('UnitAmount') or 0.0,
                                            'company_id': company,
                                            'tax_ids': [(6, 0, tax_id.ids)] if tax_id else [],
                                            'account_id': account_id.id,
                                            'quantity': -(line.get('Quantity')) if line.get('UnitAmount') < 0 else line.get('Quantity') or 1}
                                    invoice_lines.append((0, 0, inv_line_data))
                                elif not without_product:
                                    if not product_id:
                                        raise UserError(_("Please First Import Product."))
                                    inv_line_data = {
                                            'product_id': product_id.id,
                                            'name': (line.get('Description') or product_id.description or product_id.name),
                                            'price_unit': abs(line.get('UnitAmount')) if line.get('UnitAmount') < 0 else line.get('UnitAmount') or 0.0,
                                            'company_id': company,
                                            'tax_ids': [(6, 0, tax_id.ids)] if tax_id else [],
                                            'account_id': account_id.id,
                                            'quantity': -(line.get('Quantity')) if line.get('UnitAmount') < 0 else line.get('Quantity') or 1}
                                    invoice_lines.append((0, 0, inv_line_data))
                            else:
                                inv_line_data = {
                                            'name': line.get('Description') or 'Didn\'t specify',
                                            'price_unit': abs(line.get('UnitAmount')) if line.get('UnitAmount') < 0 else line.get('UnitAmount') or 0.0,
                                            'company_id': company,
                                            'tax_ids': [(6, 0, tax_id.ids)] if tax_id else [],
                                            'account_id': account_id.id,
                                            'quantity': -(line.get('Quantity')) if line.get('UnitAmount') < 0 else line.get('Quantity') or 1}
                                invoice_lines.append((0, 0, inv_line_data))

                        InvoiceData.update({'invoice_line_ids': invoice_lines})
                    if current_credit_note and import_option in ['update', 'both']:
                        if current_credit_note.state == 'posted' and credit_note.get('Status') in ['VOIDED','DRAFT']:
                            current_credit_note.button_draft()
                        if current_credit_note.state == 'draft' and credit_note.get('Status') in ['DELETED', 'VOIDED']:
                            current_credit_note.button_cancel()
                        if current_credit_note.state != 'cancel' and current_credit_note.payment_state != 'paid':
                            context = self.env.context.copy()
                            context.update({'move_type': invoice_type, 'check_move_validity': False, 'line_amount_type': credit_note.get('LineAmountTypes')})
                            current_credit_note.with_context(context).write(InvoiceData)
                            current_credit_note._compute_amount()
                            # current_credit_note.with_context({'check_move_validity': False, 'line_amount_type': credit_note.get('LineAmountTypes')})._compute_currency_id()
                            current_credit_note.with_context({'line_amount_type': credit_note.get('LineAmountTypes')})._compute_tax_totals()
                            if current_credit_note.move_type == 'in_refund':
                                purchase_id = self.env['purchase.order'].search(['|',('xero_po_number', '=', current_credit_note.ref),('name', '=', current_credit_note.ref)],limit=1)
                                if purchase_id:
                                    purchase_id.invoice_ids = [(4, current_credit_note.id)]
                                    purchase_id.invoice_count = len(purchase_id.invoice_ids)
                            if current_credit_note.move_type == 'out_refund':
                                sale_id = self.env['sale.order'].search(['|',('xero_quotes_number', '=', current_credit_note.ref),('name', '=', current_credit_note.ref)],limit=1)
                                if sale_id:
                                    for inv_line in current_credit_note.invoice_line_ids:
                                        for sale_line in sale_id.order_line:
                                            if sale_line.product_id == inv_line.product_id and sale_line.product_uom_qty == inv_line.quantity:
                                                sale_line.invoice_lines = [(4, inv_line.id)]
                                    sale_id.invoice_ids = [(4, current_credit_note.id)]
                                    sale_id.invoice_count = len(sale_id.invoice_ids)
                            self._cr.commit()
                    elif not current_credit_note and import_option in ['create', 'both']:
                        if credit_note.get('Status') not in ['VOIDED', 'DELETED']:
                            context = self.env.context.copy()
                            context.update({'move_type': invoice_type, 'check_move_validity': False, 'line_amount_type': credit_note.get('LineAmountTypes')})
                            current_credit_note = self.with_context(context).create(InvoiceData)
                            current_credit_note._compute_amount()
                            current_credit_note._compute_currency_id()
                            if current_credit_note.move_type == 'in_refund':
                                purchase_id = self.env['purchase.order'].search(['|',('xero_po_number', '=', current_credit_note.ref),('name', '=', current_credit_note.ref)],limit=1)
                                if purchase_id:
                                    purchase_id.invoice_ids = [(4, current_credit_note.id)]
                                    purchase_id.invoice_count = len(purchase_id.invoice_ids)
                            if current_credit_note.move_type == 'out_refund':
                                sale_id = self.env['sale.order'].search(['|',('xero_quotes_number', '=', current_credit_note.ref),('name', '=', current_credit_note.ref)],limit=1)
                                if sale_id:
                                    for inv_line in current_credit_note.invoice_line_ids:
                                        for sale_line in sale_id.order_line:
                                            if sale_line.product_id == inv_line.product_id and sale_line.product_uom_qty == inv_line.quantity:
                                                sale_line.invoice_lines = [(4, inv_line.id)]
                                    sale_id.invoice_ids = [(4, current_credit_note.id)]
                                    sale_id.invoice_count = len(sale_id.invoice_ids)
                            self._cr.commit()

                    if current_credit_note.state != 'cancel' and current_credit_note.payment_state != 'paid':
                        if credit_note.get('Status') in ['AUTHORISED', 'PAID'] and current_credit_note.invoice_line_ids:
                            if current_credit_note.state == 'draft':
                                current_credit_note.with_context({'CurrencyRate': credit_note.get('CurrencyRate'),'line_amount_type': credit_note.get('LineAmountTypes')}).action_post()
                            if credit_note.get('Payments'):
                                self.pay_invoice(xero_account_id, xero, current_credit_note.partner_id, credit_note, current_credit_note, type=current_credit_note.move_type, company=company)
                            if credit_note.get('Allocations') and credit_note.get('Status') == 'PAID':
                                self.pay_creditnote(xero, current_credit_note.partner_id, credit_note, current_credit_note, type=current_credit_note.move_type, company=company)
            except Exception as e:
                raise UserError(_('%s') % e)

    def import_manual_journal(self, journal_list, xero, xero_account_id, company=False, import_option=None):
        tax_pool = self.env['account.tax']
        move_line_pool = self.env['account.move.line']
        mismatch_log = self.env['mismatch.log']
        for journal in journal_list:
            try:
                journal_res = self.search([('xero_manual_journal_id', '=', journal.get('ManualJournalID'))], limit=1)
                if journal_res and journal_res.state == 'draft' and import_option in ['update', 'both'] and journal.get('Status') in ['DRAFT', 'POSTED']:
                    journal_res.write({
                        'date': journal.get('Date'),
                        'ref': journal.get('Narration'),
                        'line_amount_type': journal.get('LineAmountTypes'),
                        })
                    journal_line_list = []

                    journal_res.line_ids.with_context(dynamic_unlink=True).unlink()
                    for line_id in journal.get('JournalLines'):
                        account = self.env['account.account'].search([('code', '=', line_id.get('AccountCode')), ('company_ids', 'in', company)], limit=1)
                        if not account:
                            _logger.info("Account Code '%s' is not available. Please First Import Chart of Account.", line_id.get('AccountCode'))
                            raise UserError(_("Account Code '%s' is not available. Please First Import Chart of Account.") % line_id.get('AccountCode'))

                        credit = debit = 0
                        if journal.get('LineAmountTypes') == 'Exclusive':
                            if '-' in str(line_id.get('LineAmount')):
                                credit = abs(line_id.get('LineAmount'))
                            else:
                                debit = line_id.get('LineAmount')

                            credit_tax_amount = debit_tax_amount = 0
                            journal_line = {}
                            if line_id.get('TaxType') and line_id.get('TaxAmount') != 0:
                                tax_id = tax_pool.search([('xero_tax_type', '=', line_id.get('TaxType')), ('company_id', '=', company)], limit=1)
                                journal_line = {'tax_ids': [(6, 0, tax_id.ids)]}
                                if '-' in str(line_id.get('TaxAmount')):
                                    credit_tax_amount = abs(line_id.get('TaxAmount'))
                                else:
                                    debit_tax_amount = line_id.get('TaxAmount')

                                # for tax in tax_id.invoice_repartition_line_ids:
                                #     if tax.repartition_type == 'tax':
                                #         journal_line_list.append((0, 0, {
                                #           'account_id': tax.account_id.id if tax.account_id else account.id,
                                #           'name': tax_id[0].name,
                                #           'debit': debit_tax_amount,
                                #           'credit': credit_tax_amount,
                                #           'move_id': journal_res.id,
                                #           }))

                        elif journal.get('LineAmountTypes') == 'Inclusive':
                            credit_tax_amount = debit_tax_amount = 0
                            journal_line = {}
                            if line_id.get('TaxType') and line_id.get('TaxAmount') != 0:
                                tax_id = tax_pool.search([('xero_tax_type', '=', line_id.get('TaxType')), ('company_id', '=', company)])
                                journal_line = {'tax_ids': [(6, 0, tax_id.ids)]}
                                if '-' in str(line_id.get('TaxAmount')):
                                    credit_tax_amount = abs(line_id.get('TaxAmount'))
                                else:
                                    debit_tax_amount = line_id.get('TaxAmount')

                                # for tax in tax_id.invoice_repartition_line_ids:
                                #     if tax.repartition_type == 'tax':
                                #         journal_line_list.append((0, 0, {
                                #           'account_id': tax.account_id.id if tax.account_id else account.id,
                                #           'name': tax_id[0].name,
                                #           'debit': debit_tax_amount,
                                #           'credit': credit_tax_amount,
                                #           'move_id': journal_res.id,
                                #           }))
                            if '-' in str(line_id.get('LineAmount')):
                                credit = abs(line_id.get('LineAmount')) - credit_tax_amount
                            else:
                                debit = line_id.get('LineAmount') - debit_tax_amount

                        elif journal.get('LineAmountTypes') == 'NoTax':
                            journal_line = {'tax_ids': [(5,)]}
                            credit = debit = 0
                            if '-' in str(line_id.get('LineAmount')):
                                credit = abs(line_id.get('LineAmount'))
                            else:
                                debit = line_id.get('LineAmount')

                        journal_line.update({
                              'account_id': account.id,
                              'name': line_id.get('Description'),
                              'debit': debit,
                              'credit': credit,
                              'move_id': journal_res.id,
                              })

                        journal_line_list.append((0, 0, journal_line))
                    journal_res.write({'line_ids': journal_line_list})
                    container = {'records': journal_res}
                    # journal_res._sync_rounding_lines(container)
                    # journal_res._onchange_recompute_dynamic_lines()

                    if journal.get('Status').lower() == 'posted':
                        journal_res.action_post()
                    self._cr.commit()

                elif not journal_res and import_option in ['create', 'both'] and journal.get('Status') in ['DRAFT','POSTED']:
                    xero_account_id = self.env['xero.account'].search([('company_id', '=', company)], limit=1)
                    journal_id = self.create({
                        'date': journal.get('Date'),
                        'ref': journal.get('Narration'),
                        'line_amount_type': journal.get('LineAmountTypes'),
                        'journal_id': xero_account_id.miscellaneous_operations_journal_id.id,
                        'xero_manual_journal_id': journal.get('ManualJournalID'),
                        'is_manual_journal': True,
                        })
                    journal_line_list = []
                    for line_id in journal.get('JournalLines'):
                        account = self.env['account.account'].search([('code', '=', line_id.get('AccountCode')),('company_ids', 'in', company)], limit=1)

                        if not account:
                            _logger.info("Account Code '%s' is not available. Please First Import Chart of Account.",line_id.get('AccountCode'))
                            raise UserError(_("Account Code '%s' is not available. Please First Import Chart of Account.") % line_id.get('AccountCode'))

                        credit = debit = 0

                        if journal.get('LineAmountTypes') == 'Exclusive':
                            if '-' in str(line_id.get('LineAmount')):
                                credit = abs(line_id.get('LineAmount'))
                            else:
                                debit = line_id.get('LineAmount')
                            credit_tax_amount = debit_tax_amount = 0
                            journal_line = {}
                            if line_id.get('TaxType') and line_id.get('TaxAmount') != 0:
                                tax_id = tax_pool.search([('xero_tax_type', '=', line_id.get('TaxType')),('company_id', '=', company)], limit=1)
                                journal_line = {'tax_ids': [(6, 0, tax_id.ids)]}
                                if '-' in str(line_id.get('TaxAmount')):
                                    credit_tax_amount = abs(line_id.get('TaxAmount'))
                                else:
                                    debit_tax_amount = line_id.get('TaxAmount')
                                # for tax in tax_id.invoice_repartition_line_ids:
                                #     if tax.repartition_type == 'tax':
                                #         journal_line_list.append((0, 0, {
                                #           'account_id': tax.account_id.id if tax.account_id else account.id,
                                #           'name': tax_id.name,
                                #           'debit': debit_tax_amount,
                                #           'credit': credit_tax_amount,
                                #           'move_id': journal_id.id,
                                #           }))
                        elif journal.get('LineAmountTypes') == 'Inclusive':
                            credit_tax_amount = debit_tax_amount = 0
                            journal_line = {}
                            if line_id.get('TaxType') and line_id.get('TaxAmount') != 0:
                                tax_id = tax_pool.search([('xero_tax_type', '=', line_id.get('TaxType')),('company_id', '=', company)], limit=1)
                                journal_line = {'tax_ids': [(6, 0, tax_id.ids)]}

                                if '-' in str(line_id.get('TaxAmount')):
                                    credit_tax_amount = abs(line_id.get('TaxAmount'))
                                else:
                                    debit_tax_amount = line_id.get('TaxAmount')

                                # for tax in tax_id.invoice_repartition_line_ids:
                                #     if tax.repartition_type == 'tax':
                                #         journal_line_list.append((0, 0, {
                                #           'account_id': tax.account_id.id if tax.account_id else account.id,
                                #           'name': tax_id.name,
                                #           'debit': debit_tax_amount,
                                #           'credit': credit_tax_amount,
                                #           'move_id': journal_id.id,
                                #           }))
                            if '-' in str(line_id.get('LineAmount')):
                                credit = abs(line_id.get('LineAmount')) - credit_tax_amount
                            else:
                                debit = line_id.get('LineAmount') - debit_tax_amount

                        elif journal.get('LineAmountTypes') == 'NoTax':
                            credit = debit = 0
                            journal_line = {'tax_ids': [(5,)]}
                            if '-' in str(line_id.get('LineAmount')):
                                credit = abs(line_id.get('LineAmount'))
                            else:
                                debit = line_id.get('LineAmount')

                        journal_line.update({'account_id': account.id,
                                             'name': line_id.get('Description'),
                                             'debit': debit,
                                             'credit': credit,
                                             'move_id': journal_id.id,
                                             })
                        journal_line_list.append((0, 0, journal_line))
                    journal_id.write({'line_ids': journal_line_list})
                    container = {'records': journal_id}
                    # journal_id._sync_dynamic_lines(container)
                    # journal_id._onchange_recompute_dynamic_lines()
                    if journal.get('Status').lower() == 'posted':
                        journal_id.action_post()
                    self._cr.commit()
            except Exception as e:
                raise UserError(_('%s') % e)

    def export_invoice(self, invoice_list, xero, last_export_date, xero_account_id, company=False, disable_export=False):
        xero_account = self.env['xero.account'].search([('company_id', '=', company)], limit=1)
        if self._context.get('invoice_ids'):
            invoice_ids = self._context.get('invoice_ids')
        else:
            if last_export_date:
                invoice_ids = self.search([('company_id', '=', company),
                                           ('able_to_xero_export', '=', True),
                                           ('move_type', 'in', ['out_invoice', 'in_invoice']),
                                           '|', ('state', '!=', 'cancel'),
                                           ('xero_invoice_number', '!=', False),
                                           '|', ('write_date', '>=', last_export_date),
                                           ('create_date', '>=', last_export_date)])
            else:
                invoice_ids = self.search([('company_id', '=', company),
                                           ('able_to_xero_export', '=', True),
                                           ('move_type', 'in', ['out_invoice', 'in_invoice']),
                                           '|', ('state', '!=', 'cancel'),
                                           ('xero_invoice_number', '!=', False)])
        update_invoice_data = []
        update_invoice_data_list = []
        create_invoice_data = []
        create_invoice_data_list = []
        count = 0
        c = 0
        request = 0
        mismatch_log = self.env['mismatch.log']
        for invoice_id in invoice_ids:
            if invoice_id.move_type == 'out_invoice':
                type = u'ACCREC'
            elif invoice_id.move_type == 'in_invoice':
                type = u'ACCPAY'
            if not invoice_id.partner_id:
                description = 'Customer must be set for export (Odoo to Xero)'
                mismatch_log.create({'name': invoice_id.name,
                                     'source_model': 'account.move',
                                     'source_id': invoice_id.id,
                                     'description': description,
                                     'date': datetime.datetime.now(),
                                     'option': 'export',
                                     'xero_account_id': xero_account_id
                                     })
                continue

            if invoice_id.xero_invoice_id:
                for xero_inv in invoice_list:
                    if xero_inv.get('InvoiceID') == invoice_id.xero_invoice_id and xero_inv.get('Status') in ['DRAFT','SUBMITTED'] :
                        invoice_currency_rate = 0.0
                        status = u'DRAFT'
                        if invoice_id.state == 'draft':
                            status = u'DRAFT'
                        elif invoice_id.state == 'cancel':
                            status = u'DELETED'
                        elif invoice_id.state == 'posted':
                            status = u'AUTHORISED'
                            if invoice_id.currency_id.id != invoice_id.company_id.currency_id.id:
                                if invoice_id.move_type == 'out_invoice':
                                    if not invoice_id.line_ids[0].amount_currency == 0.0 and not invoice_id.line_ids[0].debit == 0.0:
                                        invoice_currency_rate = abs(invoice_id.line_ids[0].amount_currency / invoice_id.line_ids[0].debit)
                                else:
                                    if not invoice_id.line_ids[0].amount_currency == 0.0 and not invoice_id.line_ids[0].credit == 0.0:
                                        invoice_currency_rate = abs(invoice_id.line_ids[0].amount_currency / invoice_id.line_ids[0].credit)

                        line_amount_type = invoice_id.line_amount_type

                        partner_details = {}
                        if invoice_id.partner_id.parent_id:
                            contact = invoice_id.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            if not contact.xero_contact_id:
                                xero_account.export_contact_overwrite() if xero_account.contact_overwrite else xero_account.export_contact()
                                contact = invoice_id.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            partner_details = {u'ContactID': contact.xero_contact_id}
                        else:
                            contact = invoice_id.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            if not contact.xero_contact_id:
                                xero_account.export_contact_overwrite() if xero_account.contact_overwrite else xero_account.export_contact()
                                contact = invoice_id.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            partner_details = {u'ContactID': contact.xero_contact_id}
                        invoice_data = {u'Type': type,
                                        u'InvoiceID': invoice_id.xero_invoice_id,
                                        u'Status': status,
                                        u'LineAmountTypes': line_amount_type,
                                        u'Contact': partner_details,
                                        u'Date': invoice_id.invoice_date or fields.Date.today(),
                                        u'DueDate': invoice_id.invoice_date_due or fields.Date.today(),
                                        u'CurrencyCode': invoice_id.currency_id.name,
                                        }
                        if invoice_id.move_type == 'out_invoice':
                            invoice_data.update({u'Reference': invoice_id.name if invoice_id.name != '/' else str(invoice_id.id) or u''})
                        elif invoice_id.move_type == 'in_invoice':
                            invoice_data.update({u'InvoiceNumber': invoice_id.name if invoice_id.name != '/' else str(invoice_id.id) or u''})
                        if invoice_currency_rate:
                            invoice_data.update({u'CurrencyRate': invoice_currency_rate})
                        line_items = []
                        for inv_line in invoice_id.invoice_line_ids.filtered(lambda p: p.product_id or (p.name and p.account_id)):
                            if inv_line.product_id:
                                product = inv_line.product_id.product_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                                if product and not product[0].xero_item_id:
                                    xero_account.export_product()
                            if inv_line.tax_ids:
                                tax_type = inv_line.tax_ids[0]
                            else:
                                if invoice_id.move_type == 'out_invoice':
                                    tax_type = self.env['account.tax'].search([('type_tax_use', '=', 'sale'), ('amount', '=', 0), ('company_id', '=', company)], limit=1)
                                    if not tax_type:
                                        company_id = self.env['res.company'].browse(company)
                                        raise UserError(_('Please create account tax of type \'SALE\' and amount = 0.0 for Company: %s')% (company_id.name))
                                else:
                                    tax_type = self.env['account.tax'].search([('type_tax_use', '=', 'purchase'), ('amount', '=', 0), ('company_id', '=', company)], limit=1)
                                    if not tax_type:
                                        company_id = self.env['res.company'].browse(company)
                                        raise UserError(_('Please create account tax of type \'PURCHASE\' and amount = 0.0 for Company: %s')%(company_id.name))
                            if not tax_type.xero_tax_type:
                                tax_rates = xero.taxrates.all()
                                self.env['account.tax'].export_tax(tax_rates, xero, xero_account_id, company=company, disable_export=disable_export)
                            if inv_line.xero_invoice_line_id:
                                vals = {u'LineItemID': inv_line.xero_invoice_line_id,
                                        u'AccountCode': inv_line.account_id.code,
                                        u'Description': inv_line.name or inv_line.product_id.name,
                                        u'UnitAmount': -(inv_line.price_unit) if inv_line.quantity < 0 else inv_line.price_unit,
                                        u'TaxType': u'' if inv_line.move_id.line_amount_type == 'NoTax' else  tax_type and tax_type.xero_tax_type or u'',
                                        u'ValidationErrors': [],
                                        u'Quantity': abs(inv_line.quantity) if inv_line.quantity < 0 else inv_line.quantity,
                                        }
                            else:
                                vals = {u'AccountCode': inv_line.account_id.code,
                                        u'Description': inv_line.name or inv_line.product_id.name,
                                        u'UnitAmount': -(inv_line.price_unit) if inv_line.quantity < 0 else inv_line.price_unit,
                                        u'TaxType': u'' if inv_line.move_id.line_amount_type == 'NoTax' else  tax_type and tax_type.xero_tax_type or u'',
                                        u'ValidationErrors': [],
                                        u'Quantity': abs(inv_line.quantity) if inv_line.quantity < 0 else inv_line.quantity,
                                        }
                            if invoice_id.move_type == 'out_invoice':
                                vals.update({u'DiscountRate': inv_line.discount or 0.0})

                            if inv_line.product_id:
                                vals.update({u'ItemCode': inv_line.product_id.default_code or 'XeroTest'})
                            line_items.append(vals)
                        if line_items:
                            invoice_data.update({u'LineItems': line_items})
                        # invoice in draft state individual request
                        if invoice_id.state == 'draft':
                            request += 1
                            if request > 30:
                                time.sleep(1)
                            inv_rec = xero.invoices.save(invoice_data)
                            if inv_rec[0].get('ValidationErrors'):
                                description = inv_rec[0].get('ValidationErrors')[0].get('Message')
                                mismatch_log.create({'name': inv_rec[0].get('InvoiceNumber') or inv_rec[0].get('Reference'),
                                                     'source_model': 'account.move',
                                                     'source_id': invoice_id.id,
                                                     'description': description,
                                                     'date': fields.Datetime.now(),
                                                     'option': 'export',
                                                     'xero_account_id': xero_account_id})
                                continue
                            if inv_rec[0].get('LineItems'):
                                for lines in inv_rec[0].get('LineItems'):
                                    inv_line = invoice_id.invoice_line_ids.filtered(lambda l: not l.xero_invoice_line_id
                                        and (l.product_id.default_code or 'XeroTest') == lines.get('ItemCode')
                                        and l.price_unit == lines.get('UnitAmount')
                                        and l.quantity == lines.get('Quantity'))
                                    if inv_line:
                                        inv_line[0].xero_invoice_line_id = lines.get('LineItemID')
                                        inv_line[0].move_id = invoice_id.id

                            self._cr.commit()
                        else:
                            update_invoice_data.append(invoice_data)
                            count += 1
                            if count == 50:
                                update_invoice_data_list.append(update_invoice_data)
                                update_invoice_data = []
                                count = 0

            elif not invoice_id.xero_invoice_id and invoice_id.state != 'cancel':
                if invoice_id.state == 'posted':
                    # set state of invoice 'paid' not possible at a time.
                    status = u'AUTHORISED'
                else:
                    status = u'DRAFT'

                invoice_currency_rate = 0.0
                if invoice_id.state == 'posted' and invoice_id.currency_id.id != invoice_id.company_id.currency_id.id:
                    if invoice_id.move_type == 'out_invoice':
                        if not invoice_id.line_ids[0].amount_currency == 0.0 and not invoice_id.line_ids[0].debit == 0.0:
                            invoice_currency_rate = abs(invoice_id.line_ids[0].amount_currency / invoice_id.line_ids[0].debit)
                    else:
                        if not invoice_id.line_ids[0].amount_currency == 0.0 and not invoice_id.line_ids[0].credit == 0.0:
                            invoice_currency_rate = abs(invoice_id.line_ids[0].amount_currency / invoice_id.line_ids[0].credit)

                line_amount_type = invoice_id.line_amount_type

                partner_details = {}
                if invoice_id.partner_id.parent_id:
                    contact = invoice_id.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                    if not contact.xero_contact_id:
                        xero_account.export_contact_overwrite() if xero_account.contact_overwrite else xero_account.export_contact()
                        contact = invoice_id.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                    partner_details = {u'ContactID': contact.xero_contact_id}
                else:
                    contact = invoice_id.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                    if not contact.xero_contact_id:
                        xero_account.export_contact_overwrite() if xero_account.contact_overwrite else xero_account.export_contact()
                        contact = invoice_id.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                    partner_details = {u'ContactID': contact.xero_contact_id}
                final_invoice_data = {u'Type': type,
                                      u'Contact': partner_details,
                                      u'Date': invoice_id.invoice_date or fields.Date.today(),
                                      u'DueDate': invoice_id.invoice_date_due or fields.Date.today(),
                                      u'Status': status,
                                      u'LineAmountTypes': line_amount_type,
                                      u'CurrencyCode': invoice_id.currency_id.name,
                                      }
                if invoice_id.move_type == 'out_invoice':
                    final_invoice_data.update({u'Reference': invoice_id.name if invoice_id.name != '/' else str(invoice_id.id) or u''})
                elif invoice_id.move_type == 'in_invoice':
                    final_invoice_data.update({u'InvoiceNumber': invoice_id.name if invoice_id.name != '/' else str(invoice_id.id) or u''})

                if invoice_currency_rate:
                    final_invoice_data.update({u'CurrencyRate': invoice_currency_rate})
                if invoice_id.invoice_line_ids:
                    line_items = []
                    for invoice_line in invoice_id.invoice_line_ids.filtered(lambda p: p.product_id or (p.name and p.account_id)):
                        if invoice_line.product_id:
                            product = invoice_line.product_id.product_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            if product and not product[0].xero_item_id:
                                xero_account.export_product()
                        if invoice_line.tax_ids:
                            tax_type = invoice_line.tax_ids[0]
                        else:
                            if invoice_id.move_type == 'out_invoice':
                                tax_type = self.env['account.tax'].search([('type_tax_use', '=', 'sale'), ('amount', '=', 0), ('company_id', '=', company)], limit=1)
                                if not tax_type:
                                    company_id = self.env['res.company'].browse(company)
                                    raise UserError(_('Please create account tax of type \'SALE\' and amount = 0.0 for Company: %s')% (company_id.name))
                            else:
                                tax_type = self.env['account.tax'].search([('type_tax_use', '=', 'purchase'), ('amount', '=', 0), ('company_id', '=', company)], limit=1)
                                if not tax_type:
                                    company_id = self.env['res.company'].browse(company)
                                    raise UserError(_('Please create account tax of type \'PURCHASE\' and amount = 0.0 for Company: %s')%(company_id.name))
                        if not tax_type.xero_tax_type:
                            tax_rates = xero.taxrates.all()
                            self.env['account.tax'].export_tax(tax_rates, xero, xero_account_id, company=company, disable_export=disable_export)
                        vals = {u'AccountCode': invoice_line.account_id.code,
                                u'Description': invoice_line.name or invoice_line.product_id.name,
                                u'UnitAmount': -(invoice_line.price_unit) if invoice_line.quantity < 0 else invoice_line.price_unit,
                                u'TaxType': u'' if invoice_line.move_id.line_amount_type == 'NoTax' else  tax_type and tax_type.xero_tax_type or u'',
                                u'ValidationErrors': [],
                                u'Quantity': abs(invoice_line.quantity) if invoice_line.quantity < 0 else invoice_line.quantity,
                            }
                        if invoice_id.move_type == 'out_invoice':
                            vals.update({u'DiscountRate': invoice_line.discount or 0.0})

                        if invoice_line.product_id:
                            vals.update({u'ItemCode': invoice_line.product_id.default_code or 'XeroTest'})
                        line_items.append(vals)
                    if line_items:
                        final_invoice_data.update({u'LineItems': line_items})

                    # invoice request batch process
                create_invoice_data.append(final_invoice_data)
                c += 1
                if c == 50:
                    create_invoice_data_list.append(create_invoice_data)
                    create_invoice_data = []
                    c = 0

        if create_invoice_data:
            create_invoice_data_list.append(create_invoice_data)

        for data in create_invoice_data_list:
            inv_rec = xero.invoices.put(data)
            for inv in inv_rec:
                if inv.get('ValidationErrors'):
                    description = inv.get('ValidationErrors')[0].get('Message')
                    mismatch_log.create({'name': inv.get('InvoiceNumber') or inv.get('Reference'),
                                         'source_model': 'account.move',
                                         'description': description,
                                         'date': fields.Datetime.now(),
                                         'option': 'export',
                                         'xero_account_id': xero_account_id})
                    continue
                if inv.get('Type') == 'ACCREC':
                    try:
                        invoice_id = int(inv.get('Reference'))
                        invoice_id = self.browse(invoice_id)
                    except Exception as e:
                        invoice_id = self.search([('name', '=', inv.get('Reference')), ('company_id', '=', company), ('move_type', '=', 'out_invoice')],limit=1)
                    # invoice_id = self.search([('name', '=', inv.get('Reference')), ('company_id', '=', company), ('move_type', '=', 'out_invoice')],limit=1)
                else:
                    try:
                        invoice_id = int(inv.get('InvoiceNumber'))
                        invoice_id = self.browse(invoice_id)
                    except Exception as e:
                        invoice_id = self.search([('name', '=', inv.get('InvoiceNumber')), ('company_id', '=', company), ('move_type', '=', 'in_invoice')],limit=1)
                    # invoice_id = self.search([('name', '=', inv.get('InvoiceNumber')), ('company_id', '=', company), ('move_type', '=', 'in_invoice')],limit=1)
                if inv.get('LineItems'):
                    for lines in inv.get('LineItems'):
                        inv_line = invoice_id.invoice_line_ids.filtered(lambda l: not l.xero_invoice_line_id
                            and (l.product_id.default_code or 'XeroTest') == lines.get('ItemCode')
                            and l.price_unit == lines.get('UnitAmount')
                            and l.quantity == lines.get('Quantity'))
                        if inv_line:
                            inv_line[0].xero_invoice_line_id = lines.get('LineItemID')
                            inv_line[0].move_id = invoice_id[0].id

                invoice_id.write({'xero_invoice_id': inv.get('InvoiceID'), 'xero_invoice_number': inv.get('InvoiceNumber')})
                self._cr.commit()

        if update_invoice_data:
            update_invoice_data_list.append(update_invoice_data)

        for data in update_invoice_data_list:
            inv_rec = xero.invoices.save(data)
            for inv in inv_rec:
                if inv.get('ValidationErrors'):
                    description = inv.get('ValidationErrors')[0].get('Message')
                    mismatch_log.create({'name': inv.get('InvoiceNumber') or inv.get('Reference'),
                                         'source_model': 'account.move',
                                         'description': description,
                                         'date': fields.Datetime.now(),
                                         'option': 'export',
                                         'xero_account_id': xero_account_id})
                    continue
                if inv.get('Type') == 'ACCREC':
                    try:
                        invoice_id = int(inv.get('Reference'))
                        invoice_id = self.browse(invoice_id)
                    except Exception as e:
                        invoice_id = self.search([('name', '=', inv.get('Reference')), ('move_type', '=', 'out_invoice'), ('company_id', '=', company)],limit=1)
                    # invoice_id = self.search([('name', '=', inv.get('Reference')), ('move_type', '=', 'out_invoice'), ('company_id', '=', company)],limit=1)
                else:
                    try:
                        invoice_id = int(inv.get('InvoiceNumber'))
                        invoice_id = self.browse(invoice_id)
                    except Exception as e:
                        invoice_id = self.search([('name', '=', inv.get('InvoiceNumber')), ('move_type', '=', 'in_invoice'), ('company_id', '=', company)],limit=1)
                    # invoice_id = self.search([('name', '=', inv.get('InvoiceNumber')), ('move_type', '=', 'in_invoice'), ('company_id', '=', company)],limit=1)
                if inv.get('LineItems'):
                    for lines in inv.get('LineItems'):
                        inv_line = invoice_id.invoice_line_ids.filtered(lambda l: not l.xero_invoice_line_id
                            and (l.product_id.default_code or 'XeroTest') == lines.get('ItemCode')
                            and l.price_unit == lines.get('UnitAmount')
                            and l.quantity == lines.get('Quantity'))
                        if inv_line:
                            inv_line[0].xero_invoice_line_id = lines.get('LineItemID')
                            inv_line[0].move_id = invoice_id.id
                self._cr.commit()

    def export_payment(self, xero, xero_account_id, company=False, disable_export=False):
        data = []
        payment_data = []
        c = 0
        mismatch_log = self.env['mismatch.log']
        if self._context.get('invoice_ids'):
            invoice_ids = self._context.get('invoice_ids')
        else:
            invoice_ids = self.search([('company_id', '=', company),
                                       ('able_to_xero_export', '=', True),
                                       ('move_type', 'in', ['out_invoice', 'in_invoice']),
                                       ('state', '!=', 'cancel')])
        for invoice_id in invoice_ids:
            for inv_payment in invoice_id._get_reconciled_info_JSON_values():
                payment = self.env['account.payment'].browse(inv_payment.get('account_payment_id'))
                if not payment.xero_payment_id and payment.state in ['in_process', 'paid']:
                    if invoice_id.move_type == 'out_invoice':
                        payment_type = u'ACCRECPAYMENT'
                        type = u'ACCREC'
                    elif invoice_id.move_type == 'in_invoice':
                        payment_type = u'ACCPAYPAYMENT'
                        type = u'ACCPAY'
                    if not payment.move_id.invoice_line_ids[0].journal_id.xero_account_id:
                        raise UserError(_('Please select your xero account on payment Journal: %s')%(payment.move_id.invoice_line_ids[0].journal_id.name))
                    if not payment.move_id.invoice_line_ids[0].journal_id.xero_account_id.acc_id:
                        account_list = xero.accounts.all()
                        self.env['account.account'].export_account(account_list, xero, False, xero_account_id,company=company, disable_export=disable_export)
                    if not payment.journal_id.xero_account_id.acc_id:
                        raise UserError(_('Please select correct Xero account for Payment.'))
                    if type == 'ACCREC':
                        if payment.currency_id != payment.company_id.currency_id and payment.currency_id == invoice_id.currency_id:
                            currency_rate = abs(payment.move_id.invoice_line_ids[0].amount_currency / payment.move_id.invoice_line_ids[0].debit)
                        elif payment.currency_id == payment.company_id.currency_id and payment.currency_id != invoice_id.currency_id:
                            currency_rate = abs(payment.move_id.invoice_line_ids[1].amount_currency / payment.move_id.invoice_line_ids[1].credit)
                        elif payment.currency_id and payment.move_id.invoice_line_ids[1].debit and (payment.currency_id != invoice_id.company_id.currency_id or payment.currency_id != invoice_id.currency_id):
                            currency_rate = abs(payment.move_id.invoice_line_ids[1].amount_currency / payment.move_id.invoice_line_ids[1].debit)
                        elif payment.currency_id == payment.company_id.currency_id == invoice_id.currency_id:
                            currency_rate = 1.00
                        else:
                            currency_rate = 1.00
                    else:
                        if payment.currency_id != payment.company_id.currency_id and payment.currency_id == invoice_id.currency_id:
                            currency_rate = abs(payment.move_id.invoice_line_ids[0].amount_currency / payment.move_id.invoice_line_ids[0].credit)
                        elif payment.currency_id == payment.company_id.currency_id and payment.currency_id != invoice_id.currency_id:
                            currency_rate = abs(payment.move_id.invoice_line_ids[1].amount_currency / payment.move_id.invoice_line_ids[1].credit)
                        elif payment.currency_id and (payment.currency_id != invoice_id.company_id.currency_id or payment.currency_id != invoice_id.currency_id):
                            currency_rate = abs(payment.move_id.invoice_line_ids[1].amount_currency / payment.move_id.invoice_line_ids[1].credit)
                        elif payment.currency_id == payment.company_id.currency_id == invoice_id.currency_id:
                            currency_rate = 1.00
                        else:
                            currency_rate = 1.00

                    amount = inv_payment.get('amount')

                    if invoice_id.partner_id.parent_id:
                        contact = invoice_id.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                        if contact.xero_contact_id:
                            contact_id = {
                                u'ContactID': contact.xero_contact_id,
                                u'Name': invoice_id.partner_id.parent_id.name or False
                                }
                    else:
                        contact = invoice_id.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                        if contact.xero_contact_id:
                            contact_id = {
                                u'ContactID': contact.xero_contact_id,
                                u'Name': invoice_id.partner_id.name or False
                                }
                    payment_vals = {u'Date': payment.date or False,
                                    u'Amount': amount or 0.0,
                                    u'Reference': payment.name,
                                    u'CurrencyRate': currency_rate,
                                    u'PaymentType': payment_type,
                                    u'Status' : 'Paid',
                                    u'IsReconciled': 'true',
                                    u'Account': {
                                                u'AccountID': payment.journal_id.xero_account_id.acc_id or False,
                                                u'Code': payment.journal_id.xero_account_id.code or False,
                                                u'EnablePaymentsToAccount': True,
                                                },
                                    u'Invoice': {u'Type': type,
                                                 u'InvoiceID': invoice_id.xero_invoice_id or False,
                                                 u'InvoiceNumber': invoice_id.xero_invoice_number or False,
                                                 u'Contact': contact_id,
                                                 },
                                    }
                    data.append(payment_vals)
                    c += 1
                    if c == 50:
                        payment_data.append(data)
                        data = []
                        c = 0

        if data:
            payment_data.append(data)
        for data in payment_data:
            xero_payment = xero.payments.put(data)
            for payment in xero_payment:
                if payment.get('ValidationErrors'):
                    description = payment.get('ValidationErrors')[0].get('Message')
                    mismatch_log.create({'name': payment.get('Reference'),
                                         'source_model': 'account.payment',
                                         'description': description,
                                         'date': fields.Datetime.now(),
                                         'option': 'export',
                                         'xero_account_id': xero_account_id})
                    continue
                payment_id = self.env['account.payment'].search([('name', '=', payment.get('Reference')), ('company_id', '=', company)],limit=1)
                payment_id.xero_payment_id = payment.get('PaymentID')
                self._cr.commit()

    def export_credit_notes(self, credit_notes_list, xero, last_export_date, xero_account_id, company=False, disable_export=False):
        partner_pool = self.env['res.partner']
        product_pool = self.env['product.product']
        xero_account = self.env['xero.account'].browse(xero_account_id)
        if self._context.get('invoice_ids'):
            invoice_ids = self._context.get('invoice_ids')
        else:
            if last_export_date:
                invoice_ids = self.search([('company_id', '=', company),
                                           ('able_to_xero_export', '=', True),
                                           ('move_type', 'in', ['out_refund', 'in_refund']),
                                           '|', ('state', '!=', 'cancel'),
                                           ('xero_invoice_number', '!=', False),
                                           '|', ('write_date', '>=', last_export_date),
                                           ('create_date', '>=', last_export_date)])
            else:
                invoice_ids = self.search([('company_id', '=', company),
                                           ('able_to_xero_export', '=', True),
                                           ('move_type', 'in', ['out_refund', 'in_refund']),
                                           '|', ('state', '!=', 'cancel'),
                                           ('xero_invoice_number', '!=', False)])
        update_creditnote_data = []
        update_creditnote_data_list = []
        create_creditnote_data = []
        create_creditnote_data_list = []
        c = 0
        count = 0
        request = 0
        mismatch_log = self.env['mismatch.log']
        for invoice_id in invoice_ids:
            if invoice_id.move_type == 'out_refund':
                type = u'ACCRECCREDIT'
            elif invoice_id.move_type == 'in_refund':
                type = u'ACCPAYCREDIT'

            if not invoice_id.partner_id:
                description = 'Customer must be set for export (Odoo to Xero)'
                mismatch_log.create({'name': invoice_id.name,
                                     'source_model': 'account.move',
                                     'source_id': invoice_id.id,
                                     'description': description,
                                     'date': datetime.datetime.now(),
                                     'option': 'export',
                                     'xero_account_id': xero_account_id
                                     })
                continue
            if invoice_id.xero_invoice_id:
                for xero_inv in credit_notes_list:
                    if xero_inv.get('CreditNoteID') == invoice_id.xero_invoice_id and xero_inv.get('Status') in ['DRAFT','SUBMITTED', 'AUTHORISED'] : #['SUBMITTED','AUTHORISED']
                        invoice_currency_rate = 0.0
                        status = u'DRAFT'

                        if invoice_id.state == 'draft':
                            status = u'DRAFT'
                        elif invoice_id.state == 'cancel':
                            status = u'DELETED'
                        elif invoice_id.state == 'posted':
                            status = u'AUTHORISED'
                            if invoice_id.currency_id.id != invoice_id.company_id.currency_id.id:
                                if invoice_id.move_type == 'in_refund':
                                    if not invoice_id.line_ids[0].amount_currency == 0.0 and not invoice_id.line_ids[0].debit == 0.0:
                                        invoice_currency_rate = abs(invoice_id.line_ids[0].amount_currency / invoice_id.line_ids[0].debit)
                                else:
                                    if not invoice_id.line_ids[0].amount_currency == 0.0 and not invoice_id.line_ids[0].credit == 0.0:
                                        invoice_currency_rate = abs(invoice_id.line_ids[0].amount_currency / invoice_id.line_ids[0].credit)

                        line_amount_type = invoice_id.line_amount_type
                        partner_details = {}
                        if invoice_id.partner_id.parent_id:
                            contact = invoice_id.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            if not contact.xero_contact_id:
                                xero_account.export_contact_overwrite() if xero_account.contact_overwrite else xero_account.export_contact()
                                contact = invoice_id.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            partner_details = {u'ContactID': contact.xero_contact_id}
                        else:
                            contact = invoice_id.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            if not contact.xero_contact_id:
                                xero_account.export_contact_overwrite() if xero_account.contact_overwrite else xero_account.export_contact()
                                contact = invoice_id.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            partner_details = {u'ContactID': contact.xero_contact_id}

                        invoice_data = {u'Type': type,
                                        u'CreditNoteID': invoice_id.xero_invoice_id,
                                        u'Status': status,
                                        u'Reference': invoice_id.name or u'',
                                        u'LineAmountTypes': line_amount_type,
                                        u'Contact': partner_details,
                                        u'Date': invoice_id.invoice_date or fields.Date.today(),
                                        u'DueDate': invoice_id.invoice_date_due or fields.Date.today(),
                                        u'CurrencyCode': invoice_id.currency_id.name,
                                        }
                        if invoice_currency_rate:
                            invoice_data.update({u'CurrencyRate': invoice_currency_rate})

                        line_items = []
                        for inv_line in invoice_id.invoice_line_ids.filtered(lambda p: p.product_id or (p.name or p.account_id)):
                            if inv_line.product_id:
                                product = inv_line.product_id.product_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                                if product and not product[0].xero_item_id:
                                    xero_account.export_product()

                            if inv_line.tax_ids:
                                tax_type = inv_line.tax_ids[0]
                            else:
                                if invoice_id.move_type == 'in_refund':
                                    tax_type = self.env['account.tax'].search([('type_tax_use', '=', 'sale'), ('amount', '=', 0), ('company_id', '=', company)], limit=1)
                                    if not tax_type:
                                        company_id = self.env['res.company'].browse(company)
                                        raise UserError(_('Please create account tax of type \'SALE\' and amount = 0.0 for Company: %s')% (company_id.name))
                                else:
                                    tax_type = self.env['account.tax'].search([('type_tax_use', '=', 'purchase'), ('amount', '=', 0), ('company_id', '=', company)], limit=1)
                                    if not tax_type:
                                        company_id = self.env['res.company'].browse(company)
                                        raise UserError(_('Please create account tax of type \'PURCHASE\' and amount = 0.0 for Company: %s')%(company_id.name))
                            if not tax_type.xero_tax_type:
                                tax_rates = xero.taxrates.all()
                                self.env['account.tax'].export_tax(tax_rates, xero, xero_account_id, company=company, disable_export=disable_export)

                            if inv_line.xero_invoice_line_id:
                                vals = {u'LineItemID': inv_line.xero_invoice_line_id,
                                        u'AccountCode': inv_line.account_id.code,
                                        u'Description': inv_line.name or inv_line.product_id.name,
                                        u'UnitAmount': -(inv_line.price_unit) if inv_line.quantity < 0 else inv_line.price_unit,
                                        u'TaxType': tax_type and tax_type.xero_tax_type if line_amount_type != 'NoTax' else u'',
                                        # u'ValidationErrors': [],
                                        u'Quantity': abs(inv_line.quantity) if inv_line.quantity < 0 else inv_line.quantity,
                                        }
                            else:
                                vals = {u'AccountCode': inv_line.account_id.code,
                                        u'Description': inv_line.name or inv_line.product_id.name,
                                        u'UnitAmount': -(inv_line.price_unit) if inv_line.quantity < 0 else inv_line.price_unit,
                                        u'TaxType': tax_type and tax_type.xero_tax_type if line_amount_type != 'NoTax' else u'',
                                        # u'ValidationErrors': [],
                                        u'Quantity': abs(inv_line.quantity) if inv_line.quantity < 0 else inv_line.quantity,
                                        }

                            if inv_line.product_id:
                                vals.update({u'ItemCode': inv_line.product_id.default_code or 'XeroTest'})
                            line_items.append(vals)

                            if line_items:
                                invoice_data.update({u'LineItems': line_items})

                            if invoice_id.state == 'draft':
                                request += 1
                                if request > 30:
                                    time.sleep(1)
                                inv_rec = xero.creditnotes.save(invoice_data)
                                if inv_rec[0].get('ValidationErrors'):
                                    description = inv_rec[0].get('ValidationErrors')[0].get('Message')
                                    mismatch_log.create({'name': inv_rec[0].get('InvoiceNumber') or inv_rec[0].get('Reference'),
                                                         'source_model': 'account.move',
                                                         'source_id': invoice_id.id,
                                                         'description': description,
                                                         'date': fields.Datetime.now(),
                                                         'option': 'export',
                                                         'xero_account_id': xero_account_id})
                                    continue
                            else:
                                update_creditnote_data.append(invoice_data)
                                count += 1
                                if count == 50:
                                    update_creditnote_data_list.append(update_creditnote_data)
                                    update_creditnote_data = []
                                    count = 0

            elif not invoice_id.xero_invoice_id:
                if invoice_id.state == 'posted':
                     # set state of invoice 'paid' not possible at a time.
                    status = u'AUTHORISED'
                else:
                    status = u'DRAFT'

                invoice_currency_rate = 0.0
                if invoice_id.state == 'posted' and invoice_id.currency_id.id != invoice_id.company_id.currency_id.id:
                    if invoice_id.move_type == 'out_refund':
                        if not invoice_id.line_ids[0].amount_currency == 0.0 and not invoice_id.line_ids[0].debit == 0.0:
                            invoice_currency_rate = abs(invoice_id.line_ids[0].amount_currency / invoice_id.line_ids[0].debit)
                    else:
                        if not invoice_id.line_ids[0].amount_currency == 0.0 and not invoice_id.line_ids[0].credit == 0.0:
                            invoice_currency_rate = abs(invoice_id.line_ids[0].amount_currency / invoice_id.line_ids[0].credit)

                line_amount_type = invoice_id.line_amount_type

                partner_details = {}
                if invoice_id.partner_id.parent_id:
                    contact = invoice_id.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                    if not contact.xero_contact_id:
                        xero_account.export_contact_overwrite() if xero_account.contact_overwrite else xero_account.export_contact()
                        contact = invoice_id.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                    partner_details = {u'ContactID': contact.xero_contact_id}
                else:
                    contact = invoice_id.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                    if not contact.xero_contact_id:
                        xero_account.export_contact_overwrite() if xero_account.contact_overwrite else xero_account.export_contact()
                        contact = invoice_id.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                    partner_details = {u'ContactID': contact.xero_contact_id}

                final_invoice_data = {u'Type': type,
                                      u'Contact': partner_details,
                                      u'Reference': invoice_id.name or u'',
                                      u'Date': invoice_id.invoice_date or fields.Date.today(),
                                      u'DueDate': invoice_id.invoice_date_due or fields.Date.today(),
                                      u'Status': status,
                                      u'LineAmountTypes': line_amount_type,
                                      u'CurrencyCode': invoice_id.currency_id.name,
                                      }

                if invoice_currency_rate:
                    final_invoice_data.update({u'CurrencyRate': invoice_currency_rate})
                if invoice_id.invoice_line_ids:
                    line_items = []
                    for invoice_line in invoice_id.invoice_line_ids.filtered(lambda p: p.product_id or (p.name or p.account_id)):
                        if invoice_line.product_id:
                            product = invoice_line.product_id.product_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            if product and not product[0].xero_item_id:
                                xero_account.export_product()

                        if invoice_line.tax_ids:
                            tax_type = invoice_line.tax_ids[0]
                        else:
                            if invoice_id.move_type == 'out_refund':
                                tax_type = self.env['account.tax'].search([('type_tax_use', '=', 'sale'), ('amount', '=', 0), ('company_id', '=', company)], limit=1)
                                if not tax_type:
                                    company_id = self.env['res.company'].browse(company)
                                    raise UserError(_('Please create account tax of type \'SALE\' and amount = 0.0 for Company: %s')% (company_id.name))
                            else:
                                tax_type = self.env['account.tax'].search([('type_tax_use', '=', 'purchase'), ('amount', '=', 0), ('company_id', '=', company)], limit=1)
                                if not tax_type:
                                    company_id = self.env['res.company'].browse(company)
                                    raise UserError(_('Please create account tax of type \'PURCHASE\' and amount = 0.0 for Company: %s')%(company_id.name))
                        if not tax_type.xero_tax_type:
                            tax_rates = xero.taxrates.all()
                            self.env['account.tax'].export_tax(tax_rates, xero, xero_account_id, company=company, disable_export=disable_export)
                        vals = {u'AccountCode': invoice_line.account_id.code,
                                u'Description': invoice_line.name or invoice_line.product_id.name,
                                u'UnitAmount': -(invoice_line.price_unit) if invoice_line.quantity < 0 else invoice_line.price_unit,
                                u'TaxType': tax_type and tax_type.xero_tax_type if line_amount_type != 'NoTax' else u'',
                                # u'ValidationErrors': [],
                                u'Quantity': abs(invoice_line.quantity) if invoice_line.quantity < 0 else invoice_line.quantity,
                            }

                        if invoice_line.product_id:
                            vals.update({u'ItemCode': invoice_line.product_id.default_code or 'XeroTest'})
                        line_items.append(vals)
                    if line_items:
                        final_invoice_data.update({u'LineItems': line_items})
                # creditnote draft state individual request
                if invoice_id.state == 'draft':
                    request += 1
                    if request > 30:
                        time.sleep(1)
                    inv_rec = xero.creditnotes.put(final_invoice_data)
                    if inv_rec[0].get('ValidationErrors'):
                        description = inv_rec[0].get('ValidationErrors')[0].get('Message')
                        mismatch_log.create({'name': inv_rec[0].get('InvoiceNumber') or inv_rec[0].get('Reference'),
                                             'source_model': 'account.move',
                                             'source_id': invoice_id.id,
                                             'description': description,
                                             'date': fields.Datetime.now(),
                                             'option': 'export',
                                             'xero_account_id': xero_account_id})
                        continue
                    invoice_id.write({'xero_invoice_id': inv_rec[0].get('CreditNoteID'), 'xero_invoice_number': inv_rec[0].get('CreditNoteNumber')})
                    self._cr.commit()
                else:
                    # creditnote batch processing
                    create_creditnote_data.append(final_invoice_data)
                    c += 1
                    if c == 50:
                        create_creditnote_data_list.append(create_creditnote_data)
                        create_creditnote_data = []
                        c = 0

        if create_creditnote_data:
            create_creditnote_data_list.append(create_creditnote_data)
        for data in create_creditnote_data_list:
            inv_rec = xero.creditnotes.put(data)
            for inv in inv_rec:
                if inv.get('ValidationErrors'):
                    description = inv.get('ValidationErrors')[0].get('Message')
                    mismatch_log.create({'name': inv.get('InvoiceNumber') or inv.get('Reference'),
                                         'source_model': 'account.move',
                                         'description': description,
                                         'date': fields.Datetime.now(),
                                         'option': 'export',
                                         'xero_account_id': xero_account_id})
                    continue
                invoice_id = self.search([('name', '=', inv.get('Reference')), ('company_id', '=', company)],limit=1)
                invoice_id.write({'xero_invoice_id': inv.get('CreditNoteID'), 'xero_invoice_number': inv.get('CreditNoteNumber')})
                self._cr.commit()

        if update_creditnote_data:
            update_creditnote_data_list.append(update_creditnote_data)
        for data in update_creditnote_data_list:
            inv_rec = xero.creditnotes.save(data)
            for inv in inv_rec:
                if inv.get('ValidationErrors'):
                    description = inv.get('ValidationErrors')[0].get('Message')
                    mismatch_log.create({'name': inv.get('InvoiceNumber') or inv.get('Reference'),
                                         'source_model': 'account.move',
                                         'description': description,
                                         'date': fields.Datetime.now(),
                                         'option': 'export',
                                         'xero_account_id': xero_account_id})
                    continue

    def allocate_credit_note_payment(self, credit_note_guid, allocations, xero):
        """
        Must pass in xero as it needs credentials if using public method.

        allocations should be an array of dictionaries containing amount, invoice:invoice idXero GUIDs for the contacts.
        """
        # Store the original endpoint base_url

        old_base_url = xero.creditnotes.base_url
        old_name = xero.creditnotes.name
        old_singular = xero.creditnotes.singular
        # Call the API
        try:
            xero.creditnotes.base_url = '{}/CreditNotes/{}'.format(old_base_url, credit_note_guid)
            xero.creditnotes.name = 'Allocations'
            xero.creditnotes.singular = 'Allocation'
            xero.creditnotes.put(allocations)
        except:
            raise
        finally:
            # Reset the base_url
            xero.creditnotes.base_url = old_base_url
            xero.creditnotes.name = old_name
            xero.creditnotes.singular = old_singular

    def export_credit_notes_payment(self, xero, xero_account_id, company=False, disable_export=False):
        payment_data = []
        data = []
        c = 0
        count = 0
        if self._context.get('invoice_ids'):
            invoice_ids = self._context.get('invoice_ids')
        else:
            invoice_ids = self.search([('company_id', '=', company),
                                   ('able_to_xero_export', '=', True),
                                   ('move_type', 'in', ['out_refund', 'in_refund']),
                                   ('state', 'not in', ['draft', 'cancel'])])
        mismatch_log = self.env['mismatch.log']
        for invoice_id in invoice_ids:
            allocations = []
            if not invoice_id.xero_credit_note_allocation and invoice_id.payment_state == 'paid':
                for payment in invoice_id._get_reconciled_info_JSON_values():
                    pay_invoice = self.browse(payment['move_id'])
                    if pay_invoice.xero_invoice_id:
                        allocations.append({u'AppliedAmount': payment['amount'],
                                            u'Date': payment['date'],
                                            u'Invoice': {u'InvoiceID': pay_invoice.xero_invoice_id}})
                if allocations:
                    self.allocate_credit_note_payment(invoice_id.xero_invoice_id, allocations, xero)
                    invoice_id.xero_credit_note_allocation = True
                    self._cr.commit()

            for inv_payment in invoice_id._get_reconciled_info_JSON_values():
                payment = self.env['account.payment'].browse(inv_payment.get('account_payment_id'))
                if not payment.xero_payment_id and payment.state in ['in_process', 'paid']:
                    if invoice_id.move_type == 'in_refund':
                        payment_type = u'ACCRECPAYMENT'
                        type = u'ACCRECCREDIT'
                    elif invoice_id.move_type == 'out_refund':
                        payment_type = u'ACCPAYPAYMENT'
                        type = u'ACCPAYCREDIT'
                    if not payment.move_id.invoice_line_ids[0].journal_id.xero_account_id:
                        raise UserError(_('Please select your xero account on payment Journal: %s')%(payment.move_id.invoice_line_ids[0].journal_id.name))
                    if not payment.move_id.invoice_line_ids[0].journal_id.xero_account_id.acc_id:
                        account_list = xero.accounts.all()
                        self.env['account.account'].export_account(account_list, xero, False, xero_account_id, company=company, disable_export=disable_export)
                    if not payment.journal_id.xero_account_id.acc_id:
                        raise UserError(_('Please select correct Xero account for Payment.'))
                    if type == 'ACCRECCREDIT':
                        if payment.currency_id != payment.company_id.currency_id and payment.currency_id == invoice_id.currency_id:
                            currency_rate = abs(payment.move_id.invoice_line_ids[0].amount_currency / payment.move_id.invoice_line_ids[0].debit)
                        elif payment.currency_id == payment.company_id.currency_id and payment.currency_id != invoice_id.currency_id:
                            currency_rate = abs(payment.move_id.invoice_line_ids[1].amount_currency / payment.move_id.invoice_line_ids[1].debit)
                        elif payment.currency_id and (payment.currency_id != invoice_id.company_id.currency_id or payment.currency_id != invoice_id.currency_id):
                            currency_rate = abs(payment.move_id.invoice_line_ids[1].amount_currency / payment.move_id.invoice_line_ids[1].debit)
                        elif payment.currency_id == payment.company_id.currency_id == invoice_id.currency_id:
                            currency_rate = 1.00
                        else:
                            currency_rate = 1.00
                    else:
                        if payment.currency_id != payment.company_id.currency_id and payment.currency_id == invoice_id.currency_id:
                            currency_rate = abs(payment.move_id.invoice_line_ids[0].amount_currency / payment.move_id.invoice_line_ids[0].credit)
                        elif payment.currency_id == payment.company_id.currency_id and payment.currency_id != invoice_id.currency_id:
                            currency_rate = abs(payment.move_id.invoice_line_ids[1].amount_currency / payment.move_id.invoice_line_ids[1].credit)
                        elif payment.currency_id and payment.move_id.invoice_line_ids[1].credit and (payment.currency_id != invoice_id.company_id.currency_id or payment.currency_id != invoice_id.currency_id):
                            currency_rate = abs(payment.move_id.invoice_line_ids[1].amount_currency / payment.move_id.invoice_line_ids[1].credit)
                        elif payment.currency_id == payment.company_id.currency_id == invoice_id.currency_id:
                            currency_rate = 1.00
                        else:
                            currency_rate = 1.00
                    amount = inv_payment.get('amount')

                    if invoice_id.partner_id.parent_id:
                        contact = invoice_id.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                        if contact.xero_contact_id:
                            contact_id = {u'ContactID': contact.xero_contact_id}
                    else:
                        contact = invoice_id.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                        if contact.xero_contact_id:
                            contact_id = {u'ContactID': contact.xero_contact_id}

                    payment_vals = {u'Date': payment.date or False,
                                    u'Amount': amount or 0.0,
                                    u'Reference': payment.name or False,
                                    u'CurrencyRate': currency_rate,
                                    u'PaymentType': payment_type,
                                    u'Status': 'Paid',
                                    u'IsReconciled': 'true',
                                    u'Account': {
                                                u'AccountID': payment.journal_id.xero_account_id.acc_id or False,
                                                u'Code': payment.journal_id.xero_account_id.code or False,
                                                },
                                    u'Invoice': {
                                                u'Type': type,
                                                u'InvoiceID': invoice_id.xero_invoice_id or False,
                                                u'InvoiceNumber': invoice_id.xero_invoice_number or False,
                                                u'Contact': contact_id,
                                                },
                                    }

                    data.append(payment_vals)
                    c += 1
                    if c == 50:
                        payment_data.append(data)
                        data = []
                        c = 0

        if data:
            payment_data.append(data)
        for data in payment_data:
            xero_payment = xero.payments.put(data)
            for payment in xero_payment:
                if payment.get('ValidationErrors'):
                    description = payment.get('ValidationErrors')[0].get('Message')
                    mismatch_log.create({'name': payment.get('Reference'),
                                         'source_model': 'account.payment',
                                         'description': description,
                                         'date': fields.Datetime.now(),
                                         'option': 'export',
                                         'xero_account_id': xero_account_id})
                    continue
                payment_id = self.env['account.payment'].search([('name', '=', payment.get('Reference')), ('company_id', '=', company)])
                payment_id.xero_payment_id = payment.get('PaymentID')
                self._cr.commit()

    def action_export_invoice(self):
        context = self._context
        companies = self.env.user.company_ids
        for company_id in companies:
            invoice_data = self.filtered(lambda invoice: (invoice.company_id == company_id or not invoice.company_id) and invoice.able_to_xero_export)
            if invoice_data:
                context.update({'invoice_ids': invoice_data})
                xero_account = self.env['xero.account'].search([('company_id', '=', company_id.id)], limit=1)
                if xero_account and invoice_data[0].move_type in ['out_invoice','in_invoice']:
                    xero_account.with_context(context).export_invoice()
                    xero_account.with_context(context).export_payment()
                elif xero_account and invoice_data[0].move_type in ['out_refund','in_refund'] and xero_account.import_export_creditnotes == 'export':
                    xero_account.with_context(context).export_credit_notes()
                    xero_account.with_context(context).export_credit_notes_payment()


class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    xero_invoice_line_id = fields.Char('Xero InvoiceLine ID', readonly=True, copy=False)
    xero_invoice_payment_id = fields.Char('Xero Payment ID', readonly=True, copy=False)


    @api.depends('quantity', 'discount', 'price_unit', 'tax_ids', 'currency_id')
    def _compute_totals(self):
        for rec in self:
            context = dict(self.env.context)
            context.update({'line_amount_type': rec.move_id.line_amount_type})
            self.env.context = context
        return super(AccountMoveLine, self)._compute_totals()

    # @api.depends('tax_ids', 'currency_id', 'partner_id', 'analytic_distribution', 'balance', 'partner_id', 'move_id.partner_id', 'price_unit', 'quantity')
    # def _compute_all_tax(self):
    #     for rec in self:
    #         context = dict(self.env.context)
    #         context.update({'line_amount_type': rec.move_id.line_amount_type})
    #         self.env.context = context
    #     return super(AccountMoveLine, self)._compute_all_tax()

    # @api.model
    # def _prepare_reconciliation_single_partial(self, debit_values, credit_values, shadowed_aml_values=None):
    #     """ Prepare the values to create an account.partial.reconcile later when reconciling the dictionaries passed
    #     as parameters, each one representing an account.move.line.
    #     :param debit_values:  The values of account.move.line to consider for a debit line.
    #     :param credit_values: The values of account.move.line to consider for a credit line.
    #     :param shadowed_aml_values: A mapping aml -> dictionary to replace some original aml values to something else.
    #                                 This is usefull if you want to preview the reconciliation before doing some changes
    #                                 on amls like changing a date or an account.
    #     :return: A dictionary:
    #         * debit_values:     None if the line has nothing left to reconcile.
    #         * credit_values:    None if the line has nothing left to reconcile.
    #         * partial_values:   The newly computed values for the partial.
    #         * exchange_values:  The values to create an exchange difference linked to this partial.
    #     """
    #     # ==== Determine the currency in which the reconciliation will be done ====
    #     # In this part, we retrieve the residual amounts, check if they are zero or not and determine in which
    #     # currency and at which rate the reconciliation will be done.
    #     context = dict(self.env.context) or {}
    #     if context.get('allocation_amount'):
    #         allocation_amount = context.get('allocation_amount')

    #         res = {
    #             'debit_values': debit_values,
    #             'credit_values': credit_values,
    #         }
    #         debit_aml = debit_values['aml']
    #         credit_aml = credit_values['aml']
    #         debit_currency = debit_aml._get_reconciliation_aml_field_value('currency_id', shadowed_aml_values)
    #         credit_currency = credit_aml._get_reconciliation_aml_field_value('currency_id', shadowed_aml_values)
    #         company_currency = debit_aml.company_currency_id

    #         remaining_debit_amount_curr = allocation_amount
    #         remaining_credit_amount_curr = -(allocation_amount)
    #         remaining_debit_amount = allocation_amount
    #         remaining_credit_amount = -(allocation_amount)

    #         debit_available_residual_amounts = self._prepare_move_line_residual_amounts(
    #             debit_values,
    #             credit_currency,
    #             shadowed_aml_values=shadowed_aml_values,
    #             other_aml_values=credit_values,
    #         )
    #         credit_available_residual_amounts = self._prepare_move_line_residual_amounts(
    #             credit_values,
    #             debit_currency,
    #             shadowed_aml_values=shadowed_aml_values,
    #             other_aml_values=debit_values,
    #         )

    #         if debit_currency != company_currency \
    #             and debit_currency in debit_available_residual_amounts \
    #             and debit_currency in credit_available_residual_amounts:
    #             recon_currency = debit_currency
    #         elif credit_currency != company_currency \
    #             and credit_currency in debit_available_residual_amounts \
    #             and credit_currency in credit_available_residual_amounts:
    #             recon_currency = credit_currency
    #         else:
    #             recon_currency = company_currency

    #         debit_recon_values = debit_available_residual_amounts.get(recon_currency)
    #         credit_recon_values = credit_available_residual_amounts.get(recon_currency)

    #         # Check if there is something left to reconcile. Move to the next loop iteration if not.
    #         skip_reconciliation = False
    #         if not debit_recon_values:
    #             res['debit_values'] = None
    #             skip_reconciliation = True
    #         if not credit_recon_values:
    #             res['credit_values'] = None
    #             skip_reconciliation = True
    #         if skip_reconciliation:
    #             return res

    #         recon_debit_amount = debit_recon_values['residual']
    #         recon_credit_amount = -credit_recon_values['residual']

    #         # ==== Match both lines together and compute amounts to reconcile ====

    #         # Special case for exchange difference lines. In that case, both lines are sharing the same foreign
    #         # currency but at least one has no amount in foreign currency.
    #         # In that case, we don't want a rate for the opposite line because the exchange difference is supposed
    #         # to reduce only the amount in company currency but not the foreign one.
    #         exchange_line_mode = \
    #             recon_currency == company_currency \
    #             and debit_currency == credit_currency \
    #             and (
    #                 not debit_available_residual_amounts.get(debit_currency)
    #                 or not credit_available_residual_amounts.get(credit_currency)
    #             )

    #         # Determine which line is fully matched by the other.
    #         compare_amounts = recon_currency.compare_amounts(recon_debit_amount, recon_credit_amount)
    #         min_recon_amount = min(recon_debit_amount, recon_credit_amount)
    #         debit_fully_matched = compare_amounts <= 0
    #         credit_fully_matched = compare_amounts >= 0

    #         # ==== Computation of partial amounts ====
    #         if recon_currency == company_currency:
    #             if exchange_line_mode:
    #                 debit_rate = None
    #                 credit_rate = None
    #             else:
    #                 debit_rate = debit_available_residual_amounts.get(debit_currency, {}).get('rate')
    #                 credit_rate = credit_available_residual_amounts.get(credit_currency, {}).get('rate')

    #             # Compute the partial amount expressed in company currency.
    #             partial_amount = min_recon_amount

    #             # Compute the partial amount expressed in foreign currency.
    #             if debit_rate:
    #                 partial_debit_amount_currency = debit_currency.round(debit_rate * min_recon_amount)
    #                 partial_debit_amount_currency = min(partial_debit_amount_currency, remaining_debit_amount_curr)
    #             else:
    #                 partial_debit_amount_currency = 0.0
    #             if credit_rate:
    #                 partial_credit_amount_currency = credit_currency.round(credit_rate * min_recon_amount)
    #                 partial_credit_amount_currency = min(partial_credit_amount_currency, -remaining_credit_amount_curr)
    #             else:
    #                 partial_credit_amount_currency = 0.0

    #         else:
    #             # recon_currency != company_currency
    #             if exchange_line_mode:
    #                 debit_rate = None
    #                 credit_rate = None
    #             else:
    #                 debit_rate = debit_recon_values['rate']
    #                 credit_rate = credit_recon_values['rate']

    #             # Compute the partial amount expressed in foreign currency.
    #             if debit_rate:
    #                 partial_debit_amount = company_currency.round(min_recon_amount / debit_rate)
    #                 partial_debit_amount = min(partial_debit_amount, remaining_debit_amount)
    #             else:
    #                 partial_debit_amount = 0.0
    #             if credit_rate:
    #                 partial_credit_amount = company_currency.round(min_recon_amount / credit_rate)
    #                 partial_credit_amount = min(partial_credit_amount, -remaining_credit_amount)
    #             else:
    #                 partial_credit_amount = 0.0
    #             partial_amount = min(partial_debit_amount, partial_credit_amount)

    #             # Compute the partial amount expressed in foreign currency.
    #             # Take care to handle the case when a line expressed in company currency is mimicking the foreign
    #             # currency of the opposite line.
    #             if debit_currency == company_currency:
    #                 partial_debit_amount_currency = partial_amount
    #             else:
    #                 partial_debit_amount_currency = min_recon_amount
    #             if credit_currency == company_currency:
    #                 partial_credit_amount_currency = partial_amount
    #             else:
    #                 partial_credit_amount_currency = min_recon_amount

    #         # Computation of the partial exchange difference. You can skip this part using the
    #         # `no_exchange_difference` context key (when reconciling an exchange difference for example).
    #         if not self._context.get('no_exchange_difference'):
    #             exchange_lines_to_fix = self.env['account.move.line']
    #             amounts_list = []
    #             if recon_currency == company_currency:
    #                 if debit_fully_matched:
    #                     debit_exchange_amount = remaining_debit_amount_curr - partial_debit_amount_currency
    #                     if not debit_currency.is_zero(debit_exchange_amount):
    #                         exchange_lines_to_fix += debit_aml
    #                         amounts_list.append({'amount_residual_currency': debit_exchange_amount})
    #                         remaining_debit_amount_curr -= debit_exchange_amount
    #                 if credit_fully_matched:
    #                     credit_exchange_amount = remaining_credit_amount_curr + partial_credit_amount_currency
    #                     if not credit_currency.is_zero(credit_exchange_amount):
    #                         exchange_lines_to_fix += credit_aml
    #                         amounts_list.append({'amount_residual_currency': credit_exchange_amount})
    #                         remaining_credit_amount_curr += credit_exchange_amount

    #             else:
    #                 if debit_fully_matched:
    #                     # Create an exchange difference on the remaining amount expressed in company's currency.
    #                     debit_exchange_amount = remaining_debit_amount - partial_amount
    #                     if not company_currency.is_zero(debit_exchange_amount):
    #                         exchange_lines_to_fix += debit_aml
    #                         amounts_list.append({'amount_residual': debit_exchange_amount})
    #                         remaining_debit_amount -= debit_exchange_amount
    #                         if debit_currency == company_currency:
    #                             remaining_debit_amount_curr -= debit_exchange_amount
    #                 else:
    #                     # Create an exchange difference ensuring the rate between the residual amounts expressed in
    #                     # both foreign and company's currency is still consistent regarding the rate between
    #                     # 'amount_currency' & 'balance'.
    #                     debit_exchange_amount = partial_debit_amount - partial_amount
    #                     if company_currency.compare_amounts(debit_exchange_amount, 0.0) > 0:
    #                         exchange_lines_to_fix += debit_aml
    #                         amounts_list.append({'amount_residual': debit_exchange_amount})
    #                         remaining_debit_amount -= debit_exchange_amount
    #                         if debit_currency == company_currency:
    #                             remaining_debit_amount_curr -= debit_exchange_amount

    #                 if credit_fully_matched:
    #                     # Create an exchange difference on the remaining amount expressed in company's currency.
    #                     credit_exchange_amount = remaining_credit_amount + partial_amount
    #                     if not company_currency.is_zero(credit_exchange_amount):
    #                         exchange_lines_to_fix += credit_aml
    #                         amounts_list.append({'amount_residual': credit_exchange_amount})
    #                         remaining_credit_amount -= credit_exchange_amount
    #                         if credit_currency == company_currency:
    #                             remaining_credit_amount_curr -= credit_exchange_amount
    #                 else:
    #                     # Create an exchange difference ensuring the rate between the residual amounts expressed in
    #                     # both foreign and company's currency is still consistent regarding the rate between
    #                     # 'amount_currency' & 'balance'.
    #                     credit_exchange_amount = partial_amount - partial_credit_amount
    #                     if company_currency.compare_amounts(credit_exchange_amount, 0.0) < 0:
    #                         exchange_lines_to_fix += credit_aml
    #                         amounts_list.append({'amount_residual': credit_exchange_amount})
    #                         remaining_credit_amount -= credit_exchange_amount
    #                         if credit_currency == company_currency:
    #                             remaining_credit_amount_curr -= credit_exchange_amount

    #             if exchange_lines_to_fix:
    #                 res['exchange_values'] = exchange_lines_to_fix._prepare_exchange_difference_move_vals(
    #                     amounts_list,
    #                     exchange_date=max(
    #                         debit_aml._get_reconciliation_aml_field_value('date', shadowed_aml_values),
    #                         credit_aml._get_reconciliation_aml_field_value('date', shadowed_aml_values),
    #                     ),
    #                 )

    #         # ==== Create partials ====

    #         remaining_debit_amount -= partial_amount
    #         remaining_credit_amount += partial_amount
    #         remaining_debit_amount_curr -= partial_debit_amount_currency
    #         remaining_credit_amount_curr += partial_credit_amount_currency

    #         res['partial_values'] = {
    #             'amount': partial_amount,
    #             'debit_amount_currency': partial_debit_amount_currency,
    #             'credit_amount_currency': partial_credit_amount_currency,
    #             'debit_move_id': debit_aml.id,
    #             'credit_move_id': credit_aml.id,
    #         }

    #         debit_values['amount_residual'] = remaining_debit_amount
    #         debit_values['amount_residual_currency'] = remaining_debit_amount_curr
    #         credit_values['amount_residual'] = remaining_credit_amount
    #         credit_values['amount_residual_currency'] = remaining_credit_amount_curr

    #         if debit_fully_matched:
    #             res['debit_values'] = None
    #         if credit_fully_matched:
    #             res['credit_values'] = None
    #         return res
    #     else:
    #         return super(AccountMoveLine, self)._prepare_reconciliation_single_partial(debit_values, credit_values, shadowed_aml_values=shadowed_aml_values)

    # @api.model
    # def _prepare_reconciliation_single_partial(self, debit_vals, credit_vals):
    #     """ Prepare the values to create an account.partial.reconcile later when reconciling the dictionaries passed
    #     as parameters, each one representing an account.move.line.
    #     :param debit_vals:  The values of account.move.line to consider for a debit line.
    #     :param credit_vals: The values of account.move.line to consider for a credit line.
    #     :return:            A dictionary:
    #         * debit_vals:   None if the line has nothing left to reconcile.
    #         * credit_vals:  None if the line has nothing left to reconcile.
    #         * partial_vals: The newly computed values for the partial.
    #     """

    #     def get_odoo_rate(vals):
    #         if vals.get('record') and vals['record'].move_id.is_invoice(include_receipts=True):
    #             exchange_rate_date = vals['record'].move_id.invoice_date
    #         else:
    #             exchange_rate_date = vals['date']
    #         return recon_currency._get_conversion_rate(company_currency, recon_currency, vals['company'], exchange_rate_date)

    #     def get_accounting_rate(vals):
    #         if company_currency.is_zero(vals['balance']) or vals['currency'].is_zero(vals['amount_currency']):
    #             return None
    #         else:
    #             return abs(vals['amount_currency']) / abs(vals['balance'])

    #     # ==== Determine the currency in which the reconciliation will be done ====
    #     # In this part, we retrieve the residual amounts, check if they are zero or not and determine in which
    #     # currency and at which rate the reconciliation will be done.
    #     context = dict(self._context) or {}
    #     if context.get('allocation_amount'):
    #         allocation_amount = context.get('allocation_amount')
    #         res = {
    #             'debit_vals': debit_vals,
    #             'credit_vals': credit_vals,
    #         }
    #         remaining_debit_amount_curr = allocation_amount
    #         remaining_credit_amount_curr = -(allocation_amount)
    #         remaining_debit_amount = allocation_amount
    #         remaining_credit_amount = -(allocation_amount)

    #         company_currency = debit_vals['company'].currency_id
    #         has_debit_zero_residual = company_currency.is_zero(remaining_debit_amount)
    #         has_credit_zero_residual = company_currency.is_zero(remaining_credit_amount)
    #         has_debit_zero_residual_currency = debit_vals['currency'].is_zero(remaining_debit_amount_curr)
    #         has_credit_zero_residual_currency = credit_vals['currency'].is_zero(remaining_credit_amount_curr)
    #         is_rec_pay_account = debit_vals.get('record') \
    #                          and debit_vals['record'].account_type in ('asset_receivable', 'liability_payable')

    #         if debit_vals['currency'] == credit_vals['currency'] == company_currency \
    #                 and not has_debit_zero_residual \
    #                 and not has_credit_zero_residual:
    #             # Everything is expressed in company's currency and there is something left to reconcile.
    #             recon_currency = company_currency
    #             debit_rate = credit_rate = 1.0
    #             recon_debit_amount = remaining_debit_amount
    #             recon_credit_amount = -remaining_credit_amount
    #         elif debit_vals['currency'] == company_currency \
    #                 and is_rec_pay_account \
    #                 and not has_debit_zero_residual \
    #                 and credit_vals['currency'] != company_currency \
    #                 and not has_credit_zero_residual_currency:
    #             # The credit line is using a foreign currency but not the opposite line.
    #             # In that case, convert the amount in company currency to the foreign currency one.
    #             recon_currency = credit_vals['currency']
    #             debit_rate = get_odoo_rate(debit_vals)
    #             credit_rate = get_accounting_rate(credit_vals)
    #             recon_debit_amount = recon_currency.round(remaining_debit_amount * debit_rate)
    #             recon_credit_amount = -remaining_credit_amount_curr
    #         elif debit_vals['currency'] != company_currency \
    #                 and is_rec_pay_account \
    #                 and not has_debit_zero_residual_currency \
    #                 and credit_vals['currency'] == company_currency \
    #                 and not has_credit_zero_residual:
    #             # The debit line is using a foreign currency but not the opposite line.
    #             # In that case, convert the amount in company currency to the foreign currency one.
    #             recon_currency = debit_vals['currency']
    #             debit_rate = get_accounting_rate(debit_vals)
    #             credit_rate = get_odoo_rate(credit_vals)
    #             recon_debit_amount = remaining_debit_amount_curr
    #             recon_credit_amount = recon_currency.round(-remaining_credit_amount * credit_rate)
    #         elif debit_vals['currency'] == credit_vals['currency'] \
    #                 and debit_vals['currency'] != company_currency \
    #                 and not has_debit_zero_residual_currency \
    #                 and not has_credit_zero_residual_currency:
    #             # Both lines are sharing the same foreign currency.
    #             recon_currency = debit_vals['currency']
    #             debit_rate = get_accounting_rate(debit_vals)
    #             credit_rate = get_accounting_rate(credit_vals)
    #             recon_debit_amount = remaining_debit_amount_curr
    #             recon_credit_amount = -remaining_credit_amount_curr
    #         elif debit_vals['currency'] == credit_vals['currency'] \
    #                 and debit_vals['currency'] != company_currency \
    #                 and (has_debit_zero_residual_currency or has_credit_zero_residual_currency):
    #             # Special case for exchange difference lines. In that case, both lines are sharing the same foreign
    #             # currency but at least one has no amount in foreign currency.
    #             # In that case, we don't want a rate for the opposite line because the exchange difference is supposed
    #             # to reduce only the amount in company currency but not the foreign one.
    #             recon_currency = company_currency
    #             debit_rate = None
    #             credit_rate = None
    #             recon_debit_amount = remaining_debit_amount
    #             recon_credit_amount = -remaining_credit_amount
    #         else:
    #             # Multiple involved foreign currencies. The reconciliation is done using the currency of the company.
    #             recon_currency = company_currency
    #             debit_rate = get_accounting_rate(debit_vals)
    #             credit_rate = get_accounting_rate(credit_vals)
    #             recon_debit_amount = remaining_debit_amount
    #             recon_credit_amount = -remaining_credit_amount

    #         # ==== Match both lines together and compute amounts to reconcile ====

    #         # Determine which line is fully matched by the other.
    #         compare_amounts = recon_currency.compare_amounts(recon_debit_amount, recon_credit_amount)
    #         min_recon_amount = min(recon_debit_amount, recon_credit_amount)
    #         debit_fully_matched = compare_amounts <= 0
    #         credit_fully_matched = compare_amounts >= 0

    #         # ==== Computation of partial amounts ====
    #         if recon_currency == company_currency:
    #             # Compute the partial amount expressed in company currency.
    #             partial_amount = min_recon_amount

    #             # Compute the partial amount expressed in foreign currency.
    #             if debit_rate:
    #                 partial_debit_amount_currency = debit_vals['currency'].round(debit_rate * min_recon_amount)
    #                 partial_debit_amount_currency = min(partial_debit_amount_currency, remaining_debit_amount_curr)
    #             else:
    #                 partial_debit_amount_currency = 0.0
    #             if credit_rate:
    #                 partial_credit_amount_currency = credit_vals['currency'].round(credit_rate * min_recon_amount)
    #                 partial_credit_amount_currency = min(partial_credit_amount_currency, -remaining_credit_amount_curr)
    #             else:
    #                 partial_credit_amount_currency = 0.0

    #         else:
    #             # recon_currency != company_currency
    #             # Compute the partial amount expressed in company currency.
    #             if debit_rate:
    #                 partial_debit_amount = company_currency.round(min_recon_amount / debit_rate)
    #                 partial_debit_amount = min(partial_debit_amount, remaining_debit_amount)
    #             else:
    #                 partial_debit_amount = 0.0
    #             if credit_rate:
    #                 partial_credit_amount = company_currency.round(min_recon_amount / credit_rate)
    #                 partial_credit_amount = min(partial_credit_amount, -remaining_credit_amount)
    #             else:
    #                 partial_credit_amount = 0.0
    #             partial_amount = min(partial_debit_amount, partial_credit_amount)

    #             # Compute the partial amount expressed in foreign currency.
    #             # Take care to handle the case when a line expressed in company currency is mimicking the foreign
    #             # currency of the opposite line.
    #             if debit_vals['currency'] == company_currency:
    #                 partial_debit_amount_currency = partial_amount
    #             else:
    #                 partial_debit_amount_currency = min_recon_amount
    #             if credit_vals['currency'] == company_currency:
    #                 partial_credit_amount_currency = partial_amount
    #             else:
    #                 partial_credit_amount_currency = min_recon_amount

    #         # Computation of the partial exchange difference. You can skip this part using the
    #         # `no_exchange_difference` context key (when reconciling an exchange difference for example).
    #         if not self._context.get('no_exchange_difference'):
    #             exchange_lines_to_fix = self.env['account.move.line']
    #             amounts_list = []
    #             if recon_currency == company_currency:
    #                 if debit_fully_matched:
    #                     debit_exchange_amount = remaining_debit_amount_curr - partial_debit_amount_currency
    #                     if not debit_vals['currency'].is_zero(debit_exchange_amount):
    #                         if debit_vals.get('record'):
    #                             exchange_lines_to_fix += debit_vals['record']
    #                         amounts_list.append({'amount_residual_currency': debit_exchange_amount})
    #                         remaining_debit_amount_curr -= debit_exchange_amount
    #                 if credit_fully_matched:
    #                     credit_exchange_amount = remaining_credit_amount_curr + partial_credit_amount_currency
    #                     if not credit_vals['currency'].is_zero(credit_exchange_amount):
    #                         if credit_vals.get('record'):
    #                             exchange_lines_to_fix += credit_vals['record']
    #                         amounts_list.append({'amount_residual_currency': credit_exchange_amount})
    #                         remaining_credit_amount_curr += credit_exchange_amount

    #             else:
    #                 if debit_fully_matched:
    #                     # Create an exchange difference on the remaining amount expressed in company's currency.
    #                     debit_exchange_amount = remaining_debit_amount - partial_amount
    #                     if not company_currency.is_zero(debit_exchange_amount):
    #                         if debit_vals.get('record'):
    #                             exchange_lines_to_fix += debit_vals['record']
    #                         amounts_list.append({'amount_residual': debit_exchange_amount})
    #                         remaining_debit_amount -= debit_exchange_amount
    #                         if debit_vals['currency'] == company_currency:
    #                             remaining_debit_amount_curr -= debit_exchange_amount
    #                 else:
    #                     # Create an exchange difference ensuring the rate between the residual amounts expressed in
    #                     # both foreign and company's currency is still consistent regarding the rate between
    #                     # 'amount_currency' & 'balance'.
    #                     debit_exchange_amount = partial_debit_amount - partial_amount
    #                     if company_currency.compare_amounts(debit_exchange_amount, 0.0) > 0:
    #                         if debit_vals.get('record'):
    #                             exchange_lines_to_fix += debit_vals['record']
    #                         amounts_list.append({'amount_residual': debit_exchange_amount})
    #                         remaining_debit_amount -= debit_exchange_amount
    #                         if debit_vals['currency'] == company_currency:
    #                             remaining_debit_amount_curr -= debit_exchange_amount

    #                 if credit_fully_matched:
    #                     # Create an exchange difference on the remaining amount expressed in company's currency.
    #                     credit_exchange_amount = remaining_credit_amount + partial_amount
    #                     if not company_currency.is_zero(credit_exchange_amount):
    #                         if credit_vals.get('record'):
    #                             exchange_lines_to_fix += credit_vals['record']
    #                         amounts_list.append({'amount_residual': credit_exchange_amount})
    #                         remaining_credit_amount += credit_exchange_amount
    #                         if credit_vals['currency'] == company_currency:
    #                             remaining_credit_amount_curr -= credit_exchange_amount
    #                 else:
    #                     # Create an exchange difference ensuring the rate between the residual amounts expressed in
    #                     # both foreign and company's currency is still consistent regarding the rate between
    #                     # 'amount_currency' & 'balance'.
    #                     credit_exchange_amount = partial_amount - partial_credit_amount
    #                     if company_currency.compare_amounts(credit_exchange_amount, 0.0) < 0:
    #                         if credit_vals.get('record'):
    #                             exchange_lines_to_fix += credit_vals['record']
    #                         amounts_list.append({'amount_residual': credit_exchange_amount})
    #                         remaining_credit_amount -= credit_exchange_amount
    #                         if credit_vals['currency'] == company_currency:
    #                             remaining_credit_amount_curr -= credit_exchange_amount

    #             if exchange_lines_to_fix:
    #                 res['exchange_vals'] = exchange_lines_to_fix._prepare_exchange_difference_move_vals(
    #                     amounts_list,
    #                     exchange_date=max(debit_vals['date'], credit_vals['date']),
    #                 )

    #         # ==== Create partials ====
    #         remaining_debit_amount -= partial_amount
    #         remaining_credit_amount += partial_amount
    #         remaining_debit_amount_curr -= partial_debit_amount_currency
    #         remaining_credit_amount_curr += partial_credit_amount_currency


    #         res['partial_vals'] = {
    #             'amount': partial_amount,
    #             'debit_amount_currency': partial_debit_amount_currency,
    #             'credit_amount_currency': partial_credit_amount_currency,
    #             'debit_move_id': debit_vals.get('record') and debit_vals['record'].id,
    #             'credit_move_id': credit_vals.get('record') and credit_vals['record'].id,
    #         }
    #         debit_vals['amount_residual'] = remaining_debit_amount
    #         debit_vals['amount_residual_currency'] = remaining_debit_amount_curr
    #         credit_vals['amount_residual'] = remaining_credit_amount
    #         credit_vals['amount_residual_currency'] = remaining_credit_amount_curr

    #         if recon_currency.is_zero(recon_debit_amount) or debit_fully_matched:
    #             res['debit_vals'] = None
    #         if recon_currency.is_zero(recon_credit_amount) or credit_fully_matched:
    #             res['credit_vals'] = None
    #         return res
    #     else:
    #         return super(AccountMoveLine, self)._prepare_reconciliation_single_partial(debit_vals, credit_vals)
