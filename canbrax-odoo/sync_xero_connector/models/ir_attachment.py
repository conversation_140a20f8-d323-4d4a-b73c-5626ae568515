# -*- coding: utf-8 -*-
# Part of Odoo. See COPYRIGHT & LICENSE files for full copyright and licensing details.

from odoo import api, fields, models, _


class IrAttachment(models.Model):
    _inherit = 'ir.attachment'

    def export_attachments(self, xero, last_attachment_export_date, company=False):
        for invoice in self.env['account.move'].search([('xero_invoice_id', '!=', False), ('move_type', 'in', ['in_invoice', 'out_invoice']), ('company_id', '=', company)]):
            count = 1
            invoice_domain = [('res_model', '=', 'account.move'), ('res_id', '=', invoice.id)]
            if last_attachment_export_date:
                invoice_domain.append(('create_date', '>=', last_attachment_export_date))
            attachments = self.search(invoice_domain)

            for attachment in attachments:
                if count < 10:
                    attachment_type = 'Invoices' if invoice.move_type == 'out_invoice' else 'Receipts'
                    if not attachment.file_size > 3000000:
                        file = open(str(attachment._filestore()) + '/' + str(attachment.store_fname), 'rb')
                        xero.attachments.put_attachment(invoice.xero_invoice_id,
                                attachment.name, file, attachment.mimetype, include_online=True, attachment_model=attachment_type)
                        file.close()
                    count += 1
        for invoice in self.env['account.move'].search([('xero_invoice_id', '!=', False), ('move_type', 'in', ['in_refund', 'out_refund']), ('company_id', '=', company)]):
            count = 1
            refund_invoice_domain = [('res_model', '=', 'account.move'), ('res_id', '=', invoice.id)]
            if last_attachment_export_date:
                refund_invoice_domain.append(('create_date', '>=', last_attachment_export_date))
            attachments = self.search(refund_invoice_domain)

            for attachment in attachments:
                if count < 10:
                    attachment_type = 'Creditnotes' if invoice.move_type == 'out_refund' else 'Refunds'
                    if not attachment.file_size > 3000000:
                        file = open(str(attachment._filestore()) + '/' + str(attachment.store_fname), 'rb')
                        xero.attachments.put_attachment(invoice.xero_invoice_id,
                                attachment.name, file, attachment.mimetype, include_online=True, attachment_model=attachment_type)
                        file.close()
                    count += 1

        partners = self.env['res.partner'].search(['|', ('company_id', '=', company), ('company_id', '=', False)]).filtered(lambda partner: partner.contact_xero_company_ids.filtered(lambda contact: contact.xero_contact_id != False and contact.company_id.id == company))
        for partner in partners:
            count = 1
            partner_domain = [('res_model', '=', 'res.partner'), ('res_id', '=', partner.id)]
            if last_attachment_export_date:
                partner_domain.append(('create_date', '>=', last_attachment_export_date))
            attachments = self.search(partner_domain)

            for attachment in attachments:
                if count < 10:
                    if not attachment.file_size > 3000000:
                        file = open(str(attachment._filestore()) + '/' + str(attachment.store_fname), 'rb')
                        contact = partner.contact_xero_company_ids.filtered(lambda contact: contact.company_id.id == company)
                        xero.attachments.put_attachment(contact.xero_contact_id,
                                attachment.name, file, attachment.mimetype, include_online=True, attachment_model= 'Contacts')
                        file.close()
                    count += 1
        for so in self.env['sale.order'].search([('xero_quotes_id', '!=', False), ('company_id', '=', company)]):
            count = 1
            attachment_domain = [('res_model', '=', 'sale.order'), ('res_id', '=', so.id)]
            if last_attachment_export_date:
                attachment_domain.append(('create_date', '>=', last_attachment_export_date))
            attachments = self.search(attachment_domain)

            for attachment in attachments:
                if count < 10:
                    if not attachment.file_size > 3000000:
                        file = open(str(attachment._filestore()) + '/' + str(attachment.store_fname), 'rb')
                        xero.attachments.put_attachment(so.xero_quotes_id,
                                attachment.name, file, attachment.mimetype, include_online=True, attachment_model='Quotes')
                        file.close()
                    count += 1
        for po in self.env['purchase.order'].search([('xero_po_id', '!=', False), ('company_id', '=', company)]):
            count = 1
            attachment_domain = [('res_model', '=', 'purchase.order'), ('res_id', '=', po.id)]
            if last_attachment_export_date:
                attachment_domain.append(('create_date', '>=', last_attachment_export_date))
            attachments = self.search(attachment_domain)

            for attachment in attachments:
                if count < 10:
                    if not attachment.file_size > 3000000:
                        file = open(str(attachment._filestore()) + '/' + str(attachment.store_fname), 'rb')
                        xero.attachments.put_attachment(po.xero_po_id,
                                attachment.name, file, attachment.mimetype, include_online=True, attachment_model='PurchaseOrders')
                        file.close()
                    count += 1


