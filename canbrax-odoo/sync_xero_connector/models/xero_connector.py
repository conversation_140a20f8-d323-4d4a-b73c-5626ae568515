# -*- coding: utf-8 -*-
# Part of Odoo. See COPYRIGHT & LICENSE files for full copyright and licensing details.

import ast
import werkzeug
from odoo import api, fields, models, _
from odoo.addons.sync_xero_connector.lib.xero.auth import PublicCredentials,PrivateCredentials, OAuth2Credentials
from odoo.addons.sync_xero_connector.lib.xero.constants import XeroScopes
from odoo.exceptions import UserError, ValidationError
from odoo.addons.sync_xero_connector.lib.xero import Xero
from odoo.addons.sync_xero_connector.lib.xero.exceptions import (
    XeroAccessDenied,
    XeroBadRequest,
    XeroException,
    XeroExceptionUnknown,
    XeroForbidden,
    XeroInternalError,
    XeroNotAvailable,
    XeroNotFound,
    XeroNotImplemented,
    XeroNotVerified,
    XeroRateLimitExceeded,
    XeroUnauthorized,
)


class MisMatchLog(models.Model):
    _name = 'mismatch.log'
    _description = 'Mismatch Log'
    _order = 'date desc'

    name = fields.Char('Name')
    source_model = fields.Char('Source Model')
    source_id = fields.Char('Source Id')
    description = fields.Char('Description')
    date = fields.Datetime('Date')
    option = fields.Selection([('import', 'Import'), ('export', 'Export')], string='Option')
    xero_account_id = fields.Many2one('xero.account', string='Xero Account')
    company_id = fields.Many2one('res.company', related='xero_account_id.company_id', string='Company')

    @api.returns('self', lambda value: value.id)
    def copy(self, default=None):
        raise ValidationError(_("Not allowed to duplicate record."))
        return super(MisMatchLog, self).copy(default=default)


class XeroAccount(models.Model):
    _name = 'xero.account'
    _description = 'Xero Account'

    name = fields.Char('Name')
    import_option = fields.Selection([('create', 'Create'),
                                      ('update', 'Update'),
                                      ('both', 'Both')], string='Option', default='create')
    inv_without_product = fields.Boolean('If you want to import invoice without product tick this')
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.user.company_id.id)
    customer_inv_journal_id = fields.Many2one('account.journal', string='Customer Invoice Journal')
    vendor_bill_journal_id = fields.Many2one('account.journal', string='Vendor Bill Journal')
    miscellaneous_operations_journal_id = fields.Many2one('account.journal', string='Miscellaneous Operations Journal')
    active = fields.Boolean(string='Active', default=True)
    auth = fields.Char(string='Auth')
    journal_ids = fields.Many2many('account.journal', string="Payment Journal")
    tracked_category_id = fields.Many2one('product.category', string="Tracked Category", required=False)
    untracked_category_id = fields.Many2one('product.category', string="Untracked Category", required=False)
    adjustment_account_id = fields.Many2one('account.account', string='Inventory Adjustment Account')
    last_create_contact_import_date = fields.Datetime(string='Contact Create Import Date')
    last_update_contact_import_date = fields.Datetime(string='Contact update Import Date')
    last_contact_export_date = fields.Datetime(string='Contact Export Date')
    last_create_product_import_date = fields.Datetime(string='Product Create Import Date')
    last_update_product_import_date = fields.Datetime(string='Product Update Import Date')
    last_product_export_date = fields.Datetime(string='Product Export Date')
    last_create_invoice_import_date = fields.Datetime(string='Invoice Create Import Date')
    last_update_invoice_import_date = fields.Datetime(string='Invoice Update Import Date')
    last_invoice_export_date = fields.Datetime(string='Invoice Export Date')

    last_create_account_import_date = fields.Datetime(string='Account Create Import Date')
    last_update_account_import_date = fields.Datetime(string='Account Update Import Date')
    last_account_export_date = fields.Datetime(string='Account Export Date')

    last_create_creditnote_import_date = fields.Datetime(string='Creditnote Create Import Date')
    last_update_creditnote_import_date = fields.Datetime(string='Creditnote Update Import Date')
    last_creditnote_export_date = fields.Datetime(string='Creditnote Export Date')
    # last_journal_export_date = fields.Datetime(string='Journal Export Date')
    last_attachment_export_date = fields.Datetime(string='Attachment Export Date')
    export_disable = fields.Boolean(string='Disable Export', default=False)
    import_export_creditnotes = fields.Selection([('import', 'Import'), ('export', 'Export')], string='Credit Notes', default='import')
    client_id = fields.Char(string='Client ID')
    client_secret = fields.Char(string='Client Secret')
    callback_uri = fields.Char('Call Back URI')
    raw_url = fields.Char('Generated Raw URL')
    raw_uri = fields.Char('Raw URI')
    state = fields.Char('State')
    token = fields.Char('Token')
    xero_org_ids = fields.One2many('xero.organization', 'xero_account_id', string='Xero Organization Ids')
    xero_org_id = fields.Many2one('xero.organization', string='Import/Export Xero Organization')
    contact_overwrite = fields.Boolean(string='Contact Overwrite')
    last_create_journal_import_date = fields.Datetime(string='Journal Create Import Date')
    last_update_journal_import_date = fields.Datetime(string='Journal Update Import Date')

    # PO fields
    last_create_po_import_date = fields.Datetime(string='PO Create Import Date')
    last_update_po_import_date = fields.Datetime(string='PO Update Import Date')
    last_po_export_date = fields.Datetime(string='PO Export Date')

    # SO fields
    last_create_quotes_import_date = fields.Datetime(string='Quotes Create Import Date')
    last_update_quotes_import_date = fields.Datetime(string='Quotes Update Import Date')
    last_quotes_export_date = fields.Datetime(string='Quotes Export Date')

    @api.constrains('company_id')
    def _unique_company(self):
        for rec in self:
            company_id = self.env['xero.account'].search([('company_id', '=', rec.company_id.id), ('id', '!=', rec.id)])
            if company_id:
                raise ValidationError(_('One company has only one xero account!'))

    def generate_url_auth2(self):
        self.ensure_one()
        my_scope = [XeroScopes.ACCOUNTING_SETTINGS, XeroScopes.OPENID, XeroScopes.PROFILE, XeroScopes.EMAIL, XeroScopes.OFFLINE_ACCESS, XeroScopes.ACCOUNTING_TRANSACTIONS, XeroScopes.ACCOUNTING_CONTACTS, XeroScopes.ACCOUNTING_ATTACHMENTS, XeroScopes.PAYROLL_EMPLOYEES]
        credentials = OAuth2Credentials(self.client_id, self.client_secret, scope=my_scope, callback_uri=self.callback_uri)
        url = credentials.generate_url()
        self.raw_url = url
        self.state = credentials.state.get('auth_state')

    # def authenticate(self):
    #     self.ensure_one()

    #     my_scope = [XeroScopes.ACCOUNTING_SETTINGS, XeroScopes.OPENID, XeroScopes.PROFILE, XeroScopes.EMAIL, XeroScopes.OFFLINE_ACCESS, XeroScopes.ACCOUNTING_TRANSACTIONS, XeroScopes.ACCOUNTING_CONTACTS, XeroScopes.ACCOUNTING_ATTACHMENTS, XeroScopes.PAYROLL_EMPLOYEES]
    #     params = {
    #         'response_type': 'code',
    #         'client_id': self.client_id,
    #         'redirect_uri': self.callback_uri,
    #         'scope': my_scope,
    #         'state': self.id,
    #     }

    #     auth_url = "https://login.xero.com/identity/connect/authorize"

    #     url = f"{auth_url}?{werkzeug.urls.url_encode(params)}"
    #     return {
    #         'type': 'ir.actions.act_url',
    #         'url': url,
    #         'target': 'new',
    #     }


    def authenticate(self):
        self.ensure_one()
        my_scope = [XeroScopes.ACCOUNTING_SETTINGS, XeroScopes.OPENID, XeroScopes.PROFILE, XeroScopes.EMAIL, XeroScopes.OFFLINE_ACCESS, XeroScopes.ACCOUNTING_TRANSACTIONS, XeroScopes.ACCOUNTING_CONTACTS, XeroScopes.ACCOUNTING_ATTACHMENTS, XeroScopes.PAYROLL_EMPLOYEES]
        new_credentials = OAuth2Credentials(self.client_id, self.client_secret, scope=my_scope, callback_uri=self.callback_uri)
        if not self.raw_uri:
            raise UserError(_('Please Add Raw URI.'))
        try:
            new_credentials.verify(self.raw_uri)
        except XeroAccessDenied as e:
            raise UserError(_('XeroAccessDenied %s'% e))
        except Exception as e:
            raise UserError(e)

        self.token = new_credentials.token

        xero = Xero(new_credentials)
        self.auth = Xero(new_credentials)

        tenants = new_credentials.get_tenants()
        tenant_list = []
        for tenant in tenants:
            xero_org_id = self.xero_org_ids.filtered(lambda l: l.xero_tenant_id == tenant.get('tenantId'))
            if not xero_org_id:
                new_credentials.tenant_id = tenant.get('tenantId')
                org = xero.organisations.all()
                tenant_data = {'name': org[0].get('Name'),
                                'xero_id': tenant.get('id'),
                                'xero_tenant_id': tenant.get('tenantId'),
                                'xero_tenant_type': tenant.get('tenantType'),
                                'xero_account_id': self.id,
                                }
                tenant_list.append((0, 0, tenant_data))
        self.xero_org_ids = tenant_list

        return Xero(new_credentials, unit_price_4dps=4)

    def re_authenticate(self):
        self.ensure_one()
        my_scope = [XeroScopes.ACCOUNTING_SETTINGS, XeroScopes.OPENID, XeroScopes.PROFILE, XeroScopes.EMAIL, XeroScopes.OFFLINE_ACCESS, XeroScopes.ACCOUNTING_TRANSACTIONS, XeroScopes.ACCOUNTING_CONTACTS, XeroScopes.ACCOUNTING_ATTACHMENTS, XeroScopes.PAYROLL_EMPLOYEES]
        new_credentials = OAuth2Credentials(self.client_id, self.client_secret, scope=my_scope, callback_uri=self.callback_uri, token=ast.literal_eval(self.token))

        if new_credentials.expired():
            try:
                new_credentials.refresh()
            except Exception as e:
                if self._context.get('schedule_action'):
                    description = (_('Your Token has been expired %s, you need to Reauthentication' % e))
                    mismatch_vals = {'name': self.name,
                                 'source_model': 'xero.account',
                                 'source_id': self.id,
                                 'description': description,
                                 'date': fields.datetime.now(),
                                 'xero_account_id': self.id
                                 }
                    self.env['mismatch.log'].create(mismatch_vals)
                    return False
                else:
                    raise UserError(_('Your Token has been expired %s, you need to Reauthentication' % e))

            self.state = new_credentials.state
            self.token = new_credentials.token

        new_credentials.tenant_id = self.xero_org_id.xero_tenant_id
        return Xero(new_credentials, unit_price_4dps=4)

    def xero_auth(self):
        self.ensure_one()
        if not self.xero_org_id:
            if self._context.get('schedule_action'):
                description = (_('Please configure Xero Organization for Import/Export Operation.'))
                mismatch_vals = {'name': self.name,
                                 'source_model': 'xero.account',
                                 'source_id': self.id,
                                 'description': description,
                                 'date': fields.datetime.now(),
                                 'xero_account_id': self.id
                                 }
                self.env['mismatch.log'].create(mismatch_vals)
            else:
                raise UserError(_('Please configure Xero Organization for Import/Export Operation.'))
        xero = self.re_authenticate()
        return xero

    @api.model
    def automatic_import(self):
        context = dict(self.env.context) or {}
        context.update({'schedule_action': True})
        self.env.context = context
        for record in self.search([('active', '=', True)]):
            xero = record.xero_auth()
                # Import Currency
            if xero:
                try:
                    record.import_currency()
                    # Import Tax
                    record.import_tax()
                    # Import Account
                    record.import_account()
                    # Import Contact Group
                    # Import Contacts
                    record.import_contact_overwrite() if record.contact_overwrite else record.import_contact()
                    # Import Bank Account
                    record.import_bank_account()
                    # Import Product
                    if not record.inv_without_product:
                        record.import_product()
                    # Import PO
                    record.import_purchase_order()
                    # Import SO
                    record.import_quotes()
                    # Import Invoice
                    record.import_invoice()
                    # Import Manual Journal
                    record.import_manual_journal()
                    # Import Credit Notes
                    if record.import_export_creditnotes == 'import':
                        record.import_credit_notes()
                except Exception as e:
                    mismatch_vals = {'name': record.name,
                                 'source_model': 'ir.cron',
                                 'source_id': record.id,
                                 'description': e,
                                 'date': fields.datetime.now(),
                                 'option': 'import',
                                 'xero_account_id': record.id
                                 }
                    self.env['mismatch.log'].create(mismatch_vals)

        context.pop('schedule_action')
        self.env.context = context

    @api.model
    def automatic_export(self):
        context = dict(self.env.context) or {}
        context.update({'schedule_action': True})
        self.env.context = context
        for record in self.search([('active', '=', True)]):
            xero = record.xero_auth()
            # Export Taxes
            if xero:
                try:
                    record.export_tax()
                    # Export Accounts
                    record.export_account()
                    # Export Bank Accounts
                    record.export_bank_account()
                    # Export Contact Groups
                    # Export Contacts
                    record.export_contact_overwrite()if record.contact_overwrite else record.export_contact()
                    # Export Products
                    record.export_product()
                    # Export PO
                    record.export_purchase_order()
                    # Export SO
                    record.export_quotes()
                    # Export invoices
                    record.export_invoice()
                    record.export_payment()
                    # Export credit Notes
                    if record.import_export_creditnotes == 'export':
                        record.export_credit_notes()
                        record.export_credit_notes_payment()
                    # Export Inventory Adjustments
                    record.export_inventory_adjustments()
                    #Export Attachment
                    record.export_attachments()
                except Exception as e:
                    mismatch_vals = {'name': record.name,
                                 'source_model': 'ir.cron',
                                 'source_id': record.id,
                                 'description': e,
                                 'date': fields.datetime.now(),
                                 'option': 'export',
                                 'xero_account_id': record.id
                                 }
                    self.env['mismatch.log'].create(mismatch_vals)

        context.pop('schedule_action')
        self.env.context = context

    def import_currency(self):
        self.ensure_one()
        xero = self.xero_auth()
        currency_list = xero.currencies.all()
        self.env['res.currency'].import_currency(currency_list, xero, xero_account_id=self.id)

    def import_tax(self):
        self.ensure_one()
        xero = self.xero_auth()
        tax_list = xero.taxrates.all()
        self.env['account.tax'].import_tax(tax_list, xero, xero_account_id=self.id, company=self.company_id.id, import_option=self.import_option)

    def import_account(self):
        self.ensure_one()
        xero = self.xero_auth()
        account_list = []
        # account_list = xero.accounts.all()
        # self.env['account.account'].import_account(account_list, xero, xero_account_id=self.id, company=self.company_id.id, import_option=self.import_option)

        if self.last_create_account_import_date and self.import_option == 'create':
            account_list = xero.accounts.filter(since=self.last_create_account_import_date)
        elif self.last_update_account_import_date and self.import_option == 'update':
            account_list = xero.accounts.filter(since=self.last_update_account_import_date)
        elif self.import_option == 'both' and self.last_create_account_import_date and self.last_update_account_import_date:
            min_date = min(self.last_create_account_import_date,self.last_update_account_import_date)
            account_list = xero.accounts.filter(since=min_date)
        else:
            account_list = xero.accounts.all()
        self.env['account.account'].import_account(account_list, xero, xero_account_id=self.id, company=self.company_id.id, import_option=self.import_option)

        if self.import_option == 'create':
            self.last_create_account_import_date = fields.Datetime.now()
        elif self.import_option == 'update':
            self.last_update_account_import_date = fields.Datetime.now()
        else:
            self.last_create_account_import_date = fields.Datetime.now()
            self.last_update_account_import_date = fields.Datetime.now()


    def import_bank_account(self):
        self.ensure_one()
        xero = self.xero_auth()
        bank_account_list = xero.accounts.filter(Type='BANK')
        self.env['res.partner.bank'].import_bank_account(bank_account_list, xero, xero_account_id=self.id, import_option=self.import_option, company=self.company_id.id)

    def import_contact(self):
        self.ensure_one()
        xero = self.xero_auth()
        group_list = xero.contactgroups.all()
        self.env['res.partner.category'].import_contact_group(group_list, xero)
        page = 0
        while True:
            page += 1
            contact_list = []
            if self.last_create_contact_import_date and self.import_option == 'create':
                contact_list = xero.contacts.filter(since=self.last_create_contact_import_date, page=page)
            elif self.last_update_contact_import_date and self.import_option == 'update':
                contact_list = xero.contacts.filter(since=self.last_update_contact_import_date, page=page)
            elif self.import_option == 'both' and self.last_create_contact_import_date and self.last_update_contact_import_date:
                min_date = min(self.last_create_contact_import_date, self.last_update_contact_import_date)
                contact_list = xero.contacts.filter(since=min_date, page=page)
            else:
                contact_list = xero.contacts.filter(page=page)
            if contact_list:
                    self.env['res.partner'].import_contact(contact_list, xero, xero_account_id=self.id, company=self.company_id.id, import_option=self.import_option)
            else:
                break

        if self.import_option == 'create':
            self.last_create_contact_import_date = fields.Datetime.now()
        elif self.import_option == 'update':
            self.last_update_contact_import_date = fields.Datetime.now()
        elif self.import_option == 'both':
            self.last_create_contact_import_date = fields.Datetime.now()
            self.last_update_contact_import_date = fields.Datetime.now()

    def import_contact_overwrite(self):
        self.ensure_one()
        xero = self.xero_auth()

        group_list = xero.contactgroups.all()
        self.env['res.partner.category'].import_contact_group(group_list, xero)
        page = 0
        while True:
            page += 1
            contact_list = []
            if self.last_create_contact_import_date and self.import_option == 'create':
                contact_list = xero.contacts.filter(since=self.last_create_contact_import_date, page=page)
            elif self.last_update_contact_import_date and self.import_option == 'update':
                contact_list = xero.contacts.filter(since=self.last_update_contact_import_date, page=page)
            elif self.import_option == 'both' and self.last_create_contact_import_date and self.last_update_contact_import_date:
                min_date = min(self.last_create_contact_import_date, self.last_update_contact_import_date)
                contact_list = xero.contacts.filter(since=min_date, page=page)
            else:
                contact_list = xero.contacts.filter(page=page)
            if contact_list:
                    self.env['res.partner'].import_contact_overwrite(contact_list, xero, xero_account_id=self.id, company=self.company_id.id, import_option=self.import_option)
            else:
                break

        if self.import_option == 'create':
            self.last_create_contact_import_date = fields.Datetime.now()
        elif self.import_option == 'update':
            self.last_update_contact_import_date = fields.Datetime.now()
        elif self.import_option == 'both':
            self.last_create_contact_import_date = fields.Datetime.now()
            self.last_update_contact_import_date = fields.Datetime.now()

    def import_product(self):
        self.ensure_one()
        xero = self.xero_auth()
        if self.last_create_product_import_date and self.import_option == 'create':
            product_list = xero.items.filter(since=self.last_create_product_import_date)
        elif self.last_update_product_import_date and self.import_option == 'update':
            product_list = xero.items.filter(since=self.last_update_product_import_date)
        elif self.import_option == 'both' and self.last_create_product_import_date and self.last_update_product_import_date:
            min_date = min(self.last_create_product_import_date,self.last_update_product_import_date)
            product_list = xero.items.filter(since=min_date)
        else:
            product_list = xero.items.all()
        self.env['product.product'].import_product(product_list, xero, self.tracked_category_id, self.untracked_category_id, xero_account_id=self.id, company=self.company_id.id, import_option=self.import_option)

        if self.import_option == 'create':
            self.last_create_product_import_date = fields.Datetime.now()
        elif self.import_option == 'update':
            self.last_update_product_import_date = fields.Datetime.now()
        else:
            self.last_create_product_import_date = fields.Datetime.now()
            self.last_update_product_import_date = fields.Datetime.now()

    def import_invoice(self):
        self.ensure_one()
        xero = self.xero_auth()
        page = 0
        while True:
            page += 1
            invoice_list = []
            if self.last_create_invoice_import_date and self.import_option == 'create':
                invoice_list = xero.invoices.filter(since=self.last_create_invoice_import_date, page=page)
            elif self.last_update_invoice_import_date and self.import_option == 'update':
                invoice_list = xero.invoices.filter(since=self.last_update_invoice_import_date, page=page)
            elif self.import_option == 'both' and self.last_create_invoice_import_date and self.last_update_invoice_import_date:
                min_date = min(self.last_create_invoice_import_date, self.last_update_invoice_import_date)
                invoice_list = xero.invoices.filter(since=min_date, page=page)
            else:
                invoice_list = xero.invoices.filter(page=page)

            if invoice_list:
                self.env['account.move'].import_invoice(self.id, invoice_list , xero, company=self.company_id.id, without_product=self.inv_without_product, import_option=self.import_option, customer_inv_journal_id=self.customer_inv_journal_id, vendor_bill_journal_id=self.vendor_bill_journal_id)
            else:
                break

        if self.import_option == 'create':
            self.last_create_invoice_import_date = fields.Datetime.now()
        elif self.import_option == 'update':
            self.last_update_invoice_import_date = fields.Datetime.now()
        else:
            self.last_create_invoice_import_date = fields.Datetime.now()
            self.last_update_invoice_import_date = fields.Datetime.now()

    def import_credit_notes(self):
        self.ensure_one()
        xero = self.xero_auth()

        page = 0
        while True:
            page += 1
            credit_notes_list = []
            if self.last_create_creditnote_import_date and self.import_option == 'create':
                credit_notes_list = xero.creditnotes.filter(since=self.last_create_creditnote_import_date, page=page)
            elif self.last_update_creditnote_import_date and self.import_option == 'update':
                credit_notes_list = xero.creditnotes.filter(since=self.last_update_creditnote_import_date, page=page)
            elif self.import_option == 'both' and self.last_create_creditnote_import_date and self.last_update_creditnote_import_date:
                min_date = min(self.last_create_creditnote_import_date, self.last_update_creditnote_import_date)
                credit_notes_list = xero.creditnotes.filter(since=min_date, page=page)
            else:
                credit_notes_list = xero.creditnotes.filter(page=page)

            if credit_notes_list:
                self.env['account.move'].import_credit_notes(self.id, credit_notes_list , xero, company=self.company_id.id, without_product=self.inv_without_product, import_option=self.import_option, customer_inv_journal_id=self.customer_inv_journal_id, vendor_bill_journal_id=self.vendor_bill_journal_id)
            else:
                break

        if self.import_option == 'create':
            self.last_create_creditnote_import_date = fields.Datetime.now()
        elif self.import_option == 'update':
            self.last_update_creditnote_import_date = fields.Datetime.now()
        else:
            self.last_create_creditnote_import_date = fields.Datetime.now()
            self.last_update_creditnote_import_date = fields.Datetime.now()

    def import_manual_journal(self):
        self.ensure_one()
        xero = self.xero_auth()

        page = 0
        while True:
            page += 1
            journal_list = []
            if self.last_create_journal_import_date and self.import_option == 'create':
                journal_list = xero.manualjournals.filter(since=self.last_create_journal_import_date, page=page)
            elif self.last_update_journal_import_date and self.import_option == 'update':
                journal_list = xero.manualjournals.filter(since=self.last_update_journal_import_date, page=page)
            elif self.import_option == 'both' and self.last_create_journal_import_date and self.last_update_journal_import_date:
                min_date = min(self.last_create_journal_import_date, self.last_update_journal_import_date)
                journal_list = xero.manualjournals.filter(since=min_date, page=page)
            else:
                journal_list = xero.manualjournals.filter(page=page)

            if journal_list:
                self.env['account.move'].import_manual_journal(journal_list, xero, xero_account_id=self.id, company=self.company_id.id, import_option=self.import_option)
            else:
                break

        if self.import_option == 'create':
            self.last_create_journal_import_date = fields.Datetime.now()
        elif self.import_option == 'update':
            self.last_update_journal_import_date = fields.Datetime.now()
        else:
            self.last_create_journal_import_date = fields.Datetime.now()
            self.last_update_journal_import_date = fields.Datetime.now()

    def import_purchase_order(self):
        self.ensure_one()
        xero = self.xero_auth()
        page = 0
        while True:
            page += 1
            po_list = []
            if self.last_create_po_import_date and self.import_option == 'create':
                po_list = xero.purchaseorders.filter(since=self.last_create_po_import_date, page=page)
            elif self.last_update_po_import_date and self.import_option == 'update':
                po_list = xero.purchaseorders.filter(since=self.last_update_po_import_date, page=page)
            elif self.import_option == 'both' and self.last_create_po_import_date and self.last_update_po_import_date:
                min_date = min(self.last_create_po_import_date, self.last_update_po_import_date)
                po_list = xero.purchaseorders.filter(since=min_date, page=page)
            else:
                po_list = xero.purchaseorders.filter(page=page)

            if po_list:
                self.env['purchase.order'].import_purchase_order(po_list, xero, xero_account_id=self.id, company=self.company_id.id, without_product=self.inv_without_product, import_option=self.import_option)
            else:
                break

        if self.import_option == 'create':
            self.last_create_po_import_date = fields.Datetime.now()
        elif self.import_option == 'update':
            self.last_update_po_import_date = fields.Datetime.now()
        else:
            self.last_create_po_import_date = fields.Datetime.now()
            self.last_update_po_import_date = fields.Datetime.now()

    def import_quotes(self):
        self.ensure_one()
        xero = self.xero_auth()
        page = 0
        while True:
            page += 1
            order_list = []
            if self.last_create_quotes_import_date and self.import_option == 'create':
                order_list = xero.quotes.filter(since=self.last_create_quotes_import_date, page=page)
            elif self.last_update_quotes_import_date and self.import_option == 'update':
                order_list = xero.quotes.filter(since=self.last_update_quotes_import_date, page=page)
            elif self.import_option == 'both' and self.last_create_quotes_import_date and self.last_update_quotes_import_date:
                min_date = min(self.last_create_quotes_import_date, self.last_update_quotes_import_date)
                order_list = xero.quotes.filter(since=min_date, page=page)
            else:
                order_list = xero.quotes.filter(page=page)

            if order_list:
                self.env['sale.order'].import_quotes(order_list, xero, xero_account_id=self.id, company=self.company_id.id, without_product=self.inv_without_product, import_option=self.import_option)
            else:
                break

        if self.import_option == 'create':
            self.last_create_quotes_import_date = fields.Datetime.now()
        elif self.import_option == 'update':
            self.last_update_quotes_import_date = fields.Datetime.now()
        else:
            self.last_create_quotes_import_date = fields.Datetime.now()
            self.last_update_quotes_import_date = fields.Datetime.now()

    def export_currency(self):
        self.ensure_one()
        xero = self.xero_auth()
        currency_list = xero.currencies.all()
        self.env['res.currency'].export_currency(currency_list, xero, xero_account_id=self.id)

    def export_tax(self):
        self.ensure_one()
        xero = self.xero_auth()
        tax_rates = xero.taxrates.all()
        self.env['account.tax'].export_tax(tax_rates, xero, xero_account_id=self.id, company=self.company_id.id, disable_export=self.export_disable)

    def export_account(self):
        self.ensure_one()
        xero = self.xero_auth()
        account_list = xero.accounts.all()
        self.env['account.account'].export_account(account_list, xero, self.last_account_export_date, xero_account_id=self.id, company=self.company_id.id, disable_export=self.export_disable)
        if not self._context.get('account_ids'):
            self.last_account_export_date = fields.Datetime.now()

    def export_bank_account(self):
        self.ensure_one()
        xero = self.xero_auth()
        bank_account_list = xero.accounts.filter(Type='BANK')
        self.env['res.partner.bank'].export_bank_account(bank_account_list, xero, xero_account_id=self.id, company=self.company_id.id, disable_export=self.export_disable)

    def export_contact(self):
        self.ensure_one()
        xero = self.xero_auth()
        group_list = xero.contactgroups.all()
        self.env['res.partner.category'].export_contact_group(group_list, xero)
        contact_list = xero.contacts.all()
        self.env['res.partner'].export_contact(contact_list, xero, self.last_contact_export_date, xero_account_id=self.id, company=self.company_id.id)
        if not self._context.get('contact_ids'):
            self.last_contact_export_date = fields.Datetime.now()

    def export_contact_overwrite(self):
        self.ensure_one()
        xero = self.xero_auth()
        group_list = xero.contactgroups.all()
        self.env['res.partner.category'].export_contact_group(group_list, xero)
        contact_list = xero.contacts.all()
        self.env['res.partner'].export_contact_overwrite(contact_list, xero, self.last_contact_export_date, xero_account_id=self.id, company=self.company_id.id)
        if not self._context.get('contact_ids'):
            self.last_contact_export_date = fields.Datetime.now()

    def export_product(self):
        self.ensure_one()
        xero = self.xero_auth()
        product_list = xero.items.all()
        self.env['product.product'].export_product(product_list, xero, self.last_product_export_date, xero_account_id=self.id, company=self.company_id.id)
        if not self._context.get('product_ids'):
            self.last_product_export_date = fields.Datetime.now()

    def export_invoice(self):
        self.ensure_one()
        xero = self.xero_auth()
        invoice_list = xero.invoices.all()
        self.env['account.move'].export_invoice(invoice_list, xero, self.last_invoice_export_date, xero_account_id=self.id, company=self.company_id.id, disable_export=self.export_disable)
        if not self._context.get('invoice_ids'):
            self.last_invoice_export_date = fields.Datetime.now()

    def export_payment(self):
        self.ensure_one()
        xero = self.xero_auth()
        self.env['account.move'].export_payment(xero, xero_account_id=self.id, company=self.company_id.id, disable_export=self.export_disable)

    def export_credit_notes(self):
        self.ensure_one()
        xero = self.xero_auth()
        credit_notes_list = xero.creditnotes.all()
        self.env['account.move'].export_credit_notes(credit_notes_list , xero, self.last_creditnote_export_date, xero_account_id=self.id, company=self.company_id.id, disable_export=self.export_disable)
        if not self._context.get('invoice_ids'):
            self.last_creditnote_export_date = fields.Datetime.now()

    def export_credit_notes_payment(self):
        self.ensure_one()
        xero = self.xero_auth()
        self.env['account.move'].export_credit_notes_payment(xero, xero_account_id=self.id, company=self.company_id.id, disable_export=self.export_disable)

    def export_inventory_adjustments(self):
        self.ensure_one()
        xero = self.xero_auth()
        if not self.adjustment_account_id:
            if self._context.get('schedule_action'):
                description = (_("Please first configure 'Inventory Adjustment Account'"))
                mismatch_vals = {'name': self.name,
                                 'source_model': 'xero.account',
                                 'source_id': self.id,
                                 'description': description,
                                 'date': fields.datetime.now(),
                                 'xero_account_id': self.id
                                 }
                self.env['mismatch.log'].create(mismatch_vals)
            else:
                raise UserError(_("Please first configure 'Inventory Adjustment Account'"))
        self.env['stock.move.line'].create_inventory_adjustments(xero, xero_account_id=self.id, company=self.company_id.id, adjustment_account=self.adjustment_account_id)

    def export_attachments(self):
        self.ensure_one()
        xero = self.xero_auth()
        self.env['ir.attachment'].export_attachments(xero, self.last_attachment_export_date, company=self.company_id.id)
        self.last_attachment_export_date = fields.Datetime.now()

    def export_purchase_order(self):
        self.ensure_one()
        xero = self.xero_auth()
        po_list = xero.purchaseorders.all()
        self.env['purchase.order'].export_purchase_order(po_list, xero, self.last_po_export_date, xero_account_id=self.id, company=self.company_id.id)
        if not self._context.get('order_ids'):
            self.last_po_export_date = fields.Datetime.now()

    def export_quotes(self):
        self.ensure_one()
        xero = self.xero_auth()
        order_list = xero.quotes.all()
        self.env['sale.order'].export_quotes(order_list, xero, self.last_quotes_export_date, xero_account_id=self.id, company=self.company_id.id)
        if not self._context.get('order_ids'):
            self.last_quotes_export_date = fields.Datetime.now()


class XeroOrganization(models.Model):
    _name = 'xero.organization'
    _description = 'Xero Organization'

    name = fields.Char(string='Name', readonly=True)
    xero_id = fields.Char(string='Xero ID', readonly=True)
    xero_tenant_id = fields.Char(string='Tenant Id', readonly=True)
    xero_tenant_type = fields.Char(string='Tenant Type', readonly=True)
    active = fields.Boolean(string='Active', default=True)
    xero_account_id = fields.Many2one('xero.account', string='Xero Account')
