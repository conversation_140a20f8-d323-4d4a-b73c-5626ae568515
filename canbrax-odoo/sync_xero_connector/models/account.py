# -*- coding: utf-8 -*-
# Part of Odoo. See COPYRIGHT & LICENSE files for full copyright and licensing details.

import math
import time
from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
from odoo.tools.safe_eval import safe_eval
from odoo.tools.float_utils import float_round as round
from collections import defaultdict
from odoo.tools.misc import formatLang
from odoo.addons.account.models.account_tax import AccountTax

# @api.model
# def _prepare_tax_totals(self, base_lines, currency, tax_lines=None, is_company_currency_requested=False):
#     """ Compute the tax totals details for the business documents.
#     :param base_lines:                      A list of python dictionaries created using the '_convert_to_tax_base_line_dict' method.
#     :param currency:                        The currency set on the business document.
#     :param tax_lines:                       Optional list of python dictionaries created using the '_convert_to_tax_line_dict'
#                                             method. If specified, the taxes will be recomputed using them instead of
#                                             recomputing the taxes on the provided base lines.
#     :param is_company_currency_requested :  Optional boolean which indicates whether or not the company currency is
#                                             requested from the function. This can typically be used when using an
#                                             invoice in foreign currency and the company currency is required.

#     :return: A dictionary in the following form:
#         {
#             'amount_total':                 The total amount to be displayed on the document, including every total
#                                             types.
#             'amount_untaxed':               The untaxed amount to be displayed on the document.
#             'formatted_amount_total':       Same as amount_total, but as a string formatted accordingly with
#                                             partner's locale.
#             'formatted_amount_untaxed':     Same as amount_untaxed, but as a string formatted accordingly with
#                                             partner's locale.
#             'groups_by_subtotals':          A dictionary formed liked {'subtotal': groups_data}
#                                             Where total_type is a subtotal name defined on a tax group, or the
#                                             default one: 'Untaxed Amount'.
#                                             And groups_data is a list of dict in the following form:
#                 {
#                     'tax_group_name':                           The name of the tax groups this total is made for.
#                     'tax_group_amount':                         The total tax amount in this tax group.
#                     'tax_group_base_amount':                    The base amount for this tax group.
#                     'formatted_tax_group_amount':               Same as tax_group_amount, but as a string formatted accordingly
#                                                                 with partner's locale.
#                     'formatted_tax_group_base_amount':          Same as tax_group_base_amount, but as a string formatted
#                                                                 accordingly with partner's locale.
#                     'tax_group_id':                             The id of the tax group corresponding to this dict.
#                     'tax_group_base_amount_company_currency':   OPTIONAL: the base amount of the tax group expressed in
#                                                                 the company currency when the parameter
#                                                                 is_company_currency_requested is True
#                     'tax_group_amount_company_currency':        OPTIONAL: the tax amount of the tax group expressed in
#                                                                 the company currency when the parameter
#                                                                 is_company_currency_requested is True
#                 }
#             'subtotals':                    A list of dictionaries in the following form, one for each subtotal in
#                                             'groups_by_subtotals' keys.
#                 {
#                     'name':                             The name of the subtotal
#                     'amount':                           The total amount for this subtotal, summing all the tax groups
#                                                         belonging to preceding subtotals and the base amount
#                     'formatted_amount':                 Same as amount, but as a string formatted accordingly with
#                                                         partner's locale.
#                     'amount_company_currency':          OPTIONAL: The total amount in company currency when the
#                                                         parameter is_company_currency_requested is True
#                 }
#             'subtotals_order':              A list of keys of `groups_by_subtotals` defining the order in which it needs
#                                             to be displayed
#         }
#     """

#     # ==== Compute the taxes ====

#     to_process = []
#     for base_line in base_lines:
#         to_update_vals, tax_values_list = self._compute_taxes_for_single_line(base_line)
#         to_process.append((base_line, to_update_vals, tax_values_list))

#     def grouping_key_generator(base_line, tax_values):
#         source_tax = tax_values['tax_repartition_line'].tax_id
#         return {'tax_group': source_tax.tax_group_id}

#     global_tax_details = self._aggregate_taxes(to_process, grouping_key_generator=grouping_key_generator)

#     tax_group_vals_list = []
#     for tax_detail in global_tax_details['tax_details'].values():
#         tax_group_vals = {
#             'tax_group': tax_detail['tax_group'],
#             'base_amount': tax_detail['base_amount_currency'],
#             'tax_amount': tax_detail['tax_amount_currency'],
#         }
#         if is_company_currency_requested:
#             tax_group_vals['base_amount_company_currency'] = tax_detail['base_amount']
#             tax_group_vals['tax_amount_company_currency'] = tax_detail['tax_amount']

#         # Handle a manual edition of tax lines.
#         if self._context.get('line_amount_type') != 'Inclusive':
#             if tax_lines is not None:
#                 matched_tax_lines = [
#                     x
#                     for x in tax_lines
#                     if (x['group_tax'] or x['tax_repartition_line'].tax_id).tax_group_id == tax_detail['tax_group']
#                 ]
#                 if matched_tax_lines:
#                     tax_group_vals['tax_amount'] = sum(x['tax_amount'] for x in matched_tax_lines)

#         tax_group_vals_list.append(tax_group_vals)

#     tax_group_vals_list = sorted(tax_group_vals_list, key=lambda x: (x['tax_group'].sequence, x['tax_group'].id))

#     # ==== Partition the tax group values by subtotals ====

#     amount_untaxed = global_tax_details['base_amount_currency']
#     amount_tax = 0.0

#     amount_untaxed_company_currency = global_tax_details['base_amount']
#     amount_tax_company_currency = 0.0

#     subtotal_order = {}
#     groups_by_subtotal = defaultdict(list)
#     for tax_group_vals in tax_group_vals_list:
#         tax_group = tax_group_vals['tax_group']

#         subtotal_title = tax_group.preceding_subtotal or _("Untaxed Amount")
#         sequence = tax_group.sequence

#         subtotal_order[subtotal_title] = min(subtotal_order.get(subtotal_title, float('inf')), sequence)
#         groups_by_subtotal[subtotal_title].append({
#             'group_key': tax_group.id,
#             'tax_group_id': tax_group.id,
#             'tax_group_name': tax_group.name,
#             'tax_group_amount': tax_group_vals['tax_amount'],
#             'tax_group_base_amount': tax_group_vals['base_amount'],
#             'formatted_tax_group_amount': formatLang(self.env, tax_group_vals['tax_amount'], currency_obj=currency),
#             'formatted_tax_group_base_amount': formatLang(self.env, tax_group_vals['base_amount'], currency_obj=currency),
#         })
#         if is_company_currency_requested:
#             groups_by_subtotal[subtotal_title][-1]['tax_group_amount_company_currency'] = tax_group_vals['tax_amount_company_currency']
#             groups_by_subtotal[subtotal_title][-1]['tax_group_base_amount_company_currency'] = tax_group_vals['base_amount_company_currency']

#     # ==== Build the final result ====

#     subtotals = []
#     for subtotal_title in sorted(subtotal_order.keys(), key=lambda k: subtotal_order[k]):
#         amount_total = amount_untaxed + amount_tax
#         subtotals.append({
#             'name': subtotal_title,
#             'amount': amount_total,
#             'formatted_amount': formatLang(self.env, amount_total, currency_obj=currency),
#         })
#         if is_company_currency_requested:
#             subtotals[-1]['amount_company_currency'] = amount_untaxed_company_currency + amount_tax_company_currency
#             amount_tax_company_currency += sum(x['tax_group_amount_company_currency'] for x in groups_by_subtotal[subtotal_title])

#         amount_tax += sum(x['tax_group_amount'] for x in groups_by_subtotal[subtotal_title])

#     amount_total = amount_untaxed + amount_tax

#     display_tax_base = (len(global_tax_details['tax_details']) == 1 and tax_group_vals_list[0]['base_amount'] != amount_untaxed) \
#             or len(global_tax_details['tax_details']) > 1

#     return {
#         'amount_untaxed': currency.round(amount_untaxed) if currency else amount_untaxed,
#         'amount_total': currency.round(amount_total) if currency else amount_total,
#         'formatted_amount_total': formatLang(self.env, amount_total, currency_obj=currency),
#         'formatted_amount_untaxed': formatLang(self.env, amount_untaxed, currency_obj=currency),
#         'groups_by_subtotal': groups_by_subtotal,
#         'subtotals': subtotals,
#         'subtotals_order': sorted(subtotal_order.keys(), key=lambda k: subtotal_order[k]),
#         'display_tax_base': display_tax_base
#     }

# AccountTax._prepare_tax_totals = _prepare_tax_totals


# def _compute_amount(self, base_amount, price_unit, quantity=1.0, product=None, partner=None, fixed_multiplicator=1, line_amount_type=False):
#     """ Returns the amount of a single tax. base_amount is the actual amount on which the tax is applied, which is
#         price_unit * quantity eventually affected by previous taxes (if tax is include_base_amount XOR price_include)
#     """
#     self.ensure_one()
#     if self.amount_type == 'fixed':
#         # Use copysign to take into account the sign of the base amount which includes the sign
#         # of the quantity and the sign of the price_unit
#         # Amount is the fixed price for the tax, it can be negative
#         # Base amount included the sign of the quantity and the sign of the unit price and when
#         # a product is returned, it can be done either by changing the sign of quantity or by changing the
#         # sign of the price unit.
#         # When the price unit is equal to 0, the sign of the quantity is absorbed in base_amount then
#         # a "else" case is needed.
#         if base_amount:
#             return math.copysign(quantity, base_amount) * self.amount * abs(fixed_multiplicator)
#         else:
#             return quantity * self.amount * abs(fixed_multiplicator)

#     price_include = self._context.get('force_price_include', self.price_include)

#     # <=> new_base = base / (1 + tax_amount)
#     if self.amount_type == 'percent' and (price_include or line_amount_type == 'Inclusive'):
#         return base_amount - (base_amount / (1 + self.amount / 100))

#     # base * (1 + tax_amount) = new_base
#     if self.amount_type == 'percent' and not price_include:
#         return base_amount * self.amount / 100

#     # base / (1 - tax_amount) = new_base
#     if self.amount_type == 'division' and not price_include:
#         return base_amount / (1 - self.amount / 100) - base_amount if (1 - self.amount / 100) else 0.0

#     # <=> new_base * (1 - tax_amount) = base
#     if self.amount_type == 'division' and (price_include or line_amount_type == 'Inclusive'):
#         return base_amount - (base_amount * (self.amount / 100))

#     if self.amount_type == 'code':
#         company = self.env.company
#         localdict = {'base_amount': base_amount, 'price_unit':price_unit, 'quantity': quantity, 'product':product, 'partner':partner, 'company': company}
#         safe_eval(self.python_compute, localdict, mode="exec", nocopy=True)
#         return localdict['result']

# AccountTax._compute_amount = _compute_amount


# def compute_all(self, price_unit, currency=None, quantity=1.0, product=None, partner=None, is_refund=False, handle_price_include=True, line_amount_type=False, include_caba_tags=False, fixed_multiplicator=1):
#     """Compute all information required to apply taxes (in self + their children in case of a tax group).
#     We consider the sequence of the parent for group of taxes.
#         Eg. considering letters as taxes and alphabetic order as sequence :
#         [G, B([A, D, F]), E, C] will be computed as [A, D, F, C, E, G]



#     :param price_unit: The unit price of the line to compute taxes on.
#     :param currency: The optional currency in which the price_unit is expressed.
#     :param quantity: The optional quantity of the product to compute taxes on.
#     :param product: The optional product to compute taxes on.
#         Used to get the tags to apply on the lines.
#     :param partner: The optional partner compute taxes on.
#         Used to retrieve the lang to build strings and for potential extensions.
#     :param is_refund: The optional boolean indicating if this is a refund.
#     :param handle_price_include: Used when we need to ignore all tax included in price. If False, it means the
#         amount passed to this method will be considered as the base of all computations.
#     :param include_caba_tags: The optional boolean indicating if CABA tags need to be taken into account.
#     :param fixed_multiplicator: The amount to multiply fixed amount taxes by.
#     :return: {
#         'total_excluded': 0.0,    # Total without taxes
#         'total_included': 0.0,    # Total with taxes
#         'total_void'    : 0.0,    # Total with those taxes, that don't have an account set
#         'base_tags: : list<int>,  # Tags to apply on the base line
#         'taxes': [{               # One dict for each tax in self and their children
#             'id': int,
#             'name': str,
#             'amount': float,
#             'base': float,
#             'sequence': int,
#             'account_id': int,
#             'refund_account_id': int,
#             'analytic': bool,
#             'price_include': bool,
#             'tax_exigibility': str,
#             'tax_repartition_line_id': int,
#             'group': recordset,
#             'tag_ids': list<int>,
#             'tax_ids': list<int>,
#         }],
#     } """
#     if not line_amount_type:
#         line_amount_type = self._context.get('line_amount_type')
#     if not self:
#         company = self.env.company
#     else:
#         company = self[0].company_id._accessible_branches()[:1]
#     # 1) Flatten the taxes.
#     taxes, groups_map = self.flatten_taxes_hierarchy(create_map=True)
#     # 2) Avoid mixing taxes having price_include=False && include_base_amount=True
#     # with taxes having price_include=True. This use case is not supported as the
#     # computation of the total_excluded would be impossible.
#     base_excluded_flag = False  # price_include=False && include_base_amount=True
#     included_flag = False  # price_include=True
#     for tax in taxes:
#         if tax.price_include:
#             included_flag = True
#         elif tax.include_base_amount or line_amount_type == 'Inclusive':
#             base_excluded_flag = True
#         if base_excluded_flag and included_flag:
#             raise UserError(_('Unable to mix any taxes being price included with taxes affecting the base amount but not included in price.'))

#     # 3) Deal with the rounding methods
#     if not currency:
#         currency = company.currency_id
#     # By default, for each tax, tax amount will first be computed
#     # and rounded at the 'Account' decimal precision for each
#     # PO/SO/invoice line and then these rounded amounts will be
#     # summed, leading to the total amount for that tax. But, if the
#     # company has tax_calculation_rounding_method = round_globally,
#     # we still follow the same method, but we use a much larger
#     # precision when we round the tax amount for each line (we use
#     # the 'Account' decimal precision + 5), and that way it's like
#     # rounding after the sum of the tax amounts of each line
#     prec = currency.rounding

#     # In some cases, it is necessary to force/prevent the rounding of the tax and the total
#     # amounts. For example, in SO/PO line, we don't want to round the price unit at the
#     # precision of the currency.
#     # The context key 'round' allows to force the standard behavior.
#     round_tax = False if company.tax_calculation_rounding_method == 'round_globally' else True
#     if 'round' in self.env.context:
#         round_tax = bool(self.env.context['round'])

#     if not round_tax:
#         prec *= 1e-5

#     # 4) Iterate the taxes in the reversed sequence order to retrieve the initial base of the computation.
#     #     tax  |  base  |  amount  |
#     # /\ ----------------------------
#     # || tax_1 |  XXXX  |          | <- we are looking for that, it's the total_excluded
#     # || tax_2 |   ..   |          |
#     # || tax_3 |   ..   |          |
#     # ||  ...  |   ..   |    ..    |
#     #    ----------------------------
#     def recompute_base(base_amount, fixed_amount, percent_amount, division_amount):
#         # Recompute the new base amount based on included fixed/percent amounts and the current base amount.
#         # Example:
#         #  tax  |  amount  |   type   |  price_include  |
#         # -----------------------------------------------
#         # tax_1 |   10%    | percent  |  t
#         # tax_2 |   15     |   fix    |  t
#         # tax_3 |   20%    | percent  |  t
#         # tax_4 |   10%    | division |  t
#         # -----------------------------------------------

#         # if base_amount = 145, the new base is computed as:
#         # (145 - 15) / (1.0 + 30%) * 90% = 130 / 1.3 * 90% = 90
#         return (base_amount - fixed_amount) / (1.0 + percent_amount / 100.0) * (100 - division_amount) / 100

#     # The first/last base must absolutely be rounded to work in round globally.
#     # Indeed, the sum of all taxes ('taxes' key in the result dictionary) must be strictly equals to
#     # 'price_included' - 'price_excluded' whatever the rounding method.
#     #
#     # Example using the global rounding without any decimals:
#     # Suppose two invoice lines: 27000 and 10920, both having a 19% price included tax.
#     #
#     #                   Line 1                      Line 2
#     # -----------------------------------------------------------------------
#     # total_included:   27000                       10920
#     # tax:              27000 / 1.19 = 4310.924     10920 / 1.19 = 1743.529
#     # total_excluded:   22689.076                   9176.471
#     #
#     # If the rounding of the total_excluded isn't made at the end, it could lead to some rounding issues
#     # when summing the tax amounts, e.g. on invoices.
#     # In that case:
#     #  - amount_untaxed will be 22689 + 9176 = 31865
#     #  - amount_tax will be 4310.924 + 1743.529 = 6054.453 ~ 6054
#     #  - amount_total will be 31865 + 6054 = 37919 != 37920 = 27000 + 10920
#     #
#     # By performing a rounding at the end to compute the price_excluded amount, the amount_tax will be strictly
#     # equals to 'price_included' - 'price_excluded' after rounding and then:
#     #   Line 1: sum(taxes) = 27000 - 22689 = 4311
#     #   Line 2: sum(taxes) = 10920 - 2176 = 8744
#     #   amount_tax = 4311 + 8744 = 13055
#     #   amount_total = 31865 + 13055 = 37920
#     base = currency.round(price_unit * quantity)

#     # For the computation of move lines, we could have a negative base value.
#     # In this case, compute all with positive values and negate them at the end.
#     sign = 1
#     if currency.is_zero(base):
#         sign = -1 if fixed_multiplicator < 0 else 1
#     elif base < 0:
#         sign = -1
#         base = -base

#     # Store the totals to reach when using price_include taxes (only the last price included in row)
#     total_included_checkpoints = {}
#     i = len(taxes) - 1
#     store_included_tax_total = True
#     # Keep track of the accumulated included fixed/percent amount.
#     incl_fixed_amount = incl_percent_amount = incl_division_amount = 0
#     # Store the tax amounts we compute while searching for the total_excluded
#     cached_tax_amounts = {}
#     if handle_price_include:
#         for tax in reversed(taxes):
#             tax_repartition_lines = (
#                 is_refund
#                 and tax.refund_repartition_line_ids
#                 or tax.invoice_repartition_line_ids
#             ).filtered(lambda x: x.repartition_type == "tax")
#             sum_repartition_factor = sum(tax_repartition_lines.mapped("factor"))

#             if tax.include_base_amount or (line_amount_type and line_amount_type == 'Inclusive'):
#                 base = recompute_base(base, incl_fixed_amount, incl_percent_amount, incl_division_amount)
#                 incl_fixed_amount = incl_percent_amount = incl_division_amount = 0
#                 store_included_tax_total = True
#             if tax.price_include or self._context.get('force_price_include') or (line_amount_type and line_amount_type == 'Inclusive'):
#                 if tax.amount_type == 'percent':
#                     incl_percent_amount += tax.amount * sum_repartition_factor
#                 elif tax.amount_type == 'division':
#                     incl_division_amount += tax.amount * sum_repartition_factor
#                 elif tax.amount_type == 'fixed':
#                     incl_fixed_amount += abs(quantity) * tax.amount * sum_repartition_factor * abs(fixed_multiplicator)
#                 else:
#                     # tax.amount_type == other (python)
#                     tax_amount = tax._compute_amount(base, sign * price_unit, quantity, product, partner, fixed_multiplicator, line_amount_type=line_amount_type) * sum_repartition_factor
#                     incl_fixed_amount += tax_amount
#                     # Avoid unecessary re-computation
#                     cached_tax_amounts[i] = tax_amount
#                 # In case of a zero tax, do not store the base amount since the tax amount will
#                 # be zero anyway. Group and Python taxes have an amount of zero, so do not take
#                 # them into account.
#                 if store_included_tax_total and (
#                     tax.amount or tax.amount_type not in ("percent", "division", "fixed")
#                 ):
#                     total_included_checkpoints[i] = base
#                     store_included_tax_total = False
#             i -= 1

#     total_excluded = currency.round(recompute_base(base, incl_fixed_amount, incl_percent_amount, incl_division_amount))

#     # 5) Iterate the taxes in the sequence order to compute missing tax amounts.
#     # Start the computation of accumulated amounts at the total_excluded value.
#     base = total_included = total_void = total_excluded

#     # Flag indicating the checkpoint used in price_include to avoid rounding issue must be skipped since the base
#     # amount has changed because we are currently mixing price-included and price-excluded include_base_amount
#     # taxes.
#     skip_checkpoint = False

#     # Get product tags, account.account.tag objects that need to be injected in all
#     # the tax_tag_ids of all the move lines created by the compute all for this product.
#     product_tag_ids = product.account_tag_ids.ids if product else []

#     taxes_vals = []
#     i = 0
#     cumulated_tax_included_amount = 0
#     for tax in taxes:
#         price_include = self._context.get('force_price_include', tax.price_include)

#         if price_include or (line_amount_type and line_amount_type == 'Inclusive') or tax.is_base_affected:
#             tax_base_amount = base
#         else:
#             tax_base_amount = total_excluded

#         tax_repartition_lines = (is_refund and tax.refund_repartition_line_ids or tax.invoice_repartition_line_ids).filtered(lambda x: x.repartition_type == 'tax')
#         sum_repartition_factor = sum(tax_repartition_lines.mapped('factor'))

#         #compute the tax_amount
#         if not skip_checkpoint and (price_include or (line_amount_type and line_amount_type == 'Inclusive')) and total_included_checkpoints.get(i) is not None and sum_repartition_factor != 0:
#             # We know the total to reach for that tax, so we make a substraction to avoid any rounding issues
#             tax_amount = total_included_checkpoints[i] - (base + cumulated_tax_included_amount)
#             cumulated_tax_included_amount = 0
#         else:
#             tax_amount = tax.with_context(force_price_include=False)._compute_amount(
#                 tax_base_amount, sign * price_unit, quantity, product, partner, fixed_multiplicator, line_amount_type=line_amount_type)

#         # Round the tax_amount multiplied by the computed repartition lines factor.
#         tax_amount = round(tax_amount, precision_rounding=prec)
#         factorized_tax_amount = round(tax_amount * sum_repartition_factor, precision_rounding=prec)

#         if (price_include and total_included_checkpoints.get(i) is None) or (line_amount_type and line_amount_type == 'Inclusive'):
#             cumulated_tax_included_amount += factorized_tax_amount

#         # If the tax affects the base of subsequent taxes, its tax move lines must
#         # receive the base tags and tag_ids of these taxes, so that the tax report computes
#         # the right total
#         subsequent_taxes = self.env['account.tax']
#         subsequent_tags = self.env['account.account.tag']
#         # if tax.include_base_amount or line_amount_type == 'Inclusive':
#         #     subsequent_taxes = taxes[i+1:]
#         #     subsequent_tags = subsequent_taxes.get_tax_tags(is_refund, 'base')

#         if tax.include_base_amount or line_amount_type == 'Inclusive':
#             subsequent_taxes = taxes[i+1:].filtered('is_base_affected')

#             taxes_for_subsequent_tags = subsequent_taxes

#             if not include_caba_tags:
#                 taxes_for_subsequent_tags = subsequent_taxes.filtered(lambda x: x.tax_exigibility != 'on_payment')

#             subsequent_tags = taxes_for_subsequent_tags.get_tax_tags(is_refund, 'base')


#         # Compute the tax line amounts by multiplying each factor with the tax amount.
#         # Then, spread the tax rounding to ensure the consistency of each line independently with the factorized
#         # amount. E.g:
#         #
#         # Suppose a tax having 4 x 50% repartition line applied on a tax amount of 0.03 with 2 decimal places.
#         # The factorized_tax_amount will be 0.06 (200% x 0.03). However, each line taken independently will compute
#         # 50% * 0.03 = 0.01 with rounding. It means there is 0.06 - 0.04 = 0.02 as total_rounding_error to dispatch
#         # in lines as 2 x 0.01.
#         repartition_line_amounts = [round(tax_amount * line.factor, precision_rounding=prec) for line in tax_repartition_lines]
#         total_rounding_error = round(factorized_tax_amount - sum(repartition_line_amounts), precision_rounding=prec)
#         nber_rounding_steps = int(abs(total_rounding_error / currency.rounding))
#         rounding_error = round(nber_rounding_steps and total_rounding_error / nber_rounding_steps or 0.0, precision_rounding=prec)

#         for repartition_line, line_amount in zip(tax_repartition_lines, repartition_line_amounts):

#             if nber_rounding_steps:
#                 line_amount += rounding_error
#                 nber_rounding_steps -= 1

#             if not include_caba_tags and tax.tax_exigibility == 'on_payment':
#                 repartition_line_tags = self.env['account.account.tag']
#             else:
#                 repartition_line_tags = repartition_line.tag_ids

#             price_include = True if line_amount_type == 'Inclusive' else False
#             taxes_vals.append({
#                 'id': tax.id,
#                 'name': partner and tax.with_context(lang=partner.lang).name or tax.name,
#                 'amount': sign * line_amount,
#                 'base': round(sign * tax_base_amount, precision_rounding=prec),
#                 'sequence': tax.sequence,
#                 'account_id': repartition_line._get_aml_target_tax_account().id,
#                 'analytic': tax.analytic,
#                 'use_in_tax_closing': repartition_line.use_in_tax_closing,
#                 'price_include': price_include,
#                 'tax_exigibility': tax.tax_exigibility,
#                 'tax_repartition_line_id': repartition_line.id,
#                 'group': groups_map.get(tax),
#                 'tag_ids': (repartition_line_tags + subsequent_tags).ids + product_tag_ids,
#                 'tax_ids': subsequent_taxes.ids,
#             })

#             if not repartition_line.account_id:
#                 total_void += line_amount
#             # if price_include:
#             #     total_void += line_amount

#         # Affect subsequent taxes
#         if tax.include_base_amount or line_amount_type == 'Inclusive':
#             base += factorized_tax_amount
#             if not price_include:
#                 skip_checkpoint = True

#         total_included += factorized_tax_amount
#         i += 1

#     base_taxes_for_tags = taxes
#     if not include_caba_tags:
#         base_taxes_for_tags = base_taxes_for_tags.filtered(lambda x: x.tax_exigibility != 'on_payment')

#     base_rep_lines = base_taxes_for_tags.mapped(is_refund and 'refund_repartition_line_ids' or 'invoice_repartition_line_ids').filtered(lambda x: x.repartition_type == 'base')
#     return {
#         'base_tags': base_rep_lines.tag_ids.ids + product_tag_ids,
#         'taxes': taxes_vals,
#         'total_excluded': sign * total_excluded,
#         'total_included': sign * currency.round(total_included),
#         'total_void': sign * currency.round(total_void),
#     }

# AccountTax.compute_all = compute_all


class AccountJournal(models.Model):
    _inherit = "account.journal"

    xero_account_id = fields.Many2one('account.account', string="Xero Account")


class AccountPaymentMethodLine(models.Model):
    _inherit = "account.payment.method.line"

    payment_account_id = fields.Many2one(
        comodel_name='account.account',
        check_company=True,
        copy=False,
        ondelete='restrict',
        domain=lambda self: "[('deprecated', '=', False), ('company_ids', 'in', company_id), \
                            ('account_type', 'not in', ('asset_receivable', 'liability_payable'))]")


class TaxComponent(models.Model):
    _name = 'tax.component'
    _description = "Tax Components"

    name = fields.Char('Name')
    rate = fields.Float('Rate')
    tax_id = fields.Many2one('account.tax', 'Tax')

    _sql_constraints = [
        ('rate_check', 'CHECK(rate >= 0 AND rate <= 100)', 'Please Enter Rate Value Between 0 to 100!'),
    ]


class AccountTax(models.Model):
    _inherit = 'account.tax'
    _description = 'Account Tax'

    name = fields.Char(string='Tax Name', size=50, required=True)
    xero_tax_type = fields.Char('Xero Tax Type', copy=False)
    componet_ids = fields.One2many('tax.component', 'tax_id', 'Components')

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('componet_ids') and vals.get('componet_ids')[0][0] != 6:
                amount = 0.0
                for component in vals.get('componet_ids'):
                    if len(component) > 2:
                        amount += component[2].get('rate')
                vals.update({'amount': amount})
            if vals.get('name'):
                vals.update({'name': vals['name'] + vals.get('type_tax_use', self.type_tax_use)})
        return super(AccountTax, self).create(vals_list)

    def write(self, values):
        if values.get('componet_ids') and values.get('componet_ids')[0][0] != 6:
            amount = self.amount
            for component in values.get('componet_ids'):
                if len(component) > 2 and component[2] and component[2].get('rate'):
                    amount += component[2].get('rate')
            values.update({'amount': amount})
        return super(AccountTax, self).write(values)

    @api.constrains('name')
    def _check_unique_name(self):
        for rec in self:
            tax_ids = self.env['account.tax'].search([('id', '!=', rec.id), ('company_id', '=', rec.company_id.id), ('name', '=', rec.name)])
            if tax_ids:
                raise ValidationError(_('Tax name must be unique!'))

    def import_tax(self, tax_list, xero, xero_account_id, company=False, import_option=None):
        """
            Map: Tax Type(Odoo) with Tax Type(Xero)

            Create a tax in odoo if tax is not available with
            tax type and company.

            Note: If any record is already available with same name in odoo which we
            going to import from xero then it will skip that particuler record.

            Constraint: Tax Name must be unique per company

            If tax record is available then it will update that particular record.
        """
        mismatch_log = self.env['mismatch.log']
        for tax in tax_list:
            if tax.get('Status') == 'ACTIVE':
                sub_tax = []
                amount = 0.0
                if tax.get('ReportTaxType') == 'INPUT':
                    tax_type = 'purchase'
                elif tax.get('ReportTaxType') == 'OUTPUT':
                    tax_type = 'sale'
                else:
                    tax_type = 'none'
                for tax_comp in tax.get('TaxComponents'):
                    sub_tax.append(self.env['tax.component'].create({
                        'name': tax_comp.get('Name'),
                        'rate': tax_comp.get('Rate')}).id)
                    amount += tax_comp.get('Rate')
                parent_tax_rec = self.search([('xero_tax_type', '=', tax.get('TaxType')),
                                              ('company_id', '=', company)])
                avilable_tax_ids = self.search([('company_id', '=', company), ('name', '=', tax.get('Name'))])
                if avilable_tax_ids:
                    avilable_tax_ids.write({'xero_tax_type': tax.get('TaxType')})
                if parent_tax_rec and import_option in ['update', 'both']:
                    try:
                        parent_tax_rec[0].write({
                            'name': tax.get('Name'),
                            'amount': amount,
                            'xero_tax_type': tax.get('TaxType'),
                            'componet_ids': [(6, 0, sub_tax)],
                            'type_tax_use': tax_type,
                            'company_id': company,
                        })
                        self._cr.commit()
                    except Exception as e:
                        mismatch_log.create({'name': tax.get('Name'),
                                             'source_model': 'account.tax',
                                             'source_id': parent_tax_rec[0].id,
                                             'description': e,
                                             'date': fields.Datetime.now(),
                                             'option': 'import',
                                             'xero_account_id': xero_account_id,
                                             })
                        continue
                elif not parent_tax_rec and not avilable_tax_ids and import_option in ['create', 'both']:
                    try:
                        self.create({'name': tax.get('Name'),
                                     'amount': amount,
                                     'xero_tax_type': tax.get('TaxType'),
                                     'componet_ids': [(6, 0, sub_tax)],
                                     'type_tax_use': tax_type,
                                     'company_id': company,
                                     })
                        self._cr.commit()
                    except Exception as e:
                        mismatch_log.create({'name': tax.get('Name'),
                                             'source_model': 'account.tax',
                                             'description': e,
                                             'date': fields.Datetime.now(),
                                             'option': 'import',
                                             'xero_account_id': xero_account_id,
                                             })
                        continue

    def export_tax(self, tax_list, xero, xero_account_id, company=False, disable_export=False):
        """
            Map: Tax Type(Odoo) with Tax Type(Xero)

            Create a tax in Xero if tax is not available with
            tax type.

            Note: If any record is already available with same name in Xero which we
            going to export from Odoo then it will skip that particular record.

            Constraint: Tax Name must be unique in Xero

        """
        if not disable_export:
            mismatch_log = self.env['mismatch.log']
            same_tax = []
            final_tax_list = []
            tax_name_list = []
            organisation_detail = xero.organisations.all()
            for tax_id in self.search([('company_id', '=', company)]):
                for tax in tax_list:
                    if tax.get('Name') == tax_id.name:
                        if not tax_id.xero_tax_type:
                            tax_id.xero_tax_type = tax.get('TaxType')
                    else:
                        tax_name_list.append(tax.get('Name'))
                    if tax_id.xero_tax_type == tax.get('TaxType'):
                        same_tax.append(tax_id.id)
                final_tax_list.append(tax_id.id)

            for save_tax in self.browse(list(set(final_tax_list).difference(set(same_tax)))):
                if save_tax.name not in tax_name_list:
                    tax_type = False
                    if save_tax.type_tax_use in ['sale', 'none']:
                        tax_type = 'OUTPUT'
                    elif save_tax.type_tax_use in ['purchase']:
                        tax_type = 'INPUT'

                    components = []
                    for component in save_tax.componet_ids:
                        components.append({u'Name': component.name,
                                           u'Rate': component.rate
                                           })

                    if len(components) < 1:
                        tax_rate = save_tax.amount if save_tax.amount_type == 'percent' else 0.0
                        components.append({u'Name': ' '.join([save_tax.name, str(tax_rate)]),
                                            u'Rate': tax_rate})

                    tax_line = save_tax.invoice_repartition_line_ids.filtered(lambda l: l.repartition_type == 'tax')
                    line = save_tax.invoice_repartition_line_ids.filtered(lambda l: l.repartition_type == 'tax' and l.factor_percent != 100)
                    if save_tax.amount_type != 'percent':
                        description = save_tax.amount_type + ' type tax not exported odoo to xero.'
                        mismatch_log.create({'name': save_tax.name,
                                             'source_model': 'account.tax',
                                             'source_id': save_tax.id,
                                             'description': description,
                                             'date': fields.Datetime.now(),
                                             'option': 'export',
                                             'xero_account_id': xero_account_id,
                                             })
                        continue
                    if line or len(tax_line) != 1:
                        description = 'must be add 100 percentage repartition line for export tax (odoo to xero).'
                        mismatch_log.create({'name': save_tax.name,
                                             'source_model': 'account.tax',
                                             'source_id': save_tax.id,
                                             'description': description,
                                             'date': fields.Datetime.now(),
                                             'option': 'export',
                                             'xero_account_id': xero_account_id,
                                             })
                        continue

                    try:
                        if organisation_detail[0].get('CountryCode') in ['AU', 'NZ', 'GB']:
                            tax_details = xero.taxrates.put({u'Name': save_tax.name,
                                                             u'ReportTaxType': tax_type,
                                                             u'TaxComponents': components})
                        else:
                            tax_details = xero.taxrates.put({u'Name': save_tax.name,
                                                             u'TaxComponents': components})
                        save_tax.xero_tax_type = tax_details[0].get('TaxType', False)
                        self._cr.commit()
                    except Exception as e:
                        mismatch_log.create({'name': save_tax.name,
                                             'source_model': 'account.tax',
                                             'source_id': save_tax.id,
                                             'description': e,
                                             'date': fields.Datetime.now(),
                                             'option': 'export',
                                             'xero_account_id': xero_account_id,
                                             })


class BankAccount(models.Model):
    _inherit = 'res.partner.bank'
    _description = 'Bank Account'

    acc_id = fields.Char('Xero AccountID', readonly=True)

    def import_bank_account(self, bank_account_list, xero, xero_account_id, import_option=None, company=False):
        """
            Map: AccountID(Odoo) with AccountID(Xero)

            Create a bank account in Odoo if account is not available.

            If bank account record is available then it will update that particular record.
        """
        partner_pool = self.env['res.partner'].sudo()
        currency_pool = self.env['res.currency'].sudo()
        mismatch_log = self.env['mismatch.log'].sudo()
        for bank_account in bank_account_list:
            if bank_account.get('Status') == 'ACTIVE':
                partner = partner_pool.search([('name', '=', bank_account.get('Name'))])
                if not partner:
                    partner = partner_pool.create({'name': bank_account.get('Name'), 'company_id': company})
                else:
                    partner = partner[0]

                currency = currency_pool.search([('name', '=', bank_account.get('CurrencyCode')), '|', ('active', '=', True), ('active', '=', False)])
                bank_account_rec = self.search([('acc_id', '=', bank_account.get('AccountID'))])
                available_acc_ids = self.search([('acc_number', '=', bank_account.get('BankAccountNumber'))])
                try:
                    if bank_account_rec and import_option in ['update', 'both']:
                        bank_account_rec[0].write({'partner_id': partner.id,
                                                   'currency_id': currency and currency[0].id or False,
                                                   'acc_number': bank_account.get('BankAccountNumber')})
                        self._cr.commit()
                    elif not bank_account_rec and not available_acc_ids and import_option in ['create', 'both']:
                        self.create({'acc_id': bank_account.get('AccountID'),
                                     'bank_name': bank_account.get('Name'),
                                     'partner_id': partner.id,
                                     'currency_id': currency and currency[0].id or False,
                                     'acc_number': bank_account.get('BankAccountNumber'),
                                     'company_id': company})
                        self._cr.commit()
                except Exception as e:
                    mismatch_log.create({'name': bank_account.get('AccountID'),
                                         'source_model': 'res.partner.bank',
                                         'description': e,
                                         'date': fields.Datetime.now(),
                                         'option': 'import',
                                         'xero_account_id': xero_account_id,
                                         })
                    continue

    def export_bank_account(self, bank_account_list, xero, xero_account_id, company=False, disable_export=False):
        """
            Map: AccountID(Odoo) with AccountID(Xero)

            Create a bank account in Xero if bank account is not available.

            Note: If any record is already available with same BankAccountNumber in Xero which we
            going to export from Odoo then it will skip that particular record.

            Constraint(Xero): The Bank Account Number must be unique

            If bank account record is available in Xero then it will update that particular record.
        """
        if not disable_export:
            mismatch_log = self.env['mismatch.log']
            same_accounts = []
            final_account_list = []
            account_number_list = []
            for bank_account_id in self.search([('company_id', '=', company)]):
                for bank_account in bank_account_list:
                    account_number_list.append(bank_account.get('BankAccountNumber'))
                    if bank_account_id.acc_id == bank_account.get('AccountID'):
                        same_accounts.append(bank_account_id.id)
                final_account_list.append(bank_account_id.id)
            for save_account in self.browse(list(set(final_account_list).difference(set(same_accounts)))):
                if save_account.acc_number not in account_number_list:
                    bank_detail = {u'Name': save_account.partner_id.name + " " + save_account.acc_number,
                                 u'Type': u'BANK',
                                 u'BankAccountType': u'BANK',
                                 u'BankAccountNumber': save_account.acc_number,
                                 u'Code': save_account.acc_number[-6:] or False}
                    if save_account.currency_id:
                        bank_detail.update({u'CurrencyCode': save_account.currency_id.name})
                    acc_rec = xero.accounts.put(bank_detail)
                    if acc_rec[0].get('ValidationErrors'):
                        description = acc_rec[0].get('ValidationErrors')[0].get('Message')
                        mismatch_log.create({'name': save_account.partner_id.name + " " + save_account.acc_number,
                                             'source_model': 'res.partner.bank',
                                             'source_id': save_account.id,
                                             'description': description,
                                             'date': fields.Datetime.now(),
                                             'option': 'export',
                                             'xero_account_id': xero_account_id,
                                             })
                        continue
                    save_account.write({'acc_id': acc_rec[0]['AccountID']})
                    self._cr.commit()

            # for save_account in self.browse(same_accounts):
            #     if save_account.acc_id and save_account.acc_number in account_number_list:
            #         bank_detail = {
            #                      u'Name': save_account.partner_id.name,
            #                      u'Type': u'BANK',
            #                      u'BankAccountType': u'BANK',
            #                      u'AccountID': save_account.acc_id,
            #                      }
            #         if save_account.currency_id:
            #             bank_detail.update({u'CurrencyCode': save_account.currency_id.name})
            #         acc_rec = xero.accounts.save(bank_detail)
            #         if acc_rec[0].get('ValidationErrors'):
            #             description = acc_rec[0].get('ValidationErrors')[0].get('Message')
            #             mismatch_log.create({'name': save_account.partner_id.name + " " + save_account.acc_number,
            #                                  'source_model': 'res.partner.bank',
            #                                  'source_id': save_account.id,
            #                                  'description': description,
            #                                  'date': fields.Datetime.now(),
            #                                  'option': 'export',
            #                                  'xero_account_id': xero_account_id,
            #                                  })
            #             continue
            #         # save_account.write({'acc_id': acc_rec[0]['AccountID']})
            #         self._cr.commit()


class AccountAccount(models.Model):
    _inherit = 'account.account'
    _description = 'Account'

    acc_id = fields.Char('Xero AccountID', readonly=True, copy=False)
    account_type_xero = fields.Selection([
            # Asset
            ('current', 'CURRENT'),
            ('noncurrent', 'NONCURRENT'),
            ('fixed', 'FIXED'),
            ('inventory', 'INVENTORY'),
            ('prepayment', 'PREPAYMENT'),

            # Liability
            ('bank', 'BANK'),
            ('liability', 'LIABILITY'),
            ('currliab', 'CURRLIAB'),
            ('equity', 'EQUITY'),
            ('termliab', 'TERMLIAB'),
            ('paygliability', 'PAYGLIABILITY'),
            ('wagespayableliability', 'WAGESPAYABLELIABILITY'),
            ('superannuationliability', 'SUPERANNUATIONLIABILITY'),

            # Expense
            ('depreciatn', 'DEPRECIATN'),
            ('directcosts', 'DIRECTCOSTS'),
            ('expense', 'EXPENSE'),
            ('overheads', 'OVERHEADS'),
            ('superannuationexpense', 'SUPERANNUATIONEXPENSE'),
            ('wagesexpense', 'WAGESEXPENSE'),

            # Income
            ('otherincome', 'OTHERINCOME'),
            ('revenue', 'REVENUE'),
            ('sales', 'SALES'),
        ], string='Xero Account Type', default='sales')
    use_as_inventory_in_xero = fields.Boolean(string="Use As Inventory In Xero")
    xero_description = fields.Text(string="Xero Description")

    @api.model
    def _name_search(self, name, domain=None, operator='ilike', limit=None, order=None):
        domain = domain or []
        context = self._context or {}
        if context.get('is_payment_account'):
            domain.append(['account_type', '=', 'asset_current'])
        if context.get('xero_account_id'):
            for arg in domain:
                if arg[0] == 'account_type':
                    domain.remove(arg)
        return super(AccountAccount, self)._name_search(name, domain=domain, operator=operator, limit=limit, order=order)

    def import_account(self, account_list, xero, xero_account_id, company=False, import_option=None):
        """
            Map: AccountID(Odoo) with AccountID(Xero)

            Create a account in Odoo if account is not available with
            AccountID and company.

            Note: If any record is already available with same code in Odoo which we
            going to import from xero then it will skip that particular record.

            Constraint(Odoo): The code of the account must be unique per company

            If account record is available then it will update that particular record.
        """
        mismatch_log = self.env['mismatch.log']
        account_mapping = {'bank': 'asset_cash',
                           'current': 'asset_current',
                           'currliab': 'liability_current',
                           'depreciatn': 'expense_depreciation',
                           'directcosts': 'expense',
                           'equity': 'equity',
                           'expense': 'expense',
                           'fixed': 'asset_fixed',
                           'inventory': 'asset_current',
                           'liability': 'liability_current',
                           'noncurrent': 'asset_non_current',
                           'otherincome': 'income_other',
                           'overheads': 'expense',
                           'prepayment': 'asset_prepayments',
                           'revenue': 'expense_direct_cost',
                           'sales': 'asset_receivable',
                           'termliab': 'liability_non_current',
                           'paygliability': 'liability_current',
                           'wagesexpense': 'expense',
                           'superannuationexpense': 'expense',
                           'superannuationliability': 'liability_non_current',
                            }

        for account in account_list:
            if account.get('Status') == 'ACTIVE' and account.get('Type'):
                account_type = str(account['Type'].lower())
                if account_mapping.get(account_type):
                    user_type = account_mapping[account_type]
                    account_rec = self.search([('acc_id', '=', account.get('AccountID')),
                                               ('company_ids', 'in', company)], limit=1)
                    if account.get('Type') == 'BANK' and not account.get('Code'):
                        raise UserError(_('Please enter unique code for account \'%s\' on Xero side.')% (account.get('Name')))
                    available_acc_ids = self.search([('code', '=', account.get('Code')),
                                                     ('company_ids', 'in', company)], limit=1)

                    if available_acc_ids and not available_acc_ids.acc_id and available_acc_ids.name == account.get('Name'):
                        available_acc_ids.acc_id = account.get('AccountID')
                    tax_id = self.env['account.tax'].search([('xero_tax_type', '=', account.get('TaxType')),
                                                             ('company_id', '=', company)])

                    if account_rec and import_option in ['update', 'both']:
                        try:
                            account_rec.write({
                                     'name': account.get('Name'),
                                     'company_ids': [(4, company)],
                                     'tax_ids': [(6, 0, [tax_id[0].id])] if tax_id else [],
                                     'use_as_inventory_in_xero': True if account_type == 'inventory' else False,
                                     'xero_description': account.get('Description'),
                                     # 'account_type_xero': account_type,
                                     # 'account_type': user_type or False,
                                })
                        except Exception as e:
                            mismatch_log.create({'name': account.get('Name'),
                                                 'source_model': 'account.account',
                                                 'description': e,
                                                 'date': fields.Datetime.now(),
                                                 'option': 'import',
                                                 'xero_account_id': xero_account_id,
                                                 })
                            continue
                    elif not account_rec and not available_acc_ids and import_option in ['create', 'both']:
                        try:
                            self.create({'name': account.get('Name'),
                                         'code': account.get('Code'),
                                         'acc_id': account.get('AccountID'),
                                         'company_ids': [(4, company)],
                                         'tax_ids': [(6, 0, [tax_id[0].id])] if tax_id else [],
                                         'account_type': user_type or False,
                                         'use_as_inventory_in_xero': True if account_type == 'inventory' else False,
                                         'xero_description': account.get('Description'),
                                         'account_type_xero': account_type})
                            self._cr.commit()
                        except Exception as e:
                            mismatch_log.create({'name': account.get('Name'),
                                                 'source_model': 'account.account',
                                                 'description': e,
                                                 'date': fields.Datetime.now(),
                                                 'option': 'import',
                                                 'xero_account_id': xero_account_id,
                                                 })
                            continue

    def export_account(self, account_list, xero, last_account_export_date, xero_account_id, company=False, disable_export=False):
        """
            Map: AccountID(Odoo) with AccountID(Xero)

            Create a account in Xero if account is not available with
            AccountID.

            Note: If any record is already available with same name or code in Xero which we
            going to export from Odoo then it will skip that particular record.

            Constraint(Xero): The name and code of the account must be unique
        """
        if not disable_export:
            mismatch_log = self.env['mismatch.log']
            same_accounts = []
            final_account_list = []
            account_name_list = []
            account_code_list = []
            organisation_detail = xero.organisations.all()
            account_mapping = {
                                'asset_current': 'CURRENT',
                                'liability_payable': 'CURRLIAB',
                                'asset_receivable': 'CURRENT',
                                'asset_non_current': 'NONCURRENT',
                                'asset_prepayments': 'PREPAYMENT',
                                'asset_fixed': 'FIXED',
                                'liability_current': 'CURRLIAB',
                                'liability_non_current': 'TERMLIAB',
                                'equity': 'EQUITY',
                                'expense_direct_cost': 'REVENUE',
                                'income_other': 'OTHERINCOME',
                                'equity_unaffected': 'REVENUE',
                                'expense_depreciation': 'DEPRECIATN',
                                'expense': 'EXPENSE',
                                'income': 'OTHERINCOME',
                                }
            if last_account_export_date:
                account_ids = self.search([
                        ('company_ids', 'in', company),
                        '|', ('acc_id', '=', False),
                        '|', ('write_date', '>=', last_account_export_date),
                        ('create_date', '>=', last_account_export_date),
                    ])
            else:
                account_ids = self.search([('company_ids', 'in', company)])
            for account_id in account_ids:
                for account in account_list:
                    if account_id.name.lower() == account.get('Name').lower() and account_id.code == account.get('Code'):
                        if not account_id.acc_id:
                            account_id.write({'acc_id': account.get('AccountID')})
                    else:
                        account_name_list.append(account.get('Name'))
                    account_code_list.append(account.get('Code'))
                    if account_id.acc_id == account.get('AccountID'):
                        same_accounts.append(account_id.id)
                final_account_list.append(account_id.id)

            for save_account in self.browse(list(set(final_account_list).difference(set(same_accounts)))):
                if save_account.code not in account_code_list and save_account.name not in account_name_list:
                    if account_mapping.get(save_account.account_type):
                        if len(account_ids) >= 30:
                            time.sleep(1)
                        account = account_mapping[save_account.account_type]
                        if save_account.use_as_inventory_in_xero:
                            account = 'INVENTORY'
                        tax = None
                        if save_account.tax_ids:
                            tax = save_account.tax_ids[0]
                        data = {u'Code': save_account.code,
                                u'Name': save_account.name,
                                u'Description': save_account.xero_description,
                                u'Type': account}
                        if organisation_detail[0].get('CountryCode') in ['AU']:
                            data.update({u'TaxType': tax.xero_tax_type if tax else 'BASEXCLUDED'})
                        else:
                            data.update({u'TaxType': tax.xero_tax_type if tax else 'NONE'})
                        acc_rec = xero.accounts.put(data)
                        if acc_rec[0].get('ValidationErrors'):
                            description = acc_rec[0].get('ValidationErrors')[0].get('Message')
                            mismatch_log.create({'name': str(save_account.code) + ' ' + str(save_account.name),
                                                 'source_model': 'account.account',
                                                 'source_id': save_account.id,
                                                 'description': description,
                                                 'date': fields.Datetime.now(),
                                                 'option': 'export',
                                                 'xero_account_id': xero_account_id,
                                                 })
                            continue
                        save_account.acc_id = acc_rec[0]['AccountID']
                        self._cr.commit()
                else:
                    description = 'Account Name or Account Number is already exist in Xero.'
                    mismatch_log.create({'name': str(save_account.code) + ' ' + str(save_account.name),
                                         'source_model': 'account.account',
                                         'source_id': save_account.id,
                                         'description': description,
                                         'date': fields.Datetime.now(),
                                         'option': 'export',
                                         'xero_account_id': xero_account_id,
                                         })
                    continue

            for save_account in self.browse(same_accounts):
                if save_account.acc_id and save_account.code in account_code_list:
                    if account_mapping.get(save_account.account_type):
                        if len(account_ids) >= 30:
                            time.sleep(1)
                        account = account_mapping[save_account.account_type]
                        if save_account.use_as_inventory_in_xero:
                            account = 'INVENTORY'
                        tax = None
                        if save_account.tax_ids:
                            tax = save_account.tax_ids[0]
                        data = {
                                u'AccountID': save_account.acc_id,
                                u'Name': save_account.name,
                                u'Description': save_account.xero_description,
                                # u'Type': account,
                            }
                        if organisation_detail[0].get('CountryCode') in ['AU']:
                            data.update({u'TaxType': tax.xero_tax_type if tax else 'BASEXCLUDED'})
                        else:
                            data.update({u'TaxType': tax.xero_tax_type if tax else 'NONE'})
                        acc_rec = xero.accounts.save(data)
                        if acc_rec[0].get('ValidationErrors'):
                            description = acc_rec[0].get('ValidationErrors')[0].get('Message')
                            mismatch_log.create({'name': str(save_account.code) + ' ' + str(save_account.name),
                                                 'source_model': 'account.account',
                                                 'source_id': save_account.id,
                                                 'description': description,
                                                 'date': fields.Datetime.now(),
                                                 'option': 'export',
                                                 'xero_account_id': xero_account_id,
                                                 })
                            continue
                        # save_account.acc_id = acc_rec[0]['AccountID']
                        self._cr.commit()
