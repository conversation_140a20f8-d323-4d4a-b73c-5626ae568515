# -*- coding: utf-8 -*-
# Part of Odoo. See COPYRIGHT & LICENSE files for full copyright and licensing details.

import datetime
from odoo import api, fields, models, _
from odoo.exceptions import UserError
from bs4 import BeautifulSoup


class ProductTemplate(models.Model):
    _inherit = "product.template"

    taxes_id = fields.Many2many('account.tax', 'product_taxes_rel', 'prod_id', 'tax_id',
                                string='Customer Taxes', domain=[('type_tax_use', '!=', 'purchase')])
    supplier_taxes_id = fields.Many2many('account.tax', 'product_supplier_taxes_rel', 'prod_id', 'tax_id',
                                         string='Vendor Taxes', domain=[('type_tax_use', '!=', 'sale')])
    set_initial_stock = fields.Boolean(string='Set Initial Stock')


class ProductXeroCompany(models.Model):
    _name = "product.xero.company"
    _description = 'Product Xero'

    company_id = fields.Many2one('res.company', 'Company', required=True)
    xero_item_id = fields.Char('Xero ItemID')
    product_id = fields.Many2one('product.product', 'Product')


class ProductProduct(models.Model):
    _inherit = 'product.product'
    _description = 'Product'

    product_xero_company_ids = fields.One2many('product.xero.company', 'product_id', string="Xero Multi Company")

    _sql_constraints = [
        ('default_code_uniq', 'unique(default_code)', 'Internal Reference must be unique!'),
    ]

    @api.onchange('company_id')
    def _onchange_company(self):
        if self.company_id:
            xero_company = self.product_xero_company_ids.filtered(lambda l: l.company_id.id == self.company_id.id)
            if not xero_company:
                self.product_xero_company_ids = [(6, 0, {'company_id': self.company_id.id})]

    def import_product(self, product_list, xero, tracked_category, untracked_category, xero_account_id, company=False, import_option=None):
        """
        Map: ItemID(Odoo) with ItemID(Xero)

        Create a product in odoo if product is not available with
        ItemID and company.

        If product record is available then it will update that particular record.
        """
        account_pool = self.env['account.account']
        tax_pool = self.env['account.tax']
        mismatch_log = self.env['mismatch.log']
        for product in product_list:
            property_account_income_id = False
            property_account_expense_id = False
            sale_tax = []
            purchase_tax = []
            if product.get('SalesDetails').get('AccountCode'):
                sale_account = account_pool.search([('code', '=', product.get('SalesDetails').get('AccountCode')), ('company_ids', 'in', company)])
                property_account_income_id = sale_account and sale_account[0].id or False
            if product.get('PurchaseDetails').get('AccountCode'):
                purchase_account = account_pool.search([('code', '=', product.get('PurchaseDetails').get('AccountCode')), ('company_ids', 'in', company)])
                property_account_expense_id = purchase_account and purchase_account[0] and purchase_account[0].id or False
            if product.get('PurchaseDetails').get('COGSAccountCode'):
                purchase_account = account_pool.search([('code', '=', product.get('PurchaseDetails').get('COGSAccountCode')), ('company_ids', 'in', company)])
                property_account_expense_id = purchase_account and purchase_account[0] and purchase_account[0].id or False
            if product.get('SalesDetails').get('TaxType'):
                sale_tax = tax_pool.search([('xero_tax_type', '=', product.get('SalesDetails').get('TaxType')), ('company_id', '=', company)])
            if product.get('PurchaseDetails').get('TaxType'):
                purchase_tax = tax_pool.search([('xero_tax_type', '=', product.get('PurchaseDetails').get('TaxType')), ('company_id', '=', company)])
            product_code = self.search([('default_code', '=', product.get('Code'))], limit=1)
            xero_company = product_code.product_xero_company_ids.filtered(lambda l: l.company_id.id == company)
            if product_code and xero_company and not xero_company[0].xero_item_id:
                xero_company[0].xero_item_id = product.get('ItemID')
            elif product_code and not xero_company:
                product_code.product_xero_company_ids = [(0, 0, {'company_id': company,
                                                                 'xero_item_id': product.get('ItemID')
                                                                 })]

            product_rec = self.search([]).filtered(lambda product_id: product_id.product_xero_company_ids.filtered(lambda l: l.company_id.id == company and l.xero_item_id == product.get('ItemID')))

            is_storable = False
            product_type = 'consu'
            if product.get('IsTrackedAsInventory'):
                product_category = tracked_category
                is_storable = True
            else:
                product_category = untracked_category

            if not product_category:
                company_id = self.env['res.company'].browse(company)
                raise UserError(_('Please configure Tracked/Untracked Category for company %s')% company_id.name)

            product_vals = {
                'name': product.get('Name', product.get('Code')) or '',
                'description': product.get('Description') or '',
                'standard_price': product.get('PurchaseDetails').get('UnitPrice') or 0.0,
                'list_price': product.get('SalesDetails').get('UnitPrice') or 0.0,
                'default_code': product.get('Code') or '',
                # 'company_id': company,
                'type': product_type,
                'is_storable': is_storable,
                'property_account_income_id': property_account_income_id,
                'property_account_expense_id': property_account_expense_id,
                'taxes_id': [(6, 0, [sale_tax[0].id])] if sale_tax else [(6, 0, [])],
                'supplier_taxes_id': [(6, 0, [purchase_tax[0].id])] if purchase_tax else [(6, 0, [])],
            }
            try:
                if product_rec and import_option in ['update', 'both']:
                    product_rec[0].write(product_vals)
                elif not product_rec and import_option in ['create', 'both']:
                    product_vals.update({
                            'company_id': company,
                            'product_xero_company_ids': [(0, 0, {'company_id': company,
                                   'xero_item_id': product.get('ItemID')
                                })],
                        })
                    product_rec = self.create(product_vals)
                if product_rec:
                    # try:
                    product_rec[0].write(product_vals)
                    if product_category:
                        product_rec[0].write({'categ_id': product_category.id})
                    if product.get('IsTrackedAsInventory'):
                        onhand_qty_id = self.env['stock.change.product.qty'].with_context({'xero_opening_stock': True}).create({
                                'product_id': product_rec[0].id,
                                'product_tmpl_id': product_rec[0].product_tmpl_id.id,
                                'new_quantity': product.get('QuantityOnHand'),
                            })
                        onhand_qty_id.change_product_qty()
                    self._cr.commit()
                    # except Exception as e:
                        # raise UserError(_('%s') % e)
            except Exception as e:
                mismatch_log.create({'name': product.get('Name', '') + "[" + product.get('Code', '') + "]",
                                     'source_model': 'product.product',
                                     'source_id': product.get('Code', ''),
                                     'description': e,
                                     'date': fields.Datetime.now(),
                                     'option': 'import',
                                     'xero_account_id': xero_account_id})
                continue

    def export_product(self, product_list, xero, last_export_date, xero_account_id, company=False, item_ids=[]):
        """
        Map: ItemID(Odoo) with ItemID(Xero)

        Create a product in Xero if product is not available.

        If product record is available in xero then it will update that particular record.
        """
        # property_pool = self.env['ir.property'].sudo()
        account_pool = self.env['account.account']
        mismatch_log = self.env['mismatch.log']
        same_record = []
        final_product_list = []
        product_code_list = []
        if self._context.get('product_ids'):
            item_ids = self._context.get('product_ids')
        else:
            if len(item_ids) <= 0:
                if last_export_date:
                    item_ids = self.search(['|', ('company_id', '=', company), ('company_id', '=', False), '|', ('write_date', '>=', last_export_date), ('create_date', '>=', last_export_date)])
                else:
                    item_ids = self.search(['|', ('company_id', '=', company), ('company_id', '=', False)])
        for product_id in item_ids:
            xero_company = product_id.product_xero_company_ids.filtered(lambda l: l.company_id.id == company)
            if not xero_company:
                product_id.product_xero_company_ids = [(0, 0, {'company_id': company})]
            for name in product_list:
                xero_company_id = product_id.product_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                if product_id.default_code == name.get('Code'):
                    if xero_company_id:
                        xero_company_id[0].xero_item_id = name.get('ItemID')
                else:
                    product_code_list.append(name.get('Code'))

                if xero_company_id and xero_company_id[0].xero_item_id == name.get('ItemID'):
                    same_record.append(product_id.id)
            final_product_list.append(product_id.id)

        item_data = []
        data = []
        c = 0
        for record in self.browse(list(set(final_product_list).difference(set(same_record)))):
            description = False
            if record.description:
                soup = BeautifulSoup(record.description, 'lxml')
                description = soup.get_text()
            # res = 'product.template,' + str(record.product_tmpl_id.id)
            # purchase_account = property_pool.search([('company_id', '=', company), ('res_id', '=', res),
            #                                          ('name', '=', 'property_account_expense_id')], limit=1)
            # sale_account = property_pool.search([('company_id', '=', company), ('res_id', '=', res),
            #                                      ('name', '=', 'property_account_income_id')], limit=1)

            purchase_account = record.with_company(company).property_account_expense_id or record.categ_id.with_company(company).property_account_expense_categ_id
            sale_account = record.with_company(company).property_account_income_id or record.categ_id.with_company(company).property_account_income_categ_id
            stock_account = record.categ_id.with_company(company).property_stock_account_input_categ_id

            # categ_res = 'product.category,' + str(record.categ_id.id)
            # if not purchase_account:
            #     purchase_account = property_pool.search([('company_id', '=', company), '|' ,('res_id', '=', categ_res), ('res_id', '=', False),
            #                                              ('name', '=', 'property_account_expense_categ_id')], limit=1)
            # if not sale_account:
            #     sale_account = property_pool.search([('company_id', '=', company), '|', ('res_id', '=', categ_res), ('res_id', '=', False),
            #                                          ('name', '=', 'property_account_income_categ_id')], limit=1)

            # stock_account = property_pool.search([('company_id', '=', company), ('res_id', '=', categ_res),
            #                                       ('name', '=', 'property_stock_account_input_categ_id')], limit=1)

            purchase_account_code = purchase_account.code
            sale_account_code = sale_account.code
            stock_account_code = stock_account.code

            # purchase_account_code = account_pool.browse(int(purchase_account.value_reference.split(',')[1])).code if purchase_account else ''
            # sale_account_code = account_pool.browse(int(sale_account.value_reference.split(',')[1])).code if sale_account else ''
            # stock_account_code = account_pool.browse(int(stock_account.value_reference.split(',')[1])).code if stock_account and stock_account.value_reference else ''

            sale_tax_type = False
            if record.taxes_id:
                taxes = record.taxes_id.filtered(lambda l: l.company_id.id == company)
                sale_tax_type = taxes[0] if taxes else ''
            purchase_tax_type = False

            if record.supplier_taxes_id:
                supplier_taxes = record.supplier_taxes_id.filtered(lambda l: l.company_id.id == company)
                purchase_tax_type = supplier_taxes[0] if supplier_taxes else ''

            if record.lst_price > 0.0:
                sale_price = record.lst_price
            else:
                sale_price = record.list_price
            if record.default_code and record.default_code not in product_code_list:
                item_name = record.name and record.name[:49]
                code = record.default_code and record.default_code[:30]

                item = {
                    u'Code': code,
                    u'Name': item_name,
                    u'Description': description or u'',
                    u'PurchaseDescription': description or u'',
                    u'SalesDetails': {u'UnitPrice': sale_price,
                                      u'AccountCode': sale_account_code,
                                      u'TaxType': sale_tax_type.xero_tax_type if sale_tax_type and sale_tax_type.xero_tax_type else u''}
                }
                if record.categ_id.property_valuation == 'real_time':
                    item.update({
                            u'InventoryAssetAccountCode': stock_account_code,
                            u'PurchaseDetails': {
                                        u'UnitPrice': record.standard_price,
                                        u'COGSAccountCode': purchase_account_code,
                                        u'TaxType': purchase_tax_type.xero_tax_type if purchase_tax_type else u''
                                    },
                    })
                else:
                    item.update({
                            u'PurchaseDetails': {
                                        u'UnitPrice': record.standard_price,
                                        u'AccountCode': purchase_account_code,
                                        u'TaxType': purchase_tax_type.xero_tax_type if purchase_tax_type else u''
                                    },
                    })
                data.append(item)
                c += 1
                if c == 50:
                    item_data.append(data)
                    data = []
                    c = 0
            elif not record.default_code and record.name not in product_code_list:
                item_name = record.name and record.name[:49]
                name = record.name and record.name[:30]
                record.write({'default_code': name})

                item = {
                    u'Code': name,
                    u'Name': item_name,
                    u'Description': description or u'',
                    u'PurchaseDescription': description or u'',
                    u'SalesDetails': {u'UnitPrice': sale_price,
                                      u'AccountCode': sale_account_code,
                                      u'TaxType': sale_tax_type.xero_tax_type if sale_tax_type and sale_tax_type.xero_tax_type else u''}
                }

                if record.categ_id.property_valuation == 'real_time':
                    item.update({
                        u'InventoryAssetAccountCode': stock_account_code,
                        u'PurchaseDetails': {
                                    u'UnitPrice': record.standard_price,
                                    u'COGSAccountCode': purchase_account_code,
                                    u'TaxType': purchase_tax_type.xero_tax_type if purchase_tax_type and purchase_tax_type.xero_tax_type else u''
                                },
                    })
                else:
                    item.update({
                            u'PurchaseDetails': {
                                    u'UnitPrice': record.standard_price,
                                    u'AccountCode': purchase_account_code,
                                    u'TaxType': purchase_tax_type.xero_tax_type if purchase_tax_type and purchase_tax_type.xero_tax_type else u''
                                },
                        })
                data.append(item)
                c += 1
                if c == 50:
                    item_data.append(data)
                    data = []
                    c = 0
        if data:
            item_data.append(data)

        for data in item_data:
            item_rec = xero.items.put(data)
            for item in item_rec:
                if item.get('ValidationErrors'):
                    description = item.get('ValidationErrors')[0].get('Message')
                    mismatch_log.create({'name': item.get('Name'),
                                         'source_model': 'product.product',
                                         'description': description,
                                         'date': fields.Datetime.now(),
                                         'option': 'export',
                                         'xero_account_id': xero_account_id})
                    continue
                product_id = self.search(['|', ('default_code', '=', item.get('Code')), ('name', '=', item.get('Code'))], limit=1)
                xero_company = product_id.product_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                xero_company.xero_item_id = item.get('ItemID')
                self._cr.commit()
        item_data = []
        data = []
        c = 0
        for product in self.browse(same_record):
            description = False
            if product.description:
                soup = BeautifulSoup(product.description,'lxml')
                description = soup.get_text()
            code = product.default_code and product.default_code[:30]
            item_name = product.name and product.name[:49]

            # res = 'product.template,' + str(product.product_tmpl_id.id)
            # purchase_account = property_pool.search([('company_id', '=', company), ('res_id', '=', res),
            #                                          ('name', '=', 'property_account_expense_id')], limit=1)
            # sale_account = property_pool.search([('company_id', '=', company), ('res_id', '=', res),
            #                                      ('name', '=', 'property_account_income_id')], limit=1)

            # categ_res = 'product.category,' + str(product.categ_id.id)
            # if not purchase_account:
            #     purchase_account = property_pool.search([('company_id', '=', company), '|' ,('res_id', '=', categ_res), ('res_id', '=', False),
            #                                              ('name', '=', 'property_account_expense_categ_id')], limit=1)
            # if not sale_account:
            #     sale_account = property_pool.search([('company_id', '=', company), '|', ('res_id', '=', categ_res), ('res_id', '=', False),
            #                                          ('name', '=', 'property_account_income_categ_id')], limit=1)

            # stock_account = property_pool.search([('company_id', '=', company), ('res_id', '=', categ_res),
            #                                       ('name', '=', 'property_stock_account_input_categ_id')], limit=1)

            purchase_account = product.property_account_expense_id or product.categ_id.property_account_expense_categ_id
            sale_account = product.property_account_income_id or product.categ_id.property_account_income_categ_id
            stock_account = product.categ_id.property_stock_account_input_categ_id

            purchase_account_code = purchase_account.code
            sale_account_code = sale_account.code
            stock_account_code = stock_account.code

            # purchase_account_code = account_pool.browse(int(purchase_account.value_reference.split(',')[1])).code if purchase_account else ''
            # sale_account_code = account_pool.browse(int(sale_account.value_reference.split(',')[1])).code if sale_account else ''
            # stock_account_code = account_pool.browse(int(stock_account.value_reference.split(',')[1])).code if stock_account and stock_account.value_reference else ''

            sale_tax_type = False
            if product.taxes_id:
                taxes = product.taxes_id.filtered(lambda l: l.company_id.id == company)
                sale_tax_type = taxes[0] if taxes else False
            purchase_tax_type = False
            if product.supplier_taxes_id:
                supplier_taxes = product.supplier_taxes_id.filtered(lambda l: l.company_id.id == company)
                purchase_tax_type = supplier_taxes[0] if supplier_taxes else False

            if product.lst_price > 0.0:
                sale_price = product.lst_price
            else:
                sale_price = product.list_price
            xero_company = product.product_xero_company_ids.filtered(lambda l: l.company_id.id == company)

            items = {
                u'Code': code or name,
                u'Name': item_name,
                u'Description': description or u'',
                u'PurchaseDescription': description or u'',
                u'SalesDetails': {u'UnitPrice': sale_price,
                                  u'AccountCode': sale_account_code,
                                  u'TaxType': sale_tax_type.xero_tax_type if sale_tax_type and sale_tax_type.xero_tax_type else u''},
                u'ItemID': xero_company[0].xero_item_id if xero_company else ''
            }

            if product.categ_id.property_valuation == 'real_time':
                items.update({
                    u'InventoryAssetAccountCode': stock_account_code,
                    u'PurchaseDetails': {
                                u'UnitPrice': product.standard_price,
                                u'COGSAccountCode': purchase_account_code,
                                u'TaxType': purchase_tax_type.xero_tax_type if purchase_tax_type and purchase_tax_type.xero_tax_type else u''
                            },
                })
            else:
                items.update({
                    u'PurchaseDetails': {
                                u'UnitPrice': product.standard_price,
                                u'AccountCode': purchase_account_code,
                                u'TaxType': purchase_tax_type.xero_tax_type if purchase_tax_type and purchase_tax_type.xero_tax_type else u''
                            },
                })
            data.append(items)
            c += 1
            if c == 50:
                item_data.append(data)
                data = []
                c = 0
        if data:
            item_data.append(data)
        for data in item_data:
            item_rec = xero.items.save(data)
            for item in item_rec:
                if item.get('ValidationErrors'):
                    description = item.get('ValidationErrors')[0].get('Message')
                    mismatch_log.create({'name': item.get('Name'),
                                         'source_model': 'product.product',
                                         'description': description,
                                         'date': fields.Datetime.now(),
                                         'option': 'export',
                                         'xero_account_id': xero_account_id})
                    continue
            self._cr.commit()

    def action_export_product(self):
        context = self._context
        companies = self.env.user.company_ids
        for company_id in companies:
            product_data = self.filtered(lambda product: product.company_id == company_id or not product.company_id)
            context.update({'product_ids': product_data})
            xero_account = self.env['xero.account'].search([('company_id', '=', company_id.id)], limit=1)
            if xero_account:
                xero_account.with_context(context).export_product()


class StockMoveLine(models.Model):
    _inherit = "stock.move.line"

    xero_invoice_number = fields.Char('Xero Invoice Number', readonly=True, copy=False)
    xero_invoice_id = fields.Char('Xero Invoice ID', readonly=True, copy=False)
    xero_opening_stock = fields.Boolean(string="Xero Opening Stock")

    @api.model_create_multi
    def create(self, vals_list):
        xero_opening_stock = self._context.get('xero_opening_stock') or False
        for vals in vals_list:
            if xero_opening_stock:
                vals.update({'xero_opening_stock': True})
        return super(StockMoveLine, self).create(vals_list)

    def create_inventory_adjustments(self, xero, xero_account_id, company=False, adjustment_account=False):
        move_lines = self.search([('product_id.categ_id.property_valuation', '=', 'real_time'),
                                    ('state', '=', 'done'),
                                    ('xero_invoice_id', '=', False),
                                    ('xero_invoice_number', '=', False),
                                    ('move_id.company_id', '=', company),
                                    ('xero_opening_stock', '=', False),
                                      '|',
                                    ('location_id.usage', 'in', ['inventory', 'production']),
                                    ('location_dest_id.usage', 'in', ['inventory','production'])])
        mismatch_log = self.env['mismatch.log']
        for move in move_lines:
            product_code = move.product_id.default_code[:30] if move.product_id.default_code else move.product_id.name[:30]
            if move.location_id.usage in ['inventory', 'production']:
                description = (str(move.move_id.reference) + '-' + 'Inventory Adjustment') if move.location_id.usage == 'inventory' else (str(move.move_id.reference) + '-' + 'Inventory Adjustment (Manufacturing)')
                invoice_data = {u'Type': u'ACCPAY',
                                u'Contact': {u'Name': u'Stock Journal (Odoo)'},
                                u'InvoiceNumber': description,
                                u'Date': move.date or fields.Date.today(),
                                u'DueDate': move.date or fields.Date.today(),
                                u'LineAmountTypes': u'NoTax',
                                u'Status': u'AUTHORISED',
                                u'LineItems': [{u'ItemCode': product_code,
                                                u'Description': move.product_id.description or move.product_id.name,
                                                u'Quantity': move.quantity,
                                                u'UnitAmount': move.product_id.standard_price,
                                                u'AccountCode': (move.product_id.categ_id.property_stock_account_input_categ_id and move.product_id.categ_id.property_stock_account_input_categ_id.code) or adjustment_account.code,
                                                },
                                                {
                                                u'Description': 'Stock Movement',
                                                u'Quantity': move.quantity,
                                                u'UnitAmount': -(move.product_id.standard_price),
                                                u'AccountCode': (adjustment_account and adjustment_account.code),
                                                }]}
                inv_rec = xero.invoices.put(invoice_data)
                if inv_rec[0].get('ValidationErrors'):
                    error_description = inv_rec[0].get('ValidationErrors')[0].get('Message')
                    mismatch_log.create({'name': description,
                                         'source_model': 'stock.move.line',
                                         'description': error_description,
                                         'date': fields.Datetime.now(),
                                         'option': 'export',
                                         'xero_account_id': xero_account_id})
                    continue
                move.write({'xero_invoice_id': inv_rec[0]['InvoiceID'], 'xero_invoice_number': inv_rec[0]['InvoiceNumber']})
                self._cr.commit()
            elif move.location_dest_id.usage in ['inventory', 'production']:
                description = (str(move.move_id.reference) + '-' + 'Inventory Adjustment') if move.location_dest_id.usage == 'inventory' else (str(move.move_id.reference) + '-' + 'Inventory Adjustment (Manufacturing)')
                creditnote_data = {u'Type': u'ACCPAYCREDIT',
                                   u'Contact': {u'Name': u'Stock Journal (Odoo)'},
                                   u'CreditNoteNumber': description,
                                   u'Date': move.date or fields.Date.today(),
                                   u'DueDate': move.date or fields.Date.today(),
                                   u'LineAmountTypes': u'NoTax',
                                   u'Status': u'AUTHORISED',
                                   u'LineItems': [{u'ItemCode': product_code,
                                                   u'Description': move.product_id.description or move.product_id.name,
                                                   u'Quantity': move.quantity,
                                                   u'UnitAmount': move.product_id.standard_price,
                                                   u'AccountCode': (move.product_id.categ_id.property_stock_account_input_categ_id and move.product_id.categ_id.property_stock_account_input_categ_id.code) or adjustment_account.code,
                                                   },
                                                   {
                                                   u'Description': 'Stock Movement',
                                                   u'Quantity': move.quantity,
                                                   u'UnitAmount': -(move.product_id.standard_price),
                                                   u'AccountCode': (adjustment_account and adjustment_account.code),
                                                   }]}
                creditnote_rec = xero.creditnotes.put(creditnote_data)
                if creditnote_rec[0].get('ValidationErrors'):
                    error_description = creditnote_rec[0].get('ValidationErrors')[0].get('Message')
                    mismatch_log.create({'name': description,
                                         'source_model': 'stock.move.line',
                                         'description': error_description,
                                         'date': fields.Datetime.now(),
                                         'option': 'export',
                                         'xero_account_id': xero_account_id})
                    continue
                move.write({'xero_invoice_id': creditnote_rec[0]['CreditNoteID'], 'xero_invoice_number': creditnote_rec[0]['CreditNoteID']})
                self._cr.commit()
        return True
