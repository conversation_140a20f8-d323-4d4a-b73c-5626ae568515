# -*- coding: utf-8 -*-
# Part of Odoo. See COPYRIGHT & LICENSE files for full copyright and licensing details.

from odoo import api, fields, models, _
from odoo.exceptions import UserError
from datetime import datetime


class SaleOrder(models.Model):
    _inherit = "sale.order"

    xero_quotes_id = fields.Char(string="Xero Quotes Id", readonly=True, copy=False)
    xero_quotes_number = fields.Char(string="Xero Quotes Number", readonly=True, copy=False)
    line_amount_type = fields.Selection([('EXCLUSIVE', 'EXCLUSIVE'),
                                        ('INCLUSIVE', 'INCLUSIVE'),
                                        ('NOTAX', 'NOTAX'),
                                        ], 'Line Amount Type', default='EXCLUSIVE',required=True, copy=False)

    def write(self, vals):
        for rec in self:
            if rec.state in ['sale','done'] and vals.get('order_line'):
                raise UserError(_("You Can not add or update orderline after confirming the sale order."))
            if vals.get('partner_id') and rec.picking_ids:
                rec.picking_ids.write({'partner_id': vals['partner_id']})
        return super(SaleOrder, self).write(vals)

    @api.depends_context('lang')
    @api.depends('order_line.tax_id', 'order_line.price_unit', 'amount_total', 'amount_untaxed', 'currency_id')
    def _compute_tax_totals(self):
        for order in self:
            context =  dict(self.env.context)
            if order.line_amount_type == 'EXCLUSIVE':
                context.update({'line_amount_type': 'Exclusive'})
            elif order.line_amount_type == 'INCLUSIVE':
                context.update({'line_amount_type': 'Inclusive'})
            self.env.context = context
        return super(SaleOrder, self)._compute_tax_totals()

    def tax_calculation(self):
        for rec in self:
            if rec.line_amount_type == 'NOTAX':
                rec.order_line.with_context({'line_amount_type': rec.line_amount_type}).write({'tax_id': [(6, 0, [])]})
            else:
                if rec.line_amount_type == 'EXCLUSIVE':
                    # rec.order_line.with_context({'line_amount_type': 'Exclusive'})._compute_price_unit()
                    rec.order_line.with_context({'line_amount_type': 'Exclusive'})._compute_discount()
                    rec.order_line.with_context({'line_amount_type': 'Exclusive'})._compute_amount()
                    rec.with_context({'line_amount_type': 'Exclusive'})._compute_tax_totals()
                elif rec.line_amount_type == 'INCLUSIVE':
                    # rec.order_line.with_context({'line_amount_type': 'Inclusive'})._compute_price_unit()
                    rec.order_line.with_context({'line_amount_type': 'Inclusive'})._compute_discount()
                    rec.order_line.with_context({'line_amount_type': 'Inclusive'})._compute_amount()
                    rec.with_context({'line_amount_type': 'Inclusive'})._compute_tax_totals()

    def import_quotes(self, order_list, xero, xero_account_id,  company=False, without_product=False, import_option=None):
        products = self.env['product.product']
        orders = self.env['sale.order']
        order_line = self.env['sale.order.line']
        partners = self.env['res.partner']
        accounts = self.env['account.account']
        currencies = self.env['res.currency']
        taxes = self.env['account.tax']
        mismatch_log = self.env['mismatch.log']
        xero_account = self.env['xero.account'].browse(xero_account_id)
        for order in order_list:
            try:
                if order.get('Total') != 0.0:
                    order_res = self.search([('xero_quotes_id', '=', order.get('QuoteID'))])
                    if not order_res and import_option in ['create', 'both']:
                        flag = 0
                        if order and order.get('Status') not in ['DELETED','DECLINED']:
                            partner = partners.search(['|', ('company_id', '=', company), ('company_id', '=', False)]).filtered(lambda partner: partner.contact_xero_company_ids.filtered(lambda contact: contact.xero_contact_id == order.get('Contact').get('ContactID') and contact.company_id.id == company))
                            if not partner:
                                xero_account.import_contact_overwrite() if xero_account.contact_overwrite else xero_account.import_contact()
                                partner = partners.search(['|', ('company_id', '=', company), ('company_id', '=', False)]).filtered(lambda partner: partner.contact_xero_company_ids.filtered(lambda contact: contact.xero_contact_id == order.get('Contact').get('ContactID') and contact.company_id.id == company))

                            order_lines = []
                            sub_tax = order.get('SubTotal') + order.get('TotalTax')
                            if float("%.2f"%sub_tax) != order.get('Total'):
                                flag = 1

                            for lines in order.get('LineItems'):
                                acc = accounts.search([('code', '=', lines.get('AccountCode')),('company_ids', 'in', company)])

                                if flag == 1:
                                    line_amount = lines.get('LineAmount') - lines.get('TaxAmount')
                                    unit_amount = line_amount / lines.get('Quantity')
                                else:
                                    line_amount = lines.get('LineAmount')
                                    unit_amount = lines.get('UnitAmount')

                                tax_id = False
                                if lines.get('TaxType'):
                                    tax_id = taxes.search([('xero_tax_type', '=', lines.get('TaxType')),('company_id', '=', company)], limit=1)

                                if lines.get('ItemCode'):
                                    product = products.search([('default_code', '=', lines.get('ItemCode'))])
                                    if not product:
                                        raise UserError(_("Please First Import Product."))
                                    for product_id in product:
                                        order_line_val = {'name': lines.get('Description') or product_id.description or 'Didn\'t specify',
                                                        'price_unit': unit_amount or 0.0,
                                                        'company_id': company,
                                                        'product_uom_qty': lines.get('Quantity') or 1,
                                                        'product_id': product_id and product_id.id or False,
                                                        'tax_id': [(6, 0, tax_id.ids)] if tax_id else [],
                                                        'discount': lines.get('DiscountRate') or 0.0,
                                                        'xero_order_line_id': lines.get('LineItemID'),
                                                        }
                                        order_lines.append((0, 0, order_line_val))
                                else:
                                    vals = {'name': lines.get('Description') or 'Didn\'t specify',
                                            'price_unit': unit_amount or 0.0,
                                            'company_id': company,
                                            'tax_id': [(6, 0, tax_id.ids)] if tax_id else [],
                                            'product_uom_qty': lines.get('Quantity') or 1,
                                            'discount': lines.get('DiscountRate') or 0.0,
                                            'xero_order_line_id': lines.get('LineItemID'),
                                            }
                                    order_lines.append((0, 0, vals))

                            currency_id = currencies.search([('name', '=', order.get('CurrencyCode'))])
                            order_default = {'partner_id': partner and partner.id or False,
                                            'currency_id': currency_id and currency_id[0].id or False,
                                            'company_id': company,
                                            'date_order': order.get('DateString'),
                                            'validity_date': order.get('ExpiryDateString'),
                                            'xero_quotes_id': order.get('QuoteID'),
                                            'xero_quotes_number': order.get('QuoteNumber'),
                                            'note': order.get('Terms'),
                                            'amount_tax': order.get('TotalTax'),
                                            'line_amount_type':order.get('LineAmountTypes'),
                                            }
                            order_type = self.env.context.copy()
                            if order_lines:
                                order_default.update({'order_line': order_lines})

                            ord_id = orders.with_context(order_type).create(order_default)
                            ord_id.tax_calculation()
                            self._cr.commit()
                            if ord_id and order.get('Status') in 'SENT' and ord_id.order_line:
                                ord_id.with_context({'CurrencyRate': order.get('CurrencyRate')}).action_quotation_send()
                                ord_id.state = 'sent'
                            if ord_id and order.get('Status') in 'ACCEPTED' and ord_id.order_line:
                                ord_id.with_context({'CurrencyRate': order.get('CurrencyRate')}).action_quotation_send()
                                ord_id.with_context({'CurrencyRate': order.get('CurrencyRate')}).action_confirm()
                            if ord_id and order.get('Status') in 'INVOICED' and ord_id.order_line:
                                ord_id.action_quotation_send()
                                ord_id.action_confirm()
                                pickings = ord_id.picking_ids
                                for picking in pickings:
                                    for pick in picking.move_ids_without_package:
                                        pick.write({'quantity': pick.product_uom_qty})
                                    if picking.state in ['waiting', 'confirmed', 'assigned']:
                                        picking.button_validate()
                                invoice_id = self.env['account.move'].search(['|',('ref', '=', ord_id.xero_quotes_number),('ref','=', ord_id.name)],limit=1)
                                if not invoice_id:
                                    xero_account.import_invoice()
                                invoice_id = self.env['account.move'].search(['|',('ref', '=', ord_id.xero_quotes_number),('ref','=', ord_id.name)],limit=1)
                                if invoice_id:
                                    for inv_line in invoice_id.invoice_line_ids:
                                        for sale_line in ord_id.order_line.filtered(lambda l: l.product_id):
                                            if sale_line.product_id == inv_line.product_id and sale_line.product_uom_qty == inv_line.quantity and inv_line not in sale_line.invoice_lines:
                                                sale_line.invoice_lines = [(4, inv_line.id)]
                                                inv_line.sale_line_ids = [(4, sale_line.id)]
                                    ord_id.invoice_ids = [(4, invoice_id.id)]
                                    ord_id.invoice_count = len(ord_id.invoice_ids)
                                    ord_id.invoice_status = 'invoiced'
                                self._cr.commit()

                    if order_res and import_option in ['update', 'both']:
                        if order_res[0].state != 'cancel':
                            available_lines = []
                            flag = 0
                            if order:
                                sub_tax = order.get('SubTotal') + order.get('TotalTax')
                                if float("%.2f"%sub_tax) != order.get('Total'):
                                    flag = 1

                                line_items = []
                                for lines in order.get('LineItems'):
                                    available_lines.append(lines.get('LineItemID'))
                                    line = order_line.search([('xero_order_line_id', '=', lines.get('LineItemID')),('company_id', '=', company)])
                                    if flag == 1:
                                        line_amount = lines.get('LineAmount') - lines.get('TaxAmount')
                                        unit_amount = line_amount / lines.get('Quantity')
                                    else:
                                        line_amount = lines.get('LineAmount')
                                        unit_amount = lines.get('UnitAmount')

                                    tax_id = False
                                    if lines.get('TaxType'):
                                        tax_id = taxes.search([('xero_tax_type', '=', lines.get('TaxType')),('company_id', '=', company)], limit=1)

                                    acc = accounts.search([('code', '=', lines.get('AccountCode')),('company_ids', 'in', company)])

                                    if line:
                                        if lines.get('ItemCode'):
                                            product = products.search([('default_code', '=', lines.get('ItemCode'))])
                                            if not product:
                                                raise UserError(_("Please First Import Product."))
                                            for product_id in product:
                                                order_line_val = {'name': lines.get('Description') or product_id.description or 'Didn\'t specify',
                                                                'price_unit': unit_amount or 0.0,
                                                                'product_uom_qty': lines.get('Quantity') or 1,
                                                                'company_id': company,
                                                                'product_id': product_id and product_id.id or False,
                                                                'tax_id': [(6, 0, tax_id.ids)] if tax_id else [],
                                                                'discount': lines.get('DiscountRate') or 0.0,
                                                                'xero_order_line_id': lines.get('LineItemID'),
                                                                'order_id': order_res[0].id,
                                                                'price_subtotal': lines.get('Line')
                                                                }
                                                line.write(order_line_val)

                                        else:
                                            vals = {'name': lines.get('Description') or 'Didn\'t specify',
                                                    'price_unit': unit_amount or 0.0,
                                                    'company_id': company,
                                                    'tax_id': [(6, 0, tax_id.ids)] if tax_id else [],
                                                    'product_uom_qty': lines.get('Quantity') or 1,
                                                    'discount': lines.get('DiscountRate') or 0.0,
                                                    'xero_order_line_id': lines.get('LineItemID'),
                                                    'order_id': order_res[0].id,
                                                    }
                                            line.write(vals)

                                    else:
                                        if lines.get('ItemCode'):
                                            product = products.search([('default_code', '=', lines.get('ItemCode'))])
                                            if not product:
                                                raise UserError(_("Please First Import Product."))
                                            for product_id in product:
                                                order_line_val = {'name': lines.get('Description') or product_id.description or 'Didn\'t specify',
                                                                'price_unit': unit_amount or 0.0,
                                                                'product_uom_qty': lines.get('Quantity') or 1,
                                                                'company_id': company,
                                                                'product_id': product_id and product_id.id or False,
                                                                'tax_id': [(6, 0, [tax_id.id])] if tax_id else [],
                                                                'discount': lines.get('DiscountRate') or 0.0,
                                                                'xero_order_line_id': lines.get('LineItemID'),
                                                                'order_id': order_res[0].id,
                                                                }
                                                line_items.append(order_line_val)
                                        else:
                                            vals = {'name': lines.get('Description') or 'Didn\'t specify',
                                                    'price_unit': unit_amount or 0.0,
                                                    'company_id': company,
                                                    'tax_id': [(6, 0, [tax_id.id])] if tax_id else [],
                                                    'product_uom_qty': lines.get('Quantity') or 1,
                                                    'discount': lines.get('DiscountRate') or 0.0,
                                                    'xero_order_line_id': lines.get('LineItemID'),
                                                    'order_id': order_res[0].id,
                                                    }
                                            line_items.append(vals)

                                if line_items:
                                    line_ids = order_line.create(line_items)

                                partner = partners.search(['|', ('company_id', '=', company), ('company_id', '=', False)]).filtered(lambda partner: partner.contact_xero_company_ids.filtered(lambda contact: contact.xero_contact_id == order.get('Contact').get('ContactID') and contact.company_id.id == company))
                                if not partner:
                                    xero_account.import_contact_overwrite() if xero_account.contact_overwrite else xero_account.import_contact()
                                    partner = partners.search(['|', ('company_id', '=', company), ('company_id', '=', False)]).filtered(lambda partner: partner.contact_xero_company_ids.filtered(lambda contact: contact.xero_contact_id == order.get('Contact').get('ContactID') and contact.company_id.id == company))

                                currency_id = currencies.search([('name', '=', order.get('CurrencyCode'))], limit=1)

                                vals = {
                                        'partner_id': partner and partner[0].id or False,
                                        'currency_id': currency_id and currency_id.id or False,
                                        'date_order': order.get('DateString'),
                                        'validity_date': order.get('ExpiryDateString'),
                                        'xero_quotes_id': order.get('QuoteID'),
                                        'xero_quotes_number': order.get('QuoteNumber'),
                                        'amount_tax': order.get('TotalTax'),
                                        'company_id': company,
                                        'note': order.get('Terms'),
                                        'line_amount_type': order.get('LineAmountTypes'),
                                }
                                order_res[0].write(vals)
                                # order_res[0].tax_calculation()
                                if order.get('Status') == 'SENT':
                                    if order_res[0].state == 'draft':
                                        order_res[0].action_quotation_send()
                                        order_res[0].state = 'sent'
                                    if order_res[0].state == 'sale':
                                        pickings = order_res[0].picking_ids
                                        for pick in pickings:
                                            pick.action_cancel()
                                        order_res[0].state = 'sent'
                                if order.get('Status') in ['DELETED','DECLINED'] and order_res[0].state in ['draft','sent','sale']:
                                    order_res[0].action_cancel()
                                if order.get('Status') == 'ACCEPTED' and order_res[0].state in ['draft','sent']:
                                    order_res[0].action_quotation_send()
                                    order_res[0].action_confirm()
                                if order.get('Status') == 'INVOICED' and order_res[0].state != 'cancel' and order_res[0].state in ['draft','sent','sale','done'] and order_res[0].invoice_status != 'invoiced':
                                    order_res[0].action_quotation_send()
                                    order_res[0].action_confirm()
                                    pickings = order_res[0].picking_ids
                                    for picking in pickings:
                                        for pick in picking.move_ids_without_package:
                                           pick.write({'quantity': pick.product_uom_qty})
                                        if picking.state in ['waiting', 'confirmed', 'assigned']:
                                            picking.button_validate()
                                    invoice_id = self.env['account.move'].search(['|',('ref', '=', order_res[0].xero_quotes_number),('ref','=', order_res[0].name)],limit=1)
                                    if not invoice_id:
                                        xero_account.import_invoice()
                                    invoice_id = self.env['account.move'].search(['|',('ref', '=', order_res[0].xero_quotes_number),('ref','=', order_res[0].name)],limit=1)
                                    if invoice_id:
                                        for inv_line in invoice_id.invoice_line_ids:
                                            for sale_line in order_res[0].order_line:
                                                if sale_line.product_id == inv_line.product_id and sale_line.product_uom_qty == inv_line.quantity and inv_line not in sale_line.invoice_lines:
                                                    sale_line.invoice_lines = [(4, inv_line.id)]
                                                    inv_line.sale_line_ids = [(4, sale_line.id)]
                                        order_res[0].invoice_ids = [(4, invoice_id.id)]
                                        order_res[0].invoice_count = len(order_res[0].invoice_ids)
                                        order_res[0].invoice_status = 'invoiced'
                                for order_lines in order_res[0].order_line:
                                    if order_lines.xero_order_line_id not in available_lines:
                                        order_lines.unlink()
                                    self._cr.commit()
            except Exception as e:
                # raise UserError(_('%s') % e)
                mismatch_log.create({'name': order.get('QuoteNumber'),
                                     'source_model': 'sale.order',
                                     'source_id': order.get('QuoteNumber'),
                                     'description': e,
                                     'date': fields.Datetime.now(),
                                     'option': 'import',
                                     'xero_account_id': xero_account_id})
                continue

    def export_quotes(self, order_list, xero, last_export_date, xero_account_id, company=False, disable_export=False):
        mismatch_log = self.env['mismatch.log']
        xero_account = self.env['xero.account'].search([('company_id', '=', company)], limit=1)
        if self._context.get('order_ids'):
            order_ids = self._context.get('order_ids')
        else:
            if last_export_date:
                order_ids = self.search([('company_id', '=', company),
                                           ('state', '!=', 'cancel'),
                                           '|', ('write_date', '>=', last_export_date),
                                           ('create_date', '>=', last_export_date)])
            else:
                order_ids = self.search([('company_id', '=', company),
                                           ('state', '!=', 'cancel')])
        update_order_data = []
        update_order_data_list = []
        create_order_data = []
        create_order_data_list = []
        count = 0
        c = 0
        request = 0
        for order in order_ids:
            if not order.partner_id:
                description = 'Customer must be set for export (Odoo to Xero)'
                mismatch_log.create({'name': order.name,
                                     'source_model': 'sale.order',
                                     'source_id': order.id,
                                     'description': description,
                                     'date': datetime.datetime.now(),
                                     'option': 'export',
                                     'xero_account_id': xero_account_id
                                     })
                continue
            if not order.xero_quotes_id and order.state != 'cancel':
                status = u'DRAFT'
                line_amount_type = order.line_amount_type
                partner_details = {}
                if order.partner_id.parent_id:
                    contact = order.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                    if not contact.xero_contact_id:
                        xero_account.export_contact_overwrite() if xero_account.contact_overwrite else xero_account.export_contact()
                        contact = order.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                    partner_details = {u'ContactID': contact.xero_contact_id}
                else:
                    contact = order.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                    if not contact.xero_contact_id:
                        xero_account.export_contact_overwrite() if xero_account.contact_overwrite else xero_account.export_contact()
                        contact = order.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                    partner_details = {u'ContactID': contact.xero_contact_id}

                final_order_data = {
                                    u'Contact': partner_details,
                                    u'Reference': order.name or u'',
                                    u'Date': order.date_order.date() or fields.Date.today(),
                                    u'ExpiryDate': order.validity_date or fields.Date.today(),
                                    u'Status': status,
                                    u'TotalTax': order.amount_tax if order.amount_tax else 0.0,
                                    u'LineAmountTypes': line_amount_type,
                                    u'CurrencyCode': order.currency_id.name,
                                    }

                if order.order_line:
                    line_items = []
                    for line in order.order_line.filtered(lambda l: l.product_id):
                        if line.product_id:
                            product = line.product_id.product_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            if product and not product[0].xero_item_id:
                                xero_account.export_product()
                        if line.tax_id:
                            tax_type = line.tax_id[0]
                        else:
                            if order:
                                tax_type = self.env['account.tax'].search([('type_tax_use', '=', 'sale'), ('amount', '=', 0), ('company_id', '=', company)], limit=1)
                                if not tax_type:
                                    company_id = self.env['res.company'].browse(company)
                                    raise UserError(_('Please create account tax of type \'SALE\' and amount = 0.0 for Company: %s')% (company_id.name))
                        if not tax_type.xero_tax_type:
                            tax_rates = xero.taxrates.all()
                            self.env['account.tax'].export_tax(tax_rates, xero, xero_account_id, company=company, disable_export=disable_export)

                        account_code = ''
                        if line.product_id.property_account_income_id:
                            account_code = line.product_id.property_account_income_id.code
                        elif line.product_id.categ_id.property_account_income_categ_id:
                            account_code = line.product_id.categ_id.property_account_income_categ_id.code
                        else:
                            description = _("Need to configure products '%s - %s' sales account.", line.product_id.default_code,line.product_id.name)
                            mismatch_log.create({'name': order.name,
                                                 'source_model': 'sale.order',
                                                 'description': description,
                                                 'date': fields.Datetime.now(),
                                                 'option': 'export',
                                                 'xero_account_id': xero_account_id})
                            continue

                        vals = {
                                u'Description': line.name or line.product_id.name,
                                u'AccountCode': account_code,
                                u'UnitAmount': line.price_unit,
                                u'TaxType': u'' if line.order_id.line_amount_type == 'NOTAX' else tax_type and tax_type.xero_tax_type or u'',
                                u'DiscountRate': line.discount,
                                u'Quantity': line.product_uom_qty,
                            }

                        if line.product_id:
                            vals.update({u'ItemCode': line.product_id.default_code})
                        line_items.append(vals)
                    if line_items:
                        final_order_data.update({u'LineItems': line_items})

                create_order_data.append(final_order_data)
                c += 1
                if c == 50:
                    create_order_data_list.append(create_order_data)
                    create_order_data = []
                    c = 0

            elif order.xero_quotes_id:
                for xero_ord in order_list:
                    if xero_ord.get('QuoteID') == order.xero_quotes_id:
                        order_currency_rate = 0.0
                        status = False
                        if order.state == 'sent' and xero_ord.get('Status') in ['DRAFT', 'SENT']:
                            status = u'SENT'
                        elif order.state in ['sale', 'done']:
                            if xero_ord.get('Status') == 'DRAFT':
                                status = u'SENT'
                            if xero_ord.get('Status') == 'SENT':
                                status = u'ACCEPTED'
                            if xero_ord.get('Status') == 'ACCEPTED' and order.invoice_ids:
                                status = u'INVOICED'
                        elif order.state == 'cancel':
                            status = u'DELETED'

                        partner_details = {}
                        if order.partner_id.parent_id:
                            contact = order.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            if not contact.xero_contact_id:
                                xero_account.export_contact_overwrite() if xero_account.contact_overwrite else xero_account.export_contact()
                                contact = order.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            partner_details = {u'ContactID': contact.xero_contact_id}
                        else:
                            contact = order.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            if not contact.xero_contact_id:
                                xero_account.export_contact_overwrite() if xero_account.contact_overwrite else xero_account.export_contact()
                                contact = order.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            partner_details = {u'ContactID': contact.xero_contact_id}
                        line_amount_type = order.line_amount_type
                        order_data = {
                                        u'QuoteID': order.xero_quotes_id or u'',
                                        u'QuoteNumber': order.xero_quotes_number or u'',
                                        u'LineAmountTypes': line_amount_type,
                                        u'Contact': partner_details,
                                        u'Reference': order.name or u'',
                                        u'Date': order.date_order or fields.Date.today(),
                                        u'ExpiryDate': order.validity_date or fields.Date.today(),
                                        u'CurrencyCode': order.currency_id.name,
                                        u'TotalTax': order.amount_tax if order.amount_tax else 0.0,
                                    }
                        if status:
                            order_data.update({u'Status': status})
                        line_items = []
                        for ord_line in order.order_line.filtered(lambda l: l.product_id):
                            if ord_line.product_id:
                                product = ord_line.product_id.product_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                                if product and not product[0].xero_item_id:
                                    xero_account.export_product()
                            if ord_line.tax_id:
                                tax_type = ord_line.tax_id[0]
                            else:
                                if order:
                                    tax_type = self.env['account.tax'].search([('type_tax_use', '=', 'sale'), ('amount', '=', 0), ('company_id', '=', company)], limit=1)
                                    if not tax_type:
                                        company_id = self.env['res.company'].browse(company)
                                        raise UserError(_('Please create account tax of type \'SALE\' and amount = 0.0 for Company: %s')% (company_id.name))

                            if not tax_type.xero_tax_type:
                                tax_rates = xero.taxrates.all()
                                self.env['account.tax'].export_tax(tax_rates, xero, xero_account_id, company=company, disable_export=disable_export)

                            account_code = ''
                            if ord_line.product_id.property_account_income_id:
                                account_code = ord_line.product_id.property_account_income_id.code
                            elif ord_line.product_id.categ_id.property_account_income_categ_id:
                                account_code = ord_line.product_id.categ_id.property_account_income_categ_id.code
                            else:
                                description = _("Need to configure products '%s - %s' sales account.", ord_line.product_id.default_code,ord_line.product_id.name)
                                mismatch_log.create({'name': order.name,
                                                     'source_model': 'sale.order',
                                                     'description': description,
                                                     'date': fields.Datetime.now(),
                                                     'option': 'export',
                                                     'xero_account_id': xero_account_id})
                                continue

                            if ord_line.xero_order_line_id:
                                vals = {u'LineItemID': ord_line.xero_order_line_id,
                                        u'AccountCode': account_code,
                                        u'Description': ord_line.name or ord_line.product_id.name,
                                        u'UnitAmount': ord_line.price_unit,
                                        u'TaxType': u'' if ord_line.order_id.line_amount_type == 'NOTAX' else tax_type and tax_type.xero_tax_type or u'',
                                        u'Quantity': ord_line.product_uom_qty or 1,
                                        u'DiscountRate': ord_line.discount,
                                        u'ItemCode': ord_line.product_id.default_code
                                        }
                            else:
                                vals = {
                                        u'Description': ord_line.name or ord_line.product_id.name,
                                        u'AccountCode': account_code,
                                        u'UnitAmount': ord_line.price_unit,
                                        u'TaxType': u'' if ord_line.order_id.line_amount_type == 'NOTAX' else tax_type and tax_type.xero_tax_type or u'',
                                        u'Quantity': ord_line.product_uom_qty or 1,
                                        u'DiscountRate': ord_line.discount,
                                        u'ItemCode': ord_line.product_id.default_code
                                        }

                            line_items.append(vals)
                        if line_items:
                            order_data.update({u'LineItems': line_items})

                        if order.state in ['sale','done'] and order.invoice_status == 'invoiced':
                            xero_account.export_invoice()
                        if order.state == 'draft':
                            request += 1
                            if request > 30:
                                time.sleep(1)
                            order_rec = xero.quotes.save(order_data)
                            if order_rec[0].get('ValidationErrors'):
                                description = order_rec[0].get('ValidationErrors')[0].get('Message')
                                mismatch_log.create({'name': order_rec[0].get('QuoteNumber') or order_rec[0].get('Reference'),
                                                     'source_model': 'sale.order',
                                                     'source_id': order.id,
                                                     'description': description,
                                                     'date': fields.Datetime.now(),
                                                     'option': 'export',
                                                     'xero_account_id': xero_account_id})
                                continue
                            if order_rec[0].get('LineItems'):
                                for lines in order_rec[0].get('LineItems'):
                                    order_line = order.order_line.filtered(lambda l: not l.xero_order_line_id
                                        and l.product_id.default_code == lines.get('ItemCode')
                                        and l.price_unit == lines.get('UnitAmount')
                                        and l.product_uom_qty == lines.get('Quantity'))
                                    if order_line:
                                        order_line[0].write({'xero_order_line_id': lines.get('LineItemID'), 'order_id': order.id})
                            self._cr.commit()
                        else:
                            update_order_data.append(order_data)
                            count += 1
                            if count == 50:
                                update_order_data_list.append(update_order_data)
                                update_order_data = []
                                count = 0

        if create_order_data:
            create_order_data_list.append(create_order_data)

        for data in create_order_data_list:
            order_rec = xero.quotes.put(data)
            for odr in order_rec:
                if odr.get('ValidationErrors'):
                    description = odr.get('ValidationErrors')[0].get('Message')
                    mismatch_log.create({'name': odr.get('QuoteNumber') or odr.get('Reference'),
                                         'source_model': 'sale.order',
                                         'description': description,
                                         'date': fields.Datetime.now(),
                                         'option': 'export',
                                         'xero_account_id': xero_account_id})
                    continue
                order = self.search([('name', '=', odr.get('Reference')), ('company_id', '=', company)],limit=1)

                if odr.get('LineItems'):
                    for lines in odr.get('LineItems'):
                        order_line = order.order_line.filtered(lambda l: not l.xero_order_line_id
                            and l.product_id.default_code == lines.get('ItemCode')
                            and l.price_unit == lines.get('UnitAmount')
                            and l.product_uom_qty == lines.get('Quantity'))
                        if order_line:
                            order_line[0].write({'xero_order_line_id': lines.get('LineItemID'), 'order_id': order.id})

                order.write({'xero_quotes_id': odr.get('QuoteID'), 'xero_quotes_number': odr.get('QuoteNumber')})
                self._cr.commit()

                if order.state == 'sent' and odr.get('Status') != 'SENT':
                    status = u'SENT'
                    order.get_updated_sale_order(order, xero_account, company, xero, odr, status)

                if order.state in ['sale','done']:
                    if order.invoice_status == 'invoiced' or order.invoice_ids:
                        if odr.get('Status') == 'DRAFT':
                            status = u'SENT'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                            status = u'ACCEPTED'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                            status = u'INVOICED'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                            xero_account.export_invoice()
                        if odr.get('Status') == 'SENT':
                            status = u'ACCEPTED'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                            status = u'INVOICED'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                            xero_account.export_invoice()
                        if odr.get('Status') == 'ACCEPTED':
                            status = u'INVOICED'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                            xero_account.export_invoice()
                    else:
                        if odr.get('Status') == 'DRAFT':
                            status = u'SENT'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                            status = u'ACCEPTED'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                        if odr.get('Status') == 'SENT':
                            status = u'ACCEPTED'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                self._cr.commit()

        if update_order_data:
            update_order_data_list.append(update_order_data)

        for data in update_order_data_list:
            order_rec = xero.quotes.save(data)
            for odr in order_rec:
                if odr.get('ValidationErrors'):
                    description = odr.get('ValidationErrors')[0].get('Message')
                    mismatch_log.create({'name': odr.get('QuoteNumber') or odr.get('Reference'),
                                         'source_model': 'sale.order',
                                         'description': description,
                                         'date': fields.Datetime.now(),
                                         'option': 'export',
                                         'xero_account_id': xero_account_id})
                    continue
                order = self.search([('name', '=', odr.get('Reference')), ('company_id', '=', company)],limit=1)
                if odr.get('LineItems'):
                    for lines in odr.get('LineItems'):
                        order_line = order.order_line.filtered(lambda l: not l.xero_order_line_id
                            and l.product_id.default_code == lines.get('ItemCode')
                            and l.price_unit == lines.get('UnitAmount')
                            and l.product_uom_qty == lines.get('Quantity'))
                        if order_line:
                            order_line[0].write({'xero_order_line_id': lines.get('LineItemID'), 'order_id': order.id})
                self._cr.commit()

                if order.state == 'sent' and odr.get('Status') != 'SENT':
                    status = u'SENT'
                    order.get_updated_sale_order(order, xero_account, company, xero, odr, status)

                if order.state in ['sale','done']:
                    if order.invoice_status == 'invoiced' or order.invoice_ids:
                        if odr.get('Status') == 'DRAFT':
                            status = u'SENT'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                            status = u'ACCEPTED'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                            status = u'INVOICED'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                            xero_account.export_invoice()
                        if odr.get('Status') == 'SENT':
                            status = u'ACCEPTED'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                            status = u'INVOICED'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                            xero_account.export_invoice()
                        if odr.get('Status') == 'ACCEPTED':
                            status = u'INVOICED'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                            xero_account.export_invoice()
                    else:
                        if odr.get('Status') == 'DRAFT':
                            status = u'SENT'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                            status = u'ACCEPTED'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                        if odr.get('Status') == 'SENT':
                            status = u'ACCEPTED'
                            order.get_updated_sale_order(order, xero_account, company, xero, odr, status)
                self._cr.commit()

    def get_updated_sale_order(self, order, xero_account, company, xero, odr, status):
        if order.xero_quotes_id and odr.get('QuoteID') == order.xero_quotes_id:
            order_data = {u'QuoteID': odr.get('QuoteID'),
                        u'QuoteNumber': odr.get('QuoteNumber'),
                        u'Reference': odr.get('Reference'),
                        u'Contact': odr.get('Contact'),
                        u'Date': odr.get('Date'),
                        u'Status': status,
                        u'LineItems': odr.get('LineItems'),
                        }
            order_rec = xero.quotes.save(order_data)
            if order_rec[0].get('ValidationErrors'):
                description = order_rec[0].get('ValidationErrors')[0].get('Message')
                mismatch_log.create({'name': order_rec[0].get('QuoteNumber') or order_rec[0].get('Reference'),
                                     'source_model': 'sale.order',
                                     'source_id': order.id,
                                     'description': description,
                                     'date': fields.Datetime.now(),
                                     'option': 'export',
                                     'xero_account_id': xero_account_id})
            self._cr.commit()

    def action_export_order(self):
        context = self._context
        companies = self.env.user.company_ids
        for company_id in companies:
            order_data = self.filtered(lambda order: order.company_id == company_id or not order.company_id)
            context.update({'order_ids': order_data})
            xero_account = self.env['xero.account'].search([('company_id', '=', company_id.id)], limit=1)
            if xero_account:
                xero_account.with_context(context).export_quotes()


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    xero_order_line_id = fields.Char(string="Xero QuoteLine Id",readonly=True, copy=False)
