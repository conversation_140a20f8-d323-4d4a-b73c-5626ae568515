# -*- coding: utf-8 -*-
# Part of Odoo. See COPYRIGHT & LICENSE files for full copyright and licensing details.

from odoo import api, fields, models, _
from odoo.exceptions import UserError
from datetime import datetime
import datetime
import time


class PurchaseOrder(models.Model):
    _inherit = "purchase.order"

    xero_po_id = fields.Char(string="Xero PO Id", copy=False)
    xero_po_number = fields.Char(string="Xero PO Number", copy=False)
    line_amount_type = fields.Selection([('Exclusive', 'Exclusive'),
                                        ('Inclusive', 'Inclusive'),
                                        ('NoTax', 'NoTax'),
                                        ], 'Line Amount Type', default='Exclusive', required=True, copy=False)

    @api.depends_context('lang')
    @api.depends('order_line.taxes_id', 'order_line.price_subtotal', 'amount_total', 'amount_untaxed')
    def _compute_tax_totals(self):
        for order in self:
            context = dict(self.env.context)
            context.update({'line_amount_type': order.line_amount_type})
            self.env.context = context
        return super(PurchaseOrder, self)._compute_tax_totals()

    def tax_calculation(self):
        for rec in self:
            if rec.line_amount_type == 'NoTax':
                rec.order_line.with_context({'line_amount_type': rec.line_amount_type}).write({'taxes_id': [(6, 0, [])]})
            else:
                rec.order_line.with_context({'line_amount_type': rec.line_amount_type})._compute_amount()
                rec.with_context({'line_amount_type': rec.line_amount_type})._compute_tax_totals()

    def import_purchase_order(self, po_list, xero, xero_account_id, company=False, without_product=False, import_option=None):
        products = self.env['product.product']
        po_orders = self.env['purchase.order']
        po_order_lines = self.env['purchase.order.line']
        partners = self.env['res.partner']
        accounts = self.env['account.account']
        currencies = self.env['res.currency']
        taxes = self.env['account.tax']
        mismatch_log = self.env['mismatch.log']
        xero_account = self.env['xero.account'].browse(xero_account_id)
        for order in po_list:
            try:
                if order.get('Total') != 0.0:
                    po_orders = self.search([('xero_po_id', '=', order.get('PurchaseOrderID'))])
                    if not po_orders and import_option in ['create', 'both']:
                        flag = 0
                        if order and order.get('Status') != 'DELETED':
                            partner = partners.search(['|', ('company_id', '=', company), ('company_id', '=', False)]).filtered(lambda partner: partner.contact_xero_company_ids.filtered(lambda contact: contact.xero_contact_id == order.get('Contact').get('ContactID') and contact.company_id.id == company))
                            if not partner:
                                xero_account.import_contact_overwrite() if xero_account.contact_overwrite else xero_account.import_contact()
                                partner = partners.search(['|', ('company_id', '=', company), ('company_id', '=', False)]).filtered(lambda partner: partner.contact_xero_company_ids.filtered(lambda contact: contact.xero_contact_id == order.get('Contact').get('ContactID') and contact.company_id.id == company))

                            order_lines = []
                            sub_tax = order.get('SubTotal') + order.get('TotalTax')
                            if float("%.2f"%sub_tax) != order.get('Total'):
                                flag = 1

                            for lines in order.get('LineItems'):
                                acc = accounts.search([('code', '=', lines.get('AccountCode')),('company_ids', 'in', company)])
                                # if not acc:
                                #     acc = self.env['ir.property']._get('property_account_expense_categ_id', 'product.category')

                                if flag == 1:
                                    line_amount = lines.get('LineAmount') - lines.get('TaxAmount')
                                    unit_amount = line_amount / lines.get('Quantity')
                                else:
                                    line_amount = lines.get('LineAmount')
                                    unit_amount = lines.get('UnitAmount')

                                if lines.get('TaxType'):
                                    tax_id = taxes.search([('xero_tax_type', '=', lines.get('TaxType')),('company_id', '=', company)])
                                else:
                                    tax_id = False

                                if lines.get('ItemCode'):
                                    product = products.search([('default_code', '=', lines.get('ItemCode'))])
                                    if not product:
                                        raise UserError(_("Please First Import Product."))
                                    for product_id in product:
                                        order_line_val = {'name': lines.get('Description') or product_id.description or 'Didn\'t specify',
                                                        'price_unit': unit_amount or 0.0,
                                                        'company_id': company,
                                                        'product_qty': lines.get('Quantity') or 1,
                                                        'product_id': product_id and product_id.id or False,
                                                        'taxes_id': [(6, 0, tax_id.ids)] if tax_id else [],
                                                        'xero_order_line_id': lines.get('LineItemID'),
                                                        'discount': lines.get('DiscountRate') or 0.0,
                                                        'date_planned': order.get('DeliveryDateString') or order.get('DateString'),
                                                        }
                                        # if acc:
                                        order_lines.append((0, 0, order_line_val))
                                else:
                                    vals = {'name': lines.get('Description') or 'Didn\'t specify',
                                            'price_unit': unit_amount or 0.0,
                                            'company_id': company,
                                            'taxes_id': [(6, 0, tax_id.ids)] if tax_id else [],
                                            'product_qty': lines.get('Quantity') or 1,
                                            'xero_order_line_id': lines.get('LineItemID'),
                                            'date_planned': order.get('DeliveryDateString') or order.get('DateString'),
                                            }
                                    # if acc:
                                    order_lines.append((0, 0, vals))

                            currency_id = currencies.search([('name', '=', order.get('CurrencyCode'))])
                            order_default = {'partner_id': partner and partner.id or False,
                                            'currency_id': currency_id and currency_id[0].id or False,
                                            'company_id': company,
                                            'date_approve': order.get('DateString'),
                                            'date_planned': order.get('DeliveryDateString') or order.get('DateString'),
                                            'xero_po_id': order.get('PurchaseOrderID'),
                                            'xero_po_number': order.get('PurchaseOrderNumber'),
                                            'amount_tax': order.get('TotalTax'),
                                            'line_amount_type':order.get('LineAmountTypes'),
                                            }
                            order_type = self.env.context.copy()
                            if order_lines:
                                order_default.update({'order_line': order_lines})
                            ord_id = po_orders.with_context(order_type).create(order_default)
                            ord_id.order_line.with_context({'line_amount_type': self.line_amount_type})._compute_amount()
                            ord_id.with_context({'line_amount_type': self.line_amount_type})._compute_tax_totals()

                            self._cr.commit()
                            if order.get('SentToContact') == True:
                                ord_id.with_context({'CurrencyRate': order.get('CurrencyRate')}).action_rfq_send()
                            if ord_id and order.get('Status') in ['SUBMITTED','AUTHORISED']:
                                ord_id.with_context({'CurrencyRate': order.get('CurrencyRate')}).button_confirm()
                            if ord_id and order.get('Status') == 'BILLED' and ord_id.order_line and ord_id.state in ['draft','sent','to approve','purchase']:
                                ord_id.action_rfq_send()
                                ord_id.button_confirm()
                                pickings = ord_id.picking_ids
                                for picking in pickings:
                                    for pick in picking.move_ids_without_package:
                                        pick.write({'quantity': pick.product_uom_qty})
                                    if picking.state in ['waiting', 'confirmed', 'assigned']:
                                        picking.button_validate()
                                invoices = self.env['account.move'].search(['|', ('xero_invoice_number','=', ord_id.name), ('xero_invoice_number','=', ord_id.xero_po_number),('xero_invoice_id','!=', False)])
                                if not invoices:
                                    xero_account.import_invoice()
                                invoices = self.env['account.move'].search(['|', ('xero_invoice_number','=', ord_id.name), ('xero_invoice_number','=', ord_id.xero_po_number),('xero_invoice_id','!=', False)])
                                if invoices:
                                    invoices.is_purchase_matched = True
                                    ord_id.invoice_ids = [(4, invoices[0].id)]
                                    ord_id.invoice_count = len(ord_id.invoice_ids)
                                    ord_id.invoice_status = 'invoiced'
                                    ord_id.action_view_invoice(ord_id.invoice_ids)
                                self._cr.commit()

                    if po_orders and import_option in ['update', 'both']:
                        if po_orders[0].line_amount_type != order.get('LineAmountTypes'):
                            description = (_('You cannot change Line Amount Type %s to %s.', po_orders[0].line_amount_type, order.get('LineAmountTypes')))
                            mismatch_log.create({'name': po_orders[0].name,
                                                 'source_model': 'purchase.order',
                                                 'source_id': po_orders[0].id,
                                                 'description': description,
                                                 'date': datetime.datetime.now(),
                                                 'option': 'import',
                                                 'xero_account_id': xero_account_id
                                                 })
                            continue

                        if order.get('Status') == 'DRAFT':
                            po_orders[0].state = 'draft'
                        if order.get('SentToContact') == True:
                            po_orders[0].action_rfq_send()

                        if po_orders[0].state != 'cancel':
                            available_lines = []
                            flag = 0
                            if order:
                                sub_tax = order.get('SubTotal') + order.get('TotalTax')
                                if float("%.2f"%sub_tax) != order.get('Total'):
                                    flag = 1

                                partner = partners.search(['|', ('company_id', '=', company), ('company_id', '=', False)]).filtered(lambda partner: partner.contact_xero_company_ids.filtered(lambda contact: contact.xero_contact_id == order.get('Contact').get('ContactID') and contact.company_id.id == company))
                                if not partner:
                                    xero_account.import_contact_overwrite() if xero_account.contact_overwrite else xero_account.import_contact()
                                    partner = partners.search(['|', ('company_id', '=', company), ('company_id', '=', False)]).filtered(lambda partner: partner.contact_xero_company_ids.filtered(lambda contact: contact.xero_contact_id == order.get('Contact').get('ContactID') and contact.company_id.id == company))
                                currency_id = currencies.search([('name', '=', order.get('CurrencyCode'))], limit=1)
                                po_orders.picking_ids.write({'partner_id': partner and partner[0].id or False})
                                vals = {
                                        'partner_id': partner and partner[0].id or False,
                                        'currency_id': currency_id and currency_id.id or False,
                                        'company_id': company,
                                        'date_approve': order.get('DateString'),
                                        'date_planned': order.get('DeliveryDateString') or order.get('DateString'),
                                        'xero_po_id': order.get('PurchaseOrderID'),
                                        'xero_po_number': order.get('PurchaseOrderNumber'),
                                        'amount_tax': order.get('TotalTax'),
                                        'line_amount_type':order.get('LineAmountTypes'),
                                }
                                context = dict(self.env.context)
                                context.update({'line_amount_type': order.get('LineAmountTypes')})
                                self.env.context = context
                                po_orders[0].write(vals)
                                line_items = []
                                for lines in order.get('LineItems'):
                                    available_lines.append(lines.get('LineItemID'))
                                    line = po_order_lines.search([('xero_order_line_id', '=', lines.get('LineItemID')),('company_id', '=', company)])

                                    if flag == 1:
                                        line_amount = lines.get('LineAmount') - lines.get('TaxAmount')
                                        unit_amount = line_amount / lines.get('Quantity')
                                    else:
                                        line_amount = lines.get('LineAmount')
                                        unit_amount = lines.get('UnitAmount')

                                    tax_id = False
                                    if lines.get('TaxType'):
                                        tax_id = taxes.search([('xero_tax_type', '=', lines.get('TaxType')),('company_id', '=', company)], limit=1)

                                    acc = accounts.search([('code', '=', lines.get('AccountCode')),('company_ids', 'in', company)])
                                    if line:
                                        if lines.get('ItemCode'):
                                            product = products.search([('default_code', '=', lines.get('ItemCode'))])
                                            if not product:
                                                raise UserError(_("Please First Import Product."))
                                            for product_id in product:
                                                order_line_val = {'name': lines.get('Description') or product_id.description or 'Didn\'t specify',
                                                                'price_unit': unit_amount or product_id.lst_price,
                                                                'product_qty': lines.get('Quantity') or 1,
                                                                'company_id': company,
                                                                'product_id': product_id and product_id.id or False,
                                                                'taxes_id': [(6, 0, tax_id.ids)] if tax_id else [(5,)],
                                                                'xero_order_line_id': lines.get('LineItemID'),
                                                                'order_id': po_orders[0].id,
                                                                'discount': lines.get('DiscountRate') or 0.0,
                                                                'date_planned': po_orders[0].date_planned
                                                                }
                                                # if acc:
                                                line.with_context({'line_amount_type': order.get('LineAmountTypes')}).write(order_line_val)
                                        else:
                                            vals = {'name': lines.get('Description') or 'Didn\'t specify',
                                                    'price_unit': unit_amount or 0,
                                                    'company_id': company,
                                                    'taxes_id': [(6, 0, tax_id.ids)] if tax_id else [(5,)],
                                                    'product_qty': lines.get('Quantity') or 1,
                                                    'xero_order_line_id': lines.get('LineItemID'),
                                                    'order_id': po_orders[0].id,
                                                    'discount': lines.get('DiscountRate') or 0.0,
                                                    'date_planned': po_orders[0].date_planned,
                                                    }
                                            # if acc:
                                            line.with_context({'line_amount_type': order.get('LineAmountTypes')}).write(vals)
                                    else:
                                        if lines.get('ItemCode'):
                                            product = products.search([('default_code', '=', lines.get('ItemCode'))])
                                            if not product:
                                                raise UserError(_("Please First Import Product."))
                                            for product_id in product:
                                                order_line_val = {'name': lines.get('Description') or product_id.description or 'Didn\'t specify',
                                                                'price_unit': unit_amount or product_id.lst_price,
                                                                'product_qty': lines.get('Quantity') or 1,
                                                                'company_id': company,
                                                                'product_id': product_id and product_id.id or False,
                                                                'taxes_id': [(6, 0, tax_id.ids)] if tax_id else [(5,)],
                                                                'xero_order_line_id': lines.get('LineItemID'),
                                                                'order_id': po_orders[0].id,
                                                                'discount': lines.get('DiscountRate') or 0.0,
                                                                'date_planned': po_orders[0].date_planned,
                                                                }
                                                # if acc:
                                                line_items.append(order_line_val)
                                        else:
                                            vals = {'name': lines.get('Description') or 'Didn\'t specify',
                                                    'price_unit': unit_amount or 0.0,
                                                    'company_id': company,
                                                    'taxes_id': [(6, 0, tax_id.ids)] if tax_id else [(5,)],
                                                    'product_qty': lines.get('Quantity') or 1,
                                                    'xero_order_line_id': lines.get('LineItemID'),
                                                    'order_id': po_orders[0].id,
                                                    'discount': lines.get('DiscountRate') or 0.0,
                                                    'date_planned': po_orders[0].date_planned,
                                                    }
                                            # if acc:
                                            line_items.append(vals)
                                if line_items:
                                    line_ids = po_order_lines.with_context({'line_amount_type': order.get('LineAmountTypes')}).create(line_items)
                                po_orders[0].order_line.with_context({'line_amount_type': self.line_amount_type})._compute_amount()
                                po_orders[0].with_context({'line_amount_type': self.line_amount_type})._compute_tax_totals()
                                if order.get('Status') in ['SUBMITTED','AUTHORISED'] and po_orders[0].state != 'purchase':
                                    po_orders[0].button_confirm()
                                if order.get('Status') == 'DELETED' and po_orders[0].state in ['draft', 'sent','to approve', 'purchase']:
                                    po_orders[0].button_cancel()
                                if order.get('Status') == 'BILLED' and po_orders[0].state in ['draft','sent','to approve','purchase']:
                                    po_orders[0].button_confirm()
                                    pickings = po_orders[0].picking_ids
                                    for picking in pickings:
                                        for pick in picking.move_ids_without_package:
                                            pick.write({'quantity': pick.product_uom_qty})
                                        if picking.state in ['waiting', 'confirmed', 'assigned']:
                                            picking.button_validate()
                                    invoices = self.env['account.move'].search(['|',('xero_invoice_number','=', po_orders[0].name),('xero_invoice_number','=', po_orders[0].xero_po_number),('xero_invoice_id','!=', False)])
                                    if not invoices:
                                        xero_account.import_invoice()
                                    invoices = self.env['account.move'].search(['|',('xero_invoice_number','=', po_orders[0].name),('xero_invoice_number','=', po_orders[0].xero_po_number),('xero_invoice_id','!=', False)])
                                    if invoices:
                                        invoices.is_purchase_matched = True
                                        po_orders[0].invoice_ids = [(4, invoices[0].id)]
                                        po_orders[0].invoice_count = len(po_orders[0].invoice_ids)
                                        po_orders[0].invoice_status = 'invoiced'
                                        po_orders[0].action_view_invoice(po_orders[0].invoice_ids)
                                    self._cr.commit()
                                if order.get('Status') in ['SUBMITTED','AUTHORISED'] and po_orders[0].invoice_ids and po_orders[0].invoice_status == 'invoiced':
                                    po_orders[0].invoice_ids.write({'state': 'cancel'})
                                if po_orders[0].state in ['draft','sent','to approve']:
                                    for order_lines in po_orders[0].order_line:
                                        if order_lines.xero_order_line_id not in available_lines:
                                            order_lines.unlink()
                            if order.get('Status') in ['SUBMITTED','AUTHORISED'] and po_orders[0].state in ['draft', 'sent', 'to approve'] and po_orders[0].order_line:
                                if po_orders[0].state == 'draft':
                                    context = dict(self.env.context)
                                    context.update({'CurrencyRate': order.get('CurrencyRate')})
                                    self.env.context = context
                                    if context.get('CurrencyRate'):
                                        del context['CurrencyRate']
                                        self.env.context = context
            except Exception as e:
                # raise UserError(_('%s') % e)
                mismatch_log.create({'name': order.get('PurchaseOrderNumber') or order.get('Reference'),
                                     'source_model': 'purchase.order',
                                     'source_id': order.get('PurchaseOrderNumber'),
                                     'description': e,
                                     'date': fields.Datetime.now(),
                                     'option': 'import',
                                     'xero_account_id': xero_account_id})
                continue

    def export_purchase_order(self, po_list, xero, last_export_date, xero_account_id, company=False, disable_export=False):
        mismatch_log = self.env['mismatch.log']
        xero_account = self.env['xero.account'].search([('company_id', '=', company)], limit=1)
        if self._context.get('order_ids'):
            order_ids = self._context.get('order_ids')
        else:
            if last_export_date:
                order_ids = self.search([('company_id', '=', company),
                                           ('state', '!=', 'cancel'),
                                           '|', ('write_date', '>=', last_export_date),
                                           ('create_date', '>=', last_export_date)])
            else:
                order_ids = self.search([('company_id', '=', company),
                                           ('state', '!=', 'cancel')])
        update_order_data = []
        update_order_data_list = []
        create_order_data = []
        create_order_data_list = []
        count = 0
        c = 0
        request = 0
        for order_id in order_ids:
            if not order_id.partner_id:
                description = 'Customer must be set for export (Odoo to Xero)'
                mismatch_log.create({'name': order_id.name,
                                     'source_model': 'purchase.order',
                                     'source_id': order_id.id,
                                     'description': description,
                                     'date': datetime.datetime.now(),
                                     'option': 'export',
                                     'xero_account_id': xero_account_id
                                     })
                continue
            if not order_id.xero_po_id:
                if order_id.state == 'draft':
                    status = u'Draft'
                if order_id.state in ['purchase', 'done']:
                    status = u'AUTHORISED'
                if order_id.invoice_status == 'invoiced':
                    status = u'BILLED'

                if order_id.state == 'sent':
                    status = {u'SentToContact': True}
                line_amount_type = order_id.line_amount_type
                partner_details = {}
                if order_id.partner_id.parent_id:
                    contact = order_id.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                    if not contact.xero_contact_id:
                        xero_account.export_contact_overwrite() if xero_account.contact_overwrite else xero_account.export_contact()
                        contact = order_id.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                    partner_details = {u'ContactID': contact.xero_contact_id}
                else:
                    contact = order_id.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                    if not contact.xero_contact_id:
                        xero_account.export_contact_overwrite() if xero_account.contact_overwrite else xero_account.export_contact()
                        contact = order_id.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                    partner_details = {u'ContactID': contact.xero_contact_id}
                final_order_data = {
                                    u'Contact': partner_details,
                                    u'Date': order_id.date_approve or fields.Date.today(),
                                    u'DeliveryDate': order_id.date_planned or fields.Date.today(),
                                    u'Status': status,
                                    u'TotalTax': order_id.amount_tax if order_id.amount_tax else 0.0,
                                    u'LineAmountTypes': line_amount_type,
                                    u'CurrencyCode': order_id.currency_id.name,
                                    }
                if order_id:
                    final_order_data.update({u'Reference': order_id.name or u''})

                if order_id.order_line:
                    line_items = []
                    for line in order_id.order_line.filtered(lambda l: l.product_id):
                        if line.product_id:
                            product = line.product_id.product_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            if product and not product[0].xero_item_id:
                                xero_account.export_product()

                        if line.taxes_id:
                            tax_type = line.taxes_id[0]
                        else:
                            if order_id:
                                tax_type = self.env['account.tax'].search([('type_tax_use', '=', 'purchase'), ('amount', '=', 0), ('company_id', '=', company)], limit=1)
                                if not tax_type:
                                    company_id = self.env['res.company'].browse(company)
                                    raise UserError(_('Please create account tax of type \'PURCHASE\' and amount = 0.0 for Company: %s')% (company_id.name))
                        if not tax_type.xero_tax_type:
                            tax_rates = xero.taxrates.all()
                            self.env['account.tax'].export_tax(tax_rates, xero, xero_account_id, company=company, disable_export=disable_export)
                        vals = {
                                u'Description': line.name or line.product_id.name,
                                u'UnitAmount': line.price_unit,
                                u'TaxType': u'' if line.order_id.line_amount_type == 'NoTax' else tax_type and tax_type.xero_tax_type or u'',
                                u'DiscountRate': line.discount,
                                u'ValidationErrors': [],
                                u'Quantity': line.product_qty,
                            }
                        vals.update({u'ItemCode': line.product_id.default_code})
                        line_items.append(vals)
                    if line_items:
                        final_order_data.update({u'LineItems': line_items})
                if order_id.state in ['purchase','done'] and order_id.invoice_status == 'invoiced':
                    xero_account.export_invoice()
                if order_id.state == 'draft':
                    request += 1
                    if request > 30:
                        time.sleep(1)
                    order_rec = xero.purchaseorders.put(final_order_data)
                    if order_rec and order_rec[0].get('ValidationErrors'):
                        description = order_rec[0].get('ValidationErrors')[0].get('Message')
                        mismatch_log.create({'name': order_rec[0].get('PurchaseOrderNumber') or order_rec[0].get('Reference'),
                                             'source_model': 'purchase.order',
                                             'source_id': order_id.id,
                                             'description': description,
                                             'date': fields.Datetime.now(),
                                             'option': 'export',
                                             'xero_account_id': xero_account_id})
                        continue

                    if order_rec and order_rec[0].get('LineItems'):
                        for lines in order_rec[0].get('LineItems'):
                            order_line = order_id.order_line.filtered(lambda l: not l.xero_order_line_id
                                and l.product_id.default_code == lines.get('ItemCode')
                                and l.price_unit == lines.get('UnitAmount')
                                and l.product_qty == lines.get('Quantity'))
                            if order_line:
                                order_line[0].write({'xero_order_line_id': lines.get('LineItemID'), 'order_id': order_id.id})

                    order_id.write({'xero_po_id': order_rec and order_rec[0].get('PurchaseOrderID'), 'xero_po_number': order_rec and order_rec[0].get('PurchaseOrderNumber')})
                    self._cr.commit()

                else:
                    create_order_data.append(final_order_data)
                    c += 1
                    if c == 50:
                        create_order_data_list.append(create_order_data)
                        create_order_data = []
                        c = 0

            elif order_id.xero_po_id:
                for xero_ord in po_list:
                    if xero_ord.get('PurchaseOrderID') == order_id.xero_po_id:
                        order_currency_rate = 0.0
                        status = u'DRAFT'
                        if order_id.state == 'draft' and xero_ord.get('Status') == 'DRAFT':
                            status = u'DRAFT'
                        if order_id.state in ['purchase', 'done'] and xero_ord.get('Status') not in ['DELETED','BILLED']:
                            status = u'AUTHORISED'
                        if order_id.state == 'cancel':
                            status = u'DELETED'
                        if order_id.invoice_status == 'invoiced' and xero_ord.get('Status') not in ['DELETED', 'BILLED']:
                            status = u'BILLED'
                        if status == u'DRAFT' and xero_ord.get('Status') in ['BILLED']:
                            status = u'BILLED'
                        partner_details = {}
                        if order_id.partner_id.parent_id:
                            contact = order_id.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            if not contact.xero_contact_id:
                                xero_account.export_contact_overwrite() if xero_account.contact_overwrite else xero_account.export_contact()
                                contact = order_id.partner_id.parent_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            partner_details = {u'ContactID': contact.xero_contact_id}
                        else:
                            contact = order_id.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            if not contact.xero_contact_id:
                                xero_account.export_contact_overwrite() if xero_account.contact_overwrite else xero_account.export_contact()
                                contact = order_id.partner_id.contact_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                            partner_details = {u'ContactID': contact.xero_contact_id}
                        line_amount_type = order_id.line_amount_type

                        order_data = {
                                        u'PurchaseOrderID': order_id.xero_po_id,
                                        u'Status': status,
                                        u'LineAmountTypes': line_amount_type,
                                        u'Contact': partner_details,
                                        u'Reference': order_id.name if order_id.name else u'',
                                        u'Date': order_id.date_approve or fields.Date.today(),
                                        u'DeliveryDate': order_id.date_planned or fields.Date.today(),
                                        u'CurrencyCode': order_id.currency_id.name,
                                        u'TotalTax': order_id.amount_tax if order_id.amount_tax else 0.0,
                                    }

                        line_items = []
                        for ord_line in order_id.order_line.filtered(lambda l: l.product_id):
                            if ord_line.taxes_id:
                                tax_type = ord_line.taxes_id[0]
                            else:
                                if order_id:
                                    tax_type = self.env['account.tax'].search([('type_tax_use', '=', 'purchase'), ('amount', '=', 0), ('company_id', '=', company)], limit=1)
                                    if not tax_type:
                                        company_id = self.env['res.company'].browse(company)
                                        raise UserError(_('Please create account tax of type \'PURCHASE\' and amount = 0.0 for Company: %s')% (company_id.name))

                            if not tax_type.xero_tax_type:
                                tax_rates = xero.taxrates.all()
                                self.env['account.tax'].export_tax(tax_rates, xero, xero_account_id, company=company, disable_export=disable_export)
                            if ord_line.product_id:
                                product = ord_line.product_id.product_xero_company_ids.filtered(lambda l: l.company_id.id == company)
                                if product and not product[0].xero_item_id:
                                    xero_account.export_product()

                            vals = {
                                u'Description': ord_line.name or ord_line.product_id.name,
                                u'UnitAmount': ord_line.price_unit,
                                u'TaxType': u'' if ord_line.order_id.line_amount_type == 'NoTax' else tax_type and tax_type.xero_tax_type or u'',
                                u'Quantity': ord_line.product_qty,
                                u'ItemCode': ord_line.product_id.default_code,
                                u'DiscountRate': ord_line.discount,
                            }
                            if ord_line.xero_order_line_id:
                                vals.update({
                                    u'LineItemID': ord_line.xero_order_line_id,
                                })

                            line_items.append(vals)
                        if line_items:
                            order_data.update({u'LineItems': line_items})
                        if order_id.state in ['purchase','done'] and order_id.invoice_status == 'invoiced':
                            xero_account.export_invoice()
                        if order_id.state == 'draft':
                            request += 1
                            if request > 30:
                                time.sleep(1)
                            order_rec = xero.purchaseorders.save(order_data)
                            if order_rec and order_rec[0].get('ValidationErrors'):
                                description = order_rec[0].get('ValidationErrors')[0].get('Message')
                                mismatch_log.create({'name': order_rec[0].get('PurchaseOrderNumber') or order_rec[0].get('Reference'),
                                                     'source_model': 'purchase.order',
                                                     'source_id': order_id.id,
                                                     'description': description,
                                                     'date': fields.Datetime.now(),
                                                     'option': 'export',
                                                     'xero_account_id': xero_account_id})
                                continue
                            if order_rec in order_rec[0].get('LineItems'):
                                for lines in order_rec[0].get('LineItems'):
                                    order_line = order_id.order_line.filtered(lambda l: not l.xero_order_line_id
                                        and l.product_id.default_code == lines.get('ItemCode')
                                        and l.price_unit == lines.get('UnitAmount')
                                        and l.product_qty == lines.get('Quantity'))
                                    if order_line:
                                        order_line[0].write({'xero_order_line_id': lines.get('LineItemID'), 'order_id': order_id.id})

                            self._cr.commit()
                        else:
                            update_order_data.append(order_data)
                            count += 1
                            if count == 50:
                                update_order_data_list.append(update_order_data)
                                update_order_data = []
                                count = 0

        if create_order_data:
            create_order_data_list.append(create_order_data)
        for data in create_order_data_list:
            order_rec = xero.purchaseorders.put(data)
            for odr in order_rec:
                if odr.get('ValidationErrors'):
                    description = odr.get('ValidationErrors')[0].get('Message')
                    mismatch_log.create({'name': odr.get('PurchaseOrderNumber') or odr.get('Reference'),
                                         'source_model': 'purchase.order',
                                         'description': description,
                                         'date': fields.Datetime.now(),
                                         'option': 'export',
                                         'xero_account_id': xero_account_id})
                    continue
                order_id = self.search([('name', '=', odr.get('Reference')), ('company_id', '=', company)],limit=1)
                for lines in odr.get('LineItems'):
                    order_line = order_id.order_line.filtered(lambda l: not l.xero_order_line_id
                        and l.product_id.default_code == lines.get('ItemCode')
                        and l.price_unit == lines.get('UnitAmount')
                        and l.product_qty == lines.get('Quantity'))
                    if order_line:
                        order_line[0].write({'xero_order_line_id': lines.get('LineItemID'), 'order_id': order_id.id})

                order_id.write({'xero_po_id': odr.get('PurchaseOrderID'), 'xero_po_number': odr.get('PurchaseOrderNumber')})
                self._cr.commit()

        if update_order_data:
            update_order_data_list.append(update_order_data)
        for data in update_order_data_list:
            order_rec = xero.purchaseorders.save(data)
            for odr in order_rec:
                if odr.get('ValidationErrors'):
                    description = odr.get('ValidationErrors')[0].get('Message')
                    mismatch_log.create({'name': odr.get('PurchaseOrderNumber') or odr.get('Reference'),
                                         'source_model': 'purchase.order',
                                         'description': description,
                                         'date': fields.Datetime.now(),
                                         'option': 'export',
                                         'xero_account_id': xero_account_id})
                    continue
                order_id = self.search([('name', '=', odr.get('Reference')), ('company_id', '=', company)],limit=1)
                for lines in odr.get('LineItems'):
                    order_line = order_id.order_line.filtered(lambda l: not l.xero_order_line_id
                        and l.product_id.default_code == lines.get('ItemCode')
                        and l.price_unit == lines.get('UnitAmount')
                        and l.product_qty == lines.get('Quantity'))
                    if order_line:
                        order_line[0].write({'xero_order_line_id': lines.get('LineItemID'), 'order_id': order_id.id})
                        self._cr.commit()
                self._cr.commit()

    def action_export_order(self):
        context = self._context
        companies = self.env.user.company_ids
        for company_id in companies:
            order_data = self.filtered(lambda order: order.company_id == company_id or not order.company_id)
            context.update({'order_ids': order_data})
            xero_account = self.env['xero.account'].search([('company_id', '=', company_id.id)], limit=1)
            if xero_account:
                xero_account.with_context(context).export_purchase_order()


class PurchaseOrderLine(models.Model):
    _inherit = "purchase.order.line"

    xero_order_line_id = fields.Char(string="Xero POLine Id",readonly=True, copy=False)
