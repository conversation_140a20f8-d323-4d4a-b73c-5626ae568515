# -*- coding: utf-8 -*-
# Part of Odoo. See COPYRIGHT & LICENSE files for full copyright and licensing details.

{
    'name': 'Xero Connector (OAuth 2.0 REST API)',
    'version': '1.0.2',
    'category': 'Accounting',
    'license': 'OPL-1',
    'summary': """Xero Integration with ODOO (OAuth 2.0)""",
    'description': """
Xero Integration
Xero is the QuickBooks alternative accounting software to manage invoicing, bank reconciliation, book keeping & more.

This module does bi-directional integration between Xero and Odoo for Accounts, Taxes, Contacts, Products and Invoices.

Benefits
========

- Raise invoices from Odoo.
- Allow your sales team to view overdue client accounts in Odoo.
- Reduce double data entry between Odoo and Xero.
- Improve cash flow by accelerating payment time.

Import Xero data into Odoo
==========================

- Currency: Complete currency types will be brought over to Odoo.
- Contact Groups: Entire contact groups.
- Contacts: All contacts which are stored in Xero, it could be Supplier or Customer.
- Account: All types of Accounts.
- Tax: All Tax types with its component.
- Bank Account: Accounts with bank type.
- Manual Journal: All type of manual journal, which cannot be recorded directly.
- Product: Products with its details (sale price, cost price, accounts, taxes etc), Also manage tracked and Untracked Products.
- Invoice: Customer Invoices with taxes. Also create payment lines in Odoo if invoice are paid fully or partially in Xero.
- Bills: Bills with taxes. Also create payment lines in Odoo if bills are paid fully or partially in Xero.
- Credit Note (ACCRECCREDIT): An Account Receivable (customer) Credit Note with payment and allocations.
- Credit Note (ACCPAYCREDIT): An Accounts Payable (supplier) Credit Note with payment and allocations.

Export Odoo data to Xero
========================

- Contacts: All contacts which are stored in Odoo, whether Customer or Supplier.
- Contact Groups: Entire contact groups.
- Accounts: All account with its type.
- Tax: Taxes with its component.
- Bank Account: Accounts with bank type.
- Product: Products with its details (sale price, cost price, accounts, taxes etc), Also manage tracked and Untracked Products.
- Invoice: Customer Invoice with tax. Also create payment in Xero if invoice are paid fully or partially in Odoo.
- Bills: Bills with tax. Also create payment in Xero if bills are paid fully or partially in Odoo.
- Inventory Adjustment: For tracked product if qty will be increasing or decreasing in Odoo using option like (Buying and selling inventory - bills and invoices, By Inventory adjustment, By Manufacturing process) it will be reflect in Xero.
- Attachment: Attachments of Invoice/Vendor Bill will be send to Xero.

User Guide
==========

'user_guide.pdf' is available in the 'doc' directory inside module.

Watch Video
===========

Go on https://www.youtube.com/watch?v=xGbWWSIAvNQ to watch the video demo.

Disclaimer
==========

Whenever there is a change in API from Xero or change in related Odoo objects you can contact us to look into it.

OAuth 2.0
OAuth 2
OAuth2
Auth 2
Auth 2.0
Auth2
Xero Connector (OAuth 2.0 REST API)
Odoo integrated with Xero OAuth 2.0 REST API

Make a smart move towards more accurate and streamlined system with Odoo-Xero connector and experience a new level of efficiency in handling your accounting data. The integration will allow you to view and manage your invoices and expenses related activities efficiently in Odoo. Furthermore, the integration will Import and Export the data seamlessly from Purchase, Sales, Charts of accounts, Taxes/Tax rate, Currencies, Contacts groups/ Contact tags, Contacts (suppliers/customers), Bank accounts, Manual journal are only import from Xero to Odoo, Items/Products, Invoices, Bills, Credit Notes for customers (either import or export), Credit Notes (Refund) for suppliers (either import or export), Attachments are only exported from Odoo to Xero, for Invoices, Bills, Credit note (supplier/customer), Contact, sales order and purchase order.

Configuration is provided for all import operations, to update, create or perform both update and create operations.

Odoo Xero Integration
Xero Odoo Connector
Data Synchronization
Import/Export Data
Two-Way Sync
Bidirectional Sync
OAuth 2.0
REST API
Accounting Data
Invoices
Bills
Credit Notes
Suppliers Refund
Sales Orders
Purchase Orders
Chart of Accounts
Taxes/Tax Rates
Currencies
Contacts (Suppliers/Customers)
Contact Groups/Tags
Bank Accounts
Manual Journal
Items/Products
Attachments
Inventory Adjustment
Draft Invoices/Bills
Confirmed Invoices/Bills
Draft Credit Notes
Confirmed Credit Notes
Invoice Payment
Bill Payment
Credit Note Payment/Allocation
Suppliers Refund Payment & Allocation
Draft Invoice/Bill Import/Export
Draft Credit Note Import/Export
Invoice Import Without Products
Inventory Adjustment Export
Multi-Currency Support
Multi-Company Support
Mismatch Log Files
Single-Button Synchronization
Manual Record Update
Automatic Record Update (Scheduler)
Streamlined System
Accurate System
Efficient Accounting
Seamless Data Transfer
Configuration Options
Community Edition
Enterprise Edition
Odoo.sh
Online
On-Premises
Additional Features
Comparison Table
Import draft invoices from Xero to Odoo
Export draft bills from Odoo to Xero
Import invoices without products into Odoo
Synchronize inventory adjustments between Odoo and Xero
Odoo Xero integration with multi-currency support
Multi-company Xero Odoo connector
Track mismatched records in Odoo Xero integration
One-click synchronization for Odoo and Xero
Synconics Xero-Odoo Connector


    """,
    'author': 'Synconics Technologies Pvt. Ltd.',
    'website': 'http://www.synconics.com',
    'depends': ['sale_management', 'sale_stock', 'purchase'],
    'data': [
        'security/ir.model.access.csv',
        'security/security.xml',
        'data/data.xml',
        'views/xero_view.xml',
        'views/res_partner_view.xml',
        'views/product_view.xml',
        'views/sale_order.xml',
        'views/purchase_order.xml',
        'views/account_view.xml',
        'views/account_move_view.xml',
        'views/menu.xml',
    ],
    'images': [
        'static/description/main_screen.png'
    ],
    "price": 285,
    "currency": "USD",
    'external_dependencies': {
        'python' : ['requests_oauthlib'],
    },
    'application': True,
    'installable': True,
    'auto_install': False,
    'license': 'OPL-1',
}
