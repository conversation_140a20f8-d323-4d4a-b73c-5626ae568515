<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="xero_account_comp_rule" model="ir.rule">
        <field name="name">Xero Account</field>
        <field name="model_id" ref="model_xero_account"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|', ('company_id','=',False), ('company_id', 'in', company_ids)]</field>
    </record>

    <record id="xero_mismatch_comp_rule" model="ir.rule">
        <field name="name">Mismatch Log</field>
        <field name="model_id" ref="model_mismatch_log"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|', ('company_id','=',False), ('company_id', 'in', company_ids)]</field>
    </record>

</odoo>