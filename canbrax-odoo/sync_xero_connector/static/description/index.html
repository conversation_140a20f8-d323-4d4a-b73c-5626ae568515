<section class="py-3" style="background-color: #fff;">
    <!-- <div class="position-absolute d-none d-lg-block"
         style="background-color: #f5f5f5; width: 100vw; height: 100vh; top: 0; left: 0; margin: 0; overflow: hidden;">
    </div> -->
    <div style="padding:85px 48px 32px 48px; margin-top: 35px; border-radius: 20px; margin-left: calc(-40vw + 50%); width: 80vw;"
        class="bg-white shadow-lg position-relative">
        <div class="align-items-center d-flex justify-content-center">
            <div class="bg-white fs-3 fw-bold lh-sm px-4 py-3 rounded shadow-lg text-center"
                style="text-decoration:underline;margin-top: -165px;">Odoo integrated with <span style="color:#01418F"> Xero OAuth 2.0 REST API </span></div>
        </div>
        <div class="container">
            <div class="row gx-6">
                <div class="col">
                    <p class="text-black lead"> Make a smart move towards more accurate and streamlined system with Odoo-Xero connector and experience a new level of efficiency in handling your accounting data. The integration will allow you to view and manage your invoices and expenses related activities efficiently in Odoo. Furthermore, the integration will Import and Export the data seamlessly from Purchase, Sales, Charts of accounts, Taxes/Tax rate, Currencies, Contacts groups/ Contact tags, Contacts (suppliers/customers), Bank accounts, Manual journal are only import from Xero to Odoo, Items/Products, Invoices, Bills, Credit Notes for customers (either import or export), Credit Notes (Refund) for suppliers (either import or export), Attachments are only exported from Odoo to Xero, for Invoices, Bills, Credit note (supplier/customer), Contact, sales order and purchase order.</p>
                    <p class="text-black lead">Configuration is provided for all import operations, to update, create or perform both update and create operations.</p>
                </div>
                <div class="col-auto p-0">
                    <div class="h-100" style="width: 1px; height: 100%; background-color: #A24689;"></div>
                </div>
                <div class="col-auto">
                    <ul class="fs-5 fw-bold list-unstyled">
                        <li class="align-items-center d-flex py-1 lh-1"><img src="./img/right.png"
                                class="img-fluid me-2" alt="right" width="47">Community</li>

                        <li class="align-items-center d-flex py-1 lh-1"><img src="./img/right.png"
                                class="img-fluid me-2" alt="right" width="47">
                            <div>
                                Enterprise <br>
                                <small class="fst-italic fs-6">
                                    (On-premises)
                                </small>
                            </div>
                        </li>
                        <li class="align-items-center d-flex py-1 lh-1"><img src="./img/right.png"
                                class="img-fluid me-2" alt="right" width="47">Odoo.sh</li>
                        <li class="align-items-center d-flex py-1 lh-1"><img src="./img/wromg.png"
                                class="img-fluid me-2" alt="wromg" width="47">Online</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
        <div style="margin-bottom: 65px;"></div>

    <div style="padding:48px 48px 48px 48px; margin-top: 35px; border-radius: 20px; margin-left: calc(-40vw + 50%); width: 80vw;"
        class="bg-white shadow-lg position-relative">
        <div class="container">
        <div class="row gx-6">
                <div class="col-12">
                    <img src="./img/odoo_integrated_with_rest_api.png" class="img-fluid mx-auto py-4" alt="odoo_integrated_with_rest_api">
                </div>
            </div>
        </div>
    </div>
        <div style="margin-bottom: 65px;"></div>

    <div style="padding:48px 48px 48px 48px; margin-top: 35px; border-radius: 20px; margin-left: calc(-40vw + 50%); width: 80vw;"
        class="bg-white shadow-lg position-relative">
        <div class="container">
            <div class="fs-3 fw-bold lh-sm text-center pb-4"><span style="color: #01418F;">Xero-Odoo Connector</span> </div>
            <div class="fs-3 fw-bold lh-sm text-center pb-4"><span style="color: #01418F;">The bidirectional* data transition through our app is quicker and easily floats between</span> </div>
            <div class="fs-3 fw-bold lh-sm text-center pb-4"><span style="color: #01418F;">Odoo to Xero or Xero to Odoo vice-a-versa</span> </div>

            <div class="row gx-6">
                <div class="col-12">
                    <img src="./img/sync_xero_connector_import_img1.png" class="img-fluid mx-auto py-4" alt="sync_xero_connector_import_img_1">
                </div>
                <div class="col-12">
                    <img src="./img/sync_xero_connector_export_img2.png" class="img-fluid mx-auto py-4" alt="sync_xero_connector_export_img_1">
                </div>
            </div>
        </div>
    </div>
        <div style="margin-bottom: 65px;"></div>
        <div style="padding:83px 15px 25px 15px; margin-top: 35px; border-radius: 20px; margin-left: calc(-40vw + 50%); width: 80vw;"
            class="bg-white shadow-lg position-relative">
            <div class="align-items-center d-flex justify-content-center">
                <div class="bg-white fs-3 fw-bold lh-sm px-4 py-3 rounded shadow-lg text-center"
                    style="margin-top: -165px;"><span style="color:#01418F"> Additional Features ONLY Available in our Xero-Odoo Connector Application</span>
                </div>
            </div>
            <div class="container-fluid">
                 <div class="row g-0">
                    <div class="col-md-12">
                    </div>
                         <div class="col">
                            <div class="p-4 pt-0">
                                    <div style="border-radius:20px;" class="bg-white mb-3 p-4 shadow-lg">
                                        <div class="fs-4 fw-bold lh-sm mb-1 text-black">Import and Export of Draft Invoices/Bills from Both sides :</div>
                                        <p class="lead">The import and export of confirmed invoices are seamlessly managed from both sides. However, unlike other applications, Synconics' Xero-Odoo Connector uniquely handles the import and export of draft invoices as well. Our application extends this functionality to include draft vendor bills, purchase orders, and sales orders, ensuring comprehensive two-way synchronization.</p>
                                        <div class="fs-4 fw-bold lh-sm mb-1 text-black">Import and Export of Draft Credit Notes from Both Sides:</div>
                                        <p class="lead">Our application supports the seamless import and export of draft credit notes. However, the process operates one direction at a time. For instance, if the import option is selected, it will exclusively import credit notes in either the draft or confirmed stage. Similarly, the export operation works in the similar manner.</p>
                                        <p class="lead"><b class="text-black fw-bold">Note : </b>If tax settings of invoices and bills in draft stage are changed or updated from inclusive taxes to exclusive taxes or vice-a-versa, then those updated draft invoices and bills will not be imported or exported. </p>
                                    </div>
                                    <div style="border-radius:20px;" class="bg-white mb-3 p-4 shadow-lg">
                                        <div class="fs-4 fw-bold lh-sm mb-1 text-black">Import Invoices Without Products from Xero to Odoo:</div>
                                        <p class="lead">The Synconics Xero-Odoo Connector supports importing invoices from Xero to Odoo, even when they do not include products. This functionality is ideal for companies that manage products exclusively in Xero and not in Odoo. Both draft and confirmed invoices without products can be seamlessly imported into Odoo from Xero. </p>
                                    </div>
                                    <div style="border-radius:20px;" class="bg-white mb-3 p-4 shadow-lg">
                                        <div class="fs-4 fw-bold lh-sm mb-1 text-black">Export Inventory Adjustment:</div>
                                        <p class="lead">The Synconics Xero-Odoo Connector enables the export of inventory adjustment product quantities from Odoo to Xero, ensuring accurate synchronization of product quantities on the Xero side.</p>
                                    </div>
                                    <div style="border-radius:20px;" class="bg-white mb-3 p-4 shadow-lg">
                                        <div class="fs-4 fw-bold lh-sm mb-1 text-black">Multi Currency : </div>
                                        <p class="lead">Multi Currency is supported and managed in our application.</p>
                                    </div>
                                    <div style="border-radius:20px;" class="bg-white mb-3 p-4 shadow-lg">
                                        <div class="fs-4 fw-bold lh-sm mb-1 text-black">Multi Company : </div>
                                        <p class="lead">Multi Company is also handled by our application.</p>
                                    </div>
                                    <div style="border-radius:20px;" class="bg-white mb-3 p-4 shadow-lg">
                                        <div class="fs-4 fw-bold lh-sm mb-1 text-black">Mismatch Log Files:</div>
                                        <p class="lead">The Synconics Odoo-Xero Connector provides a comprehensive log of mismatched records generated during import or export operations. This log includes detailed information such as record name, source model, source ID, import/export date, company name (for multi-company setups), and a descriptive note. These details help users efficiently track and resolve mismatched records, streamlining import and export processes.</p>
                                    </div>
                                    <div style="border-radius:20px;" class="bg-white mb-3 p-4 shadow-lg">
                                        <div class="fs-4 fw-bold lh-sm mb-1 text-black">Single-Button Synchronization for Import/Export Operations:</div>
                                        <p class="lead">To simplify manual data synchronization, Synconics provides a single-button solution for importing and exporting data. In most other applications, when synchronizing data manually, each individual record must be transferred. For example, if you have hundreds or thousands of contact records, each one would need to be transferred manually. However, with our application, all records for a specific object can be transferred at once with the click of a single button. For instance, if you have thousands of contacts whether vendor, customer, or other types these can be imported or exported together with just one click. This feature is individually available for a wide range of objects, including Contacts, Products, Sales Orders, Purchase Orders, Invoices/Bills, Credit Notes/Supplier Refunds, Currencies, Taxes, Chart of Accounts, and Bank Accounts. Additionally, an export button is provided for Attachments and Inventory Adjustments, while an import button is available for Manual Journals.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <!-- FOR  SHEET 1 -->
        <div style="margin-bottom: 65px;"></div>

    <div style="padding:48px 48px 48px 48px; margin-top: 35px; border-radius: 20px; margin-left: calc(-40vw + 50%); width: 80vw;"
        class="bg-white shadow-lg position-relative">
        <div class="container">
            <div class="fs-3 fw-bold lh-sm text-center pb-4"><span style="color: #01418F;">Import/Export Features</span>
            </div>

            <table class="table table-bordered align-middle table-striped table-hover">
                <tbody>
                    <tr class="bg-300">
                      <th class="fw-bold fs-5 text-center" style="width: 50px;">#</th>
                      <th class="fw-bold fs-5">Functionality</th>
                      <th class="fw-bold fs-5 text-center">Import (Xero to Odoo)</th>
                      <th class="fw-bold fs-5 text-center">Export (Odoo to Xero)</th>
                      <th class="fw-bold fs-5 text-center">Other Apps</th>

                    </tr>
                    <tr>
                        <td class="text-center">1</td>
                        <td class="lead">Currencies</td>
                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                    </tr>
                    <tr>
                        <td class="text-center">2</td>
                        <td class="lead">Draft - Customer Invoices</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>


                    </tr>
                    <tr>
                        <td class="text-center">3</td>
                        <td class="lead">Draft - Vendor Bills</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>


                    </tr>
                    <tr>
                        <td class="text-center">4</td>
                        <td class="lead">Draft - Credit Note</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>


                    </tr>
                    <tr>
                        <td class="text-center">5</td>
                        <td class="lead">Draft - Suppliers Refund</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>


                    </tr>
                    <tr>
                        <td class="text-center">6</td>
                        <td class="lead">Draft - Sales Order</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">7</td>
                        <td class="lead">Draft - Purchase Order</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">8</td>
                        <td class="lead">Attachments</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">9</td>
                        <td class="lead">Inventory Adjustment (Product Quantity)</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                    </tr>
                    <tr>
                        <td class="text-center">10</td>
                        <td class="lead">Bank Accounts</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                    </tr>
                    <tr>
                        <td class="text-center">11</td>
                        <td class="lead">Contacts Groups/ Contacts tags</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>

                    </tr>

                    <tr>
                        <td class="text-center">12</td>
                        <td class="lead">Contacts (Supplier/ Customers)</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">13</td>
                        <td class="lead">Manual Update of Records</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                    </tr>
                    <tr>
                        <td class="text-center">14</td>
                        <td class="lead">Automatic Records Update (Schedular)</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">15</td>
                        <td class="lead">Accounts / Chart of Accounts </td>
                         <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">16</td>
                        <td class="lead">Taxes</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">17</td>
                        <td class="lead">Manual Journal</td>
                         <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">18</td>
                        <td class="lead">Items / Products</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">19</td>
                        <td class="lead">Sales Orders</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">20</td>
                        <td class="lead">Purchase Orders</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">21</td>
                        <td class="lead">Customer Invoices</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">22</td>
                        <td class="lead">Vendor Bills</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">23</td>
                        <td class="lead">Customer Invoice Payment</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">24</td>
                        <td class="lead">Vendor Bills Payment</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">25</td>
                        <td class="lead">Suppliers Refund</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">26</td>
                        <td class="lead">Credit Note for Customers</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">27</td>
                        <td class="lead">Credit Note Payment/Allocation</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>

                    </tr>
                    <tr>
                        <td class="text-center">28</td>
                        <td class="lead">Suppliers Refund Payment & Allocation</td>
                        <td  style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td  style="width: 50px;"class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>

                    </tr>
                </tbody>
            </table>
        </div>
    </div>
   <!-- FOR  SHEET 2 -->
        <div style="margin-bottom: 65px;"></div>

    <div style="padding:48px 48px 48px 48px; margin-top: 35px; border-radius: 20px; margin-left: calc(-40vw + 50%); width: 80vw;"
        class="bg-white shadow-lg position-relative">
        <div class="container">
            <div class="fs-3 fw-bold lh-sm text-center pb-4"><span style="color: #01418F;">Additional Features Available ONLY in Synconics Xero-Odoo Connector</span>
            </div>
            <table class="table table-bordered align-middle table-striped table-hover m-0">
                <tbody>
                    <tr class="bg-300">
                        <th class="fw-bold fs-5 text-center" style="width: 50px;">#</th>
                        <th colspan="2" class="fw-bold fs-5">Additional Features</th>
                        <th class="fw-bold fs-5 text-center">Synconics</th>
                        <th class="fw-bold fs-5 text-center">Other Apps</th>
                    </tr>
                    <tr>
                        <td class="text-center">1</td>
                        <td colspan="2" class="lead">Import invoice without product from Xero to Odoo</td>
                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                    </tr>
                    <tr>
                        <td class="text-center">2</td>
                        <td colspan="2" class="lead">Supports Multi Company</td>
                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                    </tr>
                    <tr>
                        <td class="text-center">3</td>
                        <td colspan="2" class="lead">Supports Multi Currency</td>
                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                    </tr>
                    <tr>
                        <td class="text-center">4</td>
                        <td colspan="2" class="lead">Mismatch Log Files</td>
                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                    </tr>
                    <tr>
                        <td class="text-center">5</td>
                        <td colspan="2" class="lead">Restricts Contacts Duplication</td>
                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                    </tr>
                        <table class="table table-bordered align-middle table-striped table-hover m-0">
                            <tbody>
                                <tr class="bg-300">
                                    <td class="text-center" style="width: 50px;">6</td>
                                    <th colspan="2" class="fw-bold fs-5">Sync Data with Single Button for Import & Export Operations</th>
                                    <th class="fw-bold fs-5 text-center"><span class="d-none">other column1</span></th>
                                    <th class="fw-bold fs-5 text-center"><span class="d-none">other column12</span></th>
                                </tr>
                                <tr>
                                    <td class="text-center"></td>
                                    <td colspan="2" class="lead">Currencies</td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                </tr>
                                <tr>
                                    <td class="text-center"></td>
                                    <td colspan="2" class="lead">Chart of Accounts</td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                </tr>
                                <tr>
                                    <td class="text-center"></td>
                                    <td colspan="2" class="lead">Taxes</td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                </tr>
                                <tr>
                                    <td class="text-center"></td>
                                    <td colspan="2" class="lead">Bank Accounts</td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                </tr>
                                <tr>
                                    <td class="text-center"></td>
                                    <td colspan="2" class="lead">Manual Journals</td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                </tr>
                                <tr>
                                    <td class="text-center"></td>
                                    <td colspan="2" class="lead">Contacts</td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                </tr>
                                <tr>
                                    <td class="text-center"></td>
                                    <td colspan="2" class="lead">Products</td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                </tr>
                                <tr>
                                    <td class="text-center"></td>
                                    <td colspan="2" class="lead">Sales Orders</td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                </tr>
                                <tr>
                                    <td class="text-center"></td>
                                    <td colspan="2" class="lead">Purchase Orders</td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                </tr>
                                <tr>
                                    <td class="text-center"></td>
                                    <td colspan="2" class="lead">Invoices</td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                </tr>
                                <tr>
                                    <td class="text-center"></td>
                                    <td colspan="2" class="lead">Bills</td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                </tr>
                                <tr>
                                    <td class="text-center"></td>
                                    <td colspan="2" class="lead">Credit Note</td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                </tr>
                                <tr>
                                    <td class="text-center"></td>
                                    <td colspan="2" class="lead">Refunds</td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                </tr>
                                <tr>
                                    <td class="text-center"></td>
                                    <td colspan="2" class="lead">Inventory Adjustments</td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                </tr>
                                <tr>
                                    <td class="text-center"></td>
                                    <td colspan="2" class="lead">Attachments</td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                    <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                </tr>
                            </tbody>
                        </table>
                            <table class="table table-bordered align-middle table-striped table-hover m-0">
                                <tbody>
                                    <tr class="bg-300">
                                        <td class="text-center" style="width: 50px;">7</td>
                                        <th colspan="2" class="fw-bold fs-5">Export Attachment for</th>
                                        <th class="fw-bold fs-5 text-center"><span class="d-none">other column1</span></th>
                                        <th class="fw-bold fs-5 text-center"><span class="d-none">other column12</span></th>
                                    </tr>
                                    <tr>
                                        <td class="text-center"></td>
                                        <td colspan="2" class="lead">Contacts</td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center"></td>
                                        <td colspan="2" class="lead">Sales Orders</td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center"></td>
                                        <td colspan="2" class="lead">Purchase Orders</td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center"></td>
                                        <td colspan="2" class="lead">Invoices</td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center"></td>
                                        <td colspan="2" class="lead">Bills</td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center"></td>
                                        <td colspan="2" class="lead">Credit Note</td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center"></td>
                                        <td colspan="2" class="lead">Refunds</td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                    </tr>
                                </tbody>
                            </table>
                            <table class="table table-bordered align-middle table-striped table-hover m-0">
                                <tbody>
                                    <tr class="bg-300">
                                        <td class="text-center" style="width: 50px;">8</td>
                                        <th colspan="2" class="fw-bold fs-5">Draft or Approved Status Update for</th>
                                        <th class="fw-bold fs-5 text-center"><span class="d-none">other column1</span></th>
                                        <th class="fw-bold fs-5 text-center"><span class="d-none">other column12</span></th>
                                    </tr>
                                    <tr>
                                        <td class="text-center"></td>
                                        <td colspan="2" class="lead">Purchase Order</td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center"></td>
                                        <td colspan="2" class="lead">Sales Order</td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center"></td>
                                        <td colspan="2" class="lead">Invoice</td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center"></td>
                                        <td colspan="2" class="lead">Bills</td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center"></td>
                                        <td colspan="2" class="lead">Credit Note</td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                    </tr>
                                    <tr>
                                        <td class="text-center"></td>
                                        <td colspan="2" class="lead">Suppliers Refund</td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-check text-success" style="font-style: italic;"></i></td>
                                        <td style="width: 50px;" class="lead text-center"><i class="fa fa-times text-danger"></i></td>
                                    </tr>
                                </tbody>
                            </table>
                        </tbody>
                    </table>
                </div>
            </div>
        <div style="margin-bottom: 65px;"></div>
    <div  style="padding:48px 48px 48px 48px; margin-top:35px; border-radius:20px; margin-left:calc(-40vw + 50%); width:80vw" class="bg-white shadow-lg position-relative">

            <div class="fs-3 fw-bold lh-sm text-center pt-4 pb-5"><span style="color: #01418F;">Key Features</span></div>
            <div class="row gy-4">
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/automatic_manual_synch.png" class="img-fluid" alt="automatic & manual synchronization">
                        </div>
                        <h4 class="fw-bold mt-4">Automatic & Manual Synchronization</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/suooprts_multi_company .png" class="img-fluid" alt="supports multi company">
                        </div>
                        <h4 class="fw-bold mt-4">Supports Multi Company</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/supports_multi_currency.png" class="img-fluid" alt="supports multi currency">
                        </div>
                        <h4 class="fw-bold mt-4">Supports Multi Currency</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/error_log.png" class="img-fluid" alt="import export operations error logs">
                        </div>
                        <h4 class="fw-bold mt-4">Import Export Operations Error Logs</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/import_Currencies.png" class="img-fluid" alt="import currencies">
                        </div>
                        <h4 class="fw-bold mt-4">Import & Export Currencies </h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/import_export_chart_account.png" class="img-fluid" alt="import & export charts of accounts">
                        </div>
                        <h4 class="fw-bold mt-4">Import & Export Charts of Accounts</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/import_export_tax.png" class="img-fluid" alt="import & export tax/ tax rules">
                        </div>
                        <h4 class="fw-bold mt-4">Import & Export Tax/ Tax Rules</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/import_export_contact_group_tag.png" class="img-fluid" alt="import & export contact tags">
                        </div>
                        <h4 class="fw-bold mt-4">Import & Export Contact Groups/ Contact Tags</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/import_export_customer_suplier.png" class="img-fluid" alt="import & export contact customers">
                        </div>
                        <h4 class="fw-bold mt-4">Import & Export Contacts (Supplier/ Customers)</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/import_export_bank_account.png" class="img-fluid" alt="import & export bank accounts">
                        </div>
                        <h4 class="fw-bold mt-4">Import & Export Bank Accounts</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/import_journal.png" class="img-fluid" alt="import manual journal">
                        </div>
                        <h4 class="fw-bold mt-4">Import Manual Journal</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/import_exportitem_product.png" class="img-fluid" alt="import & export items/ products">
                        </div>
                        <h4 class="fw-bold mt-4">Import & Export Items/ Products</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/import_export_invoice_payment.png" class="img-fluid" alt="import & export invoices payment">
                        </div>
                        <h4 class="fw-bold mt-4">Import & Export Invoices with Payment</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/import_export_billwith_payment.png" class="img-fluid" alt="import & export bills payment">
                        </div>
                        <h4 class="fw-bold mt-4">Import & Export Bills with Payment</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/import_export_credit_supplier.png" class="img-fluid" alt="import & export creditnotes for suppliers">
                        </div>
                        <h4 class="fw-bold mt-4">Import & Export Credit notes for Suppliers</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/import_export_credit_note_customer.png" class="img-fluid" alt="import & export creditnotes for customers">
                        </div>
                        <h4 class="fw-bold mt-4">Import & Export Credit notes for Customers</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/export_inventory.png" class="img-fluid" alt="export inventory update">
                        </div>
                        <h4 class="fw-bold mt-4">Export Inventory Update (Adjustment)</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/import_export_sale_orders.png" class="img-fluid" alt="import & export sales orders">
                        </div>
                        <h4 class="fw-bold mt-4">Import & Export Sales Orders</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/importexport_purchase.png" class="img-fluid" alt="import export purchase orders">
                        </div>
                        <h4 class="fw-bold mt-4">Import Export Purchase Orders</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/export_attachments.png" class="img-fluid" alt="export attachments">
                        </div>
                        <h4 class="fw-bold mt-4">Export Attachments</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/bi_directinal_invoice_status_update.png" class="img-fluid" alt="bi-directional invoice status update">
                        </div>
                        <h4 class="fw-bold mt-4">Bi-Directional Invoice Status Update</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/bi_bill_status_update.png" class="img-fluid" alt="bi-directional bills Status Update">
                        </div>
                        <h4 class="fw-bold mt-4">Bi-Directional Bills Status Update</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/sale_order_status_update.png" class="img-fluid" alt="bi-directional sales order status update">
                        </div>
                        <h4 class="fw-bold mt-4">Bi-Directional Sales Order Status Update</h4>
                    </div>
                </div>
                <div class="col-3">
                    <div
                        class="rounded h-100 d-flex flex-column align-items-center justify-content-center text-center p-4 shadow border">
                        <div class="d-flex align-items-center justify-content-center"
                            style="height:65px; width: 65px;">
                            <img src="/img/purchase_order_status_update.png" class="img-fluid" alt="bi-directional purchase order status update">
                        </div>
                        <h4 class="fw-bold mt-4">Bi-Directional Purchase Order Status Update</h4>
                    </div>
                </div>
            </div>
        </div>
       <!--  </div>
    </div> -->
    <div style="margin-bottom: 65px;"></div>
    <div style="padding:83px 15px 25px 15px; margin-top: 35px; border-radius: 20px; margin-left: calc(-40vw + 50%); width: 80vw;"
        class="bg-white shadow-lg position-relative">
        <div class="align-items-center d-flex justify-content-center">
            <div class="bg-white fs-3 fw-bold lh-sm px-4 py-3 rounded shadow-lg text-center"
                style="margin-top: -165px;"><span style="color:#01418F"> User Guide</span>
            </div>
        </div>
        <div class="container-fluid" id="custom_feature_tabs">
             <div class="row g-0">
                 <div class="col-md-12">
                    <div class="p-4 pt-0">
                        <div class="border p-2 rounded shadow bg-300">
                            <ul role="tablist"
                                class="bg-transparent flex-row justify-content-around nav nav-pills"
                                data-tabs="tabs">
                                <li class="nav-item my-1">
                                    <a href="#user_guide_tabs_one" class="nav-link border-0 text-start lead active"
                                        data-bs-toggle="tab">Keys Generation</a>
                                </li>
                                <li class="nav-item my-1">
                                    <a href="#user_guide_tabs_tow" class="nav-link border-0 text-start lead"
                                        data-bs-toggle="tab">Configure in Odoo</a>
                                </li>
                                <li class="nav-item my-1">
                                    <a href="#user_guide_tabs_three" class="nav-link border-0 text-start lead"
                                        data-bs-toggle="tab">Import Xero to Odoo</a>
                                </li>
                                <li class="nav-item my-1">
                                    <a href="#user_guide_tabs_four" class="nav-link border-0 text-start lead"
                                        data-bs-toggle="tab">Export Odoo to Xero</a>
                                </li>
                                <li class="nav-item my-1">
                                    <a href="#user_guide_tabs_five" class="nav-link border-0 text-start lead"
                                        data-bs-toggle="tab">Multi Accounts Configure</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                 <div class="col">
                    <div class="p-4 ps-0 pt-0">
                        <div id="custom_feature_tabs_content" class="tab-content">
                            <div style="height: 1000px;" class="tab-pane overflow-y-scroll active"
                                id="user_guide_tabs_one">
                                <div class="fs-4 fw-bold lh-sm mb-3 text-center">Keys Generation on Xero Side</div>
                                <p class="lead">Go to: <b class="text-black fw-bold">https://developer.Xero.com/</b></p>
                                <p class="lead">Click on My Apps Tab</p>
                                <img src="./img/sync_xero_connector1.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector1">
                                <img src="./img/sync_xero_connector2.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector2">
                                <p class="lead"><b class="text-black fw-bold">Login to Your Account</b></p>
                                <p class="lead"><b class="text-black fw-bold">User name: </b> Xero account username</p>
                                <p class="lead"><b class="text-black fw-bold">Password: </b> Xero account password</p>
                                <p class="lead">Create a New Application:</p>
                                <p class="lead">These are Oauth 2.0 accounts on Xero,as explained below:</p>
                                <p class="fw-bold fs-5">OAuth Type 2.0</p>
                                <p class="lead">configure Oauth 2.0 Xero Account on Xero side: - </p>
                                <p class="lead">Input Application name.</p>
                                <p class="lead">Input Company or Application URL, for e.g. - https://www.synconics.com</p>
                                <p class="lead">Input OAuth2.0 redirect URL</p>
                                <p class="lead">Here you have to write your online Odoo URL, from which you are accessing our module.</p>
                                <img src="./img/sync_xero_connector3.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector3">
                                <p class="lead">When you create a new Application and save, it will generate a new Client ID and Client Secret. Client ID and Client Secret are used for connection between Xero and Odoo.</p>

                            </div>
                            <div style="height: 1000px;" class="tab-pane overflow-y-scroll"
                                id="user_guide_tabs_tow">
                                <div class="fs-4 fw-bold lh-sm mb-3 text-center">Configure in Odoo</div>
                                <p class="fw-bold fs-5">Xero configuration video link:- https://youtu.be/fV5XVvzCCC8</p>
                                <div class="fs-4 fw-bold lh-sm mb-3">Xero Account Configuration</div>
                                <div class="fs-4 fw-bold lh-sm mb-3">Go to <b class="text-black fw-bold">Accounting >> Invoicing >> Configuration >> Xero >> Xero Accounts</b></div>
                                <p class="lead">These are Oauth 2.0 accounts on Xero, as explained below:</p>
                                <img src="./img/1_updated.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector4">
                                <!-- <p class="fw-bold fs-5">OAuth2.0</p>
                                <p class="lead">Select as OAuth2.0 </p> -->
                                <p class="lead">Client ID, Client Secret and Call Back URI (which are copied from Xero application) are to be input as described in this screenshot. </p>
                                <p class="lead">Click on <b class="text-black fw-bold">"Generate OAuth2 URL"</b> button to generate Xero Authorization URL.</p>
                                <img src="./img/2.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector5">

                                <p class="lead">Click on <b class="text-black fw-bold">URL</b> and it will redirect you on Xero Authorization page.</p>
                                <img src="./img/3.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector6">

                                <p class="lead">Where by allowing access, you can get URL.</p>
                                <img src="./img/4.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector7">

                                <p class="lead">Copy generated URL and paste it in <b class="text-black fw-bold">"Raw URI"</b> field on Xero Account configuration in Odoo.</p>
                                <p class="lead">Click on <b class="text-black fw-bold">"AUTHENTICATION"</b> button to Authenticate your Account and select Import/Export Xero Organization for Import/Export Operations.</p>
                                <p class="lead"><b class="text-black fw-bold">Note: </b> You have to click on <b class="text-black fw-bold">"Authentication"</b> button only one time.</p>
                                <img src="./img/5.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector8">

                                <p class="lead">After that Import/Export Xero Orgabization option will be visibled, from there select Xero organization to map it with Odoo.</p>
                                <img src="./img/6.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector9">

                                <p class="fw-bold fs-5">Accounting Configuration in Odoo</p>
                                <p class="lead">Customer Invoice Journal will be used for importing invoices from Xero to Odoo.</p>
                                <p class="lead">Vendor Bill Journal will be used for importing Vendor Bills from Xero to Odoo.</p>
                                <p class="lead">Miscellaneous Operations Journal will be used for importing Manual Journal entries from Xero to Odoo.</p>
                                <img src="./img/7.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector10">

                                <p class="lead">Here for payment Journal on Incoming payments tab, Outgoing receipt account we will have to select bank or cash account based on Xero payment account type.</p>
                                <img src="./img/8.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector11">

                                <p class="lead">Here for payment Journal on outgoing payments tab, incoming receipt account we will have to select bank or cash account based on Xero payment account type.</p>
                                <img src="./img/9.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector12">

                                <p class="lead">You need to configure Xero Accounts on Odoo Accounting Journals (Journals which is used for payments in Odoo).</p>
                                <img src="./img/10.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector13">

                                 <p class="lead">On Setting page need to select all Journals which is used for Odoo as well as Xero.</p>
                                <img src="./img/11.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector14">

                                 <p class="fw-bold fs-5">Disable Export (Accounts & Taxes)</p>
                                 <p class="lead">If user doesn't want to export Odoo Charts of accounts, Taxes and Bank Accounts to Xero then user can enable this option.</p>
                                 <p class="lead"><b class="text-black fw-bold">Note: </b> Make sure in the system you have configured/used Xero Chart of Accounts and taxes (Imported from Xero) instead of Chart of Accounts and taxes.</p>
                                 <img src="./img/12.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector16">

                                <p class="fw-bold fs-5">Contact overwrite</p>
                                 <p class="lead">If user want to Same name & Email are same that time contact is overwrite both side then user can enable this option.</p>
                                 <img src="./img/13.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector17">
                            </div>
                            <div style="height: 1000px;" class="tab-pane overflow-y-scroll"
                                id="user_guide_tabs_three">
                                <div class="fs-4 fw-bold lh-sm mb-3 text-center">Import (Xero to Odoo)</div>
                                <p class="lead"><b class="text-black fw-bold">Currencies: </b> All Currency from Xero will be imported and export to Odoo.</p>
                                <p class="lead"><b class="text-black fw-bold">Xero: Settings >> General Settings >> Currencies</b> </p>
                                <img src="./img/import_1.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector18">

                                <img src="./img/import_2.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector19">

                                <p class="lead"><b class="text-black fw-bold">Odoo: Accounting >> Configuration >> Multi–Currencies >> Currencies.</b></p>

                                <img src="./img/import_3.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector20">


                                <p class="lead"><b class="text-black fw-bold">Taxes: </b> All Taxes with their components will be imported from Xero to Odoo at the time of Creation and Updation at Xero side.</p>
                                 <p class="lead"><b class="text-black fw-bold">Xero: Settings >> Looking for advanced settings >> Tax rates</b> </p>

                                <p class="lead"><b class="text-black fw-bold">Tax rates</b></p>
                                <img src="./img/import_4.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector22">

                                <p class="lead"><b class="text-black fw-bold">Odoo: Accounting >> Configuration >> Accounting >> Taxes</b></p>

                                <img src="./img/import_5.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector23">
                                <p class="lead"><b class="text-black fw-bold">Chart of Accounts: </b> All types of Charts of Accounts will be imported from Xero to Odoo at the time of creation and updation at Xero side.</p>
                                <p class="lead"><b class="text-black fw-bold">Xero: Settings >> Chart of Accounts</b> </p>
                                <img src="./img/import_6.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector24">

                                <p class="lead"><b class="text-black fw-bold">Odoo: Accounting >> Chart of Accounts</b></p>

                                <img src="./img/import_7.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector25">
                                <p class="lead"><b class="text-black fw-bold">Manual Journals: </b> Manual Journals are generally used for recording Credits and Debits of unique transactions which cannot be recorded directly.</p>
                                <p class="lead">Here all kinds of Manual Journals will be imported from Xero to Odoo.</p>
                                <p class="lead"><b class="text-black fw-bold">Xero: Reports >> All reports >> Journal Report >> Manual Journal</b> </p>
                                <img src="./img/import_8.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector26">

                                <p class="lead"><b class="text-black fw-bold">Odoo: Invoicing >> Accounting >> Accounting Entries >> Journal Entries</b></p>

                                <img src="./img/import_9.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector27">

                                <p class="lead"><b class="text-black fw-bold">Bank Accounts: </b> All kinds of Bank Accounts will be imported from Xero to Odoo.</p>
                                <p class="lead"><b class="text-black fw-bold">Xero: Accounts >> Bank Accounts</b> </p>

                                <img src="./img/import_10.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector28">
                                <p class="lead"><b class="text-black fw-bold">Note:</b> Install Contact Application at Odoo side to see below menu.</p>
                                <p class="lead"><b class="text-black fw-bold">Odoo: Contacts >> Configuration >> Bank Accounts >> Bank Accounts</b></p>
                                <img src="./img/import_11.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector29">
                                <p class="lead"><b class="text-black fw-bold">Contact Groups: </b> Entire Contact Groups will be imported from Xero to Odoo.</p>
                                <p class="lead"><b class="text-black fw-bold">Note:</b> Contact Groups are also get imported automatically when you import Contacts or Contact Groups.</p>
                                <p class="lead"><b class="text-black fw-bold">Xero: Contacts >> All Contacts >> Groups</b> </p>
                                <img src="./img/import_12.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector30">

                                <p class="lead"><b class="text-black fw-bold">Odoo: Contacts >> Configuration >> Contact Tags</b></p>
                                <p class="lead">You can see in Contact tags, this contact record is coming, even from the contact from view as well you will be able to see Sales Tag there.</p>
                                <img src="./img/import_13.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector31">
                                <p class="lead"><b class="text-black fw-bold">Contacts </b> All Contacts (Suppliers or Customers) will be imported from Xero to Odoo at the time of creation and updation at Xero side.</p>

                                <p class="lead"><b class="text-black fw-bold">Xero: Contacts >> All Contacts</b> </p>
                                <img src="./img/import_14.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector32">
                                <p class="lead"><b class="text-black fw-bold">Odoo:</b> </p>
                                <img src="./img/import_15.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector33">
                                <p class="lead"><b class="text-black fw-bold">Products: </b> Products with their Sale price, Cost price and Product Category(Track/Untrack) will be imported from Xero to Odoo at the time of creation and updation at Xero side.</p>
                                <ul>
                                    <li><p class="lead"><b class="text-black fw-bold">Track able Product Category -  </b> Will be useful if we want to track and manage stock of products.</p>
                                    </li>
                                    <li>
                                        <p class="lead"><b class="text-black fw-bold">Untrackable Product Category -  </b> Will be useful if we do not want to manage stock of products that is for consumable or service related products. Generally it is used with Consumable kind of products</p>
                                    </li>
                                </ul>
                                <p class="lead"><b class="text-black fw-bold">Odoo configuration: </b> Configure Product Category like Tracked Category and Untracked Category. All tracked products of Xero will be set under the "Tracked Category" and the rest of all will be set under <b class="text-black fw-bold">"Untracked Category".</b></p>
                                <img src="./img/import_16.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector34">

                                <p class="lead"><b class="text-black fw-bold">Untracked Products: </b></p>
                                <p class="lead"><b class="text-black fw-bold">Xero: >>  Business  >>  Products and Services </b></p>
                                <img src="./img/import_17.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector35">
                                <p class="lead"><b class="text-black fw-bold">Odoo: >>  Sales  >>  Sales >> Products </b></p>
                                <img src="./img/import_18.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector36">
                                <p class="lead"><b class="text-black fw-bold">Tracked Products: </b></p>
                                <p class="lead"><b class="text-black fw-bold">Xero:  >>  Business  >>  Products and Services </b></p>
                                <img src="./img/import_19.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector37">
                                <p class="lead"><b class="text-black fw-bold">Odoo: >>  Sales  >>  Sales >> Products </b></p>
                                <img src="./img/import_20.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector38">

                                <!-- sales -->
                                <div class="fs-4 fw-bold lh-sm mb-3 text-center">Sales Orders</div>
                                <p class="lead"><b class="text-black fw-bold">Quotes:</b> All Quotes from Xero will be imported to Odoo.</p>
                                <p class="lead"><b class="text-black fw-bold">Xero >> Business >> Quotes</b> </p>
                                <img src="./img/import_21.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector78">
                                <p class="lead"><b class="text-black fw-bold">Odoo: >> Sales >> Quotations</b></p>
                                <img src="./img/import_22.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector80">

                                <!-- purchase -->
                                <div class="fs-4 fw-bold lh-sm mb-3 text-center">Purchase Orders</div>
                                <p class="lead"><b class="text-black fw-bold">Purchase Orders:</b> All Purchase Order from Xero will be imported to Odoo.</p>
                                <p class="lead"><b class="text-black fw-bold">Xero >> Business >> Purchase Orders</b> </p>
                                <img src="./img/import_po_1.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector68">
                                 <p class="lead"><b class="text-black fw-bold">Odoo:  >>  Purchase  >> Orders >> Request For Quotations</b></p>
                                 <img src="./img/import_po_2.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector70">

                                <!-- invoice -->
                                <p class="lead"><b class="text-black fw-bold">Invoices: </b>Customer and Supplier Invoices with Taxes will be imported from Xero to Odoo.</p>
                                <p class="lead"><b class="text-black fw-bold">Xero: >>  Accounts  >>  Sales >> Invoices </b></p>
                                <img src="./img/import_23.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector39">
                                <p class="lead"><b class="text-black fw-bold">Odoo: >>  Accounting  >>  Sales >> Customer Invoices </b></p>
                                <img src="./img/import_24.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector40">
                                <p class="lead"><b class="text-black fw-bold">Invoice Payments: </b>It will also create payment lines in Odoo if invoices are paid fully or partially in Xero.</p>
                                <p class="lead"><b class="text-black fw-bold">Xero: >>  Accounts  >>  Sales >> Invoices </b></p>
                                <img src="./img/import_25.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector41">
                                <p class="lead"><b class="text-black fw-bold">Odoo: >>  Accounting  >>  Sales >> Customer Invoices </b></p>
                                <img src="./img/import_26.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector42">

                                <p class="lead"><b class="text-black fw-bold">Credit Notes: </b>Credit Notes (ACCRECCREDIT & ACCPAYCREDIT) will be imported from Xero to Odoo.</p>
                                <p class="lead"><b class="text-black fw-bold">Xero: >>  Accounts  >>  Sales >> Invoices >> Credit Note</b></p>
                                <img src="./img/import_27.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector43">
                                <p class="lead"><b class="text-black fw-bold">Odoo: >>  Invoicing  >>  Customers >> Credit Notes </b></p>
                                <img src="./img/import_28.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector44">
                                 <p class="lead"><b class="text-black fw-bold">Credit Note Cash Refund: </b>In Xero if you have Refund by cash then in Odoo it will create payment.</p>
                                <p class="lead"><b class="text-black fw-bold">Xero: >>  Accounts  >>  Sales >> Invoices >> Credit Note</b></p>
                                <img src="./img/import_29.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector45">
                                <p class="lead"><b class="text-black fw-bold">Odoo: >>  Invoicing  >>  Customers >> Credit Notes </b></p>
                                <img src="./img/import_30.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector46">
                            </div>

                            <div style="height: 1000px;" class="tab-pane overflow-y-scroll"
                                id="user_guide_tabs_four">
                                <div class="fs-4 fw-bold lh-sm mb-3 text-center">Export (Odoo to Xero)</div>
                                <p class="lead"><b class="text-black fw-bold">Currencies: </b> All Currency from Xero will be exported from Odoo to Xero.</p>
                                <p class="lead"><b class="text-black fw-bold">Odoo: Accounting >> Configuration >> Multi Currencies >> Currencies.</b></p>
                                <img src="./img/export_1.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector19">
                                <p class="lead"><b class="text-black fw-bold">Xero: Settings >> General Settings >> Currencies</b> </p>
                                <img src="./img/export_2.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector20">

                                 <p class="lead"><b class="text-black fw-bold">Taxes: </b>Taxes with their components will be exported from Odoo to Xero.</p>
                                 <p class="lead"><b class="text-black fw-bold">Odoo:  >>  Accounting  >> Configuration >>  Accounting  >> Taxes</b></p>
                                 <img src="./img/export_3.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector47">
                                 <p class="lead"><b class="text-black fw-bold">Xero:  >>  Settings  >> General Settings >>  Tax Rates</b></p>
                                 <img src="./img/export_4.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector48">

                                <p class="lead"><b class="text-black fw-bold">Chart of Accounts: </b>All types of Charts of Accounts will be exported from Odoo to Xero.</p>
                                 <p class="lead"><b class="text-black fw-bold">Odoo:  >>  Accounting  >> Adviser >>  Chart of Accounts</b></p>
                                <img src="./img/export_5.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector49">
                                 <p class="lead"><b class="text-black fw-bold">Xero:  >>  Settings  >> Chart of Accounts</b></p>
                                 <img src="./img/export_6.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector50">

                                <p class="lead"><b class="text-black fw-bold">Bank Accounts: </b>All kinds of Bank Accounts will be exported from Odoo to Xero.</p>
                                 <p class="lead"><b class="text-black fw-bold">Odoo:  >>  Contacts  >> Configuration >>  Bank Accounts >> Bank Accounts</b></p>
                                 <img src="./img/export_7.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector51">
                                 <p class="lead"><b class="text-black fw-bold">Xero:  >>  Accounts  >> Bank Accounts</b></p>
                                <img src="./img/export_8.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector52">

                                <p class="lead"><b class="text-black fw-bold">Contact Groups: </b>Entire Contact Groups will be exported from Odoo to Xero.</p>
                                 <p class="lead"><b class="text-black fw-bold">Note: </b>Contact Groups are also get exported automatically when you export Contacts or Contact Groups.</p>

                                 <p class="lead"><b class="text-black fw-bold">Odoo:  >>  Contacts  >> Configuration >> Contact Tags</b></p>
                                <img src="./img/export_9.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector53">
                                 <p class="lead"><b class="text-black fw-bold">Xero:  >>  Contacts  >> All Contacts >> Groups Contacts</b></p>
                                 <img src="./img/export_10.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector54">

                                <p class="lead"><b class="text-black fw-bold">Contacts: </b>All Contacts (Suppliers or Customers) will be exported from Odoo to Xero at the time of creation and updation at Odoo side.</p>
                                 <p class="lead"><b class="text-black fw-bold">Odoo:  >>  Accounting  >> Sales >> Customer</b></p>
                                 <img src="./img/export_11.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector55">
                                 <p class="lead"><b class="text-black fw-bold">Xero:  >>  Contacts  >> All Contacts</b></p>
                                 <img src="./img/export_12.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector56">

                                <p class="lead"><b class="text-black fw-bold">Products: </b>Products with their Sale price,Cost price and Category will be exported from Odoo to Xero at the time of creation and updation at Odoo side.</p>
                                <p class="lead"><b class="text-black fw-bold"> - Untracked Products: </b>In Odoo if product/product category Inventory valuation method is <b class="text-black fw-bold">"Manual"</b> then it will created as Untracked product in Xero.</p>
                                 <p class="lead"><b class="text-black fw-bold">Odoo:  >>  Sales  >> Sales >> Products</b></p>
                                <img src="./img/export_13.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector57">
                                 <p class="lead"><b class="text-black fw-bold">Xero:  >>  Business  >> Products and Services</b></p>
                                 <img src="./img/export_14.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector58">
                                <p class="lead"><b class="text-black fw-bold"> - Tracked Products: </b></p>
                                <p class="lead"><b class="text-black fw-bold">Chart of Accounts Configuration: </b>For Assets Account which is configured on products or product categories, user needs to tick an option named <b class="text-black fw-bold">"Use As Inventory In Xero"</b> on Chart of Accounts.</p>
                                 <p class="lead"><b class="text-black fw-bold">Odoo:  >>  Invoicing  >> Configuartion >> Accounting >> Chart of Accounts</b></p>
                                 <img src="./img/export_15.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector59">
                                 <p class="lead">In Odoo if product/product category Inventory valuation method is "Automated" then it will be created as Tracked product in Xero.</p>
                                 <p class="lead">For Tracked product if qty will be increasing or decreasing in Odoo using below option it will be reflected in Xero.</p>
                                 <ul>
                                     <li>Buying and selling inventory (Bills and Invoices).</li>
                                     <li>By Inventory Adjustments.</li>
                                     <li>By Manufacturing process.</li>
                                 </ul>
                                 <p class="lead"><b class="text-black fw-bold">Odoo:  >>  Sales  >> Sales >> Products</b></p>
                                 <img src="./img/export_16.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector60">
                                 <p class="lead"><b class="text-black fw-bold">Xero:  >>  Business  >> Products and Services</b></p>
                                 <img src="./img/export_17.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector61">

                                <div class="fs-4 fw-bold lh-sm mb-3 text-center">Sales Orders</div>
                                <p class="lead"><b class="text-black fw-bold"> Quotes:</b> All Quotes will be exported from Odoo to Xero.</p>
                                <p class="lead"><b class="text-black fw-bold">Odoo: >> Sales >> Quotations</b></p>
                                <img src="./img/export_sale_1.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector81">
                                <p class="lead"><b class="text-black fw-bold">Xero: >> Business >> Quotes</b></p>
                                <img src="./img/export_sale_2.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector83">

                                <div class="fs-4 fw-bold lh-sm mb-3 text-center">Purchase Orders</div>
                                <p class="lead"><b class="text-black fw-bold">Purchase Orders:</b> All Purchase Order from Xero will be exported from Odoo to Xero.</p>
                                <p class="lead"><b class="text-black fw-bold">Odoo:  >>  Purchase  >> Orders >> Request For Quotations</b></p>
                                <img src="./img/export_purchase_1.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector68">
                                <p class="lead"><b class="text-black fw-bold">Xero >> Business >> Purchase Orders</b> </p>
                                <img src="./img/export_purchase_2.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector70">

                                <p class="lead"><b class="text-black fw-bold">Invoices: </b>Customers and Suppliers Invoices with related Taxes will be exported from Odoo to Xero.</p>
                                 <p class="lead"><b class="text-black fw-bold">Odoo:  >>  Accounting  >> Sales >> Customer Invoices</b></p>
                                 <!-- <p class="lead">when you have changed <b class="text-black fw-bold">“Line Amount Type”</b> you need to perform <b class="text-black fw-bold">“Calculate Tax”</b> Operation.</p> -->
                                <img src="./img/export_invoice_1.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector62">

                                 <p class="lead"><b class="text-black fw-bold">Xero:  >>  Accounting  >> Sales >> Invoices</b></p>
                                <img src="./img/export_invoice_2.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector63">

                                <p class="lead"><b class="text-black fw-bold">Payments: </b>Customers and Suppliers Invoices with related Payments will be exported from Odoo to Xero.</p>
                                 <p class="lead"><b class="text-black fw-bold">Odoo:  >>  Accounting  >> Sales >> Customer Invoices</b></p>
                                <img src="./img/export_invoice_payment_1.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector62">

                                 <p class="lead"><b class="text-black fw-bold">Xero:  >>  Accounting  >> Sales >> Invoices</b></p>
                                <img src="./img/export_invoice_payment_2.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector63">

                                <p class="lead"><b class="text-black fw-bold">Credit Notes: </b>Credit Notes (ACCRECCREDIT & ACCPAYCREDIT) with payments will be exported from Odoo to Xero.</p>
                                <p class="lead"><b class="text-black fw-bold">Odoo: >>  Invoicing  >>  Customers >> Credit Notes </b></p>
                                <img src="./img/export_cn_1.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector44">
                                <p class="lead"><b class="text-black fw-bold">Xero: >>  Accounts  >>  Sales >> Invoices >> Credit Note</b></p>
                                <img src="./img/export_cn_2.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector43">

                                <p class="lead"><b class="text-black fw-bold">Inventory Adjustments: </b> Product Inventory will be exported from Odoo to Xero.</p>
                                <p class="lead"><b class="text-black fw-bold">Odoo:  >>  Sales  >> Sales >> Products </b></p>
                                <img src="./img/export_inventory_1.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector44">
                                <p class="lead"><b class="text-black fw-bold">Xero:  >>  Business  >> Products and Services </b></p>
                                <img src="./img/export_inventory_2.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector43">

                                <p class="lead"><b class="text-black fw-bold">Attachments: </b>Attachments of Customer Invoices/Vendor Bills will be exported from Odoo to Xero.</p>
                                <p class="lead"><b class="text-black fw-bold">Notes: </b>Maximum upload limit for one Attachment is 3 MB.</p>
                                <p class="lead">Apart from customer Invoices and Vendor bills, attachments can be exported to :</p>
                                <ul>
                                    <li>Contacts</li>
                                    <li>Credit note - Customers and Suppliers</li>
                                    <li>Purchase orders</li>
                                    <li>Sales Orders</li>
                                </ul>
                                <img src="./img/export_attachment_1.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector64">

                                 <p class="lead"><b class="text-black fw-bold">Xero: </b></p>
                                 <img src="./img/export_attachment_2.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector65">
                            </div>
                            <div style="height: 600px;" class="tab-pane overflow-y-scroll"
                                id="user_guide_tabs_five">
                                <div class="fs-4 fw-bold lh-sm mb-3 text-center">Multi Accounts Configuration</div>
                                <p class="lead">If you have multiple Xero Accounts and you want to integrate Odoo with those multiple Xero Accounts, then you need to create multiple Accounts (company wise) in Odoo as well.</p>
                                <p class="lead">Go to<b class="text-black fw-bold"> Accounting >> Configuration >> Xero >> Xero Accounts</b></p>
                                <img src="./img/multicompany.png"
                                    class="img-fluid py-2 border rounded shadow mb-5" alt="sync_xero_connector66">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- NEW TERMS AND CONDITION START -->
                <div style="margin-bottom: 65px;"></div>
                <div style="padding:83px 15px 25px 15px; margin-top: 35px; border-radius: 20px; margin-left: calc(-40vw + 50%); width: 80vw;"
                    class="bg-white shadow-lg position-relative">
                    <div class="align-items-center d-flex justify-content-center">
                        <div class="bg-white fs-3 fw-bold lh-sm px-4 py-3 rounded shadow-lg text-center"
                            style="margin-top: -165px;"><span style="color:#01418F"> * Terms & Conditions</span>
                        </div>
                    </div>
                <div class="container-fluid">
                     <div class="row g-0">

                         <div class="col">
                            <div class="p-4 pt-0">
                                    <div style="border-radius:20px;" class="bg-white mb-3 p-4 shadow-lg">
                                        <div class="custom_aditional_condition">
                                            <ul class="list-unstyled mb-0">
                                                <li class="align-items-lg-baseline d-flex py-1 fs-5"><img src="./img/odoo-logo.png"
                                                        class="img-fluid me-2" alt="Round" style="width: 15px;">
                                                    One time free configurtion setup will be done for Xero-Odoo integration.
                                                </li>
                                                <li class="align-items-lg-baseline d-flex py-1 fs-5"><img src="./img/odoo-logo.png"
                                                        class="img-fluid me-2" alt="Round" style="width: 15px;">
                                                    The configuration setup will ONLY be established between Xero and Odoo using the HTTPS protocol. Without HTTPS protocal the connection won't be establish.
                                                </li>
                                                <li class="align-items-lg-baseline d-flex py-1 fs-5"><img src="./img/odoo-logo.png"
                                                        class="img-fluid me-2" alt="Round" style="width: 15px;">
                                                    The free configuration does not include support for data import or export operations.
                                                </li>
                                                <li class="align-items-lg-baseline d-flex py-1 fs-5"><img src="./img/odoo-logo.png"
                                                        class="img-fluid me-2" alt="Round" style="width: 15px;">
                                                    Due to API restrictions, certain bidirectional integrations are limited to either import or export operations, not both.
                                                </li>
                                                <li class="align-items-lg-baseline d-flex py-1 fs-5"><img src="./img/odoo-logo.png"
                                                        class="img-fluid me-2" alt="Round" style="width: 15px;">
                                                    90 Days bug free support is only provided for the bugs reported in our module tested with Odoo vanilla system, withour any third-party modules.
                                                </li>
                                                <li class="align-items-lg-baseline d-flex py-1 fs-5"><img src="./img/odoo-logo.png"
                                                        class="img-fluid me-2" alt="Round" style="width: 15px;">
                                                    Free support will be provided from Monday to Friday, 10:30 AM to 7 PM IST (Indian Standard Time).
                                                </li>
                                                <li class="align-items-lg-baseline d-flex py-1 fs-5"><img src="./img/odoo-logo.png"
                                                        class="img-fluid me-2" alt="Round" style="width: 15px;">
                                                    Free support will not be provided during Indian public holidays or company holidays.
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
        </div>
    </div>
    <!-- NEW TERMS AND CONDITION END -->
    <div style="margin-top: 35px; border-radius: 20px; margin-left: calc(-40vw + 50%); width: 80vw;" class="bg-white shadow-lg position-relative">
        <div id="custom_tabs">
            <!-- Static Div -->
            <div style="padding-top:48px">
                <ul role="tablist" class="nav nav-tabs justify-content-center bg-transparent navbar-light"
                    data-tabs="tabs">
                    <li class="nav-item"><a href="#faq" class="nav-link fs-4 text-uppercase active"
                            data-bs-toggle="tab">FAQ</a>
                    </li>
                    <li class="nav-item"><a href="#tandc" class="nav-link fs-4 text-uppercase"
                            data-bs-toggle="tab">Terms &
                            Conditions</a>
                    </li>
                </ul>
            </div>
            <div style="padding:30px 48px 48px 48px;">
                <div class="container">
                    <div id="custom_tabs_content" class="tab-content">
                        <!-- Add Module Related FAQ -->
                        <div class="tab-pane active" id="faq">
                            <div class="accordion accordion-flush" id="accordionExampleCustom">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqStart">
                                        <a class="accordion-button collapsed py-3 text-black px-2 fs-5"
                                            data-bs-toggle="collapse" href="#collapseFaqStart"
                                            aria-expanded="true" aria-controls="collapseFaqStart">
                                            I am not just clear on the account mapping on both ends, am I supposed to remap it again whenever I add on any data?
                                        </a>
                                    </h2>
                                    <div id="collapseFaqStart" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqStart"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">As for different countries, Xero maintains different kinds of charts of accounts and if some Chart of Accounts and Taxes names will be same at Odoo side also then we have to, first of all, rename them and then you will be able to map Chart of Accounts and Tax again for Import or an Export process. So basically it is a one-time mapping process only, you do not have to map Charts of Accounts and Taxes each and every time.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqOne">
                                        <a class="accordion-button py-3 text-black px-2 fs-5 collapsed"
                                            data-bs-toggle="collapse" href="#collapseFaqOne"
                                            aria-expanded="false" aria-controls="collapseFaqOne">
                                            Can I Import and Export Vendor bills?
                                        </a>
                                    </h2>
                                    <div id="collapseFaqOne" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqOne"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">
                                                Yes you can easily Import and Export vendor bills.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqTwo">
                                        <a class="accordion-button py-3 text-black px-2 fs-5 collapsed"
                                            data-bs-toggle="collapse" href="#collapseFaqTwo"
                                            aria-expanded="false" aria-controls="collapseFaqTwo">
                                            Are all Vendor Bills and Invoices marked as drafts when imported across? Even historic?
                                        </a>
                                    </h2>
                                    <div id="collapseFaqTwo" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqTwo"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">No, you also have to map stages for Customer Invoices and Vendor Bills here, so basically the Import and Export process system will also map stages and display records only on those relevant stages at Odoo side.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqThree">
                                        <a class="accordion-button py-3 text-black px-2 fs-5 collapsed"
                                            data-bs-toggle="collapse" href="#collapseFaqThree"
                                            aria-expanded="false" aria-controls="collapseFaqThree">
                                            Does your module provide an automatic import and export process without manual intervention?
                                        </a>
                                    </h2>
                                    <div id="collapseFaqThree" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqThree"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">Yes , in our module , we have also provided automated schedulers here for import and export processes automatically at the given time duration/ temur.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqFor">
                                        <a class="accordion-button py-3 text-black px-2 fs-5 collapsed"
                                            data-bs-toggle="collapse" href="#collapseFaqFor"
                                            aria-expanded="false" aria-controls="collapseFaqFor">
                                            Does your module provide automatic Import and Export functionality?
                                        </a>
                                    </h2>
                                    <div id="collapseFaqFor" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqFor"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">Yes we have provided automated schedulers to automate import and export functionality in our module.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqFive">
                                        <a class="accordion-button py-3 text-black px-2 fs-5 collapsed"
                                            data-bs-toggle="collapse" href="#collapseFaqFive"
                                            aria-expanded="false" aria-controls="collapseFaqFive">
                                            Does your module support a multi-company scenario?
                                        </a>
                                    </h2>
                                    <div id="collapseFaqFive" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqFive"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">Yes, our module supports multi-company scenarios, you can configure different Xero Accounts for different companies here.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqsix">
                                        <a class="accordion-button py-3 text-black px-2 fs-5 collapsed"
                                            data-bs-toggle="collapse" href="#collapseFaqsix"
                                            aria-expanded="false" aria-controls="collapseFaqsix">
                                            Does your module support Mismatch Logs or Mismatch Historical Data?
                                        </a>
                                    </h2>
                                    <div id="collapseFaqsix" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqsix"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">Yes, our module supports Mismatch Logs when any serious warning prompts users to do import and export operation, you can find basic details from created Mismatch Logs lines.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqseven">
                                        <a class="accordion-button py-3 text-black px-2 fs-5 collapsed"
                                            data-bs-toggle="collapse" href="#collapseFaqseven"
                                            aria-expanded="false" aria-controls="collapseFaqseven">
                                            Do you have full documentation for this module?
                                        </a>
                                    </h2>
                                    <div id="collapseFaqseven" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqseven"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">When you purchase this module then you will find a complete PDF guide with every import and export operations along with Screenshots in detail. For the complete functional flow of our module, you can refer to this index file or see our functional video.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqeight">
                                        <a class="accordion-button py-3 text-black px-2 fs-5 collapsed"
                                            data-bs-toggle="collapse" href="#collapseFaqeight"
                                            aria-expanded="false" aria-controls="collapseFaqeight">
                                            I want to process inventory adjustments automatically at Odoo side. How to do that?
                                        </a>
                                    </h2>
                                    <div id="collapseFaqeight" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqeight"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">We have provided this functionality by default for Import Operations and Custom functionality for Export Operations as well.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqnine">
                                        <a class="accordion-button py-3 text-black px-2 fs-5 collapsed"
                                            data-bs-toggle="collapse" href="#collapseFaqnine"
                                            aria-expanded="false" aria-controls="collapseFaqnine">
                                            I have so much data as well as Customer Invoices and Vendor Bills in my Xero Account, is your module capable of importing and exporting so much data?
                                        </a>
                                    </h2>
                                    <div id="collapseFaqnine" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqnine"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">Yes, we have optimized and developed this module in such a way, that it will work with a huge amount of data. We have boosted up the speed for API calling for import/export operations, you can also see this section in the starting of this Index file, you can find detailed information from there also.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqten">
                                        <a class="accordion-button py-3 text-black px-2 fs-5 collapsed"
                                            data-bs-toggle="collapse" href="#collapseFaqten"
                                            aria-expanded="false" aria-controls="collapseFaqten">
                                            What are Mismatch Logs ?
                                        </a>
                                    </h2>
                                    <div id="collapseFaqten" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqten"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">Mismatched Logs contain details of all record processes which have found mismatch while import/export data.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqeleven">
                                        <a class="accordion-button py-3 text-black px-2 fs-5 collapsed"
                                            data-bs-toggle="collapse" href="#collapseFaqeleven"
                                            aria-expanded="false" aria-controls="collapseFaqeleven">
                                           I or my client just wants import operations, he/she doesn't want export operations or vice versa.
                                        </a>
                                    </h2>
                                    <div id="collapseFaqeleven" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqeleven"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">Yes, you can disable or archive Export Scheduler so that the system will not do export operations from Odoo to Xero or vice versa.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqtwelve">
                                        <a class="accordion-button py-3 text-black px-2 fs-5 collapsed"
                                            data-bs-toggle="collapse" href="#collapseFaqtwelve"
                                            aria-expanded="false" aria-controls="collapseFaqtwelve">
                                            Justify me this Scenario - I have not imported the newly created Customer/Vendor from Xero to Odoo till now, now I have created an Invoice/Vendor Bill from the Xero side for that related Customer and directly imported Invoice from Xero to Odoo, now How Odoo will react then?
                                        </a>
                                    </h2>
                                    <div id="collapseFaqtwelve" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqtwelve"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">We have developed our module in such a way so that when any Entity is dependent on another Entity and while doing operations directly for a second entity then the system will find all dependencies for that record and automatically create them.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqthirteen">
                                        <a class="accordion-button py-3 text-black px-2 fs-5 collapsed"
                                            data-bs-toggle="collapse" href="#collapseFaqthirteen"
                                            aria-expanded="false" aria-controls="collapseFaqthirteen">
                                            I have so many Invoices created at the Xero side so when I import them to Odoo, will my system create any errors or hang?
                                        </a>
                                    </h2>
                                    <div id="collapseFaqthirteen" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqthirteen"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">Not at all , with our optimized code and pagination techniques, we have already tested this module for huge amounts of data so no need to panic for it.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqfourteen">
                                        <a class="accordion-button py-3 text-black px-2 fs-5 collapsed"
                                            data-bs-toggle="collapse" href="#collapseFaqfourteen"
                                            aria-expanded="false" aria-controls="collapseFaqfourteen">
                                            We have seen your module. We want some additional customization in your module according to my or my client's requirements, how can I proceed?
                                        </a>
                                    </h2>
                                    <div id="collapseFaqfourteen" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqfourteen"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">Of course, our experienced developer team is ready for any kind of customization ,feel free to contact us. Kindly Email us your <NAME_EMAIL> can also ping us on our Skype Account - synconics.technologies</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqfifteen">
                                        <a class="accordion-button py-3 text-black px-2 fs-5 collapsed"
                                            data-bs-toggle="collapse" href="#collapseFaqfifteen"
                                            aria-expanded="false" aria-controls="collapseFaqfifteen">
                                            I want to see Live Preview for this module related functionality
                                        </a>
                                    </h2>
                                    <div id="collapseFaqfifteen" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqfifteen"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">For seeing Live Preview for this module related functionality , kindly Email us your <NAME_EMAIL>. You can also ping us on our Skype Account - synconics.technologies</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFaqseventeen">
                                        <a class="accordion-button py-3 text-black px-2 fs-5 collapsed"
                                            data-bs-toggle="collapse" href="#collapseFaqseventeen"
                                            aria-expanded="false" aria-controls="collapseFaqseventeen">
                                            I want to talk with you regarding commercial or other terms and conditions and Support
                                        </a>
                                    </h2>
                                    <div id="collapseFaqseventeen" class="accordion-collapse collapse"
                                        aria-labelledby="headingFaqseventeen"
                                        data-bs-parent="#accordionExampleCustom">
                                        <div class="accordion-body">
                                            <p class="fs-5">At the bottom of this Index file there is a section named Free Support in which we have clarified our terms and conditions, you can refer to that. If you still have any query then kindly Email <NAME_EMAIL>. You can also ping us on our Skype Account - synconics.technologies</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Static Div -->
                        <div class="tab-pane" id="tandc">
                            <ul class="list-unstyled mb-0">
                                <li class="align-items-lg-baseline d-flex py-1 fs-5"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 15px;">
                                    All the apps are to be purchased separately, for individual versions and
                                    individual edition.
                                </li>
                                <li class="align-items-lg-baseline d-flex py-1 fs-5"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 15px;">
                                    One month free bug support period will be offered pertaining to any one
                                    server used, testing or live.
                                </li>
                                <li class="align-items-lg-baseline d-flex py-1 fs-5"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 15px;">
                                    Synconics is not responsible for installation or updating of the module on
                                    any of your server.
                                </li>
                                <li class="align-items-lg-baseline d-flex py-1 fs-5"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 15px;">
                                    If an error appears in our app due to other custom modules installed in your
                                    system or if Odoo base source code is not updated on your system, to make it
                                    compatible with your source code, we'll charge you for our efforts to
                                    resolve those errors and make the module compatible with your source code.
                                </li>
                                <li class="align-items-lg-baseline d-flex py-1 fs-5"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 15px;">
                                    Please refer the document for configuration. If any support is needed for
                                    module configuration, installation, updating or any help is needed, it will
                                    be considered in paid support.
                                </li>
                                <li class="align-items-lg-baseline d-flex py-1 fs-5"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 15px;">
                                    Please note that you are not allowed to distribute or resell this module
                                    after purchase.
                                </li>
                                <li class="align-items-lg-baseline d-flex py-1 fs-5"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 15px;">
                                    This module is tested and working on Odoo vanilla with Ubuntu OS.
                                </li>
                                <li class="align-items-lg-baseline d-flex py-1 fs-5"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 15px;">
                                    Support services will be provided from Monday to Friday, 10:30 AM to 7 PM
                                    IST (Indian Standard Time).
                                </li>
                                <li class="align-items-lg-baseline d-flex py-1 fs-5"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 15px;">
                                    Support will not be provided during Indian public holidays or company
                                    holidays.
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div style="margin-bottom: 65px;"></div>
    <!-- Add Suggested Apps Depends on Current Module Category -->
    <div style="padding:85px 48px 32px 48px; margin-top: 35px; border-radius: 20px; margin-left: calc(-40vw + 50%); width: 80vw;"
        class="bg-white shadow-lg position-relative">
        <div class="align-items-center d-flex justify-content-center">
            <div class="bg-white fs-3 fw-bold lh-sm px-4 py-3 rounded shadow-lg text-center"
                style="margin-top: -165px;">Suggested<span style="color:#01418F"> Apps</span></div>
        </div>
        <div class="container">
            <div class="row gx-5 gy-5 mb-4">
                <div class="col-4">
                    <a href="https://apps.odoo.com/apps/modules/17.0/authorize_net" target="_blank"
                        rel="noopener noreferrer"> <img src="./img/authorizeNet_sync.png"
                            class="img-fluid shadow rounded-3" alt="Apps"></a>
                </div>
                <div class="col-4">
                    <a href="https://apps.odoo.com/apps/modules/13.0/sync_payment_paytabs/" target="_blank"
                        rel="noopener noreferrer"> <img src="./img/paytabs_sync.png"
                            class="img-fluid shadow rounded-3" alt="Apps"></a>
                </div>
                <div class="col-4">
                    <a href="https://apps.odoo.com/apps/modules/17.0/sync_payment_braintree/" target="_blank"
                        rel="noopener noreferrer"> <img src="./img/braintree_sync.png"
                            class="img-fluid shadow rounded-3" alt="Apps"></a>
                </div>
                <div class="col-4">
                    <a href="https://apps.odoo.com/apps/modules/17.0/sync_payment_moneris" target="_blank"
                        rel="noopener noreferrer"> <img src="./img/moneris_sync.png"
                            class="img-fluid shadow rounded-3" alt="Apps"></a>
                </div>
                <div class="col-4">
                    <a href="https://apps.odoo.com/apps/modules/13.0/sync_payment_beanstream/" target="_blank"
                        rel="noopener noreferrer"> <img src="./img/bambora_beanstream_sync.png"
                            class="img-fluid shadow rounded-3" alt="Apps"></a>
                </div>
                <div class="col-4">
                    <a href="https://apps.odoo.com/apps/modules/13.0/stripe_charge/" target="_blank"
                        rel="noopener noreferrer"> <img src="./img/stripe_charge_sync.png"
                            class="img-fluid shadow rounded-3" alt="Apps"></a>
                </div>
            </div>
            <div class="row justify-content-center pt-4 pb-2">
                <div class="col-auto">
                    <a class="align-items-center fw-bold text-black fs-4 bg-200 border btn d-flex py-2 rounded"
                        target="_blank" href="https://apps.odoo.com/apps/browse?repo_maintainer_id=127038"><img
                            style="height: 48px;" src="./img/our_apps.png" class="img-fluid me-2"
                            alt="Our Apps">
                        Our Apps</a>
                </div>
                <div class="col-auto">
                    <a class="align-items-center fw-bold text-black fs-4 bg-200 border btn d-flex py-2 rounded"
                        href="https://apps.odoo.com/apps/themes/browse?repo_maintainer_id=127038"
                        target="_blank"><img style="height: 48px;" src="./img/our_theme.png"
                            class="img-fluid me-2" alt="Our Themes">Our Themes</a>
                </div>
            </div>
        </div>
    </div>
    <!-- Static Div -->
    <div style="margin-top: 35px; border-radius: 20px; margin-left: calc(-40vw + 50%); width: 80vw;" class="bg-white shadow-lg position-relative">
        <div style="padding:48px 48px 48px 48px;">
            <div class="container">
                <div class="fs-3 fw-bold lh-sm text-center pb-4">Odoo <span style="color: #01418F;"> Expert
                        Services </span>
                </div>
                <div class="container">
                    <div class="row align-items-center justify-content-center">
                        <div class="col-3">
                            <ul class="fs-5 fw_medium list-unstyled mb-0">
                                <li class="align-items-center d-flex py-1"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 11px;">Configuration
                                </li>
                                <li class="align-items-center d-flex py-1"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 11px;"> Odoo Hosting


                                </li>
                                <li class="align-items-center d-flex py-1"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 11px;">Customisation
                                </li>
                                <li class="align-items-center d-flex py-1"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 11px;">Functional
                                    Training</li>
                                <li class="align-items-center d-flex py-1"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 11px;">Technical
                                    Training</li>
                            </ul>
                        </div>
                        <div class="col-3">
                            <ul class="fs-5 fw_medium list-unstyled mb-0">
                                <li class="align-items-center d-flex py-1"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 11px;">Support &
                                    Maintenance

                                </li>
                                <li class="align-items-center d-flex py-1"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 11px;">Dedicated
                                    Developer
                                </li>
                                <li class="align-items-center d-flex py-1"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 11px;">Implementation
                                </li>
                                <li class="align-items-center d-flex py-1"><img src="./img/odoo-logo.png"
                                        class="img-fluid me-2" alt="Round" style="width: 11px;">Odoo Migration

                                </li>
                                <li class="align-items-center d-flex py-1"><img src="./img/odoo-logo.png"
                                    class="img-fluid me-2" alt="Round" style="width: 11px;"> API Integration
                            </li>
                            </ul>
                        </div>
                        <div class="col-6">
                            <div class="row gy-4 align-items-center">
                                <div class="col-6 text-center">
                                    <div class="fs-4 fw-bold" style="color: #01418F;"> 300+</div>
                                    <div class="fs-5 fw_medium">Odoo Implementation</div>
                                </div>
                                <div class="col-6 text-center">
                                    <div class="fs-4 fw-bold" style="color: #01418F;"> 250+
                                    </div>
                                    <div class="fs-5 fw_medium">Custom Odoo Apps
                                    </div>
                                </div>
                                <div class="col-6 text-center">
                                    <div class="fs-4 fw-bold" style="color: #01418F;">13+
                                    </div>
                                    <div class="fs-5 fw_medium">Years of Experience
                                    </div>
                                </div>
                                <div class="col-6 text-center">
                                    <div class="fs-4 fw-bold" style="color: #01418F;">50+
                                    </div>
                                    <div class="fs-5 fw_medium">Industry Expertise
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <div class="w-100" style="height: 1px; background-color: #A24689;"></div>
        </div>
        <div style="padding:48px 48px 48px 48px;">
            <div class="container">
                <div class="fs-3 fw-bold lh-sm text-center pb-4">Get In Touch </div>
                <div class="container">
                    <div class="row justify-content-center align-items-center gx-xl-5">
                        <div class="col">
                            <img src="./img/odoo_certificate.png" class="img-fluid mx-auto"
                                style="width: 280px;" alt="Odoo Certificate">
                        </div>
                        <div class="col">
                            <div class="fs-5 fw_medium list-unstyled mb-0">
                                <a href="skype:synconics.technologies?chat" target="_self" rel="nofollow"
                                    class="align-items-center d-flex my-3 text-black">
                                    <img src="./img/skype.png" class="img-fluid me-3" alt="Skype"
                                        style="width: 40px;">
                                    <div class="text-black"> synconics.technologies</div>
                                </a>
                                <a href="mailto:<EMAIL>?subject=%20Product%20Support."
                                    target="_self" rel="nofollow"
                                    class="align-items-center d-flex my-3 text-black">
                                    <img src="./img/mail.png" class="img-fluid me-3" alt="Mail"
                                        style="width: 40px;">
                                    <div class="text-black"> <EMAIL></div>
                                </a>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="fs-5 fw_medium list-unstyled mb-0">
                                <a href="tel:+91 9426864274" target="_blank" rel="nofollow"
                                    class="align-items-center d-flex my-3 text-black">
                                    <img src="./img/whatsapp.png" class="img-fluid me-3" alt="Whatsapp"
                                        style="width: 40px;">
                                    <div class="text-black">+91 9426864274</div>
                                </a>
                                <a href="https://synconics.com" target="_self" rel="nofollow"
                                    class="align-items-center d-flex my-3 text-black">
                                    <img src="./img/website.png" class="img-fluid me-3" alt="Website"
                                        style="width: 40px;">
                                    <div class="text-black"> www.synconics.com</div>
                                </a>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="text-center">
                                <img src="./img/Whatsapp_Synconics_Odoo_ERP-min.png" class="img-fluid mx-auto"
                                    alt="QR Code" style="width: 128px;">
                                <div class="fs-5 fw_medium"> Scan QR to <br> connect on whatsapp.</div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div style="margin-bottom: 48px;"></div>
</section>

