# -*- coding: utf-8 -*-

from . import models
from . import wizards
from . import controllers

def post_init_hook(env):
    """Post installation hook - simplified to avoid dependency issues"""
    # For now, just pass - the module should install without additional setup
    pass

def uninstall_hook(env):
    """Uninstall hook - simplified to avoid dependency issues"""
    # For now, just pass - the module should uninstall cleanly
    pass
