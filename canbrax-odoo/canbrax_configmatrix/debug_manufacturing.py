#!/usr/bin/env python3
"""
Debug script to test Manufacturing Order creation for configurable products.
This script helps identify why Manufacturing Orders are not being created.
"""

import sys
import os

# Add the Odoo path
sys.path.insert(0, '/home/<USER>/odoo/itms/canbrax18/odoo')
sys.path.insert(0, '/home/<USER>/odoo/itms/canbrax18/canbrax-odoo')

def debug_manufacturing_order_creation():
    """Debug Manufacturing Order creation step by step"""
    
    print("🔍 Debugging Manufacturing Order Creation")
    print("=" * 50)
    
    try:
        # Mock Odoo environment for testing
        print("1. ✅ Setting up mock environment...")
        
        # Check if routes exist
        print("2. 🔍 Checking routes...")
        print("   - MTO Route: stock.route_warehouse0_mto")
        print("   - Manufacturing Route: mrp.route_warehouse0_manufacture")
        
        # Check warehouse configuration
        print("3. 🔍 Checking warehouse configuration...")
        print("   - manufacture_to_resupply should be True")
        print("   - Manufacturing rules should be active")
        
        # Check product configuration
        print("4. 🔍 Checking product configuration...")
        print("   - Product type: 'product' (storable)")
        print("   - Routes: MTO + Manufacturing")
        print("   - BOM exists: Required for manufacturing")
        
        # Check procurement flow
        print("5. 🔍 Checking procurement flow...")
        print("   - Sale Order confirmation")
        print("   - _action_launch_stock_rule called")
        print("   - Procurement created with customer location")
        print("   - Rule selection based on routes and location")
        
        # Expected flow
        print("6. 📋 Expected flow:")
        print("   a) Sale Order confirmed")
        print("   b) Procurement created for customer location")
        print("   c) Manufacturing rule selected (action='manufacture')")
        print("   d) Manufacturing Order created")
        print("   e) Stock moves created from MO to customer")
        
        # Potential issues
        print("7. ⚠️  Potential issues:")
        print("   a) MTO rule selected instead of Manufacturing rule")
        print("   b) Warehouse not configured for manufacturing")
        print("   c) BOM missing or not found")
        print("   d) Product type filtering in _action_launch_stock_rule")
        print("   e) Route priority/sequence issues")
        
        # Debug steps
        print("8. 🛠️  Debug steps:")
        print("   a) Check which rule is selected by procurement")
        print("   b) Verify warehouse.manufacture_to_resupply = True")
        print("   c) Verify BOM exists for the product")
        print("   d) Check route sequences and priorities")
        print("   e) Verify _action_launch_stock_rule is called")
        
        print("\n✅ Debug analysis complete!")
        print("Next step: Run actual test to see which issue occurs")
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_manufacturing_order_creation()
