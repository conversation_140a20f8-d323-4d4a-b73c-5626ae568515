# ConfigMatrix: Data Models

This document outlines the database models needed for the ConfigMatrix system.

## Core Models

### 1. Configuration Template

The central model that defines a configuration template for a product. Each configurable product will have one template.

```python
class ConfigMatrix(models.Model):
    _name = 'config.matrix'
    _description = 'Configuration Matrix'
    
    name = fields.Char("Template Name", required=True)
    product_tmpl_id = fields.Many2one('product.template', "Product Template", required=True)
    active = fields.Boolean("Active", default=True)
    
    section_ids = fields.One2many('config.matrix.section', 'matrix_id', "Sections", order="sequence")
    
    # For BOM generation
    bom_product_ids = fields.Many2many('product.product', string="Base BOM Products",
                                     help="Products always included in the BOM regardless of configuration")
    
    def get_matrix_data(self, configuration_id=False):
        """Get full matrix data structure for the configurator UI"""
        self.ensure_one()
        
        result = {
            'id': self.id,
            'name': self.name,
            'product_id': self.product_tmpl_id.id,
            'sections': []
        }
        
        # Get current configuration if provided
        config_values = {}
        if configuration_id:
            configuration = self.env['config.matrix.configuration'].browse(configuration_id)
            if configuration.exists() and configuration.config_data:
                config_values = json.loads(configuration.config_data)
        
        # Get sections with fields
        for section in self.section_ids:
            section_data = section.get_section_data(config_values)
            if section_data['fields']:  # Only include sections with fields
                result['sections'].append(section_data)
        
        return result
```

### 2. Matrix Section

Sections group related fields together (e.g., Door, Hardware, Extrusions).

```python
class ConfigMatrixSection(models.Model):
    _name = 'config.matrix.section'
    _description = 'Configuration Matrix Section'
    _order = 'sequence, id'
    
    name = fields.Char("Section Name", required=True)
    matrix_id = fields.Many2one('config.matrix', "Configuration Matrix", required=True, ondelete='cascade')
    sequence = fields.Integer("Sequence", default=10)
    
    field_ids = fields.One2many('config.matrix.field', 'section_id', "Fields", order="sequence")
    
    def get_section_data(self, config_values=None):
        """Get section data including fields"""
        self.ensure_one()
        
        if config_values is None:
            config_values = {}
        
        fields_data = []
        for field in self.field_ids:
            field_data = field.get_field_data(config_values)
            fields_data.append(field_data)
        
        return {
            'id': self.id,
            'name': self.name,
            'sequence': self.sequence,
            'fields': fields_data
        }
```

### 3. Matrix Field

Individual questions in the configuration process.

```python
class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    _description = 'Configuration Matrix Field'
    _order = 'sequence, id'
    
    name = fields.Char("Field Label", required=True, help="The question text shown to the user")
    technical_name = fields.Char("Technical Name", required=True, 
                               help="Unique identifier used in formulas and conditions")
    section_id = fields.Many2one('config.matrix.section', "Section", required=True, ondelete='cascade')
    sequence = fields.Integer("Sequence", default=10)
    
    field_type = fields.Selection([
        ('text', 'Text Input'),
        ('number', 'Numeric Input'),
        ('selection', 'Selection'),
        ('boolean', 'Yes/No'),
    ], "Field Type", required=True, default='text')
    
    # Field attributes
    required = fields.Boolean("Required", default=False)
    default_value = fields.Char("Default Value")
    help_text = fields.Text("Help Text")
    
    # For numeric fields
    min_value = fields.Float("Minimum Value")
    max_value = fields.Float("Maximum Value")
    
    # For selection fields
    option_ids = fields.One2many('config.matrix.option', 'field_id', "Options")
    
    # Visibility conditions - Python expression referencing other fields
    visibility_condition = fields.Char("Visibility Condition",
                                      help="Python expression determining when this field is visible")
    
    # Component mapping for BOM generation
    component_product_id = fields.Many2one('product.product', "Component Product",
                                         help="Product to include in BOM when this field has a value")
    quantity_formula = fields.Char("Quantity Formula",
                                  help="Python expression to calculate component quantity")
    
    _sql_constraints = [
        ('technical_name_unique', 'unique(section_id,technical_name)', 
         'Technical name must be unique within a section!')
    ]
    
    def get_field_data(self, config_values=None):
        """Get field data including options"""
        self.ensure_one()
        
        if config_values is None:
            config_values = {}
        
        # Get options for selection fields
        options = []
        if self.field_type == 'selection':
            for option in self.option_ids:
                options.append({
                    'id': option.id,
                    'value': option.value,
                    'name': option.name,
                    'sequence': option.sequence
                })
                
            options.sort(key=lambda x: x['sequence'])
        
        # Get current value if exists
        current_value = config_values.get(self.technical_name, self.default_value or '')
        
        return {
            'id': self.id,
            'name': self.name,
            'technical_name': self.technical_name,
            'type': self.field_type,
            'required': self.required,
            'default': self.default_value or '',
            'help': self.help_text or '',
            'min': self.min_value if self.field_type == 'number' else False,
            'max': self.max_value if self.field_type == 'number' else False,
            'options': options,
            'visibility_condition': self.visibility_condition or '',
            'value': current_value
        }
```

### 4. Matrix Option

Options for selection fields.

```python
class ConfigMatrixOption(models.Model):
    _name = 'config.matrix.option'
    _description = 'Configuration Matrix Option'
    _order = 'sequence, id'
    
    name = fields.Char("Option Text", required=True, help="The option text shown to the user")
    value = fields.Char("Option Value", required=True, help="The value stored when this option is selected")
    field_id = fields.Many2one('config.matrix.field', "Field", required=True, ondelete='cascade')
    sequence = fields.Integer("Sequence", default=10)
    
    # Component mapping for BOM generation
    component_product_id = fields.Many2one('product.product', "Component Product",
                                         help="Product to include in BOM when this option is selected")
    quantity_formula = fields.Char("Quantity Formula",
                                  help="Python expression to calculate component quantity")
```

### 5. Configuration Instance

Stores a specific configuration for a product in a sales order.

```python
class ConfigMatrixConfiguration(models.Model):
    _name = 'config.matrix.configuration'
    _description = 'Product Configuration'
    
    name = fields.Char("Configuration Name", compute='_compute_name', store=True)
    product_id = fields.Many2one('product.product', "Product", required=True)
    matrix_id = fields.Many2one('config.matrix', "Configuration Matrix", required=True)
    sale_order_line_id = fields.Many2one('sale.order.line', "Sales Order Line")
    create_date = fields.Datetime("Created On", readonly=True)
    
    # Store configuration as JSON
    config_data = fields.Text("Configuration Data", help="JSON string with all configuration values")
    
    # Generated BOM
    bom_id = fields.Many2one('mrp.bom', "Generated BOM")
    price = fields.Float("Calculated Price", digits='Product Price')
    
    @api.depends('product_id', 'create_date')
    def _compute_name(self):
        for config in self:
            if config.product_id:
                date_str = fields.Datetime.to_string(config.create_date or fields.Datetime.now())
                config.name = f"{config.product_id.name} ({date_str})"
            else:
                config.name = "New Configuration"
    
    def generate_bom(self):
        """Generate BOM from configuration"""
        self.ensure_one()
        
        if not self.config_data:
            return False
        
        # Load configuration values
        config_values = json.loads(self.config_data)
        
        # Create BOM values
        bom_vals = {
            'product_tmpl_id': self.product_id.product_tmpl_id.id,
            'product_id': self.product_id.id,
            'code': f'CONFIG-{self.id}',
            'type': 'normal',
        }
        
        # Update existing or create new BOM
        if self.bom_id:
            bom = self.bom_id
            # Clear existing BOM lines
            bom.bom_line_ids.unlink()
            bom.write(bom_vals)
        else:
            bom = self.env['mrp.bom'].create(bom_vals)
            self.bom_id = bom.id
        
        # Generate BOM lines
        self._generate_bom_lines(bom, config_values)
        
        return bom
    
    def _generate_bom_lines(self, bom, config_values):
        """Create BOM lines based on configuration values"""
        BOMLine = self.env['mrp.bom.line']
        bom_lines = []
        
        # Add base products
        for product in self.matrix_id.bom_product_ids:
            bom_lines.append({
                'bom_id': bom.id,
                'product_id': product.id,
                'product_qty': 1.0,
            })
        
        # Process all sections and fields
        for section in self.matrix_id.section_ids:
            for field in section.field_ids:
                # Skip fields that aren't visible based on configuration
                if not self._is_field_visible(field, config_values):
                    continue
                
                # Get field value
                field_value = config_values.get(field.technical_name)
                if not field_value and field.field_type != 'boolean':
                    continue
                
                # Handle component mapping
                if field.component_product_id:
                    qty = 1.0
                    if field.quantity_formula:
                        try:
                            # Create safe evaluation context with configuration values
                            ctx = dict(config_values)
                            # Add math functions
                            math_context = {k: getattr(math, k) for k in dir(math) if not k.startswith('_')}
                            ctx.update(math_context)
                            qty = safe_eval(field.quantity_formula, ctx)
                        except Exception as e:
                            _logger.error(f"Error evaluating quantity formula for {field.name}: {str(e)}")
                    
                    bom_lines.append({
                        'bom_id': bom.id,
                        'product_id': field.component_product_id.id,
                        'product_qty': qty,
                    })
                
                # For selection fields, check if selected option has component
                if field.field_type == 'selection':
                    option = self.env['config.matrix.option'].search([
                        ('field_id', '=', field.id),
                        ('value', '=', field_value)
                    ], limit=1)
                    
                    if option and option.component_product_id:
                        qty = 1.0
                        if option.quantity_formula:
                            try:
                                # Create safe evaluation context with configuration values
                                ctx = dict(config_values)
                                # Add math functions
                                math_context = {k: getattr(math, k) for k in dir(math) if not k.startswith('_')}
                                ctx.update(math_context)
                                qty = safe_eval(option.quantity_formula, ctx)
                            except Exception as e:
                                _logger.error(f"Error evaluating quantity formula for option {option.name}: {str(e)}")
                        
                        bom_lines.append({
                            'bom_id': bom.id,
                            'product_id': option.component_product_id.id,
                            'product_qty': qty,
                        })
        
        # Create all BOM lines
        for line in bom_lines:
            BOMLine.create(line)
    
    def _is_field_visible(self, field, config_values):
        """Check if a field is visible based on configuration values"""
        if not field.visibility_condition:
            return True
        
        try:
            # Create safe evaluation context with configuration values
            ctx = dict(config_values)
            # Add math functions
            math_context = {k: getattr(math, k) for k in dir(math) if not k.startswith('_')}
            ctx.update(math_context)
            return safe_eval(field.visibility_condition, ctx)
        except Exception as e:
            _logger.error(f"Error evaluating visibility condition for {field.name}: {str(e)}")
            return True  # Default to visible if evaluation fails
```

## Extended Models

These extensions connect ConfigMatrix to the standard Odoo models.

### Product Template Extension

```python
class ProductTemplate(models.Model):
    _inherit = 'product.template'
    
    is_configurable = fields.Boolean("Configurable Product", 
                                    help="This product can be configured with ConfigMatrix")
    matrix_id = fields.Many2one('config.matrix', "Configuration Matrix")
```

### Sale Order Line Extension

```python
class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'
    
    is_configurable = fields.Boolean(related='product_id.product_tmpl_id.is_configurable', 
                                    readonly=True, string="Configurable")
    config_id = fields.Many2one('config.matrix.configuration', "Configuration", 
                              copy=False, ondelete="set null")
    is_configured = fields.Boolean(compute='_compute_is_configured', store=True, 
                                 string="Configured")
    
    @api.depends('config_id')
    def _compute_is_configured(self):
        for line in self:
            line.is_configured = bool(line.config_id)
    
    def action_configure_product(self):
        """Open the product configurator for this order line"""
        self.ensure_one()
        
        # Check if product is configurable
        if not self.is_configurable:
            raise UserError(_("This product is not configurable."))
        
        # Get configuration matrix
        matrix = self.product_id.product_tmpl_id.matrix_id
        if not matrix:
            raise UserError(_("No configuration matrix found for this product."))
        
        return {
            'type': 'ir.actions.client',
            'tag': 'config_matrix',
            'name': _('Configure %s') % self.product_id.name,
            'params': {
                'product_id': self.product_id.id,
                'order_line_id': self.id,
                'matrix_id': matrix.id,
                'config_id': self.config_id.id if self.config_id else False
            }
        }
```

### Sale Order Extension

```python
class SaleOrder(models.Model):
    _inherit = 'sale.order'
    
    has_configurable_products = fields.Boolean(compute='_compute_has_configurable_products', 
                                             store=True, string="Has Configurable Products")
    
    @api.depends('order_line.is_configurable')
    def _compute_has_configurable_products(self):
        for order in self:
            order.has_configurable_products = any(line.is_configurable for line in order.order_line)
```
