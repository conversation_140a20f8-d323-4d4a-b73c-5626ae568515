# ConfigMatrix: Complete Data Model Architecture

This document outlines the comprehensive database models for the ConfigMatrix system, including all advanced features and capabilities.

## Core Models

### 1. Configuration Template (`config.matrix.template`)

The central model that defines a configuration template for a product. Each configurable product has one template that supports multiple use cases.

```python
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    _description = 'Configuration Template'
    _order = 'name'

    # Basic Information
    name = fields.Char("Template Name", required=True)
    product_template_id = fields.Many2one('product.template', "Product Template", required=True)
    description = fields.Text("Description")
    code = fields.Char("Template Code", required=True)
    version = fields.Char("Version", default="1.0")
    
    # Administrative Fields
    active = fields.Boolean("Active", default=True)
    admin_notes = fields.Text("Administrative Notes")
    
    # Use Case Enablement
    enable_check_measure = fields.Boolean("Enable Check Measure", default=True)
    enable_sales = fields.Boolean("Enable Sales/Quoting", default=True)
    enable_online = fields.Boolean("Enable Online Sales", default=True)
    
    # Template Lifecycle
    state = fields.Selection([
        ('draft', 'Draft'),
        ('testing', 'Testing'),
        ('active', 'Active'),
        ('archived', 'Archived')
    ], default='draft', string="Status")
    
    # Structure
    section_ids = fields.One2many('config.matrix.section', 'matrix_id', "Sections")
    
    # Base Components (always included)
    bom_product_ids = fields.Many2many('product.product', string="Base Components")
    
    # Component Mappings
    component_mapping_ids = fields.One2many('config.matrix.component.mapping', 'template_id', string="Component Mappings")
    
    # Visual Components
    svg_component_ids = fields.One2many('config.matrix.svg.component', 'template_id', string="SVG Components")
    has_svg_preview = fields.Boolean("Has SVG Preview", compute="_compute_has_svg_preview", store=True)
    
    # Pricing Integration
    matrix_assignment_ids = fields.One2many('config.matrix.template.matrix.assignment', 'template_id', string="Matrix Assignments")
    category_assignment_ids = fields.One2many('config.matrix.template.category.assignment', 'template_id', string="Matrix Categories")
    labor_operation_matrix_ids = fields.Many2many('config.matrix.labor.time.matrix', string="Pricing and Labor Matrices")
    
    # Statistics
    configuration_count = fields.Integer("Configurations", compute='_compute_configuration_count')
    has_price_matrices = fields.Boolean("Has Price Matrices", compute='_compute_matrix_info', store=True)
    has_labor_matrices = fields.Boolean("Has Labor Matrices", compute='_compute_matrix_info', store=True)
    price_matrix_count = fields.Integer("Price Matrices", compute='_compute_matrix_info', store=True)
    labor_matrix_count = fields.Integer("Labor Matrices", compute='_compute_matrix_info', store=True)
    category_count = fields.Integer("Matrix Categories", compute='_compute_category_info', store=True)
    labor_operation_count = fields.Integer("Pricing and Labor Matrices", compute='_compute_category_info', store=True)
    
    # Visibility Statistics
    visibility_condition_count = fields.Integer("Visibility Conditions", compute='_compute_visibility_stats')
    complex_condition_count = fields.Integer("Complex Conditions", compute='_compute_visibility_stats')
    range_condition_count = fields.Integer("Range Conditions", compute='_compute_visibility_stats')
    
    # Virtual Fields for Visibility Management
    field_visibility_conditions = fields.One2many('config.matrix.visibility.condition', 'matrix_id', 
                                                string="Field Visibility Conditions", compute='_compute_visibility_conditions')
    option_visibility_conditions = fields.One2many('config.matrix.option.visibility', 'matrix_id',
                                                 string="Option Visibility Conditions", compute='_compute_visibility_conditions')
```

### 2. Matrix Section (`config.matrix.section`)

Sections group related fields together (e.g., Door, Hardware, Extrusions).

```python
class ConfigMatrixSection(models.Model):
    _name = 'config.matrix.section'
    _description = 'Configuration Matrix Section'
    _order = 'sequence, id'

    name = fields.Char("Section Name", required=True)
    matrix_id = fields.Many2one('config.matrix.template', "Configuration Matrix", required=True, ondelete='cascade')
    sequence = fields.Integer("Sequence", default=10)
    description = fields.Text("Description")
    
    # Fields in this section
    field_ids = fields.One2many('config.matrix.field', 'section_id', "Fields", order="sequence")
    
    # Use case visibility
    check_measure_visible = fields.Boolean("Check Measure: Visible", default=True)
    sales_visible = fields.Boolean("Sales: Visible", default=True)
    online_visible = fields.Boolean("Online: Visible", default=True)
```

### 3. Configuration Field (`config.matrix.field`)

Individual configuration questions with advanced features for dynamic behavior.

```python
class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    _description = 'Configuration Field'
    _order = 'sequence, id'

    # Basic Information
    name = fields.Char('Field Label', required=True)
    technical_name = fields.Char('Technical Name', required=True)
    section_id = fields.Many2one('config.matrix.section', 'Section', required=True, ondelete='cascade')
    sequence = fields.Integer('Sequence', default=10)
    question_number = fields.Integer('Question', compute='_compute_question_number', store=True)
    
    # Field Type and Options
    field_type = fields.Selection([
        ('text', 'Text'),
        ('number', 'Number'),
        ('selection', 'Selection'),
        ('boolean', 'Yes/No'),
        ('date', 'Date'),
    ], required=True, default='text')
    
    # Dynamic Help Text per Use Case
    check_measure_use_dynamic_help = fields.Boolean('Use Dynamic Help (Check Measure)', default=False)
    check_measure_dynamic_help_template = fields.Text('Dynamic Help Template (Check Measure)')
    sales_use_dynamic_help = fields.Boolean('Use Dynamic Help (Sales)', default=False)
    sales_dynamic_help_template = fields.Text('Dynamic Help Template (Sales)')
    online_use_dynamic_help = fields.Boolean('Use Dynamic Help (Online)', default=False)
    online_dynamic_help_template = fields.Text('Dynamic Help Template (Online)')
    
    # Dynamic Default Values per Use Case
    check_measure_use_dynamic_default = fields.Boolean('Use Dynamic Default (Check Measure)', default=False)
    check_measure_dynamic_default_template = fields.Text('Dynamic Default Template (Check Measure)')
    sales_use_dynamic_default = fields.Boolean('Use Dynamic Default (Sales)', default=False)
    sales_dynamic_default_template = fields.Text('Dynamic Default Template (Sales)')
    online_use_dynamic_default = fields.Boolean('Use Dynamic Default (Online)', default=False)
    online_dynamic_default_template = fields.Text('Dynamic Default Template (Online)')
    
    # Dynamic Error Messages per Use Case
    check_measure_use_dynamic_error = fields.Boolean('Use Dynamic Error (Check Measure)', default=False)
    check_measure_dynamic_error_template = fields.Text('Dynamic Error Template (Check Measure)')
    check_measure_error_condition = fields.Char('Error Condition (Check Measure)')
    sales_use_dynamic_error = fields.Boolean('Use Dynamic Error (Sales)', default=False)
    sales_dynamic_error_template = fields.Text('Dynamic Error Template (Sales)')
    sales_error_condition = fields.Char('Error Condition (Sales)')
    online_use_dynamic_error = fields.Boolean('Use Dynamic Error (Online)', default=False)
    online_dynamic_error_template = fields.Text('Dynamic Error Template (Online)')
    online_error_condition = fields.Char('Error Condition (Online)')
    
    # Use Case Properties - Check Measure
    check_measure_visible = fields.Boolean('Check Measure: Visible', default=True)
    check_measure_min_value = fields.Char('Check Measure: Min Value')
    check_measure_max_value = fields.Char('Check Measure: Max Value')
    check_measure_default_value = fields.Char('Check Measure: Default Value')
    check_measure_default_option_id = fields.Many2one('config.matrix.option', string='Check Measure: Default Option')
    check_measure_help_text = fields.Text('Check Measure: Help Text')
    check_measure_error_text = fields.Text('Check Measure: Error Message')
    
    # Use Case Properties - Sales/Quoting
    sales_visible = fields.Boolean('Sales: Visible', default=True)
    sales_min_value = fields.Char('Sales: Min Value')
    sales_max_value = fields.Char('Sales: Max Value')
    sales_default_value = fields.Char('Sales: Default Value')
    sales_default_option_id = fields.Many2one('config.matrix.option', string='Sales: Default Option')
    sales_help_text = fields.Text('Sales: Help Text')
    sales_error_text = fields.Text('Sales: Error Message')
    
    # Use Case Properties - Online Sales
    online_visible = fields.Boolean('Online: Visible', default=True)
    online_min_value = fields.Char('Online: Min Value')
    online_max_value = fields.Char('Online: Max Value')
    online_default_value = fields.Char('Online: Default Value')
    online_default_option_id = fields.Many2one('config.matrix.option', string='Online: Default Option')
    online_help_text = fields.Text('Online: Help Text')
    online_error_text = fields.Text('Online: Error Message')
    
    # Field Type Specific Options
    min_length = fields.Integer('Minimum Length')
    max_length = fields.Integer('Maximum Length')
    pattern = fields.Char('Validation Pattern')
    decimal_precision = fields.Integer('Decimal Precision', default=3)
    uom_id = fields.Many2one('uom.uom', string='Unit of Measure')
    is_searchable_dropdown = fields.Boolean('Searchable Dropdown', default=False)
    boolean_true_label = fields.Char('True Label', default='Yes')
    boolean_false_label = fields.Char('False Label', default='No')
    
    # Component Mapping
    component_product_id = fields.Many2one('product.product', 'Component Product')
    quantity_formula = fields.Char('Quantity Formula', default='1')
    component_mapping_ids = fields.One2many('config.matrix.field.component.mapping', 'field_id', 'Component Mappings')
    has_multiple_components = fields.Boolean('Has Multiple Components', compute='_compute_has_multiple_components', store=True)
    
    # Visual Elements
    instructional_image = fields.Binary('Instructional Image')
    instructional_image_filename = fields.Char('Image Filename')
    show_in_preview = fields.Boolean('Show in Preview Panel', default=False)
    
    # Advanced Features
    visibility_condition = fields.Char('Visibility Condition')
    visibility_condition_ids = fields.One2many('config.matrix.visibility.condition', 'field_id', string='Visibility Conditions')
    dependency_ids = fields.One2many('config.matrix.dependency', 'field_id', string='Dependencies')
    
    # Related Fields
    matrix_id = fields.Many2one('config.matrix.template', related='section_id.matrix_id', string='Template', store=True)
    template_state = fields.Selection(related='matrix_id.state', string='Template State')
```

### 4. Configuration Option (`config.matrix.option`)

Selection choices for selection-type fields with individual component mappings.

```python
class ConfigMatrixOption(models.Model):
    _name = 'config.matrix.option'
    _description = 'Configuration Option'
    _order = 'sequence, id'

    name = fields.Char('Option Name', required=True)
    field_id = fields.Many2one('config.matrix.field', 'Field', required=True, ondelete='cascade')
    sequence = fields.Integer('Sequence', default=10)
    value = fields.Char('Value', help='The actual value stored when this option is selected')
    
    # Component Mapping
    component_product_id = fields.Many2one('product.product', 'Component Product')
    quantity_formula = fields.Char('Quantity Formula', default='1')
    
    # Use Case Properties
    check_measure_visible = fields.Boolean('Check Measure: Visible', default=True)
    sales_visible = fields.Boolean('Sales: Visible', default=True)
    online_visible = fields.Boolean('Online: Visible', default=True)
    
    # Related Fields
    matrix_id = fields.Many2one('config.matrix.template', related='field_id.matrix_id', string='Template')
```

### 5. Configuration (`config.matrix.configuration`)

Stores actual configuration instances with values and generated BOMs.

```python
class ConfigMatrixConfiguration(models.Model):
    _name = 'config.matrix.configuration'
    _description = 'Configuration Instance'
    _order = 'create_date desc'

    name = fields.Char('Configuration Name', required=True)
    template_id = fields.Many2one('config.matrix.template', 'Template', required=True)
    use_case = fields.Selection([
        ('check_measure', 'Check Measure'),
        ('sales', 'Sales/Quoting'),
        ('online', 'Online Sales')
    ], required=True, default='check_measure')
    
    # Configuration Values
    field_values = fields.Text('Field Values', help='JSON object containing field values')
    calculated_values = fields.Text('Calculated Values', help='JSON object containing calculated field values')
    
    # Generated BOM
    bom_line_ids = fields.One2many('config.matrix.configuration.bom.line', 'configuration_id', 'BOM Lines')
    total_price = fields.Float('Total Price', digits='Product Price')
    total_cost = fields.Float('Total Cost', digits='Product Price')
    
    # Related Records
    sale_order_line_id = fields.Many2one('sale.order.line', 'Sale Order Line')
    mrp_production_id = fields.Many2one('mrp.production', 'Manufacturing Order')
    
    # Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('manufacturing', 'In Manufacturing'),
        ('done', 'Done')
    ], default='draft', string='Status')
```

## Advanced Models

### 6. Visibility Condition (`config.matrix.visibility.condition`)

Defines complex visibility rules for fields and options.

```python
class ConfigMatrixVisibilityCondition(models.Model):
    _name = 'config.matrix.visibility.condition'
    _description = 'Visibility Condition'
    _order = 'sequence, id'

    name = fields.Char('Condition Name', required=True)
    field_id = fields.Many2one('config.matrix.field', 'Field', ondelete='cascade')
    matrix_id = fields.Many2one('config.matrix.template', 'Matrix', ondelete='cascade')
    sequence = fields.Integer('Sequence', default=10)
    
    # Condition Definition
    condition_type = fields.Selection([
        ('simple', 'Simple Condition'),
        ('range', 'Range Condition'),
        ('complex', 'Complex Expression')
    ], required=True, default='simple')
    
    condition_expression = fields.Text('Condition Expression', required=True)
    condition_description = fields.Text('Condition Description')
    
    # Use Case Specific
    check_measure_active = fields.Boolean('Check Measure: Active', default=True)
    sales_active = fields.Boolean('Sales: Active', default=True)
    online_active = fields.Boolean('Online: Active', default=True)
```

### 7. Calculated Field (`config.matrix.calculated.field`)

Formula-based computed fields with dependency tracking.

```python
class ConfigMatrixCalculatedField(models.Model):
    _name = 'config.matrix.calculated.field'
    _description = 'Calculated Field'
    _order = 'sequence, id'

    name = fields.Char('Field Name', required=True)
    technical_name = fields.Char('Technical Name', required=True)
    template_ids = fields.Many2many('config.matrix.template', string='Templates')
    sequence = fields.Integer('Sequence', default=10)
    
    # Calculation Definition
    formula = fields.Text('Formula', required=True, help='Python expression for calculation')
    formula_description = fields.Text('Formula Description')
    
    # Dependencies
    dependency_field_ids = fields.Many2many('config.matrix.field', string='Dependency Fields')
    
    # Display Options
    decimal_precision = fields.Integer('Decimal Precision', default=2)
    uom_id = fields.Many2one('uom.uom', string='Unit of Measure')
    
    # Use Case Properties
    check_measure_visible = fields.Boolean('Check Measure: Visible', default=True)
    sales_visible = fields.Boolean('Sales: Visible', default=True)
    online_visible = fields.Boolean('Online: Visible', default=True)
```

### 8. Component Mapping (`config.matrix.component.mapping`)

Defines relationships between configuration fields and product components.

```python
class ConfigMatrixComponentMapping(models.Model):
    _name = 'config.matrix.component.mapping'
    _description = 'Component Mapping'
    _order = 'sequence, id'

    template_id = fields.Many2one('config.matrix.template', 'Template', required=True, ondelete='cascade')
    component_product_id = fields.Many2one('product.product', 'Component Product', required=True)
    sequence = fields.Integer('Sequence', default=10)
    
    # Mapping Definition
    quantity_formula = fields.Char('Quantity Formula', default='1', required=True)
    condition = fields.Char('Condition', help='Optional condition for when to include this component')
    
    # Dynamic Mapping
    is_dynamic = fields.Boolean('Dynamic Mapping', default=False)
    reference_field_id = fields.Many2one('config.matrix.field', 'Reference Field')
    mapping_type = fields.Selection([
        ('fixed', 'Fixed Product'),
        ('filtered', 'Filtered Products'),
        ('calculated', 'Calculated Product')
    ], default='fixed')
```

### 9. Field Component Mapping (`config.matrix.field.component.mapping`)

1:many component mappings for individual fields.

```python
class ConfigMatrixFieldComponentMapping(models.Model):
    _name = 'config.matrix.field.component.mapping'
    _description = 'Field Component Mapping'
    _order = 'sequence, id'

    field_id = fields.Many2one('config.matrix.field', 'Field', required=True, ondelete='cascade')
    component_product_id = fields.Many2one('product.product', 'Component Product', required=True)
    sequence = fields.Integer('Sequence', default=10)
    
    # Mapping Definition
    quantity_formula = fields.Char('Quantity Formula', default='1', required=True)
    condition = fields.Char('Condition', help='Optional condition for when to include this component')
    
    # Dynamic Mapping
    is_dynamic = fields.Boolean('Dynamic Mapping', default=False)
    reference_value = fields.Char('Reference Value')
    filter_domain = fields.Text('Filter Domain', help='Domain to filter available products')
```

## Pricing Models

### 10. Price Matrix (`config.matrix.price.matrix`)

Multi-dimensional pricing matrices for configurable products.

```python
class ConfigMatrixPriceMatrix(models.Model):
    _name = 'config.matrix.price.matrix'
    _description = 'Price Matrix for Configurable Products'
    _order = 'name, sequence'

    name = fields.Char("Matrix Name", required=True)
    description = fields.Text("Description")
    product_template_id = fields.Many2one('product.template', "Product Template", required=True)
    matrix_id = fields.Many2one('config.matrix.template', "Matrix", related='product_template_id.matrix_id')
    
    # Matrix Configuration
    matrix_type = fields.Selection([
        ('price', 'Sale Price Matrix'),
        ('cost', 'Cost Matrix')
    ], string="Matrix Type", default='price', required=True)
    
    category_id = fields.Many2one('config.matrix.category', string="Matrix Category")
    
    # Dimension Fields
    height_calculated_field_id = fields.Many2one('config.matrix.calculated.field', "Height Calculated Field")
    width_calculated_field_id = fields.Many2one('config.matrix.calculated.field', "Width Calculated Field")
    
    # Matrix Data
    height_ranges = fields.Text("Height Ranges", default="[]")
    width_ranges = fields.Text("Width Ranges", default="[]")
    matrix_data = fields.Text("Matrix Data")
    special_conditions = fields.Text("Special Conditions")
    
    # Statistics
    total_cells = fields.Integer("Total Cells", compute='_compute_matrix_stats')
    filled_cells = fields.Integer("Filled Cells", compute='_compute_matrix_stats')
    completion_rate = fields.Float("Completion Rate", compute='_compute_matrix_stats')
    
    # Administrative
    sequence = fields.Integer("Sequence", default=10)
    active = fields.Boolean("Active", default=True)
    currency_id = fields.Many2one('res.currency', "Currency", default=lambda self: self.env.company.currency_id)
    last_imported = fields.Datetime("Last Imported")
    import_source = fields.Char("Import Source")
```

### 11. Labor Time Matrix (`config.matrix.labor.time.matrix`)

Labor time and cost calculations for manufacturing.

```python
class ConfigMatrixLaborTimeMatrix(models.Model):
    _name = 'config.matrix.labor.time.matrix'
    _description = 'Labor Time Matrix'
    _order = 'name, sequence'

    name = fields.Char("Matrix Name", required=True)
    description = fields.Text("Description")
    product_template_id = fields.Many2one('product.template', "Product Template", required=True)
    matrix_id = fields.Many2one('config.matrix.template', "Matrix", related='product_template_id.matrix_id')
    
    # Matrix Configuration
    category_id = fields.Many2one('config.matrix.category', string="Matrix Category")
    operation_type = fields.Selection([
        ('cutting', 'Cutting'),
        ('assembly', 'Assembly'),
        ('finishing', 'Finishing'),
        ('packaging', 'Packaging')
    ], string="Operation Type", required=True)
    
    # Dimension Fields
    height_calculated_field_id = fields.Many2one('config.matrix.calculated.field', "Height Calculated Field")
    width_calculated_field_id = fields.Many2one('config.matrix.calculated.field', "Width Calculated Field")
    
    # Matrix Data
    height_ranges = fields.Text("Height Ranges", default="[]")
    width_ranges = fields.Text("Width Ranges", default="[]")
    matrix_data = fields.Text("Matrix Data")
    
    # Labor Rates
    labor_rate_per_hour = fields.Float("Labor Rate per Hour", digits='Product Price')
    currency_id = fields.Many2one('res.currency', "Currency", default=lambda self: self.env.company.currency_id)
    
    # Statistics
    total_cells = fields.Integer("Total Cells", compute='_compute_matrix_stats')
    filled_cells = fields.Integer("Filled Cells", compute='_compute_matrix_stats')
    completion_rate = fields.Float("Completion Rate", compute='_compute_matrix_stats')
    
    # Administrative
    sequence = fields.Integer("Sequence", default=10)
    active = fields.Boolean("Active", default=True)
```

### 12. Matrix Category (`config.matrix.category`)

Organizes pricing and labor matrices by type and purpose.

```python
class ConfigMatrixCategory(models.Model):
    _name = 'config.matrix.category'
    _description = 'Matrix Category'
    _order = 'sequence, name'

    name = fields.Char("Category Name", required=True)
    description = fields.Text("Description")
    category_type = fields.Selection([
        ('price', 'Price Matrix'),
        ('labor', 'Labor Matrix'),
        ('material', 'Material Matrix'),
        ('other', 'Other')
    ], string="Category Type", required=True, default='price')
    
    # Relationships
    price_matrix_ids = fields.One2many('config.matrix.price.matrix', 'category_id', string="Price Matrices")
    labor_matrix_ids = fields.One2many('config.matrix.labor.time.matrix', 'category_id', string="Labor Matrices")
    
    # Administrative
    sequence = fields.Integer("Sequence", default=10)
    active = fields.Boolean("Active", default=True)
```

## Visual Models

### 13. SVG Component (`config.matrix.svg.component`)

Vector-based visual representations for product previews.

```python
class ConfigMatrixSvgComponent(models.Model):
    _name = 'config.matrix.svg.component'
    _description = 'SVG Component'
    _order = 'sequence, id'

    name = fields.Char("Component Name", required=True)
    template_id = fields.Many2one('config.matrix.template', "Template", required=True, ondelete='cascade')
    sequence = fields.Integer("Sequence", default=10)
    
    # SVG Definition
    svg_content = fields.Text("SVG Content", required=True)
    svg_width = fields.Integer("SVG Width", default=400)
    svg_height = fields.Integer("SVG Height", default=300)
    
    # Conditional Display
    visibility_condition = fields.Char("Visibility Condition")
    layer_name = fields.Char("Layer Name")
    
    # Use Case Properties
    check_measure_visible = fields.Boolean("Check Measure: Visible", default=True)
    sales_visible = fields.Boolean("Sales: Visible", default=True)
    online_visible = fields.Boolean("Online: Visible", default=True)
    
    # Administrative
    active = fields.Boolean("Active", default=True)
    description = fields.Text("Description")
```

## Integration Models

### 14. Sale Order Line Extension

Extends sale order lines to support configurations.

```python
class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    # Configuration Support
    is_configurable = fields.Boolean("Is Configurable", related='product_template_id.is_configurable')
    configuration_id = fields.Many2one('config.matrix.configuration', "Configuration")
    is_configured = fields.Boolean("Is Configured", compute='_compute_is_configured', store=True)
    
    # Configuration Data
    field_values = fields.Text("Field Values")
    calculated_values = fields.Text("Calculated Values")
    bom_line_ids = fields.One2many('config.matrix.configuration.bom.line', 'sale_order_line_id', "BOM Lines")
    
    # Pricing
    configuration_price = fields.Float("Configuration Price", digits='Product Price')
    configuration_cost = fields.Float("Configuration Cost", digits='Product Price')
```

### 15. Product Template Extension

Extends product templates to support configuration.

```python
class ProductTemplate(models.Model):
    _inherit = 'product.template'

    # Configuration Support
    is_configurable = fields.Boolean("Is Configurable", default=False)
    matrix_id = fields.Many2one('config.matrix.template', "Configuration Matrix")
    
    # Configuration Options
    requires_measurement = fields.Boolean("Requires Measurement", default=False)
    has_visual_preview = fields.Boolean("Has Visual Preview", default=False)
    use_matrix_pricing = fields.Boolean("Use Matrix Pricing", default=False)
```

## Data Relationships

### Template Hierarchy
```
config.matrix.template
├── config.matrix.section (One2many)
│   └── config.matrix.field (One2many)
│       ├── config.matrix.option (One2many)
│       ├── config.matrix.field.component.mapping (One2many)
│       └── config.matrix.visibility.condition (One2many)
├── config.matrix.component.mapping (One2many)
├── config.matrix.svg.component (One2many)
├── config.matrix.template.matrix.assignment (One2many)
└── config.matrix.template.category.assignment (One2many)
```

### Pricing Hierarchy
```
config.matrix.category
├── config.matrix.price.matrix (One2many)
└── config.matrix.labor.time.matrix (One2many)
```

### Configuration Flow
```
config.matrix.configuration
├── config.matrix.configuration.bom.line (One2many)
├── sale.order.line (Many2one)
└── mrp.production (Many2one)
```

## Key Features

### 1. Multi-Use Case Support
Each field and option can have different properties for different use cases:
- **Check Measure**: Measurement-based configuration
- **Sales/Quoting**: Sales order configuration
- **Online Sales**: Customer portal configuration

### 2. Dynamic Content
- **Dynamic Help**: Context-sensitive help text using field placeholders
- **Dynamic Defaults**: Intelligent default values based on other fields
- **Dynamic Errors**: Conditional error messages with expressions

### 3. Advanced Validation
- **Range Validation**: Dynamic min/max values with expressions
- **Pattern Validation**: Regular expression validation for text fields
- **Dependency Validation**: Circular dependency detection

### 4. Component Mapping
- **1:Many Relationships**: Multiple components per field
- **Dynamic Conditions**: Include components based on configuration
- **Quantity Formulas**: Calculate quantities using expressions
- **Product Filtering**: Dynamic product selection

### 5. Pricing System
- **Multi-Dimensional Matrices**: Height/width based pricing
- **Labor Calculations**: Time and cost calculations
- **Special Conditions**: Complex pricing scenarios
- **Import/Export**: Excel and CSV support

### 6. Visual Components
- **SVG Rendering**: Vector-based visual representations
- **Conditional Layers**: Show/hide elements based on configuration
- **Instructional Images**: Guide users through processes

### 7. Performance Optimization
- **Caching**: Computed field caching with dependencies
- **Debouncing**: UI update optimization
- **Memory Management**: Efficient data handling for large configurations

This comprehensive data model architecture supports all the advanced features of the ConfigMatrix system while maintaining performance and scalability for large-scale deployments.
