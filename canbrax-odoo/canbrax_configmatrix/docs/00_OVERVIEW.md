# ConfigMatrix: Dynamic BOM Builder

## Overview

ConfigMatrix is an elegant product configurator for Odoo that mimics the familiar spreadsheet-like interface of the existing Excel configuration system while providing dynamic visibility, validation, and BOM generation.

## Key Features

- **Familiar Interface**: Form-based UI that looks like the existing Excel sheet
- **Dynamic Question/Answer**: Questions appear/disappear based on previous answers
- **Real-time BOM Generation**: Components added to Bill of Materials as options are selected
- **Integrated with Sales**: Appears directly in sales order when configurable products are selected
- **Flexible Configuration**: Admin-friendly interface for defining configuration templates

## Core Concepts

1. **Configuration Templates**: Define the structure of questions for each configurable product
2. **Sections**: Organize questions into logical groups (Door, Hardware, Extrusions, etc.)
3. **Fields**: Individual configuration questions with different types (text, number, selection)
4. **Rules**: Visibility conditions that determine when questions appear
5. **Component Mapping**: <PERSON> answers directly to products for BOM generation

## User Flow

1. User selects a configurable product in the sales order
2. The "Configure" button appears next to the product line
3. User clicks "Configure" and the ConfigMatrix form opens, showing all relevant questions
4. As the user answers questions, other questions appear/disappear based on rules
5. User completes the configuration and saves
6. A Bill of Materials is automatically generated from the answers
7. The configured product is added to the sales order with its configuration summary

## Implementation Approach

The implementation is broken down into the following key areas:

1. **Data Models**: Define the structure for configuration templates, sections, fields, and options
2. **User Interface**: Create a clean, spreadsheet-like interface using Odoo's OWL framework
3. **Rule Engine**: Implement visibility rules to show/hide questions based on answers
4. **BOM Generation**: Automatically create Bills of Materials from configuration choices
5. **Sales Integration**: Seamlessly integrate with the sales order workflow

See the detailed implementation files in this directory for specific instructions on each component.
