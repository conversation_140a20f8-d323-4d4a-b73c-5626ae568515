# Enhanced JSON Import for Configuration Matrix

The JSON import functionality has been enhanced to support use-case-specific configurations. This allows you to define different behaviors, help text, default values, and constraints for different use cases (Check Measure, Sales/Quoting, and Online Sales).

## Enhanced Features

### 1. Use-Case-Specific Help Text

You can now define different help text for each use case:

```json
{
  "name": "Product Quantity",
  "technical_name": "product_quantity",
  "field_type": "number",
  "help_text": "General help text for all use cases",
  "check_measure_help_text": "Check Measure: Enter the quantity to be measured",
  "sales_help_text": "Sales: Enter the quantity for quotation",
  "online_help_text": "Online: Select quantity for online ordering"
}
```

**Fallback Behavior**: If use-case-specific help text is not provided, the general `help_text` will be used for all use cases.

### 2. Use-Case-Specific Default Values

Define different default values for each use case:

```json
{
  "name": "Product Quantity",
  "technical_name": "product_quantity",
  "field_type": "number",
  "default_value": 1,
  "check_measure_default_value": "1",
  "sales_default_value": "5",
  "online_default_value": "1"
}
```

**Fallback Behavior**: If use-case-specific defaults are not provided, the general `default_value` will be used for all use cases.

### 3. Use-Case-Specific Min/Max Values (Number Fields)

Define different constraints for each use case:

```json
{
  "name": "Height (mm)",
  "technical_name": "height_mm",
  "field_type": "number",
  "min_value": 100,
  "max_value": 3000,
  "check_measure_min_value": "100",
  "check_measure_max_value": "3000",
  "sales_min_value": "200",
  "sales_max_value": "2500",
  "online_min_value": "300",
  "online_max_value": "2000"
}
```

**Fallback Behavior**: If use-case-specific min/max values are not provided, the general `min_value` and `max_value` will be used for all use cases.

### 4. Use-Case-Specific Visibility

Control which fields are visible in each use case:

```json
{
  "name": "Special Instructions",
  "technical_name": "special_instructions",
  "field_type": "text",
  "check_measure_visible": true,
  "sales_visible": true,
  "online_visible": false
}
```

**Default Behavior**: If not specified, fields are visible in all use cases by default.

### 5. Enhanced Option Help Text

Options in selection fields can have help text that will be applied to the field's use-case-specific help text fields:

```json
{
  "name": "Product Type",
  "technical_name": "product_type",
  "field_type": "selection",
  "options": [
    {
      "name": "Standard",
      "value": "standard",
      "help_text": "Standard product configuration with basic features"
    },
    {
      "name": "Premium",
      "value": "premium",
      "help_text": "Premium product with additional features and customization"
    }
  ]
}
```

**Behavior**: When an option has `help_text`, the import will write this text to the field's `check_measure_help_text`, `sales_help_text`, and `online_help_text` fields (only if those fields are currently empty). This ensures that option-specific help text is available across all use cases.

### 6. Improved Visibility Conditions

Visibility conditions are now properly imported and applied:

```json
{
  "name": "Special Instructions",
  "technical_name": "special_instructions",
  "field_type": "text",
  "visibility_condition": "product_type == 'custom'"
}
```

## Use Case Field Names

The following field names are supported for use-case-specific configurations:

### Help Text
- `check_measure_help_text`
- `sales_help_text`
- `online_help_text`

### Default Values
- `check_measure_default_value`
- `sales_default_value`
- `online_default_value`

### Min/Max Values (Number fields only)
- `check_measure_min_value`
- `check_measure_max_value`
- `sales_min_value`
- `sales_max_value`
- `online_min_value`
- `online_max_value`

### Visibility
- `check_measure_visible`
- `sales_visible`
- `online_visible`

## Backward Compatibility

The enhanced import is fully backward compatible with existing JSON files. If you don't specify use-case-specific fields, the import will use the general fields for all use cases, maintaining the same behavior as before.

## Example

See `canbrax_configmatrix/data/enhanced_import_example.json` for a complete example demonstrating all the enhanced features.
