# ConfigMatrix: Developer Guide

This comprehensive developer guide provides technical information for extending and customizing the ConfigMatrix system.

## Architecture Overview

ConfigMatrix is built on a modular architecture with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer (OWL)                     │
├─────────────────────────────────────────────────────────────┤
│  Configurator Widget  │  Field Components  │  SVG Renderer  │
├─────────────────────────────────────────────────────────────┤
│                   Controller Layer                          │
├─────────────────────────────────────────────────────────────┤
│  Main Controller  │  Portal Controller  │  Website Controller │
├─────────────────────────────────────────────────────────────┤
│                    Model Layer                              │
├─────────────────────────────────────────────────────────────┤
│  Core Models  │  Pricing Models  │  Component Models  │  Visual Models │
├─────────────────────────────────────────────────────────────┤
│                   Integration Layer                         │
├─────────────────────────────────────────────────────────────┤
│  Sales Integration  │  Manufacturing Integration  │  Portal Integration │
└─────────────────────────────────────────────────────────────┘
```

## Core Development Patterns

### 1. Model Inheritance Patterns

#### Template Extension
```python
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    
    # Use computed fields for performance
    @api.depends('section_ids', 'section_ids.field_ids')
    def _compute_field_count(self):
        for template in self:
            template.field_count = sum(len(section.field_ids) for section in template.section_ids)
    
    # Use selection_add for extending existing selections
    state = fields.Selection(selection_add=[
        ('archived', 'Archived')
    ], ondelete={'archived': 'set draft'})
    
    # Use related fields for efficient queries
    product_name = fields.Char(related='product_template_id.name', store=True)
    
    # Use constraints for data integrity
    _sql_constraints = [
        ('unique_code', 'unique(code)', 'Template code must be unique!')
    ]
```

#### Field Extension
```python
class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    
    # Use computed fields with proper dependencies
    @api.depends('sequence', 'section_id.sequence')
    def _compute_question_number(self):
        for field in self:
            if field.section_id:
                # Calculate question number based on sequence
                field.question_number = field.section_id.sequence * 100 + field.sequence
    
    # Use onchange for immediate UI updates
    @api.onchange('field_type')
    def _onchange_field_type(self):
        if self.field_type == 'selection':
            self.is_searchable_dropdown = True
        else:
            self.is_searchable_dropdown = False
    
    # Use constraints for validation
    @api.constrains('technical_name')
    def _check_technical_name(self):
        for field in self:
            if not field.technical_name.isidentifier():
                raise ValidationError(_("Technical name must be a valid Python identifier"))
```

### 2. Controller Patterns

#### RESTful API Design
```python
class ConfigMatrixController(http.Controller):
    
    @http.route('/config_matrix/api/template/<int:template_id>', 
                type='json', auth='user', methods=['GET'])
    def get_template(self, template_id, **kw):
        """Get template data with caching"""
        try:
            template = request.env['config.matrix.template'].browse(template_id)
            if not template.exists():
                return {'error': 'Template not found'}, 404
            return {
                'id': template.id,
                'name': template.name,
                'sections': template.section_ids.read(['name', 'sequence']),
                'fields': template.field_ids.read(['name', 'field_type', 'technical_name'])
            }
        except Exception as e:
            return {'error': str(e)}, 500
```

#### Portal Controller Structure
```python
class ConfigMatrixCustomerPortal(CustomerPortal):
    """Portal controller for customer-facing ConfigMatrix features"""
    
    def _check_portal_access(self):
        """Check if current user has portal access"""
        try:
            if not request.env.user.has_group('base.group_portal'):
                return False
            # Use existing partner categorization
            local_freight_type = request.env.ref(
                'modula_contact_extended.res_partner_type_local_freight', 
                raise_if_not_found=False
            )
            if not local_freight_type or request.env.user.partner_id.customer_type_id != local_freight_type:
                return False
            return True
        except Exception:
            return False
    
    @http.route(['/my/config-matrices'], type='http', auth="user", website=True)
    def portal_my_config_matrices(self, **kw):
        """List view with access control"""
        if not self._check_portal_access():
            return request.redirect('/my')
        try:
            domain = [
                ('active', '=', True),
                ('state', '=', 'active'),
                ('enable_online', '=', True)
            ]
            templates = request.env['config.matrix.template'].search(domain)
            values = {
                'templates': templates,
            }
            return request.render("config_matrix.portal_my_config_matrices", values)
        except Exception as e:
            return request.render("portal.portal_my_home", {'error': str(e)})
```

### 3. Frontend Component Patterns

#### OWL Component Structure
```javascript
/** @odoo-module **/
import { Component, onMounted, useRef, useState } from "@odoo/owl";

export class ConfigMatrixConfigurator extends Component {
    setup() {
        this.rootRef = useRef("root");
        this.csrfToken = odoo.csrf_token;
        
        // Use useState for reactive state management
        this.state = useState({
            loading: false,
            error: false,
            success: false,
            fieldValues: {},
            calculatedValues: {},
        });
        
        this.localFiles = useState([]);
        this.signature = useState({
            name: '',
            getSignatureImage: () => "",
            resetSignature: () => { },
            isSignatureEmpty: true,
        });
    }
    
    async onFieldChange(fieldName, value) {
        this.state.fieldValues[fieldName] = value;
        await this.updateCalculatedFields();
        await this.updateVisibility();
        await this.updatePreview();
    }
    
    async updateCalculatedFields() {
        try {
            const response = await this.rpc('/config_matrix/get_calculated_fields', {
                template_id: this.props.templateId,
                field_values: this.state.fieldValues,
            });
            this.state.calculatedValues = response.calculated_values || {};
        } catch (error) {
            console.error('Error updating calculated fields:', error);
        }
    }
}
```

#### Visual Matrix Editor
```javascript
/** @odoo-module **/
import { Component, onMounted, useRef, useState } from "@odoo/owl";

export class EnhancedExcelMatrixEditor extends Component {
    setup() {
        this.rootRef = useRef("root");
        this.state = useState({
            matrixData: {},
            heightRanges: [],
            widthRanges: [],
            selectedCell: null,
            editingValue: '',
        });
        
        this.matrixWidget = useRef("matrixWidget");
    }
    
    async onMounted() {
        await this.loadMatrixData();
        this.initializeMatrixWidget();
    }
    
    async loadMatrixData() {
        try {
            const response = await this.rpc('/config_matrix/get_matrix_data', {
                matrix_id: this.props.matrixId,
            });
            this.state.matrixData = response.matrix_data || {};
            this.state.heightRanges = response.height_ranges || [];
            this.state.widthRanges = response.width_ranges || [];
        } catch (error) {
            console.error('Error loading matrix data:', error);
        }
    }
    
    onCellClick(height, width) {
        this.state.selectedCell = { height, width };
        this.state.editingValue = this.state.matrixData[`height_${height}_width_${width}`] || '';
    }
    
    async saveCellValue() {
        if (!this.state.selectedCell) return;
        
        const { height, width } = this.state.selectedCell;
        const key = `height_${height}_width_${width}`;
        this.state.matrixData[key] = this.state.editingValue;
        
        try {
            await this.rpc('/config_matrix/save_matrix_data', {
                matrix_id: this.props.matrixId,
                matrix_data: this.state.matrixData,
            });
        } catch (error) {
            console.error('Error saving matrix data:', error);
        }
    }
}
```

## Advanced Features Implementation

### 1. Dynamic Content System

#### Dynamic Help Text
```python
class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    
    def generate_dynamic_help(self, values=None, use_case='check_measure'):
        """Generate dynamic help text using field placeholders"""
        if not values:
            values = {}
        
        # Get the appropriate template based on use case
        template_field = f'{use_case}_dynamic_help_template'
        template = getattr(self, template_field, '')
        
        if not template:
            return ''
        
        # Replace placeholders with actual values
        def replace_expression(match):
            field_name = match.group(1)
            if field_name in values:
                return str(values[field_name])
            return f'{{{field_name}}}'
        
        import re
        return re.sub(r'\{([^}]+)\}', replace_expression, template)
```

#### Dynamic Default Values
```python
class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    
    def generate_dynamic_default(self, values=None, use_case='check_measure'):
        """Generate dynamic default value using field placeholders"""
        if not values:
            values = {}
        
        # Get the appropriate template based on use case
        template_field = f'{use_case}_dynamic_default_template'
        template = getattr(self, template_field, '')
        
        if not template:
            return ''
        
        # Evaluate the expression safely
        try:
            # Create a safe context for evaluation
            safe_context = {
                'values': values,
                'field_values': values,
                'config_values': values,
            }
            
            # Add field values as individual variables
            for key, value in values.items():
                safe_context[key] = value
            
            # Evaluate the expression
            result = safe_eval(template, safe_context)
            return result
        except Exception as e:
            _logger.error(f"Error evaluating dynamic default: {e}")
            return ''
```

### 2. Visibility Condition System

#### Complex Visibility Evaluation
```python
class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    
    @tools.ormcache('self.id', 'values_hash')
    def evaluate_visibility_cached(self, values_hash, field_values):
        """Cached visibility evaluation for performance"""
        return self._evaluate_visibility_uncached(field_values)
    
    def _evaluate_visibility_uncached(self, field_values):
        """Evaluate field visibility based on conditions"""
        if not self.visibility_condition:
            return True
        
        try:
            # Create safe context for evaluation
            safe_context = {
                'values': field_values,
                'field_values': field_values,
                'config_values': field_values,
            }
            
            # Add field values as individual variables
            for key, value in field_values.items():
                safe_context[key] = value
            
            # Evaluate the condition
            result = safe_eval(self.visibility_condition, safe_context)
            return bool(result)
        except Exception as e:
            _logger.error(f"Error evaluating visibility condition: {e}")
            return True
```

### 3. Component Mapping System

#### Dynamic Component Selection
```python
class ConfigMatrixFieldComponentMapping(models.Model):
    _name = 'config.matrix.field.component.mapping'
    
    def get_filtered_products(self, reference_value=None, field_values=None):
        """Get filtered products based on dynamic conditions"""
        if not self.is_dynamic:
            return self.component_product_id
        
        try:
            # Build domain from filter_domain
            domain = []
            if self.filter_domain:
                domain = safe_eval(self.filter_domain, {
                    'reference_value': reference_value,
                    'field_values': field_values or {},
                })
            
            # Add reference value condition if specified
            if self.reference_value and reference_value:
                domain.append(('name', 'ilike', reference_value))
            
            # Search for products
            products = self.env['product.product'].search(domain)
            return products
        except Exception as e:
            _logger.error(f"Error filtering products: {e}")
            return self.component_product_id
```

### 4. Pricing Matrix System

#### Multi-Dimensional Pricing
```python
class ConfigMatrixPriceMatrix(models.Model):
    _name = 'config.matrix.price.matrix'
    
    def get_price_for_dimensions(self, height, width):
        """Get price for specific dimensions"""
        try:
            height_ranges = json.loads(self.height_ranges) if self.height_ranges else []
            width_ranges = json.loads(self.width_ranges) if self.width_ranges else []
            matrix_data = json.loads(self.matrix_data) if self.matrix_data else {}
            
            # Find matching height range
            height_range = None
            for hr in height_ranges:
                if hr['min'] <= height <= hr['max']:
                    height_range = hr
                    break
            
            # Find matching width range
            width_range = None
            for wr in width_ranges:
                if wr['min'] <= width <= wr['max']:
                    width_range = wr
                    break
            
            if not height_range or not width_range:
                return 0.0
            
            # Get price from matrix
            key = f"height_{height_range['label']}_width_{width_range['label']}"
            price = matrix_data.get(key, 0.0)
            
            return float(price)
        except Exception as e:
            _logger.error(f"Error getting price for dimensions: {e}")
            return 0.0
```

### 5. Visual Component System

#### SVG Rendering
```python
class ConfigMatrixSvgComponent(models.Model):
    _name = 'config.matrix.svg.component'
    
    def get_rendered_svg(self, field_values=None):
        """Get rendered SVG with conditional layers"""
        if not field_values:
            field_values = {}
        
        svg_content = self.svg_content
        
        # Apply visibility conditions
        if self.visibility_condition:
            try:
                safe_context = {
                    'values': field_values,
                    'field_values': field_values,
                }
                for key, value in field_values.items():
                    safe_context[key] = value
                
                is_visible = safe_eval(self.visibility_condition, safe_context)
                if not is_visible:
                    return ''
            except Exception as e:
                _logger.error(f"Error evaluating SVG visibility: {e}")
        
        return svg_content
```

## Performance Optimization

### 1. Caching Strategies

#### Computed Field Caching
```python
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    
    @tools.ormcache('template_id', 'use_case')
    def get_template_structure_cached(self, template_id, use_case=None):
        """Cached template structure for performance"""
        return self._get_template_structure_uncached(template_id, use_case)
    
    def _get_template_structure_uncached(self, template_id, use_case=None):
        """Uncached template structure generation"""
        template = self.browse(template_id)
        if not template.exists():
            return {}
        
        # Build optimized structure
        sections = []
        for section in template.section_ids:
            if not self._is_section_visible_for_use_case(section, use_case):
                continue
            
            section_data = {
                'id': section.id,
                'name': section.name,
                'sequence': section.sequence,
                'fields': []
            }
            
            for field in section.field_ids:
                if not self._is_field_visible_for_use_case(field, use_case):
                    continue
                
                field_data = self._prepare_field_data_optimized(field, use_case)
                section_data['fields'].append(field_data)
            
            if section_data['fields']:
                sections.append(section_data)
        
        return {
            'template_id': template_id,
            'name': template.name,
            'sections': sections
        }
```

### 2. Database Optimization

#### Efficient Queries
```python
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    
    def _get_sections_optimized(self, template_id, use_case):
        """Optimized section retrieval with prefetching"""
        template = self.browse(template_id)
        
        # Prefetch related records
        sections = template.section_ids.with_context(prefetch_fields=True)
        sections.mapped('field_ids')  # Prefetch fields
        sections.mapped('field_ids.option_ids')  # Prefetch options
        
        # Filter sections efficiently
        visible_sections = []
        for section in sections:
            if self._is_section_visible_for_use_case(section, use_case):
                visible_fields = []
                for field in section.field_ids:
                    if self._is_field_visible_for_use_case(field, use_case):
                        visible_fields.append(field)
                
                if visible_fields:
                    section.visible_fields = visible_fields
                    visible_sections.append(section)
        
        return visible_sections
```

### 3. Frontend Performance

#### Debounced Updates
```javascript
export class ConfigMatrixConfigurator extends Component {
    setup() {
        // Debounce expensive operations
        this.debouncedUpdate = this.debounce(this.updateCalculatedFields, 300);
        this.debouncedVisibilityUpdate = this.debounce(this.updateVisibility, 200);
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    async onFieldChange(fieldName, value) {
        this.state.fieldValues[fieldName] = value;
        
        // Use debounced updates for performance
        this.debouncedUpdate();
        this.debouncedVisibilityUpdate();
    }
}
```

## Security Implementation

### 1. Access Control

#### Portal Access Validation
```python
class ConfigMatrixCustomerPortal(CustomerPortal):
    
    def _check_portal_access(self):
        """Check if current user has portal access"""
        try:
            # Check if user is portal user
            if not request.env.user.has_group('base.group_portal'):
                return False
            
            # Check specific access rights using existing partner categorization
            local_freight_type = request.env.ref(
                'modula_contact_extended.res_partner_type_local_freight', 
                raise_if_not_found=False
            )
            if not local_freight_type or request.env.user.partner_id.customer_type_id != local_freight_type:
                return False
            
            return True
        except Exception:
            return False
```

### 2. Input Validation

#### Expression Validation
```python
class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    
    def _validate_dynamic_expression(self, template):
        """Validate dynamic expressions for security"""
        expressions = [
            self.check_measure_dynamic_help_template,
            self.sales_dynamic_help_template,
            self.online_dynamic_help_template,
            self.check_measure_dynamic_default_template,
            self.sales_dynamic_default_template,
            self.online_dynamic_default_template,
        ]
        
        for expression in expressions:
            if expression:
                self._validate_single_expression(expression)
    
    def _validate_single_expression(self, expression):
        """Validate a single expression for security"""
        # Check for dangerous patterns
        dangerous_patterns = [
            'import', 'exec', 'eval', '__import__',
            'open', 'file', 'os.', 'sys.',
            'subprocess', 'globals', 'locals'
        ]
        
        for pattern in dangerous_patterns:
            if pattern in expression.lower():
                raise ValidationError(_(f"Expression contains forbidden pattern: {pattern}"))
```

## Integration Patterns

### 1. Sales Integration

#### Sale Order Line Extension
```python
class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'
    
    def action_configure_product(self):
        """Open configuration interface for this line"""
        self.ensure_one()
        
        if not self.product_template_id.is_configurable:
            raise UserError(_("This product is not configurable"))
        
        return {
            'name': _('Configure %s') % self.product_template_id.name,
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.configuration',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_template_id': self.product_template_id.matrix_id.id,
                'default_use_case': 'sales',
                'default_sale_order_line_id': self.id,
            },
        }
    
    def _compute_configuration_price(self):
        """Compute price based on configuration"""
        for line in self:
            if line.configuration_id:
                line.configuration_price = line.configuration_id.total_price
            else:
                line.configuration_price = 0.0
```

### 2. Manufacturing Integration

#### BOM Generation
```python
class ConfigMatrixConfiguration(models.Model):
    _name = 'config.matrix.configuration'
    
    def generate_bom(self):
        """Generate BOM from configuration"""
        self.ensure_one()
        
        # Create BOM lines from component mappings
        bom_lines = []
        
        # Get field values
        field_values = json.loads(self.field_values) if self.field_values else {}
        
        # Process component mappings
        for mapping in self.template_id.component_mapping_ids:
            if self._evaluate_component_condition(mapping, field_values):
                quantity = self._calculate_component_quantity(mapping, field_values)
                if quantity > 0:
                    bom_lines.append({
                        'product_id': mapping.component_product_id.id,
                        'product_qty': quantity,
                        'product_uom_id': mapping.component_product_id.uom_id.id,
                    })
        
        # Process field component mappings
        for field in self.template_id.field_ids:
            for mapping in field.component_mapping_ids:
                if self._evaluate_field_component_condition(mapping, field_values):
                    quantity = self._calculate_field_component_quantity(mapping, field_values)
                    if quantity > 0:
                        bom_lines.append({
                            'product_id': mapping.component_product_id.id,
                            'product_qty': quantity,
                            'product_uom_id': mapping.component_product_id.uom_id.id,
                        })
        
        return bom_lines
```

## Testing and Debugging

### 1. Unit Testing

#### Model Testing
```python
class TestConfigMatrixTemplate(TransactionCase):
    
    def setUp(self):
        super().setUp()
        self.template = self.env['config.matrix.template'].create({
            'name': 'Test Template',
            'code': 'TEST001',
            'product_template_id': self.env['product.template'].create({
                'name': 'Test Product'
            }).id,
        })
    
    def test_template_creation(self):
        """Test template creation and basic functionality"""
        self.assertEqual(self.template.name, 'Test Template')
        self.assertEqual(self.template.code, 'TEST001')
        self.assertEqual(self.template.state, 'draft')
    
    def test_field_visibility(self):
        """Test field visibility conditions"""
        field = self.env['config.matrix.field'].create({
            'name': 'Test Field',
            'technical_name': 'test_field',
            'field_type': 'text',
            'section_id': self.env['config.matrix.section'].create({
                'name': 'Test Section',
                'matrix_id': self.template.id,
            }).id,
            'visibility_condition': 'test_value == "visible"',
        })
        
        # Test visibility evaluation
        self.assertTrue(field.evaluate_visibility({'test_value': 'visible'}))
        self.assertFalse(field.evaluate_visibility({'test_value': 'hidden'}))
```

### 2. Performance Testing

#### Load Testing
```python
class TestConfigMatrixPerformance(TransactionCase):
    
    def test_large_configuration_performance(self):
        """Test performance with large configurations"""
        # Create template with many fields
        template = self.env['config.matrix.template'].create({
            'name': 'Large Template',
            'code': 'LARGE001',
            'product_template_id': self.env['product.template'].create({
                'name': 'Large Product'
            }).id,
        })
        
        # Create 100 fields
        section = self.env['config.matrix.section'].create({
            'name': 'Large Section',
            'matrix_id': template.id,
        })
        
        for i in range(100):
            self.env['config.matrix.field'].create({
                'name': f'Field {i}',
                'technical_name': f'field_{i}',
                'field_type': 'text',
                'section_id': section.id,
                'sequence': i,
            })
        
        # Test template structure retrieval performance
        import time
        start_time = time.time()
        
        structure = template.get_template_structure_cached(template.id, 'check_measure')
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should complete within 1 second
        self.assertLess(duration, 1.0)
        self.assertEqual(len(structure['sections'][0]['fields']), 100)
```

## Deployment and Maintenance

### 1. Database Migration

#### Version Migration
```python
def migrate_config_matrix_18_0_1_0_5(env):
    """Migrate ConfigMatrix to version 18.0.1.0.5"""
    
    # Update field types
    env.cr.execute("""
        UPDATE config_matrix_field 
        SET field_type = 'text' 
        WHERE field_type = 'string'
    """)
    
    # Add new computed fields
    env.cr.execute("""
        ALTER TABLE config_matrix_template 
        ADD COLUMN IF NOT EXISTS has_price_matrices boolean DEFAULT false
    """)
    
    # Update computed fields
    templates = env['config.matrix.template'].search([])
    templates._compute_matrix_info()
```

### 2. Performance Monitoring

#### Query Monitoring
```python
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    
    def _log_performance_metrics(self, operation, duration):
        """Log performance metrics for monitoring"""
        _logger.info(f"ConfigMatrix Performance: {operation} took {duration:.3f}s")
        
        # Store metrics for analysis
        self.env['config.matrix.performance.log'].create({
            'operation': operation,
            'duration': duration,
            'template_id': self.id if self else False,
            'user_id': self.env.user.id,
        })
```

This comprehensive developer guide provides the technical foundation for extending and customizing the ConfigMatrix system while maintaining performance, security, and maintainability.
