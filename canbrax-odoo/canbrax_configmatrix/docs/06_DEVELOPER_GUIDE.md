# ConfigMatrix: Developer Guide

This document provides technical guidance for developers who need to extend or customize the ConfigMatrix module.

## Architecture Overview

The ConfigMatrix system is designed with a modular architecture to allow for easy extension:

```
ConfigMatrix
├── Data Models
│   ├── Template
│   ├── Section
│   ├── Field
│   ├── Option
│   └── Configuration
├── UI Components
│   ├── Admin Interface
│   ├── Sales Configurator
│   └── Visualization Engine
├── BOM Generation
│   ├── Component Mapping
│   └── Quantity Calculation
└── Integration Points
    ├── Sales
    ├── Manufacturing
    └── Product
```

## Core Technical Concepts

### Configuration Data Storage

ConfigMatrix uses a hybrid approach to data storage:

1. **Structure definition**: Stored in relational tables (template, section, field, option)
2. **Configuration values**: Stored as JSON in the `config_data` field
3. **Generated assets**: BOM, visualization data, and price are stored as separate fields

This provides a balance between structure and flexibility.

### Event-Driven UI

The configuration UI is event-driven, with several key events:

- `field_value_changed`: Fired when a field value changes
- `section_changed`: Fired when navigation between sections occurs
- `visibility_changed`: Fired when field visibility changes
- `validation_failed`: Fired when validation fails
- `preview_updated`: Fired when preview data is refreshed

Developers can hook into these events to add custom behavior.

### Field Visibility Evaluation

Field visibility is determined by evaluating Python expressions against the current configuration values:

```python
def _is_field_visible(self, field, config_values):
    """Check if a field is visible based on configuration values"""
    if not field.visibility_condition:
        return True
    
    try:
        # Create safe evaluation context with configuration values
        ctx = dict(config_values)
        
        # Add math functions
        math_context = {
            'round': round,
            'ceil': math.ceil,
            'floor': math.floor,
            'abs': abs,
            'max': max,
            'min': min,
            'sum': sum
        }
        ctx.update(math_context)
        
        # Evaluate visibility condition
        return safe_eval(field.visibility_condition, ctx)
    except Exception as e:
        _logger.error(f"Error evaluating visibility condition for {field.name}: {str(e)}")
        return True  # Default to visible if evaluation fails
```

## Extending the System

### Adding Custom Field Types

To add a new field type:

1. Extend the field type selection in the `config.matrix.field` model:

```python
class ConfigMatrixField(models.Model):
    _inherit = 'config.matrix.field'
    
    field_type = fields.Selection(selection_add=[
        ('color', 'Color Picker')
    ])
    
    # Custom fields for the new type
    color_format = fields.Selection([
        ('hex', 'Hexadecimal'),
        ('rgb', 'RGB'),
        ('name', 'Color Name')
    ], string="Color Format", default='hex')
```

2. Add rendering support in the configurator component:

```javascript
// In the template
if (field.field_type === 'color') {
    // Render color picker
    <div class="color-picker-container">
        <input type="color" 
               t-att-value="state.values[field.technical_name] || '#000000'"
               t-on-change="(ev) => this.updateValue(field.technical_name, ev.target.value)"/>
    </div>
}

// In the component code
_renderColorField(field) {
    // Custom rendering logic
}
```

3. Add validation support:

```python
def validate_field(self, field, value):
    errors = super().validate_field(field, value)
    
    if field.field_type == 'color' and value:
        if field.color_format == 'hex' and not re.match(r'^#[0-9A-F]{6}$', value, re.I):
            errors.append(f"{field.name} must be a valid hex color code (e.g. #FF0000)")
    
    return errors
```

### Custom Component Mapping

To create a custom component mapping strategy:

1. Subclass the configuration model:

```python
class CustomConfigMatrix(models.Model):
    _inherit = 'config.matrix.configuration'
    
    def generate_bom(self):
        # Check if this is a special product type
        if self.template_id.has_custom_bom_generation:
            return self._generate_custom_bom()
        
        # Fall back to standard BOM generation
        return super().generate_bom()
    
    def _generate_custom_bom(self):
        """Custom BOM generation logic"""
        # Implementation details
        pass
```

2. Add custom fields to support the new strategy:

```python
class ConfigMatrixTemplate(models.Model):
    _inherit = 'config.matrix.template'
    
    has_custom_bom_generation = fields.Boolean("Custom BOM Generation")
    custom_bom_strategy = fields.Selection([
        ('assembly', 'Assembly Components'),
        ('cut_to_size', 'Cut to Size Materials'),
        ('composite', 'Composite Materials')
    ], string="Custom BOM Strategy")
```

### Visualization Extensions

To add custom visualization capabilities:

1. Create a visualization engine class:

```python
class CustomVisualizationEngine:
    def __init__(self, configuration):
        self.configuration = configuration
        self.config_values = json.loads(configuration.config_data or '{}')
        self.template = configuration.template_id
        
    def generate(self):
        """Generate custom visualization"""
        # Implementation specific to your needs
        pass
    
    def _generate_svg(self):
        """Generate SVG representation"""
        # Implementation details
        pass
    
    def _generate_threejs(self):
        """Generate 3D representation using Three.js"""
        # Implementation details
        pass
```

2. Register the custom engine:

```python
class ConfigMatrixConfiguration(models.Model):
    _inherit = 'config.matrix.configuration'
    
    def generate_visualization(self):
        # Check for custom visualization
        if hasattr(self.template_id, 'visualization_engine') and self.template_id.visualization_engine:
            engine = self.template_id.get_visualization_engine()
            if engine == 'custom':
                return self._generate_custom_visualization()
        
        # Fall back to standard visualization
        return super().generate_visualization()
    
    def _generate_custom_visualization(self):
        """Generate visualization using custom engine"""
        engine = CustomVisualizationEngine(self)
        return engine.generate()
```

## Performance Optimization

### Condition Evaluation Caching

To improve performance of visibility condition evaluation:

```python
class ConfigCache(models.AbstractModel):
    _name = 'config.matrix.cache'
    _description = 'Configuration Cache'
    
    @tools.ormcache('field_id', 'json.dumps(values)')
    def evaluate_condition(self, field_id, values):
        """Cached evaluation of visibility conditions"""
        field = self.env['config.matrix.field'].browse(field_id)
        if not field.visibility_condition:
            return True
            
        # Implementation of condition evaluation
        # ...
        
        return result
```

### Batch Field Processing

For complex configurations with many fields:

```python
def update_all_field_visibility(self, values):
    """Update visibility for all fields in batch"""
    visible_fields = set()
    field_ids = self.env['config.matrix.field'].search([
        ('section_id.matrix_id', '=', self.template_id.id)
    ])
    
    # Group fields by dependency
    dependency_graph = self._build_field_dependency_graph(field_ids)
    
    # Process fields in dependency order
    for level in dependency_graph:
        # Process each level in parallel
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = {executor.submit(self._is_field_visible, field, values): field 
                      for field in level}
            for future in as_completed(futures):
                field = futures[future]
                if future.result():
                    visible_fields.add(field.id)
    
    return visible_fields
```

## Integration with External Systems

### Web Service API

To expose configuration functionality via API:

```python
class ConfigMatrixAPI(http.Controller):
    @http.route('/api/config_matrix/templates', auth='api_key', type='json', methods=['GET'])
    def get_templates(self, **kwargs):
        """Get available configuration templates"""
        templates = request.env['config.matrix.template'].search([('active', '=', True)])
        return {
            'status': 'success',
            'data': templates.mapped(lambda t: {
                'id': t.id,
                'name': t.name,
                'product': t.product_template_id.name,
            })
        }
    
    @http.route('/api/config_matrix/configure', auth='api_key', type='json', methods=['POST'])
    def configure_product(self, **kwargs):
        """Create a configuration via API"""
        template_id = kwargs.get('template_id')
        product_id = kwargs.get('product_id')
        values = kwargs.get('values', {})
        
        # Create configuration
        config = request.env['config.matrix.configuration'].sudo().create_or_update_configuration({
            'id': False,
            'template_id': template_id,
            'product_id': product_id,
            'values': values
        })
        
        if not config.get('success'):
            return {
                'status': 'error',
                'message': config.get('error', 'Unknown error')
            }
        
        # Get configuration details
        config_id = config.get('config_id')
        config_obj = request.env['config.matrix.configuration'].sudo().browse(config_id)
        
        return {
            'status': 'success',
            'data': {
                'id': config_obj.id,
                'price': config_obj.price,
                'bom_id': config_obj.bom_id.id if config_obj.bom_id else False,
                'visualization': config_obj.visualization_data
            }
        }
```

### JSON Import/Export

For transferring configurations between systems:

```python
class ConfigMatrixTemplate(models.Model):
    _inherit = 'config.matrix.template'
    
    def export_to_json(self):
        """Export template to JSON format"""
        self.ensure_one()
        
        data = {
            'name': self.name,
            'code': self.code,
            'version': self.version,
            'product': self.product_template_id.name,
            'sections': []
        }
        
        for section in self.section_ids:
            section_data = {
                'name': section.name,
                'sequence': section.sequence,
                'fields': []
            }
            
            for field in section.field_ids:
                field_data = {
                    'name': field.name,
                    'technical_name': field.technical_name,
                    'field_type': field.field_type,
                    'required': field.required,
                    'visibility_condition': field.visibility_condition,
                }
                
                # Add type-specific properties
                if field.field_type == 'selection':
                    field_data['options'] = [
                        {'value': opt.value, 'name': opt.name} 
                        for opt in field.option_ids
                    ]
                
                section_data['fields'].append(field_data)
            
            data['sections'].append(section_data)
        
        return json.dumps(data, indent=2)
    
    @api.model
    def import_from_json(self, json_data):
        """Import template from JSON format"""
        data = json.loads(json_data)
        
        # Implementation details
        pass
```

## Troubleshooting & Debugging

### Debugging Tools

To simplify debugging of complex configurations:

```python
class ConfigDebugger(models.TransientModel):
    _name = 'config.matrix.debugger'
    _description = 'Configuration Debugger'
    
    config_id = fields.Many2one('config.matrix.configuration', "Configuration")
    field_id = fields.Many2one('config.matrix.field', "Field")
    visibility_trace = fields.Text("Visibility Evaluation Trace", readonly=True)
    formula_trace = fields.Text("Formula Evaluation Trace", readonly=True)
    
    @api.onchange('config_id', 'field_id')
    def _onchange_field(self):
        if not self.config_id or not self.field_id:
            return
        
        # Get configuration values
        config_values = json.loads(self.config_id.config_data or '{}')
        
        # Trace visibility condition evaluation
        if self.field_id.visibility_condition:
            self.visibility_trace = self._trace_expression_evaluation(
                self.field_id.visibility_condition, 
                config_values
            )
        
        # Trace quantity formula evaluation
        if self.field_id.quantity_formula:
            self.formula_trace = self._trace_expression_evaluation(
                self.field_id.quantity_formula,
                config_values
            )
    
    def _trace_expression_evaluation(self, expression, values):
        """Trace the evaluation of an expression, step by step"""
        # Implementation for tracing expression evaluation
        pass
```

### Logging and Monitoring

Implement enhanced logging for configuration operations:

```python
class ConfigMatrixLogging(models.Model):
    _inherit = 'config.matrix.configuration'
    
    def generate_bom(self):
        _logger.info("Generating BOM for configuration %s (template: %s)", 
                    self.id, self.template_id.name)
        
        start_time = time.time()
        result = super().generate_bom()
        duration = time.time() - start_time
        
        _logger.info("BOM generation completed in %.2f seconds. Components: %d", 
                    duration, len(result.bom_line_ids))
        
        return result
```

## Testing Framework

### Test Data Generator

Create a test data generator for complex configurations:

```python
class ConfigMatrixTestGenerator(models.TransientModel):
    _name = 'config.matrix.test.generator'
    _description = 'Configuration Test Generator'
    
    template_id = fields.Many2one('config.matrix.template', "Template", required=True)
    test_count = fields.Integer("Number of Test Configurations", default=10)
    test_strategy = fields.Selection([
        ('random', 'Random Values'),
        ('min_max', 'Min/Max Values'),
        ('edge_cases', 'Edge Cases')
    ], string="Test Strategy", default='random')
    
    def generate_test_configurations(self):
        """Generate test configurations based on selected strategy"""
        test_configs = []
        
        for i in range(self.test_count):
            values = self._generate_test_values()
            
            config = self.env['config.matrix.configuration'].create({
                'template_id': self.template_id.id,
                'product_id': self.template_id.product_template_id.product_variant_id.id,
                'config_data': json.dumps(values),
                'name': f"Test Config {i+1} - {self.test_strategy}"
            })
            
            # Generate BOM and price
            config.generate_bom()
            config.calculate_price()
            
            test_configs.append(config.id)
        
        # Show generated configurations
        return {
            'name': _('Generated Test Configurations'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.configuration',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', test_configs)],
        }
    
    def _generate_test_values(self):
        """Generate test values based on strategy"""
        # Implementation details
        pass
```

## Best Practices

### Field Naming Conventions

- Use lowercase with underscores for technical names
- Keep technical names short but descriptive
- Use prefixes for related fields (e.g., `door_height`, `door_width`)
- Don't use special characters or spaces

### Visibility Condition Guidelines

- Keep conditions as simple as possible
- Use parentheses to clarify logic
- Test edge cases (all values at min/max)
- Avoid circular dependencies
- Use default visibility (empty condition) when possible

### Performance Considerations

- Minimize the number of fields in a template
- Group related fields into logical sections
- Use appropriate field types (e.g., selection instead of text when options are fixed)
- Index frequently queried fields
- Cache computed values
- Batch process updates when possible

## Example: Create a Custom Widget

Here's a complete example of creating a custom measurement widget:

```javascript
// Measurement Widget
odoo.define('config_matrix.measurement_widget', function (require) {
"use strict";

var core = require('web.core');
var Widget = require('web.Widget');
var registry = require('web.field_registry');

var MeasurementWidget = Widget.extend({
    template: 'MeasurementWidget',
    events: {
        'change .feet-input': '_onFeetChange',
        'change .inches-input': '_onInchesChange',
        'change .mm-input': '_onMillimetersChange',
        'click .unit-toggle': '_onUnitToggle'
    },
    
    init: function (parent, name, record, options) {
        this._super.apply(this, arguments);
        this.name = name;
        this.record = record;
        this.options = options || {};
        
        // Internal state
        this.unit = 'mm';  // Default unit (mm or imperial)
        this.value = 0;    // Value in mm
    },
    
    start: function () {
        var self = this;
        return this._super.apply(this, arguments).then(function () {
            // Initialize with current value
            var value = self.record.data[self.name] || 0;
            self.value = parseFloat(value);
            
            // Update display
            self._updateDisplay();
        });
    },
    
    _updateDisplay: function () {
        if (this.unit === 'mm') {
            // Update millimeter input
            this.$('.mm-input').val(Math.round(this.value));
            this.$('.imperial-inputs').hide();
            this.$('.metric-inputs').show();
        } else {
            // Update feet and inches inputs
            var totalInches = this.value / 25.4;
            var feet = Math.floor(totalInches / 12);
            var inches = Math.round(totalInches % 12 * 100) / 100;
            
            this.$('.feet-input').val(feet);
            this.$('.inches-input').val(inches);
            this.$('.imperial-inputs').show();
            this.$('.metric-inputs').hide();
        }
    },
    
    _onFeetChange: function (ev) {
        var feet = parseInt(this.$('.feet-input').val()) || 0;
        var inches = parseFloat(this.$('.inches-input').val()) || 0;
        var totalInches = feet * 12 + inches;
        
        this.value = totalInches * 25.4;
        this._setValue(this.value);
    },
    
    _onInchesChange: function (ev) {
        var feet = parseInt(this.$('.feet-input').val()) || 0;
        var inches = parseFloat(this.$('.inches-input').val()) || 0;
        var totalInches = feet * 12 + inches;
        
        this.value = totalInches * 25.4;
        this._setValue(this.value);
    },
    
    _onMillimetersChange: function (ev) {
        this.value = parseFloat(this.$('.mm-input').val()) || 0;
        this._setValue(this.value);
    },
    
    _onUnitToggle: function (ev) {
        this.unit = this.unit === 'mm' ? 'imperial' : 'mm';
        this._updateDisplay();
    },
    
    _setValue: function (value) {
        this.value = value;
        this._updateDisplay();
        
        // Update field value
        this.trigger_up('field_changed', {
            dataPointID: this.record.id,
            changes: {
                [this.name]: this.value
            }
        });
    }
});

registry.add('measurement', MeasurementWidget);

return MeasurementWidget;
});
```

Add the template to QWeb:

```xml
<templates>
    <t t-name="MeasurementWidget">
        <div class="o_measurement_widget">
            <div class="metric-inputs">
                <input class="mm-input form-control" type="number" step="1"/>
                <span class="unit">mm</span>
            </div>
            
            <div class="imperial-inputs" style="display:none;">
                <input class="feet-input form-control" type="number" min="0"/>
                <span class="unit">ft</span>
                <input class="inches-input form-control" type="number" min="0" step="0.01"/>
                <span class="unit">in</span>
            </div>
            
            <button class="unit-toggle btn btn-sm btn-link">
                <i class="fa fa-exchange"></i> Switch Units
            </button>
        </div>
    </t>
</templates>
```

Then use it in the field definition:

```python
class ConfigMatrixField(models.Model):
    _inherit = 'config.matrix.field'
    
    field_type = fields.Selection(selection_add=[
        ('measurement', 'Measurement')
    ])
    
    @api.model
    def get_field_widget(self, field_type):
        """Get appropriate widget for field type"""
        widgets = super().get_field_widget(field_type)
        widgets.update({
            'measurement': 'measurement'
        })
        return widgets
```
