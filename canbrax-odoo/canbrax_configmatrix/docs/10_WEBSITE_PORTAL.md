# website Portal for ConfigMatrix

## Overview

The website Portal is a specialized interface for construction industry professionals to configure and order building products like doors, windows, and cabinets. The portal offers enhanced features tailored to professional websites who frequently order multiple configured products.

## Key Features

### 1. Professional website Interface

- Clean, intuitive interface designed specifically for construction professionals
- Optimized workflow for frequent ordering of configurable products
- Mobile-responsive design that works on construction sites

### 2. Multi-item Management

- Easily duplicate existing configurations with small changes
- Split quantity items into individual configurations
- Batch split functionality for multiple line items at once
- Clear visual summaries of configured products

### 3. Configuration Comparison

- Side-by-side comparison of multiple configurations
- Highlight differences between similar configurations
- Quick add-to-cart from comparison view

### 4. Saved Configurations Library

- Save and reuse common configurations
- Filter configurations by product type (doors, windows, cabinets)
- Easily search through past configurations

## How It Works

### Automatic Redirection for websites

The system automatically detects website users through:
- Membership in the "website" security group
- Partner tags like "website" or "Contractor" 

When a website attempts to configure a product, they are automatically redirected to the specialized website portal interface rather than the standard configurator.

### website Group

A new security group "website" has been added to give specific access rights to professional users:
- Access to the website portal
- Ability to save and reuse configurations
- Batch operations for configured products

## Technical Implementation

The website Portal consists of:

1. **Custom Templates**:
   - `/views/website_portal/website_portal_templates.xml`

2. **Specialized Controller**:
   - `/controllers/website_portal/website_portal_controller.py`

3. **Dedicated Assets**:
   - CSS: `/static/src/css/website_portal/website_portal.css`
   - JS: `/static/src/js/website_portal/website_portal.js`

4. **Security Groups**:
   - `group_config_matrix_website` in security.xml

## Testing

### Demo Data

For testing purposes, two demo website users are provided:

1. **John website**
   - Login: <EMAIL>
   - Password: website
   - Has "website" tag

2. **Sarah Contractor**
   - Login: <EMAIL>
   - Password: contractor
   - Has "Contractor" tag

Sample saved configurations are also included for demonstration.

### Testing Process

1. Log in as one of the demo website users
2. Go to the shop and add a configurable product to your cart
3. Observe that you're redirected to the website portal interface
4. Complete and save the configuration
5. Test the split functionality with quantities greater than 1
6. Try the duplicate configuration feature
7. Explore the saved configurations interface

## Customization

The website portal is designed to be easily customizable:

- Styling can be adjusted through the CSS file
- Workflow modifications can be made in the controller
- Portal layout can be modified through template inheritance

## Future Enhancements

Planned features for future versions:

1. Project-based configuration grouping
2. Bulk-ordering interface for multiple products
3. Template configurations for common builds
4. Job site delivery scheduling integration
5. Item status tracking through manufacturing
