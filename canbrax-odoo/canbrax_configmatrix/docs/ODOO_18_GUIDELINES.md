# 🎯 Odoo 18 Development Guidelines - Strict Compliance

## � Common Syntax Errors and Fixes

### Python Dictionary Syntax Errors
**Error**: `SyntaxError: '{' was never closed`

**Common Cause**: Missing closing brace `}` in dictionary definitions, especially in model `create()` calls.

**Example Fix**:
```python
# ❌ WRONG - Missing closing brace
matrix = self.create({
    'name': name,
    'product_template_id': product_template_id,
    'matrix_type': 'price',
    'last_imported': fields.Datetime.now(),
return matrix

# ✅ CORRECT - Proper closing brace
matrix = self.create({
    'name': name,
    'product_template_id': product_template_id,
    'matrix_type': 'price',
    'last_imported': fields.Datetime.now(),
})
return matrix
```

### Import Resolution Issues
**Error**: `Import "odoo" could not be resolved`

**Solution**: Ensure proper Odoo environment setup and imports:
```python
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
```

### Missing Model Methods
**Error**: `action_open_matrix_editor is not a valid action on config.matrix.labor.time.matrix`

**Solution**: Ensure all referenced methods exist in the model:
```python
def action_open_matrix_editor(self):
    """Open the visual matrix editor for this matrix"""
    self.ensure_one()
    return {
        'name': _('Visual Matrix Editor - %s') % self.name,
        'type': 'ir.actions.act_window',
        'res_model': 'config.matrix.visual.editor',
        'view_mode': 'form',
        'target': 'new',
        'context': {
            'default_matrix_id': self.id,
            'default_matrix_type': 'labor',
        },
    }
```

## �📋 Critical XML Syntax Changes

### ❌ DEPRECATED - Do NOT Use These
```xml
<!-- OLD Odoo 17 and earlier -->
<tree>  <!-- WRONG - Use <list> instead -->
<form attrs="{'invisible': [('field', '=', False)]}">  <!-- WRONG - Use invisible attribute -->
</form>
```

### ✅ CORRECT - Odoo 18 Syntax
```xml
<!-- NEW Odoo 18 syntax -->
<list>  <!-- CORRECT - Use <list> instead of <tree> -->
<form invisible="field == False">  <!-- CORRECT - Direct invisible attribute -->
</form>
```

## 🔄 XML View Element Changes

### List Views (formerly Tree Views)
```xml
<!-- Odoo 18 - Use <list> tag -->
<field name="arch" type="xml">
    <list string="Matrix Categories" editable="bottom">
        <field name="name"/>
        <field name="category_type"/>
        <field name="active"/>
    </list>
</field>
```

### Visibility Conditions
```xml
<!-- Odoo 18 - Direct invisible attribute -->
<field name="field_name" invisible="other_field == False"/>
<field name="conditional_field" invisible="state != 'active'"/>
<group invisible="matrix_type != 'price'"/>
```

### Conditional Attributes
```xml
<!-- Odoo 18 - Direct attribute conditions -->
<field name="amount" readonly="state == 'confirmed'"/>
<button name="action_confirm" invisible="state != 'draft'"/>
<field name="notes" required="is_required == True"/>
```

## 🎨 Modern CSS Guidelines

### Use CSS Grid and Flexbox
```css
/* Modern responsive layouts */
.matrix-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.matrix-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
}
```

### Contemporary Design Patterns
```css
/* Modern card design */
.matrix-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.matrix-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

/* Excel-like cell styling */
.matrix-cell {
    border: 1px solid #d1d5db;
    padding: 8px;
    min-height: 32px;
    background: white;
    transition: all 0.15s ease;
}

.matrix-cell:hover {
    background: #f8fafc;
    border-color: #3b82f6;
}

.matrix-cell:focus {
    outline: 2px solid #3b82f6;
    outline-offset: -2px;
    background: white;
}
```

## 🔧 JavaScript/OWL 2.0 Guidelines

### Modern OWL Components
```javascript
/** @odoo-module **/
import { Component, useState, onMounted, onWillUnmount } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

export class ExcelMatrixEditor extends Component {
    static template = "canbrax_configmatrix.ExcelMatrixEditor";

    setup() {
        this.state = useState({
            matrix: {},
            selectedCell: null,
            editMode: false
        });

        this.rpc = useService("rpc");
        this.notification = useService("notification");

        onMounted(() => {
            this.setupKeyboardHandlers();
        });

        onWillUnmount(() => {
            this.cleanupKeyboardHandlers();
        });
    }

    async loadMatrix() {
        try {
            const result = await this.rpc("/matrix/load", {
                matrix_id: this.props.matrixId
            });
            this.state.matrix = result;
        } catch (error) {
            this.notification.add("Failed to load matrix", { type: "danger" });
        }
    }
}
```

### Modern Event Handling
```javascript
// Use modern event patterns
setupKeyboardHandlers() {
    this.keyHandler = (event) => {
        switch(event.key) {
            case 'ArrowUp':
                event.preventDefault();
                this.navigateCell(-1, 0);
                break;
            case 'ArrowDown':
                event.preventDefault();
                this.navigateCell(1, 0);
                break;
            case 'Tab':
                event.preventDefault();
                this.navigateCell(0, event.shiftKey ? -1 : 1);
                break;
            case 'Enter':
                event.preventDefault();
                this.confirmEdit();
                break;
            case 'Escape':
                event.preventDefault();
                this.cancelEdit();
                break;
        }
    };

    document.addEventListener('keydown', this.keyHandler);
}
```

## 📊 Model Field Definitions

### Modern Field Types
```python
# Use modern field patterns
class ConfigMatrixCategory(models.Model):
    _name = 'config.matrix.category'
    _description = 'Matrix Category'
    _order = 'sequence, name'

    name = fields.Char("Category Name", required=True, translate=True)
    category_type = fields.Selection([
        ('door_frame', 'Door Frame'),
        ('hardware', 'Hardware'),
        ('glass_panel', 'Glass & Panel'),
        ('labor', 'Labor Operations'),
        ('custom', 'Custom')
    ], string="Category Type", required=True, default='door_frame')

    # Use proper field relationships
    matrix_ids = fields.One2many(
        'config.matrix.price.matrix',
        'category_id',
        string="Price Matrices"
    )

    # Modern computed fields
    matrix_count = fields.Integer(
        "Matrix Count",
        compute='_compute_matrix_count',
        store=True
    )

    @api.depends('matrix_ids')
    def _compute_matrix_count(self):
        for category in self:
            category.matrix_count = len(category.matrix_ids)
```

### Validation Patterns
```python
# Modern validation methods
@api.constrains('name', 'category_type')
def _check_unique_category_name(self):
    for category in self:
        existing = self.search([
            ('name', '=', category.name),
            ('category_type', '=', category.category_type),
            ('id', '!=', category.id)
        ])
        if existing:
            raise ValidationError(
                _("Category name must be unique within the same type.")
            )

# API method patterns
@api.model
def create_default_categories(self):
    """Create default matrix categories if they don't exist"""
    defaults = [
        {'name': 'Door Frame Pricing', 'category_type': 'door_frame'},
        {'name': 'Hardware Components', 'category_type': 'hardware'},
        {'name': 'Glass & Panel Options', 'category_type': 'glass_panel'},
        {'name': 'Manufacturing Operations', 'category_type': 'labor'},
    ]

    for default in defaults:
        if not self.search([('category_type', '=', default['category_type'])]):
            self.create(default)
```

## 🎯 Action Definitions

### Modern Action Patterns
```xml
<!-- Window actions with proper context -->
<record id="action_config_matrix_category" model="ir.actions.act_window">
    <field name="name">Matrix Categories</field>
    <field name="res_model">config.matrix.category</field>
    <field name="view_mode">list,form</field>
    <field name="context">{
        'default_active': True,
        'search_default_active': True
    }</field>
    <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
            Create your first matrix category!
        </p>
        <p>
            Organize your pricing and labor matrices by category for better management.
        </p>
    </field>
</record>

<!-- Server actions for complex operations -->
<record id="action_create_sample_matrices" model="ir.actions.server">
    <field name="name">Create Sample Matrices</field>
    <field name="model_id" ref="model_config_matrix_category"/>
    <field name="state">code</field>
    <field name="code">
        env['config.matrix.category'].create_default_categories()
        action = env.ref('canbrax_configmatrix.action_config_matrix_category')
    </field>
</record>
```

## 🔗 Menu Structure

### Modern Menu Hierarchy
```xml
<!-- Root menu -->
<menuitem id="menu_matrix_root"
          name="Matrix"
          sequence="10"
          web_icon="canbrax_configmatrix,static/description/icon.png"/>

<!-- Category submenus -->
<menuitem id="menu_matrix_configuration"
          name="Configuration"
          parent="menu_matrix_root"
          sequence="10"/>

<menuitem id="menu_matrix_pricing"
          name="Pricing Matrices"
          parent="menu_matrix_root"
          sequence="20"/>

<!-- Leaf menu items -->
<menuitem id="menu_config_matrix_template"
          name="Templates"
          parent="menu_matrix_configuration"
          action="action_config_matrix_template"
          sequence="10"/>

<menuitem id="menu_config_matrix_category"
          name="Matrix Categories"
          parent="menu_matrix_configuration"
          action="action_config_matrix_category"
          sequence="20"/>
```

## 🎨 QWeb Template Guidelines

### Modern Template Structure
```xml
<templates id="template" xml:space="preserve">
    <t t-name="canbrax_configmatrix.ExcelMatrixEditor" owl="1">
        <div class="excel-matrix-editor">
            <div class="matrix-toolbar">
                <div class="toolbar-left">
                    <button class="btn btn-primary" t-on-click="saveMatrix">
                        <i class="fa fa-save"/> Save Matrix
                    </button>
                    <button class="btn btn-secondary" t-on-click="testLookup">
                        <i class="fa fa-search"/> Test Lookup
                    </button>
                </div>
                <div class="toolbar-right">
                    <span class="matrix-stats">
                        <t t-esc="state.cellCount"/> cells configured
                    </span>
                </div>
            </div>

            <div class="matrix-grid-container">
                <table class="matrix-grid">
                    <thead>
                        <tr>
                            <th class="corner-cell">H\W</th>
                            <th t-foreach="state.widths" t-as="width" t-key="width">
                                <t t-esc="width"/>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr t-foreach="state.heights" t-as="height" t-key="height">
                            <th class="row-header">
                                <t t-esc="height"/>
                            </th>
                            <td t-foreach="state.widths" t-as="width" t-key="width"
                                class="matrix-cell"
                                t-att-class="{
                                    'selected': isSelected(height, width),
                                    'edited': isEdited(height, width)
                                }"
                                t-on-click="() => this.selectCell(height, width)"
                                t-on-dblclick="() => this.editCell(height, width)">
                                <input t-if="isEditing(height, width)"
                                       type="number"
                                       class="cell-input"
                                       t-model="getCellValue(height, width)"
                                       t-on-blur="confirmEdit"
                                       t-on-keydown="handleCellKeydown"/>
                                <span t-else="" class="cell-value">
                                    <t t-esc="formatCellValue(height, width)"/>
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </t>
</templates>
```

## 🔒 Security Rules

### Modern Security Patterns
```xml
<!-- Record rules with proper domain syntax -->
<record id="matrix_category_user_rule" model="ir.rule">
    <field name="name">Matrix Category User Access</field>
    <field name="model_id" ref="model_config_matrix_category"/>
    <field name="domain_force">[('active', '=', True)]</field>
    <field name="groups" eval="[(4, ref('base.group_user'))]"/>
    <field name="perm_read" eval="True"/>
    <field name="perm_write" eval="False"/>
    <field name="perm_create" eval="False"/>
    <field name="perm_unlink" eval="False"/>
</record>

<!-- Access rights with proper permissions -->
<record id="access_config_matrix_category_user" model="ir.model.access">
    <field name="name">config.matrix.category user</field>
    <field name="model_id" ref="model_config_matrix_category"/>
    <field name="group_id" ref="base.group_user"/>
    <field name="perm_read" eval="True"/>
    <field name="perm_write" eval="False"/>
    <field name="perm_create" eval="False"/>
    <field name="perm_unlink" eval="False"/>
</record>
```

## ⚡ Performance Guidelines

### Database Optimization
```python
# Use proper indexing
class ConfigMatrixPriceMatrix(models.Model):
    _name = 'config.matrix.price.matrix'
    _order = 'name, sequence'

    # Add indexes for frequently queried fields
    product_template_id = fields.Many2one(
        'product.template',
        "Product Template",
        required=True,
        index=True  # Add index for performance
    )

    category_id = fields.Many2one(
        'config.matrix.category',
        string="Matrix Category",
        index=True  # Index for category filtering
    )

    # Use efficient search methods
    @api.model
    def find_matrices_for_template(self, template_id):
        return self.search([
            ('product_template_id', '=', template_id),
            ('active', '=', True)
        ], order='sequence, name')
```

### Frontend Performance
```javascript
// Use efficient DOM manipulation
updateMatrixCell(height, width, value) {
    // Batch DOM updates
    const cellId = `cell_${height}_${width}`;
    const cell = document.getElementById(cellId);

    if (cell) {
        // Use requestAnimationFrame for smooth updates
        requestAnimationFrame(() => {
            cell.textContent = this.formatValue(value);
            cell.classList.add('updated');

            // Remove animation class after animation
            setTimeout(() => {
                cell.classList.remove('updated');
            }, 200);
        });
    }
}
```

## 🚀 Deployment Guidelines

### Asset Management
```xml
<!-- Properly organize assets -->
'assets': {
    'web.assets_backend': [
        # CSS files first
        'canbrax_configmatrix/static/src/css/matrix_base.css',
        'canbrax_configmatrix/static/src/css/excel_editor.css',
        'canbrax_configmatrix/static/src/css/category_management.css',

        # JavaScript files second
        'canbrax_configmatrix/static/src/js/matrix_utils.js',
        'canbrax_configmatrix/static/src/js/excel_editor.js',
        'canbrax_configmatrix/static/src/js/category_manager.js',

        # Templates last
        'canbrax_configmatrix/static/src/xml/matrix_templates.xml',
        'canbrax_configmatrix/static/src/xml/excel_editor_templates.xml',
    ],
}
```

## 🚨 XML Template Error Fixes

### Unescaped Characters in XML Templates
**Error**: `Invalid XML template: Unescaped '<' not allowed in attributes values`

**Common Causes and Fixes**:
```xml
<!-- ❌ WRONG - Unescaped comparison operators -->
<button t-if="state.currentStep < 4">Next</button>
<div t-if="props.category.matrices.length > 0">Content</div>
<div class="step" t-att-class="{'completed': state.currentStep > 1}">

<!-- ✅ CORRECT - Properly escaped operators -->
<button t-if="state.currentStep &lt; 4">Next</button>
<div t-if="props.category.matrices.length &gt; 0">Content</div>
<div class="step" t-att-class="{'completed': state.currentStep &gt; 1}">
```

### XML Entity Escaping Rules
```xml
<!-- Required XML entity escaping in attributes -->
&lt;   <!-- Less than < -->
&gt;   <!-- Greater than > -->
&amp;  <!-- Ampersand & -->
&quot; <!-- Double quote " -->
&apos; <!-- Single quote ' -->
```

## 🎯 **Custom Widget Development - Critical Learnings**

### **Widget Registration Pattern**
**✅ CORRECT Registration (Working Pattern)**:
```javascript
// Use object with component property
registry.category("fields").add("widget_name", {
    component: WidgetClass,
});
```

**❌ INCORRECT Registration (Causes OWL Errors)**:
```javascript
// Direct class registration causes lifecycle errors
registry.category("fields").add("widget_name", WidgetClass);
```

### **Template Access Patterns**
**✅ CORRECT Template Access**:
```xml
<!-- Access props directly -->
<span t-esc="props.name"></span>
<span t-esc="props.record.data[props.name]"></span>
```

**❌ INCORRECT Template Access**:
```xml
<!-- Trying to access component methods causes undefined errors -->
<span t-esc="component.fieldValue"></span>
```

### **Component Architecture**
**✅ WORKING Base Classes**:
- `Component` from `@odoo/owl` - Works for custom widgets
- Copy patterns from existing working widgets (e.g., price_matrix_widget.js)

**❌ PROBLEMATIC Base Classes**:
- `CharField`, `TextField` - Can cause inheritance issues
- Complex field classes without proper props handling

### **Development Process**
1. **Start Simple**: Begin with basic Component and minimal template
2. **Test Registration**: Ensure widget registers and loads without errors
3. **Add Features Incrementally**: Build functionality step by step
4. **Copy Working Patterns**: Use existing successful widgets as templates
5. **Debug Template Issues**: Template errors often show as "Cannot read properties of undefined"

### **Common Error Patterns**
- **"Cannot read properties of undefined (reading 'name')"**: Usually registration or props access issue
- **OWL Lifecycle Errors**: Often caused by incorrect registration pattern
- **Template Compilation Errors**: Check template syntax and prop access patterns

### **Visual Builder Widget Learnings**
- **Canvas Implementation**: Use `position: relative` container with `position: absolute` nodes
- **Drag & Drop**: Implement with `mousedown`, `mousemove`, `mouseup` event handlers
- **State Management**: Use `useState` for reactive node positions and selections
- **Data Persistence**: Save canvas state as JSON string to database field
- **Node Rendering**: Create DOM elements dynamically with proper styling and event handlers

### Label Tag Requirements (Odoo 18)
**Error**: `Label tag must contain a "for". To match label style without corresponding field or button, use 'class="o_form_label"'.`

**Solution**: All label tags must have a `for` attribute linking to the field's `id`:

```xml
<!-- ❌ WRONG - Label without for attribute -->
<label>Matrix Name</label>
<field name="matrix_name"/>

<!-- ❌ WRONG - o_form_label class may not work in all Odoo 18 versions -->
<label class="o_form_label">Matrix Name</label>
<field name="matrix_name"/>

<!-- ✅ CORRECT - Using for attribute with matching field id -->
<label for="matrix_name">Matrix Name</label>
<field name="matrix_name" id="matrix_name"/>
```

**Important**: The `o_form_label` class approach may not work consistently across all Odoo 18 versions. Always use the `for` attribute approach for guaranteed compatibility.

### Common Template Syntax Errors
```xml
<!-- ❌ WRONG - Missing closing tags -->
<div><strong>Text</strong> More text</div>

<!-- ✅ CORRECT - Proper closing tags -->
<div><strong>Text</strong> More text</div>

<!-- ❌ WRONG - Unescaped operators in conditions -->
<div t-if="value > 0 && value < 100">

<!-- ✅ CORRECT - Escaped operators -->
<div t-if="value &gt; 0 &amp;&amp; value &lt; 100">

<!-- ❌ WRONG - Invalid label tags -->
<label>Field Label</label>

<!-- ✅ CORRECT - Proper label tags -->
<label class="o_form_label">Field Label</label>
```

### Custom Widget Requirements (Odoo 18)
**Error**: `Missing template for component "CustomWidgetName"`

**Problem**: Using custom widgets without proper template registration.

```xml
<!-- ❌ WRONG - Custom widget without template -->
<field name="matrix_data" widget="interactive_matrix"/>

<!-- ✅ CORRECT - Use standard widgets -->
<field name="matrix_data" widget="html"/>
<field name="matrix_data" widget="ace" options="{'mode': 'json'}"/>
<field name="matrix_data" widget="text"/>
```

**Solution**: Either create proper OWL templates and register the widget, or use standard Odoo widgets.

### OWL Template Registration (Odoo 18)
**Error**: `Missing template for component "ComponentName"` - inline templates unreliable

**Problem**: Inline templates in JavaScript components may not load consistently in Odoo 18.

```javascript
// ❌ UNRELIABLE - Inline template may not load
export class MyComponent extends Component {
    static template = `<div>My content</div>`;
}

// ✅ RELIABLE - Separate XML template file
export class MyComponent extends Component {
    static template = "module_name.MyComponent";
}
```

**Best Practice**: Always use separate XML template files for OWL components:

1. **Create XML template file**: `static/src/xml/my_templates.xml`
```xml
<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="module_name.MyComponent" owl="1">
        <div>My content</div>
    </t>
</templates>
```

2. **Add to manifest assets**:
```python
'assets': {
    'web.assets_backend': [
        'module_name/static/src/xml/my_templates.xml',
    ],
}
```

3. **Reference in component**:
```javascript
static template = "module_name.MyComponent";
```

### Additional XML Validation Rules (Odoo 18)

#### Form Structure Requirements
```xml
<!-- ❌ WRONG - Missing required attributes -->
<form>
    <field name="name"/>
</form>

<!-- ✅ CORRECT - Proper form structure -->
<form string="Form Title">
    <sheet>
        <field name="name"/>
    </sheet>
</form>
```

#### Field Widget Validation
```xml
<!-- ❌ WRONG - Invalid widget options -->
<field name="data" widget="ace" options="{'mode': json}"/>

<!-- ✅ CORRECT - Proper widget options -->
<field name="data" widget="ace" options="{'mode': 'json'}"/>
```

#### Button Requirements
```xml
<!-- ❌ WRONG - Missing required attributes -->
<button name="action_save">Save</button>

<!-- ✅ CORRECT - Complete button definition -->
<button name="action_save" type="object" string="Save" class="btn-primary"/>
```

#### Notebook and Page Structure
```xml
<!-- ❌ WRONG - Invalid notebook structure -->
<notebook>
    <field name="description"/>
</notebook>

<!-- ✅ CORRECT - Proper notebook with pages -->
<notebook>
    <page string="General" name="general">
        <field name="description"/>
    </page>
</notebook>
```

## ✅ Compliance Checklist

### Before Committing Code
- [ ] All `<tree>` elements replaced with `<list>`
- [ ] All `attrs` attributes replaced with direct attributes
- [ ] All visibility conditions use proper Odoo 18 syntax
- [ ] All comparison operators (`<`, `>`, `<=`, `>=`) properly escaped in XML templates
- [ ] All label tags use `class="o_form_label"` or proper `for` attribute
- [ ] All XML templates validated for proper syntax
- [ ] All widget options properly quoted with single quotes
- [ ] All buttons have required `type` and `string` attributes
- [ ] CSS uses modern patterns (Grid, Flexbox)
- [ ] JavaScript uses OWL 2.0 patterns
- [ ] Models use proper field definitions
- [ ] Security rules are properly defined
- [ ] Assets are properly organized
- [ ] Performance considerations addressed

## 🚨 Quick Reference: Common XML Validation Errors

### Error Messages and Solutions

| Error Message | Solution |
|---------------|----------|
| `Label tag must contain a "for"` | Add `for="field_name"` to label and `id="field_name"` to field |
| `Unescaped '<' not allowed` | Replace `<` with `&lt;` and `>` with `&gt;` |
| `Field "field_name" does not exist` | Add missing field to model or remove from view |
| `action_method_name is not a valid action` | Add missing method to model |
| `Invalid widget options` | Quote widget option values with single quotes |
| `'tree' is not a valid view type` | Replace `<tree>` with `<list>` |
| `'attrs' attribute is deprecated` | Use direct attributes instead of attrs |
| `Missing template for component` | Remove custom widget or create proper template |
| `a mandatory field is not set` | Make field non-required or provide default values |

### XML Validation Commands
```bash
# Quick validation commands for common issues
grep -n "attrs=" *.xml          # Find deprecated attrs
grep -n "<tree" *.xml           # Find old tree views
grep -n "<label>" *.xml         # Find invalid labels
grep -n "[<>]" *.xml            # Find unescaped operators
grep -n "widget.*options" *.xml # Check widget options
```

### Testing Requirements
- [ ] All views load without console errors
- [ ] Excel-like functionality works (keyboard navigation, cell editing)
- [ ] Matrix categorization works correctly
- [ ] Import/export functions properly
- [ ] Integration with existing templates works
- [ ] Mobile responsiveness verified
- [ ] Accessibility standards met

## 🎯 Common Mistakes to Avoid

### ❌ Don't Do This
```xml
<!-- Wrong - Old Odoo syntax -->
<tree>
    <field name="name" attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
</tree>

<!-- Wrong - Incorrect action reference -->
<button name="action_that_doesnt_exist" type="object"/>

<!-- Wrong - Poor CSS practices -->
<style>
    .matrix-cell { position: absolute; left: 10px; top: 10px; }
</style>
```

### ✅ Do This Instead
```xml
<!-- Correct - New Odoo 18 syntax -->
<list>
    <field name="name" readonly="state == 'confirmed'"/>
</list>

<!-- Correct - Proper action reference -->
<button name="%(canbrax_configmatrix.action_config_matrix_category)d" type="action"/>

<!-- Correct - Modern CSS -->
<style>
    .matrix-cell {
        display: grid;
        place-items: center;
        transition: all 0.2s ease;
    }
</style>
```

---

**Always validate your code against this guide before deployment!** 🎯

Following these guidelines ensures your Odoo 18 module is:
- ✅ **Compatible** with Odoo 18 standards
- ✅ **Performant** and scalable
- ✅ **Maintainable** for future updates
- ✅ **Professional** in appearance and function
