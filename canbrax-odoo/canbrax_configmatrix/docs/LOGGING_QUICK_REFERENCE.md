# Logging Quick Reference Guide

## Overview

This is a quick reference for adding logging back to the CanBrax ConfigMatrix module. The comprehensive implementation guide is available in [LOGGING_IMPLEMENTATION_GUIDE.md](LOGGING_IMPLEMENTATION_GUIDE.md).

## Why Logging Was Removed

- **Performance**: Production performance requirements
- **Log Volume**: Reduced log file sizes
- **Maintenance**: Simplified production deployment

## Quick Implementation Steps

### 1. Add Import to File
```python
import logging
_logger = logging.getLogger(__name__)
```

### 2. Use Standard Logging Patterns
```python
# Process initiation
_logger.info("🚀 CANBRAX: Process started")

# Success/completion
_logger.info("✅ CANBRAX: Operation successful")

# Warnings
_logger.warning("⚠️ CANBRAX: Warning condition")

# Errors
_logger.error("❌ CANBRAX: Error occurred")

# Debug information
_logger.debug("🔍 CANBRAX: Debug details")
```

### 3. Key Files to Add Logging

#### `models/sale_order_line.py`
- `_has_mto_and_manufacture_routes()` - Route detection
- `_requires_configured_bom()` - BOM requirement check
- `_prepare_procurement_values()` - BOM injection
- `_action_launch_stock_rule()` - Stock rule processing

#### `models/stock_move.py`
- `_prepare_procurement_values()` - BOM preservation

#### `models/stock_rule.py`
- `_get_matching_bom()` - BOM selection
- `_run_manufacture()` - Manufacturing process
- `_prepare_mo_vals()` - MO creation

#### `models/sale_order.py`
- `action_confirm()` - Order confirmation
- `_validate_configurable_products_boms()` - Validation

## Selective Logging Implementation

### Option 1: Environment Variable
```python
import os
CANBRAX_DEBUG = os.environ.get('CANBRAX_DEBUG', 'False').lower() == 'true'

if CANBRAX_DEBUG:
    _logger.info("🔍 CANBRAX: Debug information")
```

### Option 2: Configuration Flag
```python
# Add to module configuration
ENABLE_MANUFACTURING_LOGGING = False  # Set to True when needed

if ENABLE_MANUFACTURING_LOGGING:
    _logger.info("🎯 CANBRAX: Manufacturing process")
```

### Option 3: Method-Level Control
```python
def _prepare_procurement_values(self, group_id=False, enable_logging=False):
    values = super()._prepare_procurement_values(group_id)
    
    if enable_logging and self.is_configurable:
        _logger.info("🎯 CANBRAX: Added BOM to procurement values")
    
    return values
```

## Manufacturing Order Creation Logging Points

### Critical Logging Points
1. **Sale Order Confirmation Start**
2. **BOM Validation Process**
3. **Procurement Value Preparation**
4. **Stock Rule Launch**
5. **BOM Preservation in Stock Moves**
6. **Manufacturing Rule Selection**
7. **BOM Matching Process**
8. **Manufacturing Order Creation**
9. **Sale Line Linking**
10. **Process Completion**

### Expected Log Flow
```
🚀 CANBRAX: Sale Order confirmation started
🔍 CANBRAX: Validating configurable products BOMs
✅ CANBRAX: Validation passed
🚀 CANBRAX: _action_launch_stock_rule called
🎯 CANBRAX: Added BOM to procurement values
🔄 CANBRAX: stock.move._prepare_procurement_values called
✅ CANBRAX: Preserved configuration BOM
🏭 CANBRAX: _get_matching_bom called
✅ CANBRAX: Using configuration BOM
🏭 CANBRAX: _run_manufacture called
🏭 CANBRAX: _prepare_mo_vals called
🔗 CANBRAX: Added sale_line_id to MO values
🎉 CANBRAX: Sale Order confirmation completed
```

## Configuration Options

### Odoo Configuration
```ini
# For development/debugging
[options]
log_level = debug
log_handler = :DEBUG

# For production with selective logging
[options]
log_level = info
log_handler = :INFO
```

### Python Logger Configuration
```python
# Set specific module logging level
logging.getLogger('odoo.addons.canbrax_configmatrix').setLevel(logging.INFO)

# Set specific file logging level
logging.getLogger('odoo.addons.canbrax_configmatrix.models.sale_order_line').setLevel(logging.DEBUG)
```

## Log Analysis Commands

### Filter CanBrax Logs
```bash
# All CanBrax logs
grep "CANBRAX:" /var/log/odoo/odoo.log

# Manufacturing specific
grep "🏭 CANBRAX:" /var/log/odoo/odoo.log

# Errors only
grep "❌ CANBRAX:" /var/log/odoo/odoo.log

# Success operations
grep "✅ CANBRAX:" /var/log/odoo/odoo.log
```

### Real-time Monitoring
```bash
# Follow logs in real-time
tail -f /var/log/odoo/odoo.log | grep "CANBRAX:"

# Follow specific process
tail -f /var/log/odoo/odoo.log | grep "🚀 CANBRAX:"
```

## Performance Considerations

### Efficient Logging
```python
# Good: Check log level first
if _logger.isEnabledFor(logging.DEBUG):
    _logger.debug("🔍 CANBRAX: Expensive operation: %s", expensive_function())

# Better: Use lazy evaluation
_logger.debug("🔍 CANBRAX: Data: %s", lambda: complex_data_structure)
```

### Production Settings
```python
# Minimal production logging
_logger.info("✅ CANBRAX: Manufacturing Order %s created", mo.name)

# Avoid in production
_logger.debug("🔍 CANBRAX: Full procurement values: %s", values)  # Too verbose
```

## Common Logging Patterns

### Method Entry/Exit
```python
def _prepare_procurement_values(self, group_id=False):
    _logger.debug("🔍 CANBRAX: Entering _prepare_procurement_values for %s", self.product_id.name)
    
    values = super()._prepare_procurement_values(group_id)
    
    _logger.debug("✅ CANBRAX: Exiting _prepare_procurement_values with %d values", len(values))
    return values
```

### Error Handling
```python
try:
    result = self._generate_bom()
    _logger.info("✅ CANBRAX: BOM generated successfully: %s", result.id)
except Exception as e:
    _logger.error("❌ CANBRAX: BOM generation failed: %s", str(e))
    _logger.debug("🔍 CANBRAX: Full traceback:", exc_info=True)
    raise
```

### Data Validation
```python
if not self.config_id:
    _logger.warning("⚠️ CANBRAX: Missing configuration for product %s", self.product_id.name)
    return False

if not self.config_id.bom_id:
    _logger.warning("⚠️ CANBRAX: Missing BOM for configuration %s", self.config_id.id)
    return False

_logger.info("✅ CANBRAX: Configuration validation passed for %s", self.product_id.name)
```

## Troubleshooting with Logs

### No Manufacturing Order Created
Look for these log patterns:
```
❌ Missing: "🏭 CANBRAX: _run_manufacture called"
❌ Missing: "✅ CANBRAX: Selected rule: (Action: manufacture)"
```

### Wrong BOM Used
Look for these log patterns:
```
❌ Missing: "✅ CANBRAX: Using configuration BOM"
❌ Present: "ℹ️ CANBRAX: No bom_id in procurement values"
```

### MO Not Linked to Sale Line
Look for these log patterns:
```
❌ Missing: "🔗 CANBRAX: Added sale_line_id to MO values"
❌ Missing: "🔗 CANBRAX: Preserved sale_line_id in stock move"
```

## Quick Enable/Disable

### Global Toggle
```python
# Add to module's __init__.py or main model
CANBRAX_LOGGING_ENABLED = False  # Change to True to enable

# In each method
if CANBRAX_LOGGING_ENABLED:
    _logger.info("🎯 CANBRAX: Process information")
```

### Environment-Based Toggle
```bash
# Enable logging
export CANBRAX_DEBUG=true

# Disable logging
export CANBRAX_DEBUG=false
```

## Related Documentation

- **[LOGGING_IMPLEMENTATION_GUIDE.md](LOGGING_IMPLEMENTATION_GUIDE.md)**: Complete implementation guide
- **[04_MANUFACTURING_ORDER_CREATION.md](04_MANUFACTURING_ORDER_CREATION.md)**: Manufacturing workflow
- **[05_MANUFACTURING_TECHNICAL_IMPLEMENTATION.md](05_MANUFACTURING_TECHNICAL_IMPLEMENTATION.md)**: Technical details
- **[MANUFACTURING_QUICK_REFERENCE.md](MANUFACTURING_QUICK_REFERENCE.md)**: Manufacturing quick reference

This quick reference provides the essential information needed to add logging back to the CanBrax ConfigMatrix module when debugging or monitoring is required.
