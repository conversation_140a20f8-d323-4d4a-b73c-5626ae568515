# ConfigMatrix: BOM Generation

This document details how the ConfigMatrix system automatically generates Bills of Materials (BOMs) based on configuration choices.

## BOM Generation Process

The BOM generation process takes the user's configuration choices and translates them into components that make up the final product. This happens in several steps:

1. **Configuration Mapping**: Map configuration answers to components
2. **Quantity Calculation**: Calculate required quantities for each component
3. **BOM Creation**: Create the BOM in Odoo's manufacturing module
4. **Component Additions**: Add components to the BOM based on configuration

## Implementation Details

### 1. Configuration to BOM Mapping

Each field or option in the configuration can map to a product component in the BOM. This is defined in two places:

1. **Field Component Mapping**: A product linked directly to a configuration field
2. **Option Component Mapping**: A product linked to a specific option of a selection field

This approach allows for maximum flexibility in BOM generation:

- Text and numeric fields can map to components with quantity formulas
- Selection fields can map each option to different components
- Complex component structures can be built with nested dependencies

### 2. Quantity Formulas

Each component can have a quantity formula that dynamically calculates the required quantity based on the configuration values. This is particularly useful for components that depend on dimensions or other numeric inputs.

Quantity formulas are Python expressions that have access to all configuration values, allowing for complex calculations such as:

- Area calculations for materials (e.g., `height * width / 1000000` for square meters)
- Length calculations for linear components (e.g., `2 * height + 2 * width` for frame pieces)
- Quantity based on other selections (e.g., `3 if hinge_count == "3" else 2`)

### 3. BOM Generation Logic

The core BOM generation method is implemented in the `config.matrix.configuration` model:

```python
def generate_bom(self):
    """Generate BOM from configuration"""
    self.ensure_one()
    
    if not self.config_data:
        return False
    
    # Parse configuration data
    config_values = json.loads(self.config_data)
    
    # Create or update BOM
    BOM = self.env['mrp.bom']
    BOMLine = self.env['mrp.bom.line']
    
    bom_vals = {
        'product_tmpl_id': self.product_id.product_tmpl_id.id,
        'product_id': self.product_id.id,
        'code': f'CONFIG-{self.id}',
        'type': 'normal',
    }
    
    if self.bom_id:
        # Update existing BOM
        bom = self.bom_id
        # Clear existing BOM lines
        bom.bom_line_ids.unlink()
        bom.write(bom_vals)
    else:
        # Create new BOM
        bom = BOM.create(bom_vals)
        self.bom_id = bom.id
    
    # Create BOM lines
    bom_lines = []
    
    # 1. Add base products (always included)
    for product in self.matrix_id.bom_product_ids:
        bom_lines.append({
            'bom_id': bom.id,
            'product_id': product.id,
            'product_qty': 1.0,
        })
    
    # 2. Process field components
    for section in self.matrix_id.section_ids:
        for field in section.field_ids:
            # Skip fields that aren't visible based on configuration
            if not self._is_field_visible(field, config_values):
                continue
            
            # Get field value
            field_value = config_values.get(field.technical_name)
            if not field_value and field.field_type != 'boolean':
                continue
            
            # Add component if field has a component product
            if field.component_product_id:
                qty = self._calculate_component_quantity(field.quantity_formula, config_values, 1.0)
                
                bom_lines.append({
                    'bom_id': bom.id,
                    'product_id': field.component_product_id.id,
                    'product_qty': qty,
                })
            
            # For selection fields, check if selected option has component
            if field.field_type == 'selection':
                option = self.env['config.matrix.option'].search([
                    ('field_id', '=', field.id),
                    ('value', '=', field_value)
                ], limit=1)
                
                if option and option.component_product_id:
                    qty = self._calculate_component_quantity(option.quantity_formula, config_values, 1.0)
                    
                    bom_lines.append({
                        'bom_id': bom.id,
                        'product_id': option.component_product_id.id,
                        'product_qty': qty,
                    })
    
    # Create all BOM lines
    for line in bom_lines:
        BOMLine.create(line)
    
    return bom

def _calculate_component_quantity(self, formula, config_values, default=1.0):
    """Calculate component quantity using formula"""
    if not formula:
        return default
    
    try:
        # Create safe evaluation context with configuration values
        ctx = dict(config_values)
        
        # Add math functions
        math_context = {
            'round': round,
            'ceil': math.ceil,
            'floor': math.floor,
            'abs': abs,
            'max': max,
            'min': min,
            'sum': sum
        }
        ctx.update(math_context)
        
        # Evaluate formula
        qty = safe_eval(formula, ctx)
        return float(qty)
    except Exception as e:
        _logger.error(f"Error evaluating quantity formula '{formula}': {str(e)}")
        return default

def _is_field_visible(self, field, config_values):
    """Check if a field is visible based on configuration values"""
    if not field.visibility_condition:
        return True
    
    try:
        # Create safe evaluation context with configuration values
        ctx = dict(config_values)
        
        # Add math functions
        math_context = {
            'round': round,
            'ceil': math.ceil,
            'floor': math.floor,
            'abs': abs,
            'max': max,
            'min': min,
            'sum': sum
        }
        ctx.update(math_context)
        
        # Evaluate visibility condition
        return safe_eval(field.visibility_condition, ctx)
    except Exception as e:
        _logger.error(f"Error evaluating visibility condition for {field.name}: {str(e)}")
        return True  # Default to visible if evaluation fails
```

## Example: Door Configuration to BOM

Let's illustrate how a door configuration would translate into a BOM:

### Configuration:
```
Door Type: Hinged
Door Height: 2100mm
Door Width: 900mm
Frame Color: Black
Lock Type: Premium
Lock Color: Black
Number of Hinges: 3
```

### Resulting BOM:
1. **Door Frame** (base product, always included)
   - Quantity: 1
   
2. **Door Panel**
   - Component linked to door dimensions (Door Height & Door Width)
   - Quantity formula: `1`
   
3. **Black Paint**
   - Component linked to Frame Color option "Black"
   - Quantity formula: `(door_height * door_width) / 1000000 * 0.25` (coverage based on area)
   
4. **Premium Lock**
   - Component linked to Lock Type option "Premium"
   - Quantity formula: `1`
   
5. **Lock Paint - Black**
   - Component linked to Lock Color option "Black"
   - Quantity formula: `1`
   
6. **Security Hinges**
   - Component linked to Number of Hinges option "3"
   - Quantity formula: `3` (direct from option value)

## Utility Methods

### Update BOM from Configuration

When a configuration is updated, the BOM is regenerated:

```python
def action_update_bom(self):
    """Regenerate BOM when configuration is updated"""
    self.ensure_one()
    
    if not self.config_data:
        raise UserError(_("No configuration data available"))
    
    # Generate BOM
    bom = self.generate_bom()
    
    return {
        'type': 'ir.actions.act_window',
        'name': _('Bill of Materials'),
        'res_model': 'mrp.bom',
        'res_id': bom.id,
        'view_mode': 'form',
        'target': 'current',
    }
```

### Preview BOM Before Saving

Allow users to preview the BOM before finalizing:

```python
def action_preview_bom(self):
    """Preview BOM without saving"""
    self.ensure_one()
    
    if not self.config_data:
        raise UserError(_("No configuration data available"))
    
    # Create temporary BOM
    temp_bom = self.with_context(preview_mode=True).generate_bom()
    
    # Return BOM view
    return {
        'type': 'ir.actions.act_window',
        'name': _('BOM Preview'),
        'res_model': 'mrp.bom',
        'res_id': temp_bom.id,
        'view_mode': 'form',
        'target': 'new',
        'context': {'create': False, 'edit': False}
    }
```

## Handling Special Cases

### Conditional Components

Some components should only be included under specific conditions beyond simple field values. These can be handled using special visibility formulas:

```python
# Example: Add a reinforcement bar only for doors over 2000mm high
field = self.env['config.matrix.field'].create({
    'name': 'Reinforcement Bar',
    'technical_name': 'reinforcement_bar',
    'section_id': hardware_section.id,
    'field_type': 'boolean',
    'default_value': 'True',
    'visibility_condition': 'door_height >= 2000',
    'component_product_id': reinforcement_product.id
})
```

### Assembly Instructions

For cases where assembly is conditional (like the pickup example), we can include instruction documents in the BOM:

```python
# Example: Include assembly instructions for pickup orders
field = self.env['config.matrix.field'].create({
    'name': 'Assembly Instructions',
    'technical_name': 'assembly_instructions',
    'section_id': documentation_section.id,
    'field_type': 'boolean',
    'default_value': 'True',
    'visibility_condition': 'delivery_pickup == "Pick Up"',
    'component_product_id': instruction_document.id
})
```

### Component Variants

Selection fields can map to different variants of the same component type:

```python
# Example: Different lock types as product variants
lock_field = self.env['config.matrix.field'].create({
    'name': 'Lock Type',
    'technical_name': 'lock_type',
    'section_id': hardware_section.id,
    'field_type': 'selection'
})

# Create options with different product variants
options = [
    ('standard', 'Standard Lock', standard_lock_product.id),
    ('premium', 'Premium Lock', premium_lock_product.id),
    ('digital', 'Digital Lock', digital_lock_product.id),
    ('none', 'No Lock', False)
]

for value, name, product_id in options:
    self.env['config.matrix.option'].create({
        'field_id': lock_field.id,
        'value': value,
        'name': name,
        'component_product_id': product_id
    })
```

## Price Calculation

The ConfigMatrix system also calculates the final price based on the BOM components:

```python
def calculate_price(self):
    """Calculate price based on BOM components"""
    self.ensure_one()
    
    if not self.bom_id:
        # Generate BOM if not exists
        self.generate_bom()
    
    if not self.bom_id:
        return 0.0
    
    # Get base product price
    base_price = self.product_id.list_price
    
    # Calculate component cost
    component_cost = 0.0
    for line in self.bom_id.bom_line_ids:
        component_cost += line.product_id.standard_price * line.product_qty
    
    # Apply markup
    markup_factor = 1.3  # 30% markup, configurable
    calculated_price = component_cost * markup_factor
    
    # Use higher of calculated price or base price
    price = max(calculated_price, base_price)
    
    # Update price
    self.price = price
    
    return price
```

## Visualization Integration

The BOM generation can also create visualization data for displaying the configured product:

```python
def generate_visualization(self):
    """Generate visualization data based on configuration"""
    self.ensure_one()
    
    if not self.config_data:
        return False
    
    # Parse configuration data
    config_values = json.loads(self.config_data)
    
    # Generate SVG or other visualization format
    # This would be customized to your specific product types
    
    # Example for doors
    if 'door_height' in config_values and 'door_width' in config_values:
        height = float(config_values.get('door_height', 2000))
        width = float(config_values.get('door_width', 900))
        
        # Generate SVG with proper dimensions
        svg = f"""
        <svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
            <rect x="50" y="50" width="200" height="300" 
                  style="fill:white;stroke:black;stroke-width:2" />
            
            <!-- Door dimensions -->
            <text x="150" y="30" text-anchor="middle">{width}mm</text>
            <text x="30" y="200" text-anchor="middle" transform="rotate(-90,30,200)">{height}mm</text>
            
            <!-- Add other components based on configuration -->
            {self._generate_lock_svg(config_values)}
            {self._generate_hinges_svg(config_values)}
        </svg>
        """
        
        # Store visualization
        self.visualization_data = svg
        
        return svg
    
    return False

def _generate_lock_svg(self, config_values):
    """Generate SVG for lock component"""
    lock_type = config_values.get('lock_type')
    if not lock_type or lock_type == 'none':
        return ''
    
    # Position lock on the door
    return '<rect x="220" y="200" width="20" height="40" style="fill:gray;stroke:black;stroke-width:1" />'

def _generate_hinges_svg(self, config_values):
    """Generate SVG for hinges"""
    door_type = config_values.get('door_type')
    if door_type != 'Hinged':
        return ''
    
    hinge_count = config_values.get('hinge_count', '2')
    
    svg = ''
    if hinge_count == '2':
        svg += '<rect x="60" y="100" width="10" height="20" style="fill:silver;stroke:black;stroke-width:1" />'
        svg += '<rect x="60" y="280" width="10" height="20" style="fill:silver;stroke:black;stroke-width:1" />'
    elif hinge_count == '3':
        svg += '<rect x="60" y="100" width="10" height="20" style="fill:silver;stroke:black;stroke-width:1" />'
        svg += '<rect x="60" y="190" width="10" height="20" style="fill:silver;stroke:black;stroke-width:1" />'
        svg += '<rect x="60" y="280" width="10" height="20" style="fill:silver;stroke:black;stroke-width:1" />'
    elif hinge_count == '4':
        svg += '<rect x="60" y="100" width="10" height="20" style="fill:silver;stroke:black;stroke-width:1" />'
        svg += '<rect x="60" y="160" width="10" height="20" style="fill:silver;stroke:black;stroke-width:1" />'
        svg += '<rect x="60" y="220" width="10" height="20" style="fill:silver;stroke:black;stroke-width:1" />'
        svg += '<rect x="60" y="280" width="10" height="20" style="fill:silver;stroke:black;stroke-width:1" />'
    
    return svg
```

## Summary

The BOM generation system in ConfigMatrix:

1. **Maps configuration choices directly to components** using a flexible mapping system
2. **Calculates quantities dynamically** using formulas that reference other configuration values
3. **Generates complete BOMs** with all required components for manufacturing
4. **Updates existing BOMs** when configurations change
5. **Supports complex dependencies** between components and configuration options
6. **Integrates with visualization** to show the configured product

This approach ensures that every configuration choice directly impacts the resulting BOM, allowing for accurate manufacturing instructions and costing.
