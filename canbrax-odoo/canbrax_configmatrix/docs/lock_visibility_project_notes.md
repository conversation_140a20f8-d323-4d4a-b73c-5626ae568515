# Lock Type Visibility Project - Implementation Notes

## Project Overview
Create Python expressions for lock type option visibility in Odoo Product Configurator based on lock height and door dimensions.

## Current Status
- ✅ Field visibility expressions (mostly complete)
- ✅ Component mappings (handled)
- 🔄 **Lock Type Option Visibility** (current focus)

---

## Problem Analysis

### Original Complex Conditions
The provided conditions were overly complex with contradictory OR clauses:
```
Lock Height >= (Smallest Door Height/2 + 79mm) & 1250mm <= Lock Height <= (Smallest Door Height - 940mm) OR
Lock Height < (Smallest Door Height/2 + 79mm) & 1100mm <= Lock Height <= (Smallest Door Height - 1090mm)
```

### Simplified Approach
Break down into clear, maintainable expressions based on:
1. **Lock height thresholds** (e.g., 1050mm, 971mm, 892mm)
2. **Door height relationships** (Smallest Door Height - offset)
3. **Lock position logic** (upper/lower half of door)

---

## Technical Requirements

### 1. Calculated Field for Smallest Door Height
**Field Name:** `_CALCULATED_smallest_door_height`

**Purpose:** Calculate the smallest door height based on deduction assistance mode

**Logic:**
- Manual mode: `min(left_door_height, right_door_height)`
- Even split: `each_door_height`
- Uneven split: `min(left_door_height, right_door_height)`

### 2. Lock Brands & Complexity Levels

#### Simple Patterns:
- **CommandeX Hinged**
- **Lockwood 8654** 
- **Whitco Tasman ESCAPE**
- **All CUT OUT ONLY variants**

#### Complex Patterns:
- **Austral Elegance XC**
- **Whitco MK2**

### 3. Lock Height Locations
Each brand supports 4 height measurement types:
- Top of Cut Out
- Centre of Cut Out  
- Bottom of Cut Out
- Centre of Handle Lever

### 4. Lock Type Options
Standard options across brands:
- Single
- 3PT (various types)
- Double with TOP Auxiliary ONLY
- Double with BOTTOM Auxiliary ONLY
- 3PT with TOP/BOTTOM Auxiliary CUT DOWN
- Double with TOP/BOTTOM Auxiliary CUT DOWN

---

## Implementation Strategy

### Phase 1: Setup Calculated Field ✏️
1. Create `_CALCULATED_smallest_door_height` calculated field
2. Test field appears in debug conditions
3. Verify calculation works correctly

### Phase 2: Simple Brand Testing 🧪
1. Start with **Whitco Tasman ESCAPE** (simplest conditions)
2. Test one lock type visibility expression
3. Verify expression works in configurator

### Phase 3: Complex Brand Implementation 🔧
1. Implement **Austral Elegance XC** expressions
2. Add deduction assistance logic to all expressions
3. Test all lock type options

### Phase 4: Complete All Brands 🏁
1. Apply patterns to remaining brands
2. Create comprehensive expression library
3. Document all expressions for maintenance

---

## File Structure

### Expressions Library
- `lock_type_visibility_expressions.md` - All simplified expressions
- `field_visibility_expressions.csv` - Field visibility (mostly complete)
- `calculated_fields.py` - Calculated field definitions

### Testing Documentation
- `test_scenarios.md` - Test cases for each brand/height combination
- `debug_guide.md` - How to use Odoo debug conditions

---

## Key Insights

### Pattern Recognition
1. **Simple brands**: Basic threshold comparisons
2. **Complex brands**: Multi-condition logic with door height ratios
3. **Deduction assistance**: Changes which door height fields to use

### Critical Success Factors
1. **Calculated field accuracy** - All expressions depend on correct smallest_door_height
2. **Expression simplification** - Maintainable vs. complex logic
3. **Testing methodology** - Systematic validation across scenarios

---

## Next Steps

1. **Immediate**: Create calculated field for smallest_door_height
2. **Test**: Single expression with Whitco Tasman ESCAPE
3. **Validate**: Debug conditions show correct calculated values
4. **Scale**: Apply to all lock brands systematically

---

## Notes for Developer

### Odoo Specifics
- Calculated fields appear as `_CALCULATED_` prefix in debug
- Use proper Python syntax for Odoo expressions
- Handle None/null values with `or 9999` pattern
- Test expressions in "Show Conditions" debug mode

### Field Naming Convention
```
bx_dbl_hinge_[brand]_[type]_lh_[location]
bx_dbl_hinge_[brand]_[type]_lt_[location]
```

### Door Height Fields
```
Manual: bx_dbl_hinge_make_left_door_height_mm_manual
        bx_dbl_hinge_make_right_door_height_mm_manual

Even:   bx_dbl_hinge_make_each_door_height_mm_even

Uneven: bx_dbl_hinge_make_left_door_height_mm_uneven
        bx_dbl_hinge_make_right_door_height_mm_uneven
```