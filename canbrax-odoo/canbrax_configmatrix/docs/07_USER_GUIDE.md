# ConfigMatrix: User Guide

This guide provides instructions for end users on how to use the ConfigMatrix system for configuring products within Odoo.

## Introduction

ConfigMatrix allows you to easily configure complex products with multiple options and variants. The system guides you through each step of the configuration process, shows you a visual preview of your configured product, calculates accurate pricing, and automatically generates the bill of materials (BOM) for manufacturing.

## Sales Process Overview

The typical workflow for configuring a product is:

1. Add a configurable product to a sales order
2. Click the "Configure" button on the order line
3. Complete the configuration by answering all required questions
4. Review the configuration, price, and visual preview
5. Confirm the configuration
6. Proceed with the order as usual

## Configuring a Product

### Starting a Configuration

1. Open a sales order or create a new one
2. Add a configurable product to the order (configurable products are marked with the ⚙️ icon)
3. Click the "Configure" button on the order line

![Configure Button](../static/images/user_configure_button.png)

### Navigation

The configuration interface is divided into sections. Each section contains related questions or options.

![Configuration Interface](../static/images/user_config_interface.png)

Navigation controls:
- **Next Section**: Move to the next section
- **Previous Section**: Move to the previous section
- **Section Tabs**: Jump directly to a specific section
- **Progress Bar**: Shows your progress through the configuration

### Answering Questions

Each question in the configuration has:
- **Label**: The name of the option or question
- **Input Field**: Where you enter your answer
- **Help Text**: Additional information or instructions (when available)
- **Validation Messages**: Error messages if your answer doesn't meet requirements

Types of questions you may encounter:

#### Text Input
Simply type your answer in the text field. Some text fields may have minimum or maximum length requirements.

![Text Input](../static/images/user_text_input.png)

#### Number Input
Enter numeric values. Number fields often have minimum and maximum values.

![Number Input](../static/images/user_number_input.png)

#### Selection
Choose from a list of predefined options.

![Selection Input](../static/images/user_selection_input.png)

#### Yes/No
Toggle between Yes and No.

![Yes/No Input](../static/images/user_boolean_input.png)

### Dynamic Questions

Some questions may appear or disappear based on your answers to other questions. For example, if you select "Hinged Door" as the door type, you'll see questions about hinges that wouldn't appear if you selected "Sliding Door".

### Validation

The system validates your answers in real-time:
- **Required Fields**: Must be completed before continuing
- **Range Validation**: Numbers must be within specified ranges
- **Format Validation**: Text must match required formats (when applicable)

Error messages appear directly below the field when validation fails.

![Validation Error](../static/images/user_validation_error.png)

### Visual Preview

As you configure your product, a visual preview updates in real-time to show you what your configured product will look like.

![Visual Preview](../static/images/user_visual_preview.png)

The preview panel includes:
- **Visual Representation**: A diagram or rendering of your product
- **Price**: The calculated price based on your configuration
- **Key Specifications**: Important dimensions or features

### Saving and Completing the Configuration

Once you've answered all required questions:

1. Review your configuration
2. Click "Save Configuration"
3. The system returns you to the sales order with the updated product information

The order line will now show:
- Updated product description with configuration details
- Accurate price based on your configuration
- A "Reconfigure" button if you need to make changes

## Reusing Configurations

### Saving Configurations for Future Use

If you frequently use the same configuration, you can save it for future use:

1. Configure a product as usual
2. Before saving, check the "Save for Reuse" option
3. Enter a name for the configuration
4. Optionally, associate it with a specific customer
5. Click "Save Configuration"

![Save Configuration](../static/images/user_save_config.png)

### Applying Saved Configurations

To apply a saved configuration:

1. Add the configurable product to an order
2. Click "Configure"
3. Click "Load Saved Configuration"
4. Select the configuration from the list
5. Click "Apply"

All fields will be automatically filled with the saved values. You can still make changes if needed before saving.

## Order Management

### Viewing Configuration Details

To view the details of a configured product:

1. Open the sales order
2. Click on the "i" icon next to the configured product
3. A dialog appears showing the complete configuration details

![Configuration Details](../static/images/user_config_details.png)

### Modifying a Configuration

To change an existing configuration:

1. Open the sales order
2. Click the "Reconfigure" button on the configured product line
3. Make your changes
4. Click "Save Configuration"

Note: After the order is confirmed, configurations can no longer be modified.

## Manufacturing Integration

When a sales order with configured products is confirmed and a manufacturing order is created:

1. The system automatically selects the correct BOM based on the configuration
2. All components are included according to the configuration
3. Manufacturing instructions reflect the specific configuration
4. Production can proceed without manual intervention

Manufacturing staff can view the complete configuration details from the manufacturing order to ensure accurate production.

![Manufacturing View](../static/images/user_manufacturing_view.png)

## Tips and Best Practices

### Navigation Shortcuts

- **Tab**: Move to the next field
- **Shift+Tab**: Move to the previous field
- **Enter** (when focused on a field): Submit and validate the current field
- **Page Down**: Go to the next section
- **Page Up**: Go to the previous section

### Efficient Configuration

- **Complete sections in order**: Some later questions may depend on earlier answers
- **Watch for validation messages**: Fix errors as they appear rather than waiting until the end
- **Use the visual preview**: Confirm that your configuration looks correct
- **Save frequently used configurations**: Save time on repeat orders

### Common Issues

- **Missing fields**: If a field you expect to see is not visible, check your answers to related questions, as some fields only appear based on certain conditions
- **Price changes**: The price updates automatically as you configure the product. Major changes in price may indicate that you've selected options that significantly affect the cost
- **Validation errors**: Red error messages indicate problems that must be fixed before proceeding

## Customer-Specific Configurations

For products that are frequently ordered by specific customers with the same configuration:

1. Create and save the configuration once
2. Associate it with the customer
3. Next time the customer orders, simply load their saved configuration

This ensures consistency across repeat orders and saves time.

## Bulk Configuration

For orders with multiple identical items:

1. Configure one product line completely
2. Save the configuration
3. Add additional lines with the same product
4. Apply the saved configuration to each line
5. Adjust quantities as needed

## Walk Through

ConfigMatrix: User Experience Guide
Let me walk you through what you can expect as a user of the ConfigMatrix module in Odoo 18. This guide will explain the typical workflow from a user's perspective.
Initial Setup
As an administrator or product manager, you'll first need to set up configurable products:

Creating a Configurable Product:

Go to Products → Products
Create a new product or select an existing one
Check the "Configurable Product" checkbox
Save the product
Click the "Create Configuration Template" button that appears


Building the Configuration Template:

Once you click "Create Configuration Template," you'll be taken to a new screen
Here you can define sections, fields, and options for your configurable product
Each section represents a group of related questions (e.g., "Basic Dimensions," "Hardware Options")
Within each section, you add fields (questions) that customers will answer
For selection fields, you add options that users can choose from



Sales Process
Once your products are configured, here's how the sales process works:
1. Creating a Sales Order

Navigate to Sales → Orders → Quotations
Create a new quotation
Add a customer and other standard order information

2. Adding a Configurable Product

Click "Add a product" to add a line item
Select one of your configurable products
A "Configure" button will appear on the order line

3. Configuration Interface
When you click the "Configure" button, you'll see a configuration interface with these elements:

Header: Shows the product name and price
Navigation Tabs: Allow you to move between different sections of questions
Configuration Form: Shows the questions for the current section

Questions appear in the order you defined
Some questions may appear or disappear based on previous answers (dynamic visibility)
Required fields are marked with an asterisk
Different field types have appropriate input controls (text fields, dropdowns, checkboxes, etc.)


Visual Preview (if implemented): Shows a visual representation of your configured product
Price: Updates in real-time as you make configuration choices

4. Completing the Configuration

Work through each section, answering all required questions
Use the "Next" and "Previous" buttons to navigate between sections
The system validates your answers in real-time
Once all required fields are filled, click "Save Configuration"

5. Updated Order Line
After saving, you'll return to the sales order with:

An updated product description that includes configuration details
An updated price based on your configuration choices
A "Reconfigure" button that allows you to modify the configuration

Manufacturing Process
When a sales order with configured products is confirmed:

BOM Generation:

The system automatically generates a Bill of Materials based on your configuration
Components are included based on your configuration choices
Quantities are calculated using the formulas defined in the configuration template


Manufacturing Orders:

When a manufacturing order is created, it uses the generated BOM
Manufacturing staff can see the complete configuration details
The BOM includes all components needed for the specific configuration



Example User Journey
Let's follow a typical user journey for a configurable cabinet:

Sales Rep: Creates a new quotation for a customer
Adding the Product:

Adds "Configurable Cabinet" to the order
Clicks the "Configure" button


Configuration Interface:

First section "Dimensions" appears with fields:

Width (mm): Enters 800
Height (mm): Enters 2000
Depth (mm): Enters 600


Clicks "Next" to move to "Materials" section:

Cabinet Material: Selects "Melamine"
Color: Selects "White"


Clicks "Next" to move to "Features" section:

Door Type: Selects "Hinged"
Number of Shelves: Selects "3"
Include Drawers?: Checks "Yes"
Number of Drawers: Selects "2" (this field appeared because "Include Drawers" was set to Yes)


Visual preview updates to show a white melamine cabinet with hinged doors and two drawers
Price updates to reflect the configured options


Saving the Configuration:

Clicks "Save Configuration"
Returns to the sales order
The order line now shows "Configurable Cabinet (White Melamine, 800x2000x600mm, 3 shelves, 2 drawers)"
The price has been updated based on the configuration


Order Processing:

The sales order is confirmed
A manufacturing order is created with the correct BOM
Manufacturing staff see the complete configuration details and build accordingly



Key User Benefits

Guided Product Selection: Step-by-step process ensures all necessary options are considered
Dynamic Experience: Questions appear or disappear based on previous answers
Real-time Feedback: Price updates as options are selected
Visual Confirmation: Preview shows what the configured product will look like
Accurate Manufacturing: Automatically generated BOMs ensure products are built correctly
Reusable Configurations: Configurations can be saved and reused for repeat orders

The ConfigMatrix module provides a seamless experience from sales to manufacturing, ensuring that complex products are properly configured, accurately priced, and correctly built according to customer specifications.