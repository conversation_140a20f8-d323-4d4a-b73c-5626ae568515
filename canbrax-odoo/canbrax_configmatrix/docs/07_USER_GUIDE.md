# ConfigMatrix: Complete User Guide

This comprehensive user guide provides step-by-step instructions for all ConfigMatrix users, from administrators to end users.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Administrative Setup](#administrative-setup)
3. [Template Management](#template-management)
4. [Sales Configuration](#sales-configuration)
5. [Online Sales](#online-sales)
6. [Builder Portal](#builder-portal)
7. [Troubleshooting](#troubleshooting)
8. [Best Practices](#best-practices)

## Getting Started

### System Requirements

- **Odoo 18.0+**: Full compatibility with Odoo 18 features
- **User Permissions**: Appropriate access rights for your role
- **Browser**: Modern browser with JavaScript enabled
- **Network**: Stable internet connection for real-time features

### User Roles and Permissions

#### Administrator
- Create and manage configuration templates
- Set up pricing matrices and labor calculations
- Configure security and access controls
- Manage system settings and performance

#### Sales User
- Configure products for customers
- Generate quotes and sales orders
- Access customer configuration history
- Manage pricing and discounts

#### Builder/Contractor
- Access specialized builder portal
- Manage project configurations
- Track multiple configurations per project
- Access advanced measurement tools

#### Customer (Online)
- Configure products through website
- Save and retrieve configurations
- View real-time pricing
- Place orders with configurations

## Administrative Setup

### 1. Initial Configuration

#### Step 1: Install and Configure Module
1. Navigate to **Apps** → **Update Apps List**
2. Search for "ConfigMatrix" and install the module
3. Go to **Settings** → **ConfigMatrix** → **Configuration**
4. Configure basic settings:
   - **Enable Check Measure**: Allow measurement-based configurations
   - **Enable Sales/Quoting**: Enable sales order integration
   - **Enable Online Sales**: Enable customer portal configuration
   - **Default Currency**: Set default currency for pricing
   - **Max Fields per Template**: Set maximum fields (default: 100)

#### Step 2: Set Up Security Groups
1. Go to **Settings** → **Users & Companies** → **Groups**
2. Configure the following groups:
   - **ConfigMatrix User**: Basic configuration access
   - **ConfigMatrix Administrator**: Full administrative access
   - **ConfigMatrix Builder**: Builder portal access
3. Assign users to appropriate groups

#### Step 3: Create Matrix Categories
1. Navigate to **ConfigMatrix** → **Pricing** → **Matrix Categories**
2. Create categories for organizing pricing matrices:
   - **Price Matrix**: For product pricing
   - **Labor Matrix**: For labor time calculations
   - **Material Matrix**: For material costs
   - **Other**: For miscellaneous calculations

### 2. Product Setup

#### Step 1: Configure Products
1. Go to **Inventory** → **Products** → **Products**
2. Select a product to make configurable
3. Check **"Is Configurable"** checkbox
4. Set additional options:
   - **Requires Measurement**: Enable measurement tools
   - **Has Visual Preview**: Enable SVG previews
   - **Use Matrix Pricing**: Enable pricing matrices

#### Step 2: Create Configuration Template
1. Click **"Create Configuration Template"** button
2. Fill in template details:
   - **Template Name**: Descriptive name (e.g., "Standard Door Configuration")
   - **Template Code**: Unique identifier (e.g., "DOOR_STD_001")
   - **Description**: Detailed description of the template
   - **Version**: Template version (e.g., "1.0")
3. Configure use case enablement:
   - **Enable Check Measure**: For measurement-based configuration
   - **Enable Sales/Quoting**: For sales order configuration
   - **Enable Online Sales**: For customer portal configuration

### 3. Template Structure Setup

#### Step 1: Create Sections
1. In the template form, go to the **"Sections"** tab
2. Click **"Add a line"** to create a new section
3. Configure section details:
   - **Section Name**: Logical grouping (e.g., "Door Dimensions", "Hardware")
   - **Sequence**: Display order
   - **Description**: Section description
4. Set use case visibility for each section

#### Step 2: Add Fields
1. Within each section, go to the **"Fields"** tab
2. Click **"Add a line"** to create configuration fields
3. Configure field properties:
   - **Field Label**: User-friendly name
   - **Technical Name**: Internal identifier (must be valid Python identifier)
   - **Field Type**: Text, Number, Selection, Boolean, or Date
   - **Sequence**: Display order within section

#### Step 3: Configure Field Types

##### Text Fields
- **Minimum Length**: Minimum character requirement
- **Maximum Length**: Maximum character limit
- **Validation Pattern**: Regular expression for validation
- **Help Text**: Context-sensitive help for users

##### Number Fields
- **Decimal Precision**: Number of decimal places
- **Unit of Measure**: Associated unit (e.g., mm, inches)
- **Min/Max Values**: Dynamic range validation with expressions

##### Selection Fields
- **Options**: Add selection choices
- **Searchable Dropdown**: Enable search functionality
- **Default Option**: Pre-selected choice

##### Boolean Fields
- **True Label**: Custom label for "Yes" (e.g., "Include")
- **False Label**: Custom label for "No" (e.g., "Exclude")

### 4. Advanced Field Configuration

#### Dynamic Content
1. **Dynamic Help Text**: Enable context-sensitive help
   - Check **"Use Dynamic Help"** for the use case
   - Enter template with field placeholders: `"Width should be {door_height} + 100mm"`
2. **Dynamic Default Values**: Intelligent defaults
   - Check **"Use Dynamic Default"** for the use case
   - Enter expression: `door_height * 0.8`
3. **Dynamic Error Messages**: Conditional validation
   - Check **"Use Dynamic Error"** for the use case
   - Enter condition: `width > height`
   - Enter error message: `"Width cannot exceed height"`

#### Visibility Conditions
1. **Simple Conditions**: Show/hide based on field values
   - Enter expression: `door_type == "sliding"`
2. **Range Conditions**: Show/hide based on value ranges
   - Enter expression: `width >= 800 and width <= 1200`
3. **Complex Conditions**: Multiple field dependencies
   - Enter expression: `(door_type == "sliding" and width > 1000) or has_glass`

#### Component Mapping
1. **Single Component**: Map one product to field
   - Select **Component Product**
   - Enter **Quantity Formula**: `1` or `width / 1000`
2. **Multiple Components**: Map multiple products
   - Go to **Component Mappings** tab
   - Add multiple component mappings
   - Set conditions for each mapping

### 5. Pricing Matrix Setup

#### Step 1: Create Price Matrix
1. Navigate to **ConfigMatrix** → **Pricing** → **Price Matrices**
2. Click **"Create"** to add a new matrix
3. Configure matrix details:
   - **Matrix Name**: Descriptive name (e.g., "Door Pricing Matrix")
   - **Product Template**: Associated configurable product
   - **Matrix Type**: Sale Price or Cost Matrix
   - **Category**: Organize by purpose

#### Step 2: Configure Dimensions
1. **Height Calculated Field**: Select field for height dimension
2. **Width Calculated Field**: Select field for width dimension
3. **Generate Standard Ranges**: Auto-generate common ranges
4. **Custom Ranges**: Add specific ranges as needed

#### Step 3: Populate Matrix Data
1. **Manual Entry**: Enter prices for each cell
2. **Import from Excel**: Upload Excel file with pricing data
3. **Fill Sample Data**: Generate test data for development
4. **Special Conditions**: Add complex pricing rules

#### Step 4: Labor Time Matrix
1. Create **Labor Time Matrix** for manufacturing
2. Configure operation types: Cutting, Assembly, Finishing, Packaging
3. Set labor rates per hour
4. Populate time data for different dimensions

### 6. Visual Components

#### Step 1: SVG Components
1. In template form, go to **"SVG Components"** tab
2. Click **"Add a line"** to create visual component
3. Configure component details:
   - **Component Name**: Descriptive name
   - **SVG Content**: Vector graphics code
   - **Dimensions**: Width and height
   - **Layer Name**: For conditional display

#### Step 2: Conditional Layers
1. **Visibility Condition**: Show/hide based on configuration
   - Enter expression: `has_glass == True`
2. **Use Case Visibility**: Control per use case
   - Check appropriate use cases
3. **Layer Management**: Organize visual elements

#### Step 3: Instructional Images
1. **Upload Images**: Add measurement guides
2. **Show in Preview**: Display in preview panel
3. **Field Association**: Link to specific fields

## Template Management

### 1. Template Lifecycle

#### Draft Stage
- **Create Structure**: Add sections, fields, and options
- **Configure Rules**: Set visibility conditions and validation
- **Test Logic**: Verify field dependencies and calculations
- **Review Content**: Check help text and error messages

#### Testing Stage
- **Enable Testing**: Change state to "Testing"
- **User Testing**: Have users test configurations
- **Bug Fixes**: Address issues and refine logic
- **Performance Testing**: Test with large configurations

#### Active Stage
- **Production Ready**: Change state to "Active"
- **User Training**: Train users on new template
- **Monitor Usage**: Track configuration usage
- **Gather Feedback**: Collect user feedback

#### Archived Stage
- **Discontinue**: Change state to "Archived"
- **Data Migration**: Move to new template if needed
- **Cleanup**: Remove unused components

### 2. Template Maintenance

#### Regular Updates
1. **Review Performance**: Monitor template performance
2. **Update Pricing**: Refresh pricing matrices
3. **Add Features**: Enhance with new capabilities
4. **User Feedback**: Incorporate user suggestions

#### Version Management
1. **Version Control**: Track template versions
2. **Change Log**: Document modifications
3. **Rollback Plan**: Prepare for issues
4. **Testing**: Test changes before deployment

### 3. Import/Export

#### Export Template
1. **Full Export**: Export complete template structure
2. **Selective Export**: Export specific components
3. **Format Options**: JSON or Excel format
4. **Include Data**: Include sample configurations

#### Import Template
1. **Validate Format**: Check import file format
2. **Review Changes**: Preview import changes
3. **Execute Import**: Run import process
4. **Verify Results**: Check imported data

## Sales Configuration

### 1. Sales Order Configuration

#### Step 1: Add Configurable Product
1. Create or edit a sales order
2. Add a configurable product to the order
3. Notice the **"Configure"** button appears next to the line
4. Click **"Configure"** to open the configuration interface

#### Step 2: Configure Product
1. **Template Loading**: System loads appropriate template
2. **Field Display**: Fields appear based on use case
3. **Dynamic Updates**: Fields show/hide based on answers
4. **Real-time Validation**: Immediate feedback on inputs
5. **Visual Preview**: SVG preview updates in real-time

#### Step 3: Complete Configuration
1. **Review Values**: Check all field values
2. **Validate Configuration**: Ensure all requirements met
3. **Calculate Pricing**: System calculates total price
4. **Generate BOM**: System creates bill of materials
5. **Save Configuration**: Configuration saved to order line

### 2. Configuration Interface

#### Field Types and Inputs

##### Text Fields
- **Standard Input**: Type text values
- **Validation**: Real-time validation feedback
- **Help Text**: Context-sensitive assistance

##### Number Fields
- **Numeric Input**: Enter numbers with decimal support
- **Unit Display**: Shows associated unit of measure
- **Range Validation**: Checks min/max values
- **Dynamic Ranges**: Ranges based on other fields

##### Selection Fields
- **Dropdown List**: Choose from available options
- **Searchable**: Type to filter options
- **Default Selection**: Pre-selected option
- **Option Descriptions**: Additional information

##### Boolean Fields
- **Checkbox**: Simple yes/no selection
- **Custom Labels**: User-friendly labels
- **Conditional Logic**: Affects other fields

#### Advanced Features

##### Dynamic Help
- **Context-Sensitive**: Help text changes based on values
- **Field References**: References other field values
- **Examples**: Provides practical examples

##### Real-time Validation
- **Immediate Feedback**: Validation as you type
- **Error Messages**: Clear error descriptions
- **Dynamic Rules**: Validation rules change based on context

##### Visual Preview
- **SVG Rendering**: Vector-based visual representation
- **Conditional Layers**: Elements show/hide based on configuration
- **Real-time Updates**: Preview updates as you configure

### 3. Pricing and BOM

#### Price Calculation
1. **Matrix Pricing**: Look up prices in pricing matrices
2. **Component Pricing**: Add component costs
3. **Labor Calculation**: Include labor time costs
4. **Special Conditions**: Apply complex pricing rules
5. **Total Calculation**: Sum all pricing elements

#### BOM Generation
1. **Component Mapping**: Map configuration to components
2. **Quantity Calculation**: Calculate component quantities
3. **Dynamic Selection**: Select components based on configuration
4. **Conditional Inclusion**: Include components based on rules
5. **BOM Structure**: Create manufacturing bill of materials

## Online Sales

### 1. Customer Portal

#### Step 1: Customer Access
1. **Portal Login**: Customer logs into portal
2. **Product Catalog**: Browse configurable products
3. **Product Selection**: Select product to configure
4. **Configuration Interface**: Open customer-friendly interface

#### Step 2: Customer Configuration
1. **Simplified Interface**: Optimized for customer use
2. **Guided Process**: Step-by-step configuration
3. **Visual Aids**: Instructional images and previews
4. **Real-time Pricing**: See price updates as you configure
5. **Save Configuration**: Save for future reference

#### Step 3: Order Placement
1. **Review Configuration**: Final review of choices
2. **Price Confirmation**: Confirm final pricing
3. **Add to Cart**: Add configured product to cart
4. **Checkout Process**: Complete purchase process

### 2. Portal Features

#### Product Catalog
- **Configurable Products**: Clearly marked configurable items
- **Product Information**: Detailed product descriptions
- **Configuration Options**: Preview of configuration capabilities
- **Pricing Information**: Base pricing and configuration costs

#### Configuration Interface
- **Mobile Responsive**: Works on all devices
- **Intuitive Design**: Easy-to-use interface
- **Progress Indicator**: Shows configuration progress
- **Save and Resume**: Save partial configurations

#### Customer Account
- **Configuration History**: View past configurations
- **Saved Configurations**: Access saved configurations
- **Order History**: Track order status
- **Account Settings**: Manage account preferences

## Builder Portal

### 1. Builder Access

#### Step 1: Portal Setup
1. **Builder Registration**: Register as builder/contractor
2. **Account Verification**: Verify builder credentials
3. **Access Approval**: Get portal access approval
4. **Training**: Receive portal training

#### Step 2: Project Management
1. **Create Projects**: Set up customer projects
2. **Project Organization**: Organize by customer or job
3. **Configuration Slots**: Multiple configurations per project
4. **Project Tracking**: Track project progress

### 2. Builder Features

#### Advanced Configuration
- **Professional Tools**: Advanced measurement tools
- **Bulk Configuration**: Configure multiple items
- **Template Management**: Access to template library
- **Custom Calculations**: Advanced calculation tools

#### Project Management
- **Customer Management**: Manage customer information
- **Project Timeline**: Track project milestones
- **Configuration Tracking**: Track configuration changes
- **Documentation**: Generate project documentation

#### Order Management
- **Bulk Orders**: Place orders for multiple configurations
- **Order Tracking**: Track order status
- **Delivery Management**: Manage delivery schedules
- **Invoice Management**: Handle invoicing

## Troubleshooting

### 1. Common Issues

#### Configuration Issues
- **Field Not Visible**: Check visibility conditions
- **Validation Errors**: Review field validation rules
- **Price Calculation Errors**: Check pricing matrix data
- **BOM Generation Issues**: Verify component mappings

#### Performance Issues
- **Slow Loading**: Check template complexity
- **Memory Issues**: Monitor system resources
- **Database Performance**: Optimize database queries
- **Caching Issues**: Clear system cache

#### User Access Issues
- **Permission Errors**: Check user group assignments
- **Portal Access**: Verify portal user setup
- **Template Access**: Confirm template visibility
- **Data Access**: Check record rules

### 2. Debugging Tools

#### Template Debugging
1. **Template Structure**: Review template structure
2. **Field Dependencies**: Check field relationships
3. **Visibility Logic**: Test visibility conditions
4. **Validation Rules**: Verify validation logic

#### Performance Monitoring
1. **Query Analysis**: Monitor database queries
2. **Response Times**: Track response times
3. **Memory Usage**: Monitor memory consumption
4. **Error Logs**: Review error logs

#### User Testing
1. **Test Configurations**: Create test configurations
2. **User Scenarios**: Test common user scenarios
3. **Edge Cases**: Test unusual configurations
4. **Performance Testing**: Test with large configurations

### 3. Support Resources

#### Documentation
- **User Manuals**: Complete user documentation
- **Video Tutorials**: Step-by-step video guides
- **FAQ Section**: Common questions and answers
- **Best Practices**: Recommended procedures

#### Technical Support
- **Support Tickets**: Submit support requests
- **Community Forum**: User community discussions
- **Developer Documentation**: Technical documentation
- **Training Resources**: Training materials and courses

## Best Practices

### 1. Template Design

#### Structure Best Practices
- **Logical Grouping**: Group related fields in sections
- **Progressive Disclosure**: Show fields as needed
- **Consistent Naming**: Use consistent naming conventions
- **Clear Labels**: Use clear, descriptive labels

#### Field Design Best Practices
- **Appropriate Types**: Choose correct field types
- **Validation Rules**: Set appropriate validation
- **Help Text**: Provide helpful guidance
- **Default Values**: Set sensible defaults

#### Performance Best Practices
- **Efficient Conditions**: Optimize visibility conditions
- **Minimize Dependencies**: Reduce field dependencies
- **Caching Strategy**: Use appropriate caching
- **Database Optimization**: Optimize database queries

### 2. User Experience

#### Interface Design
- **Intuitive Layout**: Design intuitive interfaces
- **Consistent Design**: Maintain design consistency
- **Responsive Design**: Ensure mobile compatibility
- **Accessibility**: Ensure accessibility compliance

#### User Guidance
- **Clear Instructions**: Provide clear instructions
- **Progressive Help**: Offer help as needed
- **Error Messages**: Write clear error messages
- **Success Feedback**: Provide positive feedback

#### Performance Optimization
- **Fast Loading**: Ensure fast page loading
- **Smooth Interactions**: Provide smooth user interactions
- **Real-time Updates**: Update interface in real-time
- **Offline Capability**: Consider offline functionality

### 3. Data Management

#### Data Quality
- **Data Validation**: Validate all input data
- **Data Consistency**: Maintain data consistency
- **Data Backup**: Regular data backups
- **Data Security**: Ensure data security

#### Configuration Management
- **Version Control**: Track configuration versions
- **Change Management**: Manage configuration changes
- **Testing Procedures**: Test all changes
- **Rollback Plans**: Plan for rollbacks

#### Integration Best Practices
- **API Design**: Design robust APIs
- **Error Handling**: Handle errors gracefully
- **Logging**: Comprehensive logging
- **Monitoring**: Monitor system health

This comprehensive user guide provides all the information needed to effectively use the ConfigMatrix system, from initial setup to advanced configuration and troubleshooting.