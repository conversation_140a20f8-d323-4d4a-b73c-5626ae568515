
Given the redundancy and potential variations in the input inequalities, and the structure of your example output, we will define calculated fields for:

1.  **Inputs and Basic Derived Values:** Fields directly from input or simple calculations.
2.  **Derived Height Thresholds:** Key calculated values based on `Smallest Door Height`.
3.  **Range Validity:** Boolean flags indicating which `Smallest Door Height` input ranges are met.
4.  **Atomic Lock Height Conditions:** Boolean results of comparing `Lock Height` against various thresholds.
5.  **Composite Kit Conditions (per Rule Block):** Boolean results for whether the Lock Height meets the criteria for a potential "Regular" or "Low" kit within a specific rule block (like R4 which matches your example). *Note: We will define representative composite conditions as enumerating ALL variations from the input is not condensation.*
6.  **Kit Results (Contextual):** Final boolean flags for which kit applies based on mode/range.
7.  **Example-Specific Conditions & Results:** Fields replicating the logic shown in your example output for clarity, as they seem to represent a specific evaluation path (Manual Mode, likely R4 block).

Here is the condensed list of calculated fields:

```json
{
  // Input Fields (Assuming these exist or are captured from user interface)
  "_CALCULATED_manual_left_height": {
    "description": "Input: Manual height for the left door leaf.",
    "formula": "[manual_left_height]" // Placeholder for input field reference
  },
  "_CALCULATED_manual_right_height": {
    "description": "Input: Manual height for the right door leaf.",
    "formula": "[manual_right_height]" // Placeholder for input field reference
  },
  "_CALCULATED_height_calculation_method": {
    "description": "Input: Method used to determine door height (e.g., 'manual').",
    "formula": "[height_calculation_method]" // Placeholder for input field reference
  },
  "_CALCULATED_door_split_type": {
    "description": "Input: Type of door split (e.g., 'even', 'uneven').",
    "formula": "[door_split_type]" // Placeholder for input field reference
  },
   "_CALCULATED_deduction_assistance": {
    "description": "Input: Whether deduction assistance is used (e.g., 'yes', 'no').",
    "formula": "[deduction_assistance]" // Placeholder for input field reference
  },
  "_CALCULATED_lock_height": {
    "description": "Input: The specified lock height.",
    "formula": "[lock_height]" // Placeholder for input field reference
  },

  // Basic Derived Values
  "_CALCULATED_smallest_door_height": {
    "description": "Minimum height of the door leaves.",
    "formula": "MIN(_CALCULATED_manual_left_height, _CALCULATED_manual_right_height)" // Assumes manual mode; adjust if other methods exist
  },
   "_CALCULATED_manual_formula": {
    "description": "String representation of the smallest door height calculation in manual mode.",
    "formula": "CONCAT('Math.min(', _CALCULATED_manual_left_height, ', ', _CALCULATED_manual_right_height, ') = ', _CALCULATED_smallest_door_height)"
  },
  "_CALCULATED_is_manual_mode": {
    "description": "Boolean: True if height calculation method is manual.",
    "formula": "_CALCULATED_height_calculation_method === 'manual'" // Assuming 'manual' is the value
  },
  "_CALCULATED_is_even_split": {
    "description": "Boolean: True if door split type is even.",
    "formula": "_CALCULATED_door_split_type === 'even'" // Assuming 'even' is the value
  },
  "_CALCULATED_is_uneven_split": {
    "description": "Boolean: True if door split type is uneven.",
    "formula": "_CALCULATED_door_split_type === 'uneven'" // Assuming 'uneven' is the value
  },

  // Derived Height Thresholds (based on Smallest Door Height)
  "_CALCULATED_halfway_point": {
    "description": "Smallest Door Height / 2.",
    "formula": "_CALCULATED_smallest_door_height / 2"
  },
   "_CALCULATED_halfway_plus_16": {
    "description": "Smallest Door Height / 2 + 16.",
    "formula": "_CALCULATED_halfway_point + 16"
  },
  "_CALCULATED_halfway_plus_32": {
    "description": "Smallest Door Height / 2 + 32.",
    "formula": "_CALCULATED_halfway_point + 32"
  },
  "_CALCULATED_halfway_plus_79": {
    "description": "Smallest Door Height / 2 + 79.",
    "formula": "_CALCULATED_halfway_point + 79"
  },
   "_CALCULATED_halfway_minus_79": {
    "description": "Smallest Door Height / 2 - 79.",
    "formula": "_CALCULATED_halfway_point - 79"
  },
  "_CALCULATED_height_minus_270": {
    "description": "Smallest Door Height - 270.",
    "formula": "_CALCULATED_smallest_door_height - 270"
  },
  "_CALCULATED_height_minus_317": {
    "description": "Smallest Door Height - 317.",
    "formula": "_CALCULATED_smallest_door_height - 317"
  },
  "_CALCULATED_height_minus_330": {
    "description": "Smallest Door Height - 330.",
    "formula": "_CALCULATED_smallest_door_height - 330"
  },
   "_CALCULATED_height_minus_333": {
    "description": "Smallest Door Height - 333.",
    "formula": "_CALCULATED_smallest_door_height - 333"
  },
   "_CALCULATED_height_minus_349": {
    "description": "Smallest Door Height - 349.",
    "formula": "_CALCULATED_smallest_door_height - 349"
  },
   "_CALCULATED_height_minus_428": {
    "description": "Smallest Door Height - 428.",
    "formula": "_CALCULATED_smallest_door_height - 428"
  },
  "_CALCULATED_height_minus_740": {
    "description": "Smallest Door Height - 740.",
    "formula": "_CALCULATED_smallest_door_height - 740"
  },
   "_CALCULATED_height_minus_787": {
    "description": "Smallest Door Height - 787.",
    "formula": "_CALCULATED_smallest_door_height - 787"
  },
   "_CALCULATED_height_minus_800": {
    "description": "Smallest Door Height - 800.",
    "formula": "_CALCULATED_smallest_door_height - 800"
  },
   "_CALCULATED_height_minus_803": {
    "description": "Smallest Door Height - 803.",
    "formula": "_CALCULATED_smallest_door_height - 803"
  },
  "_CALCULATED_height_minus_819": {
    "description": "Smallest Door Height - 819.",
    "formula": "_CALCULATED_smallest_door_height - 819"
  },
   "_CALCULATED_height_minus_898": {
    "description": "Smallest Door Height - 898.",
    "formula": "_CALCULATED_smallest_door_height - 898"
  },
   "_CALCULATED_height_minus_940": {
    "description": "Smallest Door Height - 940.",
    "formula": "_CALCULATED_smallest_door_height - 940"
  },
   "_CALCULATED_height_minus_987": {
    "description": "Smallest Door Height - 987.",
    "formula": "_CALCULATED_smallest_door_height - 987"
  },
   "_CALCULATED_height_minus_1000": {
    "description": "Smallest Door Height - 1000.",
    "formula": "_CALCULATED_smallest_door_height - 1000"
  },
   "_CALCULATED_height_minus_1003": {
    "description": "Smallest Door Height - 1003.",
    "formula": "_CALCULATED_smallest_door_height - 1003"
  },
   "_CALCULATED_height_minus_1019": {
    "description": "Smallest Door Height - 1019.",
    "formula": "_CALCULATED_smallest_door_height - 1019"
  },
   "_CALCULATED_height_minus_1090": {
    "description": "Smallest Door Height - 1090.",
    "formula": "_CALCULATED_smallest_door_height - 1090"
  },
   "_CALCULATED_height_minus_1098": {
    "description": "Smallest Door Height - 1098.",
    "formula": "_CALCULATED_smallest_door_height - 1098"
  },
  "_CALCULATED_height_minus_1137": {
    "description": "Smallest Door Height - 1137.",
    "formula": "_CALCULATED_smallest_door_height - 1137"
  },
   "_CALCULATED_height_minus_1153": {
    "description": "Smallest Door Height - 1153.",
    "formula": "_CALCULATED_smallest_door_height - 1153"
  },
   "_CALCULATED_height_minus_1169": {
    "description": "Smallest Door Height - 1169.",
    "formula": "_CALCULATED_smallest_door_height - 1169"
  },
   "_CALCULATED_height_minus_1248": {
    "description": "Smallest Door Height - 1248.",
    "formula": "_CALCULATED_smallest_door_height - 1248"
  },


  // Smallest Door Height Range Validity Flags (R1-R6 identified from input)
  "_CALCULATED_is_range_R1_valid": {
    "description": "Boolean: Smallest Door Height is within [430, H-270].",
    "formula": "_CALCULATED_smallest_door_height >= 430 && _CALCULATED_smallest_door_height <= _CALCULATED_height_minus_270"
  },
  "_CALCULATED_is_range_R2_valid": {
    "description": "Boolean: Smallest Door Height is within [351, H-349].",
    "formula": "_CALCULATED_smallest_door_height >= 351 && _CALCULATED_smallest_door_height <= _CALCULATED_height_minus_349"
  },
  "_CALCULATED_is_range_R3_valid": {
    "description": "Boolean: Smallest Door Height is within [272, H-428].",
    "formula": "_CALCULATED_smallest_door_height >= 272 && _CALCULATED_smallest_door_height <= _CALCULATED_height_minus_428"
  },
  "_CALCULATED_is_range_R4_valid": {
    "description": "Boolean: Smallest Door Height is within [383, H-317]. (Matches example block)",
    "formula": "_CALCULATED_smallest_door_height >= 383 && _CALCULATED_smallest_door_height <= _CALCULATED_height_minus_317"
  },
   "_CALCULATED_is_range_R5_valid": {
    "description": "Boolean: Smallest Door Height is within [370, H-330].",
    "formula": "_CALCULATED_smallest_door_height >= 370 && _CALCULATED_smallest_door_height <= _CALCULATED_height_minus_330"
  },
   "_CALCULATED_is_range_R6_valid": {
    "description": "Boolean: Smallest Door Height is within [367, H-333].",
    "formula": "_CALCULATED_smallest_door_height >= 367 && _CALCULATED_smallest_door_height <= _CALCULATED_height_minus_333"
  },

  // Atomic Lock Height Conditions (Common comparisons found in rules)
  "_CALCULATED_condition_lh_ge_halfway": {
    "description": "Boolean: Lock Height >= Smallest Door Height / 2.",
    "formula": "_CALCULATED_lock_height >= _CALCULATED_halfway_point"
  },
   "_CALCULATED_condition_lh_lt_halfway": {
    "description": "Boolean: Lock Height < Smallest Door Height / 2.",
    "formula": "_CALCULATED_lock_height < _CALCULATED_halfway_point"
  },
   "_CALCULATED_condition_lh_ge_halfway_p16": {
    "description": "Boolean: Lock Height >= Smallest Door Height / 2 + 16.",
    "formula": "_CALCULATED_lock_height >= _CALCULATED_halfway_plus_16"
  },
   "_CALCULATED_condition_lh_lt_halfway_p16": {
    "description": "Boolean: Lock Height < Smallest Door Height / 2 + 16.",
    "formula": "_CALCULATED_lock_height < _CALCULATED_halfway_plus_16"
  },
  "_CALCULATED_condition_lh_ge_halfway_p32": {
    "description": "Boolean: Lock Height >= Smallest Door Height / 2 + 32.",
    "formula": "_CALCULATED_lock_height >= _CALCULATED_halfway_plus_32"
  },
  "_CALCULATED_condition_lh_lt_halfway_p32": {
    "description": "Boolean: Lock Height < Smallest Door Height / 2 + 32.",
    "formula": "_CALCULATED_lock_height < _CALCULATED_halfway_plus_32"
  },
  "_CALCULATED_condition_lh_ge_halfway_p79": {
    "description": "Boolean: Lock Height >= Smallest Door Height / 2 + 79.",
    "formula": "_CALCULATED_lock_height >= _CALCULATED_halfway_plus_79"
  },
  "_CALCULATED_condition_lh_lt_halfway_p79": {
    "description": "Boolean: Lock Height < Smallest Door Height / 2 + 79.",
    "formula": "_CALCULATED_lock_height < _CALCULATED_halfway_plus_79"
  },
   "_CALCULATED_condition_lh_ge_halfway_m79": {
    "description": "Boolean: Lock Height >= Smallest Door Height / 2 - 79.",
    "formula": "_CALCULATED_lock_height >= _CALCULATED_halfway_minus_79"
  },
   "_CALCULATED_condition_lh_lt_halfway_m79": {
    "description": "Boolean: Lock Height < Smallest Door Height / 2 - 79.",
    "formula": "_CALCULATED_lock_height < _CALCULATED_halfway_minus_79"
  },

  // Atomic Fixed/Height-based Threshold Conditions (Selected common ones)
  "_CALCULATED_condition_lh_ge_892": {"description": "Boolean: Lock Height >= 892.", "formula": "_CALCULATED_lock_height >= 892"},
  "_CALCULATED_condition_lh_ge_942": {"description": "Boolean: Lock Height >= 942.", "formula": "_CALCULATED_lock_height >= 942"},
  "_CALCULATED_condition_lh_ge_971": {"description": "Boolean: Lock Height >= 971.", "formula": "_CALCULATED_lock_height >= 971"},
  "_CALCULATED_condition_lh_ge_987": {"description": "Boolean: Lock Height >= 987.", "formula": "_CALCULATED_lock_height >= 987"},
  "_CALCULATED_condition_lh_ge_990": {"description": "Boolean: Lock Height >= 990.", "formula": "_CALCULATED_lock_height >= 990"},
  "_CALCULATED_condition_lh_ge_1003": {"description": "Boolean: Lock Height >= 1003.", "formula": "_CALCULATED_lock_height >= 1003"},
  "_CALCULATED_condition_lh_ge_1021": {"description": "Boolean: Lock Height >= 1021.", "formula": "_CALCULATED_lock_height >= 1021"},
  "_CALCULATED_condition_lh_ge_1037": {"description": "Boolean: Lock Height >= 1037.", "formula": "_CALCULATED_lock_height >= 1037"},
  "_CALCULATED_condition_lh_ge_1050": {"description": "Boolean: Lock Height >= 1050.", "formula": "_CALCULATED_lock_height >= 1050"},
  "_CALCULATED_condition_lh_ge_1053": {"description": "Boolean: Lock Height >= 1053. (Matches example)", "formula": "_CALCULATED_lock_height >= 1053"},
  "_CALCULATED_condition_lh_ge_1092": {"description": "Boolean: Lock Height >= 1092.", "formula": "_CALCULATED_lock_height >= 1092"},
  "_CALCULATED_condition_lh_ge_1100": {"description": "Boolean: Lock Height >= 1100.", "formula": "_CALCULATED_lock_height >= 1100"},
  "_CALCULATED_condition_lh_ge_1171": {"description": "Boolean: Lock Height >= 1171.", "formula": "_CALCULATED_lock_height >= 1171"},
  "_CALCULATED_condition_lh_ge_1187": {"description": "Boolean: Lock Height >= 1187.", "formula": "_CALCULATED_lock_height >= 1187"},
   "_CALCULATED_condition_lh_ge_1190": {"description": "Boolean: Lock Height >= 1190.", "formula": "_CALCULATED_lock_height >= 1190"},
  "_CALCULATED_condition_lh_ge_1203": {"description": "Boolean: Lock Height >= 1203.", "formula": "_CALCULATED_lock_height >= 1203"},
  "_CALCULATED_condition_lh_ge_1250": {"description": "Boolean: Lock Height >= 1250.", "formula": "_CALCULATED_lock_height >= 1250"},

  "_CALCULATED_condition_lh_lt_892": {"description": "Boolean: Lock Height < 892.", "formula": "_CALCULATED_lock_height < 892"},
   "_CALCULATED_condition_lh_lt_971": {"description": "Boolean: Lock Height < 971.", "formula": "_CALCULATED_lock_height < 971"},
   "_CALCULATED_condition_lh_lt_1003": {"description": "Boolean: Lock Height < 1003.", "formula": "_CALCULATED_lock_height < 1003"},
   "_CALCULATED_condition_lh_lt_1050": {"description": "Boolean: Lock Height < 1050.", "formula": "_CALCULATED_lock_height < 1050"},
   "_CALCULATED_condition_lh_lt_1100": {"description": "Boolean: Lock Height < 1100.", "formula": "_CALCULATED_lock_height < 1100"},
   "_CALCULATED_condition_lh_lt_1203": {"description": "Boolean: Lock Height < 1203.", "formula": "_CALCULATED_lock_height < 1203"},
   "_CALCULATED_condition_lh_lt_1250": {"description": "Boolean: Lock Height < 1250.", "formula": "_CALCULATED_lock_height < 1250"},
   "_CALCULATED_condition_lh_lt_1171": {"description": "Boolean: Lock Height < 1171.", "formula": "_CALCULATED_lock_height < 1171"},
   "_CALCULATED_condition_lh_lt_1187": {"description": "Boolean: Lock Height < 1187.", "formula": "_CALCULATED_lock_height < 1187"},


  "_CALCULATED_condition_lh_le_height_m740": {"description": "Boolean: Lock Height <= Smallest Door Height - 740.", "formula": "_CALCULATED_lock_height <= _CALCULATED_height_minus_740"},
  "_CALCULATED_condition_lh_gt_height_m740": {"description": "Boolean: Lock Height > Smallest Door Height - 740.", "formula": "_CALCULATED_lock_height > _CALCULATED_height_minus_740"}, // Covers both '>' and '>='
  "_CALCULATED_condition_lh_le_height_m819": {"description": "Boolean: Lock Height <= Smallest Door Height - 819.", "formula": "_CALCULATED_lock_height <= _CALCULATED_height_minus_819"},
  "_CALCULATED_condition_lh_gt_height_m819": {"description": "Boolean: Lock Height > Smallest Door Height - 819.", "formula": "_CALCULATED_lock_height > _CALCULATED_height_minus_819"},
   "_CALCULATED_condition_lh_le_height_m898": {"description": "Boolean: Lock Height <= Smallest Door Height - 898.", "formula": "_CALCULATED_lock_height <= _CALCULATED_height_minus_898"},
  "_CALCULATED_condition_lh_gt_height_m898": {"description": "Boolean: Lock Height > Smallest Door Height - 898.", "formula": "_CALCULATED_lock_height > _CALCULATED_height_minus_898"},
   "_CALCULATED_condition_lh_le_height_m940": {"description": "Boolean: Lock Height <= Smallest Door Height - 940.", "formula": "_CALCULATED_lock_height <= _CALCULATED_height_minus_940"},
   "_CALCULATED_condition_lh_gt_height_m940": {"description": "Boolean: Lock Height > Smallest Door Height - 940.", "formula": "_CALCULATED_lock_height > _CALCULATED_height_minus_940"},
   "_CALCULATED_condition_lh_le_height_m987": {"description": "Boolean: Lock Height <= Smallest Door Height - 987.", "formula": "_CALCULATED_lock_height <= _CALCULATED_height_minus_987"},
   "_CALCULATED_condition_lh_gt_height_m987": {"description": "Boolean: Lock Height > Smallest Door Height - 987.", "formula": "_CALCULATED_lock_height > _CALCULATED_height_minus_987"},
   "_CALCULATED_condition_lh_le_height_m1019": {"description": "Boolean: Lock Height <= Smallest Door Height - 1019.", "formula": "_CALCULATED_lock_height <= _CALCULATED_height_minus_1019"},
   "_CALCULATED_condition_lh_gt_height_m1019": {"description": "Boolean: Lock Height > Smallest Door Height - 1019.", "formula": "_CALCULATED_lock_height > _CALCULATED_height_minus_1019"},
   "_CALCULATED_condition_lh_le_height_m1090": {"description": "Boolean: Lock Height <= Smallest Door Height - 1090.", "formula": "_CALCULATED_lock_height <= _CALCULATED_height_minus_1090"},
   "_CALCULATED_condition_lh_gt_height_m1090": {"description": "Boolean: Lock Height > Smallest Door Height - 1090.", "formula": "_CALCULATED_lock_height > _CALCULATED_height_minus_1090"},
   "_CALCULATED_condition_lh_le_height_m1098": {"description": "Boolean: Lock Height <= Smallest Door Height - 1098.", "formula": "_CALCULATED_lock_height <= _CALCULATED_height_minus_1098"},
   "_CALCULATED_condition_lh_gt_height_m1098": {"description": "Boolean: Lock Height > Smallest Door Height - 1098.", "formula": "_CALCULATED_lock_height > _CALCULATED_height_minus_1098"},
  "_CALCULATED_condition_lh_le_height_m1137": {
    "description": "Boolean: Lock Height <= Smallest Door Height - 1137. (Matches example)",
    "formula": "_CALCULATED_lock_height <= _CALCULATED_height_minus_1137"
  },
   "_CALCULATED_condition_lh_gt_height_m1137": {"description": "Boolean: Lock Height > Smallest Door Height - 1137.", "formula": "_CALCULATED_lock_height > _CALCULATED_height_minus_1137"},
   "_CALCULATED_condition_lh_le_height_m1169": {"description": "Boolean: Lock Height <= Smallest Door Height - 1169.", "formula": "_CALCULATED_lock_height <= _CALCULATED_height_minus_1169"},
   "_CALCULATED_condition_lh_gt_height_m1169": {"description": "Boolean: Lock Height > Smallest Door Height - 1169.", "formula": "_CALCULATED_lock_height > _CALCULATED_height_minus_1169"},
   "_CALCULATED_condition_lh_le_height_m1248": {"description": "Boolean: Lock Height <= Smallest Door Height - 1248.", "formula": "_CALCULATED_lock_height <= _CALCULATED_height_minus_1248"},
   "_CALCULATED_condition_lh_gt_height_m1248": {"description": "Boolean: Lock Height > Smallest Door Height - 1248.", "formula": "_CALCULATED_lock_height > _CALCULATED_height_minus_1248"},


  // Composite Kit Conditions (Representative examples from R4 block rules)
  // Note: This is a condensation; the input lists many variations of these ranges.
  "_CALCULATED_R4_RegularKit_ValidRange1": {
    "description": "Boolean: R4 Reg Kit Valid Range Check 1 (1203 <= LH <= H-987).",
    "formula": "_CALCULATED_lock_height >= 1203 && _CALCULATED_lock_height <= _CALCULATED_height_minus_987"
  },
   "_CALCULATED_R4_RegularKit_ValidRange2": {
    "description": "Boolean: R4 Reg Kit Valid Range Check 2 (1003 <= LH < 1203 && LH <= H-987).",
    "formula": "_CALCULATED_lock_height >= 1003 && _CALCULATED_lock_height < 1203 && _CALCULATED_lock_height <= _CALCULATED_height_minus_987"
  },
  // ... (Define similar fields for other specific range checks within R4 Regular path)

  "_CALCULATED_R4_LowKit_ValidRange1": {
    "description": "Boolean: R4 Low Kit Valid Range Check 1 (1053 <= LH <= H-1137). (Matches example)",
    "formula": "_CALCULATED_lock_height >= 1053 && _CALCULATED_lock_height <= _CALCULATED_height_minus_1137"
  },
  "_CALCULATED_R4_LowKit_ValidRange2": {
    "description": "Boolean: R4 Low Kit Valid Range Check 2 (1003 <= LH < 1053 && LH <= H-1137).",
    "formula": "_CALCULATED_lock_height >= 1003 && _CALCULATED_lock_height < 1053 && _CALCULATED_lock_height <= _CALCULATED_height_minus_1137"
  },
  // ... (Define similar fields for other specific range checks within R4 Low path)


  // Overall Kit Validity per Range Block (OR of all specific ranges for each kit type)
  "_CALCULATED_R4_RegularKit_Valid": {
      "description": "Boolean: Lock Height meets ANY Regular Kit condition in R4 range.",
      "formula": "_CALCULATED_is_valid_range_R4 && _CALCULATED_condition_lh_ge_halfway_p32 && (_CALCULATED_R4_RegularKit_ValidRange1 || _CALCULATED_R4_RegularKit_ValidRange2 /* || ... add other R4 Reg ranges */)"
  },
   "_CALCULATED_R4_LowKit_Valid": {
      "description": "Boolean: Lock Height meets ANY Low Kit condition in R4 range.",
      "formula": "_CALCULATED_is_valid_range_R4 && _CALCULATED_condition_lh_lt_halfway_p32 && (_CALCULATED_R4_LowKit_ValidRange1 || _CALCULATED_R4_LowKit_ValidRange2 /* || ... add other R4 Low ranges */)"
  },
  // ... (Define similar _CALCULATED_R{X}_RegularKit_Valid and _CALCULATED_R{X}_LowKit_Valid for R2, R3, R6 ranges)


  // Final Kit Results (Assuming Manual Mode uses R4 rules)
   "_CALCULATED_regular_kit": {
       "description": "Boolean: True if Regular Kit requirements are met.",
       "formula": "_CALCULATED_is_manual_mode ? _CALCULATED_R4_RegularKit_Valid : ( (_CALCULATED_is_valid_range_R2 && _CALCULATED_R2_RegularKit_Valid) || (_CALCULATED_is_valid_range_R3 && _CALCULATED_R3_RegularKit_Valid) || (_CALCULATED_is_valid_range_R6 && _CALCULATED_R6_RegularKit_Valid) /* || ... add other range/mode logic */ )"
   },
   "_CALCULATED_low_kit": {
       "description": "Boolean: True if Low Kit requirements are met.",
       "formula": "_CALCULATED_is_manual_mode ? _CALCULATED_R4_LowKit_Valid : ( (_CALCULATED_is_valid_range_R2 && _CALCULATED_R2_LowKit_Valid) || (_CALCULATED_is_range_R3_valid && _CALCULATED_R3_LowKit_Valid) || (_CALCULATED_is_range_R6_valid && _CALCULATED_R6_LowKit_Valid) /* || ... add other range/mode logic */ )"
   },

  // Example-Specific Conditions & Results (Replicating the example structure)
  // These match the R4 Low Kit Check 1 and the split condition
  "_CALCULATED_condition_1053": { // Matches example name, refers to >=1053
    "description": "Boolean: Lock Height >= 1053 (Specific check from example).",
    "formula": "_CALCULATED_condition_lh_ge_1053"
  },
  "_CALCULATED_condition_halfway_plus_32": { // Matches example name, refers to < Halfway+32
    "description": "Boolean: Lock Height < Halfway + 32 (Specific check from example).",
    "formula": "_CALCULATED_condition_lh_lt_halfway_p32"
  },
  "_CALCULATED_condition_height_minus_1137": { // Matches example name, refers to <= H-1137
    "description": "Boolean: Lock Height <= Height - 1137 (Specific check from example).",
    "formula": "_CALCULATED_condition_lh_le_height_m1137"
  },

  // Composite condition matching the example breakdown (specific R4 Low Kit check)
  "_CALCULATED_unified_condition_result": {
    "description": "Boolean: Composite condition result matching example breakdown (LH >= 1053 && LH < H/2+32 && LH <= H-1137).",
    "formula": "_CALCULATED_condition_1053 && _CALCULATED_condition_halfway_plus_32 && _CALCULATED_condition_height_minus_1137"
  },
  "_CALCULATED_unified_condition_breakdown": {
     "description": "String representation of the unified condition calculation.",
     "formula": "CONCAT('(', _CALCULATED_lock_height, ' >= 1053) && (', _CALCULATED_lock_height, ' < ', _CALCULATED_halfway_plus_32, ') && (', _CALCULATED_lock_height, ' <= ', _CALCULATED_height_minus_1137, ') = ', _CALCULATED_unified_condition_result)"
  },
  // Manual mode specific aliases/results mirroring the example structure
  "_CALCULATED_manual_condition_1053": {"description": "Alias for _CALCULATED_condition_1053.", "formula": "_CALCULATED_condition_1053"},
  "_CALCULATED_manual_condition_halfway_plus_32": {"description": "Alias for _CALCULATED_condition_halfway_plus_32.", "formula": "_CALCULATED_condition_halfway_plus_32"},
  "_CALCULATED_manual_condition_height_minus_1137": {"description": "Alias for _CALCULATED_condition_height_minus_1137.", "formula": "_CALCULATED_condition_height_minus_1137"},
  "_CALCULATED_manual_full_condition_result": {
      "description": "Boolean: Full composite condition result for manual mode (matches unified result in example).",
      "formula": "_CALCULATED_unified_condition_result"
  },
  "_CALCULATED_manual_low_kit": {
      "description": "Boolean: True if Low Kit conditions are met in Manual Mode (based on example logic).",
      "formula": "_CALCULATED_is_manual_mode && _CALCULATED_unified_condition_result" // Example logic implies this specific composite determines Low Kit in manual mode
  },
  "_CALCULATED_manual_regular_kit": {
      "description": "Boolean: True if Regular Kit conditions met in Manual Mode (representative check).",
      "formula": "_CALCULATED_is_manual_mode && _CALCULATED_condition_lh_ge_halfway_p32 && _CALCULATED_R4_RegularKit_ValidRange1" // Using one example condition from R4 Reg path
  },

  // Final Overall Result (Based on Example's simplified final breakdown)
  "_CALCULATED_FINAL_OR_CONDITION_RESULT": {
      "description": "Boolean: Final OR condition result (based on example, seems to be the manual composite result).",
      "formula": "_CALCULATED_manual_full_condition_result" // Or potentially _CALCULATED_low_kit || _CALCULATED_regular_kit depending on overall logic
  },
   "_CALCULATED_FINAL_OR_CONDITION_BREAKDOWN": {
       "description": "String representation of the final OR condition result.",
       "formula": "CONCAT('Unified condition (manual mode): ', _CALCULATED_manual_full_condition_result)" // Matches example string format
   }

  // Note: Many conditions from the input were not explicitly defined as separate fields
  // to avoid excessive verbosity, focusing on the structure and key conditions from the example.
  // To fully represent "all" conditions, every unique OR statement and simple range check
  // would need its own boolean field.
}
```

