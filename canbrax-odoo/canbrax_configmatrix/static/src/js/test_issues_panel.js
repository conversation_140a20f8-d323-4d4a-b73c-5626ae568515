/**
 * Test Script for Fixed Issues Panel System
 * 
 * This script tests the cascade detection and clearing functionality
 * for the specific scenario: Question 3 → Question 220 → Question 223
 */

// Test function to simulate the problematic scenario
function testCascadeScenario() {
    console.log('🧪 Testing Cascade Detection Scenario...');
    console.log('========================================');
    
    // Step 1: Set Question 3 to "No - Less Than 20mm"
    console.log('Step 1: Setting Question 3 to "No - Less Than 20mm"');
    if (window.setFieldValue) {
        window.setFieldValue('bx_dbl_hinge_jamb_reveal_gt_20mm', 'No');
    }
    
    setTimeout(() => {
        // Step 2: Set Question 220 to "Short Leg Jamb Adaptor w/ Mohair"
        console.log('Step 2: Setting Question 220 to "Short Leg Jamb Adaptor w/ Mohair"');
        if (window.setFieldValue) {
            window.setFieldValue('bx_dbl_hinge_short_leg_jamb_adaptor', 'Short Leg Jamb Adaptor w/ <PERSON>hair');
        }
        
        setTimeout(() => {
            // Step 3: Set Question 223 value (this should appear because Q220 has a value)
            console.log('Step 3: Setting Question 223 value');
            if (window.setFieldValue) {
                window.setFieldValue('bx_dbl_hinge_short_leg_jamb_adaptor_other', 'Test Value for Q223');
            }
            
            setTimeout(() => {
                // Step 4: Change Question 3 to "Yes" (this should hide Q220)
                console.log('Step 4: Changing Question 3 to "Yes" (should hide Q220)');
                if (window.setFieldValue) {
                    window.setFieldValue('bx_dbl_hinge_jamb_reveal_gt_20mm', 'Yes');
                }
                
                setTimeout(() => {
                    // Step 5: Check for issues
                    console.log('Step 5: Checking for hidden fields with values...');
                    testIssuesDetection();
                }, 500);
            }, 500);
        }, 500);
    }, 500);
}

// Test the issues detection system
function testIssuesDetection() {
    console.log('🔍 Testing Issues Detection...');
    console.log('==============================');
    
    if (window.findHiddenFieldsWithValues) {
        const hiddenFields = window.findHiddenFieldsWithValues();
        console.log(`Found ${hiddenFields.length} hidden fields with values:`);
        
        hiddenFields.forEach((field, index) => {
            console.log(`  ${index + 1}. ${field.name}: "${field.value}" (${field.hiddenReason})`);
        });
        
        if (hiddenFields.length > 0) {
            console.log('✅ Issues detection is working correctly!');
            
            // Test clearing
            setTimeout(() => {
                testClearAllIssues();
            }, 1000);
        } else {
            console.log('❌ No issues detected - this might indicate a problem with detection');
        }
    } else {
        console.log('❌ findHiddenFieldsWithValues function not available');
    }
}

// Test the clear all issues functionality
function testClearAllIssues() {
    console.log('🧹 Testing Clear All Issues...');
    console.log('==============================');
    
    if (window.clearAllIssues) {
        const clearedCount = window.clearAllIssues();
        console.log(`Clear All Issues returned: ${clearedCount} fields cleared`);
        
        // Verify clearing worked
        setTimeout(() => {
            const remainingIssues = window.findHiddenFieldsWithValues();
            console.log(`After clearing, ${remainingIssues.length} issues remain`);
            
            if (remainingIssues.length === 0) {
                console.log('✅ Clear All Issues is working correctly!');
            } else {
                console.log('❌ Some issues remain after clearing:');
                remainingIssues.forEach((field, index) => {
                    console.log(`  ${index + 1}. ${field.name}: "${field.value}"`);
                });
            }
            
            // Final verification
            testFinalVerification();
        }, 1000);
    } else {
        console.log('❌ clearAllIssues function not available');
    }
}

// Final verification of field states
function testFinalVerification() {
    console.log('🔬 Final Verification...');
    console.log('=========================');
    
    const testFields = [
        'bx_dbl_hinge_jamb_reveal_gt_20mm',
        'bx_dbl_hinge_short_leg_jamb_adaptor', 
        'bx_dbl_hinge_short_leg_jamb_adaptor_other'
    ];
    
    testFields.forEach(fieldName => {
        const value = window.fieldValues?.[fieldName];
        const container = document.querySelector(`[data-technical-name="${fieldName}"]`)?.closest('.config-field-container');
        const isVisible = container ? !container.classList.contains('conditional-field-pending') && container.style.display !== 'none' : false;
        
        console.log(`Field ${fieldName}:`);
        console.log(`  Value: "${value}"`);
        console.log(`  Visible: ${isVisible}`);
        console.log(`  Classes: ${container?.className || 'N/A'}`);
        console.log('');
    });
    
    console.log('🎉 Test completed! Check the results above.');
}

// Function to test "Show All Fields" toggle behavior
function testShowAllFieldsToggle() {
    console.log('🔀 Testing Show All Fields Toggle...');
    console.log('====================================');
    
    const toggle = document.getElementById('show-all-fields');
    if (toggle) {
        console.log('Current toggle state:', toggle.checked);
        
        // Toggle it
        toggle.checked = !toggle.checked;
        toggle.dispatchEvent(new Event('change'));
        
        console.log('Toggled to:', toggle.checked);
        
        setTimeout(() => {
            console.log('Testing detection with toggle state:', toggle.checked);
            testIssuesDetection();
        }, 500);
    } else {
        console.log('❌ Show All Fields toggle not found');
    }
}

// Main test function
function runCompleteTest() {
    console.log('🚀 Starting Complete Issues Panel Test...');
    console.log('=========================================');
    
    // Wait for systems to be ready
    if (!window.configuratorComponent) {
        console.log('⏳ Waiting for configurator component...');
        setTimeout(runCompleteTest, 1000);
        return;
    }
    
    if (!window.issuesPanelManager) {
        console.log('⏳ Waiting for issues panel manager...');
        setTimeout(runCompleteTest, 1000);
        return;
    }
    
    console.log('✅ All systems ready! Starting test...');
    
    // Run the cascade scenario test
    testCascadeScenario();
}

// Quick test functions for manual testing
window.testCascadeScenario = testCascadeScenario;
window.testIssuesDetection = testIssuesDetection;
window.testClearAllIssues = testClearAllIssues;
window.testShowAllFieldsToggle = testShowAllFieldsToggle;
window.runCompleteTest = runCompleteTest;

// Auto-run test when script loads (with delay to ensure systems are ready)
setTimeout(() => {
    console.log('🔧 Issues Panel Test Script Loaded');
    console.log('Available test functions:');
    console.log('  - testCascadeScenario()');
    console.log('  - testIssuesDetection()');  
    console.log('  - testClearAllIssues()');
    console.log('  - testShowAllFieldsToggle()');
    console.log('  - runCompleteTest()');
    console.log('');
    console.log('Run runCompleteTest() to test the complete scenario');
}, 2000);

console.log('✅ Issues Panel Test Script loaded - functions will be available shortly');
