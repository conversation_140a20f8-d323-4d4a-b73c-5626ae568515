/** @odoo-module **/

import { Component, useState, onMounted, useRef, markup } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";
import { _t } from "@web/core/l10n/translation";

export class VisualMatrixEditor extends Component {
    static template = "canbrax_configmatrix.VisualMatrixEditor";
    static props = {
        matrixData: { type: Object, optional: true },
        heightValues: { type: String, optional: true },
        widthValues: { type: String, optional: true },
        onMatrixChange: { type: Function, optional: true },
    };

    setup() {
        this.orm = useService("orm");
        this.notification = useService("notification");
        
        this.state = useState({
            matrixData: this.props.matrixData || {},
            heights: this._parseValues(this.props.heightValues || "1000,1200,1500"),
            widths: this._parseValues(this.props.widthValues || "600,800,1000"),
            selectedCell: null,
            editingCell: null,
        });

        this.matrixRef = useRef("matrixTable");
        
        onMounted(() => {
            this._setupKeyboardNavigation();
            this._setupCellEvents();
        });
    }

    _parseValues(valueString) {
        if (!valueString) return [];
        return valueString.split(',').map(v => parseInt(v.trim())).filter(v => !isNaN(v));
    }

    _setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            if (!this.state.selectedCell) return;
            
            const [row, col] = this.state.selectedCell;
            let newRow = row, newCol = col;
            
            switch(e.key) {
                case 'ArrowUp':
                    newRow = Math.max(0, row - 1);
                    e.preventDefault();
                    break;
                case 'ArrowDown':
                    newRow = Math.min(this.state.heights.length - 1, row + 1);
                    e.preventDefault();
                    break;
                case 'ArrowLeft':
                    newCol = Math.max(0, col - 1);
                    e.preventDefault();
                    break;
                case 'ArrowRight':
                    newCol = Math.min(this.state.widths.length - 1, col + 1);
                    e.preventDefault();
                    break;
                case 'Tab':
                    if (e.shiftKey) {
                        newCol = col > 0 ? col - 1 : this.state.widths.length - 1;
                        if (newCol === this.state.widths.length - 1) {
                            newRow = Math.max(0, row - 1);
                        }
                    } else {
                        newCol = col < this.state.widths.length - 1 ? col + 1 : 0;
                        if (newCol === 0) {
                            newRow = Math.min(this.state.heights.length - 1, row + 1);
                        }
                    }
                    e.preventDefault();
                    break;
                case 'Enter':
                    this._startEditing(row, col);
                    e.preventDefault();
                    break;
                case 'Escape':
                    this._stopEditing();
                    e.preventDefault();
                    break;
            }
            
            if (newRow !== row || newCol !== col) {
                this._selectCell(newRow, newCol);
            }
        });
    }

    _setupCellEvents() {
        const table = this.matrixRef.el;
        if (!table) return;
        
        table.addEventListener('click', (e) => {
            const cell = e.target.closest('.matrix-cell');
            if (cell) {
                const row = parseInt(cell.dataset.row);
                const col = parseInt(cell.dataset.col);
                this._selectCell(row, col);
            }
        });
        
        table.addEventListener('dblclick', (e) => {
            const cell = e.target.closest('.matrix-cell');
            if (cell) {
                const row = parseInt(cell.dataset.row);
                const col = parseInt(cell.dataset.col);
                this._startEditing(row, col);
            }
        });
    }

    _selectCell(row, col) {
        // Remove previous selection
        document.querySelectorAll('.matrix-cell').forEach(cell => {
            cell.classList.remove('selected');
        });
        
        // Add new selection
        const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
        if (cell) {
            cell.classList.add('selected');
            cell.focus();
        }
        
        this.state.selectedCell = [row, col];
    }

    _startEditing(row, col) {
        const height = this.state.heights[row];
        const width = this.state.widths[col];
        const key = `${height}_${width}`;
        const currentValue = this.state.matrixData[key] || '';
        
        this.state.editingCell = [row, col];
        
        // Create input element
        const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
        if (cell) {
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentValue;
            input.className = 'matrix-input';
            input.style.width = '100%';
            input.style.border = 'none';
            input.style.outline = 'none';
            input.style.textAlign = 'center';
            input.style.background = 'transparent';
            
            input.addEventListener('blur', () => this._finishEditing(row, col, input.value));
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this._finishEditing(row, col, input.value);
                    e.preventDefault();
                } else if (e.key === 'Escape') {
                    this._stopEditing();
                    e.preventDefault();
                }
            });
            
            cell.innerHTML = '';
            cell.appendChild(input);
            input.focus();
            input.select();
        }
    }

    _finishEditing(row, col, value) {
        const height = this.state.heights[row];
        const width = this.state.widths[col];
        const key = `${height}_${width}`;
        
        if (value && value.trim()) {
            const numValue = parseFloat(value);
            if (!isNaN(numValue)) {
                this.state.matrixData[key] = numValue;
            } else {
                this.state.matrixData[key] = value.trim();
            }
        } else {
            delete this.state.matrixData[key];
        }
        
        this._stopEditing();
        this._updateCell(row, col);
        
        // Notify parent component
        if (this.props.onMatrixChange) {
            this.props.onMatrixChange(this.state.matrixData);
        }
    }

    _stopEditing() {
        this.state.editingCell = null;
        this.render();
    }

    _updateCell(row, col) {
        const height = this.state.heights[row];
        const width = this.state.widths[col];
        const key = `${height}_${width}`;
        const value = this.state.matrixData[key] || '';
        
        const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
        if (cell) {
            cell.textContent = value;
            cell.classList.toggle('has-value', !!value);
        }
    }

    getCellValue(row, col) {
        const height = this.state.heights[row];
        const width = this.state.widths[col];
        const key = `${height}_${width}`;
        return this.state.matrixData[key] || '';
    }

    getCellClass(row, col) {
        const height = this.state.heights[row];
        const width = this.state.widths[col];
        const key = `${height}_${width}`;
        const hasValue = !!this.state.matrixData[key];
        
        let classes = 'matrix-cell';
        if (hasValue) classes += ' has-value';
        if (this.state.selectedCell && this.state.selectedCell[0] === row && this.state.selectedCell[1] === col) {
            classes += ' selected';
        }
        return classes;
    }

    fillSampleData() {
        const newData = {};
        this.state.heights.forEach((height, i) => {
            this.state.widths.forEach((width, j) => {
                // Generate realistic sample prices
                const basePrice = 150;
                const price = basePrice + (i * 25) + (j * 20) + (height * width * 0.0001);
                newData[`${height}_${width}`] = Math.round(price * 100) / 100;
            });
        });
        
        this.state.matrixData = newData;
        
        if (this.props.onMatrixChange) {
            this.props.onMatrixChange(this.state.matrixData);
        }
        
        this.notification.add(_t("Sample data generated successfully!"), { type: "success" });
    }

    clearMatrix() {
        this.state.matrixData = {};
        
        if (this.props.onMatrixChange) {
            this.props.onMatrixChange(this.state.matrixData);
        }
        
        this.notification.add(_t("Matrix cleared!"), { type: "info" });
    }

    testLookup() {
        const totalCells = this.state.heights.length * this.state.widths.length;
        const filledCells = Object.keys(this.state.matrixData).length;
        
        // Test with middle values
        const midHeight = this.state.heights[Math.floor(this.state.heights.length / 2)];
        const midWidth = this.state.widths[Math.floor(this.state.widths.length / 2)];
        const testKey = `${midHeight}_${midWidth}`;
        const testValue = this.state.matrixData[testKey] || 'No value';
        
        const message = `Matrix Status:
- Size: ${this.state.heights.length} × ${this.state.widths.length} (${totalCells} cells)
- Filled: ${filledCells} cells (${Math.round(filledCells/totalCells*100)}%)
- Test lookup (${midHeight}×${midWidth}): ${testValue}`;
        
        this.notification.add(message, { type: "info", sticky: true });
    }
}
