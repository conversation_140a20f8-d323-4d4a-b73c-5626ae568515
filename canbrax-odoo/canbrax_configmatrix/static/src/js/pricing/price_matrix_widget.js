/** @odoo-module **/

import { Component, onMounted, onWillUnmount, useState, useRef } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";

/**
 * Price Matrix Visual Editor Widget
 * Provides Excel-like editing experience for price matrix data
 */
export class PriceMatrixWidget extends Component {
    static template = "canbrax_configmatrix.InteractiveMatrixEditor";

    setup() {
        this.state = useState({
            heights: [],
            widths: [],
            matrixData: {},
            selectedCell: null,
            editingCell: null,
            editValue: '',
            stats: { total: 0, filled: 0, completion: 0 }
        });

        this.matrixRef = useRef("matrixGrid");
        this.notification = useService("notification");

        onMounted(() => {
            this.loadMatrixData();
            this.setupEventHandlers();
        });

        onWillUnmount(() => {
            this.cleanupEventHandlers();
        });
    }

    loadMatrixData() {
        try {
            // Get data from the price matrix record
            const record = this.props.record;
            if (!record) return;

            // Handle Python-style single quotes by converting to valid JSON
            const heightRangesJson = (record.data.height_ranges || '[]').replace(/'/g, '"');
            const widthRangesJson = (record.data.width_ranges || '[]').replace(/'/g, '"');
            const matrixDataJson = (record.data.matrix_data || '{}').replace(/'/g, '"');

            // Parse height and width ranges
            const heightRanges = JSON.parse(heightRangesJson);
            const widthRanges = JSON.parse(widthRangesJson);
            const matrixData = JSON.parse(matrixDataJson);

            // Extract labels from ranges (support both old min/max format and new value format)
            this.state.heights = heightRanges.map(range => {
                if (range.value !== undefined) {
                    return range.label || range.value.toString();
                } else {
                    return range.label || range.max.toString();
                }
            });
            this.state.widths = widthRanges.map(range => {
                if (range.value !== undefined) {
                    return range.label || range.value.toString();
                } else {
                    return range.label || range.max.toString();
                }
            });
            this.state.matrixData = matrixData;

            this.calculateStats();
            this.renderMatrix();
        } catch (error) {
            console.error('Failed to load matrix data:', error);
            this.state.heights = [];
            this.state.widths = [];
            this.state.matrixData = {};
        }
    }

    calculateStats() {
        const total = this.state.heights.length * this.state.widths.length;
        const filled = Object.keys(this.state.matrixData).filter(key => this.state.matrixData[key]).length;
        const completion = total > 0 ? Math.round((filled / total) * 100) : 0;

        this.state.stats = { total, filled, completion };
    }

    renderMatrix() {
        if (!this.matrixRef.el) return;

        const table = this.matrixRef.el;
        table.innerHTML = '';

        if (this.state.heights.length === 0 || this.state.widths.length === 0) {
            table.innerHTML = '<tr><td colspan="100%" class="text-center text-muted p-4">Configure height and width ranges in the Matrix Configuration tab to see the matrix</td></tr>';
            return;
        }

        // Create header row
        const headerRow = document.createElement('tr');
        const cornerCell = document.createElement('th');
        cornerCell.className = 'matrix-corner-cell';
        cornerCell.textContent = 'H\\W';
        cornerCell.style.cssText = 'background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); position: sticky; left: 0; z-index: 10; min-width: 80px; text-align: center; font-weight: bold; border: 1px solid #dee2e6;';
        headerRow.appendChild(cornerCell);

        this.state.widths.forEach(width => {
            const th = document.createElement('th');
            th.className = 'matrix-header-cell';
            th.textContent = width;
            th.style.cssText = 'background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); text-align: center; min-width: 90px; font-weight: 600; position: sticky; top: 0; z-index: 5; border: 1px solid #dee2e6; color: #495057;';
            headerRow.appendChild(th);
        });

        table.appendChild(headerRow);

        // Create data rows
        this.state.heights.forEach((height, rowIndex) => {
            const row = document.createElement('tr');

            // Row header
            const rowHeader = document.createElement('th');
            rowHeader.className = 'matrix-row-header';
            rowHeader.textContent = height;
            rowHeader.style.cssText = 'background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); position: sticky; left: 0; z-index: 9; text-align: center; font-weight: 600; border: 1px solid #dee2e6; color: #495057; border-right: 2px solid #adb5bd;';
            row.appendChild(rowHeader);

            // Data cells
            this.state.widths.forEach((width, colIndex) => {
                const cell = document.createElement('td');
                const cellKey = `${height}_${width}`;
                const cellValue = this.state.matrixData[cellKey] || '';

                cell.className = 'matrix-data-cell';
                cell.dataset.height = height;
                cell.dataset.width = width;
                cell.dataset.key = cellKey;

                // Apply Excel-like styling
                let cellStyle = 'text-align: center; cursor: pointer; padding: 8px; min-width: 90px; border: 1px solid #d1d5db; transition: all 0.15s ease; font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;';

                // Alternating row colors
                if (rowIndex % 2 === 1) {
                    cellStyle += 'background-color: #fafbfc;';
                } else {
                    cellStyle += 'background-color: #ffffff;';
                }

                if (cellValue) {
                    cell.textContent = this.formatCellValue(cellValue);
                    cell.classList.add('has-value');
                    if (rowIndex % 2 === 1) {
                        cellStyle += 'background-color: #f8f9fa; font-weight: 500; color: #374151;';
                    } else {
                        cellStyle += 'background-color: #f9fafb; font-weight: 500; color: #374151;';
                    }
                } else {
                    cell.textContent = '-';
                    cell.classList.add('empty-value');
                    cellStyle += 'color: #9ca3af; font-style: italic;';
                }

                cell.style.cssText = cellStyle;

                // Add hover effect
                cell.addEventListener('mouseenter', () => {
                    cell.style.backgroundColor = '#f8fafc';
                    cell.style.borderColor = '#3b82f6';
                    cell.style.boxShadow = 'inset 0 0 0 1px #3b82f6';
                });

                cell.addEventListener('mouseleave', () => {
                    if (!cell.classList.contains('selected')) {
                        if (cellValue) {
                            if (rowIndex % 2 === 1) {
                                cell.style.backgroundColor = '#f8f9fa';
                            } else {
                                cell.style.backgroundColor = '#f9fafb';
                            }
                        } else {
                            if (rowIndex % 2 === 1) {
                                cell.style.backgroundColor = '#fafbfc';
                            } else {
                                cell.style.backgroundColor = '#ffffff';
                            }
                        }
                        cell.style.borderColor = '#d1d5db';
                        cell.style.boxShadow = 'none';
                    }
                });

                // Add click handlers
                cell.addEventListener('click', () => this.onCellClick(height, width, cellKey));
                cell.addEventListener('dblclick', () => this.onCellDoubleClick(height, width, cellKey));

                row.appendChild(cell);
            });

            table.appendChild(row);
        });
    }

    formatCellValue(value) {
        if (typeof value === 'number') {
            return value.toFixed(4);
        }
        return value.toString();
    }

    onCellClick(height, width, cellKey) {
        // Clear previous selection
        this.clearSelection();

        // Set new selection
        this.state.selectedCell = { height, width, key: cellKey };

        // Update visual selection
        const cell = this.matrixRef.el.querySelector(`[data-key="${cellKey}"]`);
        if (cell) {
            cell.classList.add('selected');
            cell.style.backgroundColor = '#dbeafe';
            cell.style.borderColor = '#2563eb';
            cell.style.boxShadow = '0 0 0 1px #2563eb';
        }
    }

    onCellDoubleClick(height, width, cellKey) {
        this.startCellEdit(height, width, cellKey);
    }

    startCellEdit(height, width, cellKey) {
        this.state.editingCell = { height, width, key: cellKey };
        this.state.editValue = this.state.matrixData[cellKey] || '';

        const cell = this.matrixRef.el.querySelector(`[data-key="${cellKey}"]`);
        if (cell) {
            cell.classList.add('editing');

            // Create input element
            const input = document.createElement('input');
            input.type = 'number';
            input.step = '0.01';
            input.className = 'matrix-cell-input';
            input.value = this.state.editValue;
            input.style.cssText = 'width: 100%; border: none; outline: none; text-align: center; background: #ffffff; font-weight: 500; color: #1f2937; font-size: 13px; padding: 8px; box-shadow: inset 0 0 0 2px #059669;';

            // Replace cell content with input
            cell.innerHTML = '';
            cell.appendChild(input);

            // Focus and select
            input.focus();
            input.select();

            // Handle input events
            input.addEventListener('blur', () => this.confirmCellEdit(cellKey));
            input.addEventListener('keydown', (e) => this.handleInputKeydown(e, cellKey));
        }
    }

    handleInputKeydown(event, cellKey) {
        switch(event.key) {
            case 'Enter':
                event.preventDefault();
                this.confirmCellEdit(cellKey);
                break;
            case 'Escape':
                event.preventDefault();
                this.cancelCellEdit(cellKey);
                break;
            case 'Tab':
                event.preventDefault();
                this.confirmCellEdit(cellKey);
                this.navigateToNextCell(cellKey, event.shiftKey ? -1 : 1);
                break;
        }
    }

    confirmCellEdit(cellKey) {
        const cell = this.matrixRef.el.querySelector(`[data-key="${cellKey}"]`);
        const input = cell?.querySelector('.matrix-cell-input');

        if (input) {
            const newValue = parseFloat(input.value) || 0;

            // Update matrix data
            if (newValue === 0) {
                delete this.state.matrixData[cellKey];
            } else {
                this.state.matrixData[cellKey] = newValue;
            }

            // Update the record field
            this.updateRecordField();

            // Re-render the cell
            this.endCellEdit(cellKey);
            this.calculateStats();

            // No notification - silent update
        }
    }

    cancelCellEdit(cellKey) {
        this.endCellEdit(cellKey);
    }

    endCellEdit(cellKey) {
        this.state.editingCell = null;
        this.state.editValue = '';
        this.renderMatrix(); // Re-render to restore proper styling
    }

    updateRecordField() {
        // Update the matrix_data field in the record
        try {
            const jsonString = JSON.stringify(this.state.matrixData);
            this.props.record.update({ [this.props.name]: jsonString });
        } catch (error) {
            console.error('Failed to update record field:', error);
        }
    }

    clearSelection() {
        this.matrixRef.el?.querySelectorAll('.matrix-data-cell.selected').forEach(cell => {
            cell.classList.remove('selected');
        });
        this.state.selectedCell = null;
        this.renderMatrix(); // Re-render to restore proper styling
    }

    setupEventHandlers() {
        // Add keyboard handlers for the matrix
        this.keyHandler = (event) => {
            if (!this.matrixRef.el?.contains(event.target)) return;

            // Handle matrix navigation
            if (this.state.selectedCell && !this.state.editingCell) {
                switch(event.key) {
                    case 'Enter':
                    case 'F2':
                        event.preventDefault();
                        const { height, width, key } = this.state.selectedCell;
                        this.startCellEdit(height, width, key);
                        break;
                    case 'Delete':
                    case 'Backspace':
                        event.preventDefault();
                        this.deleteSelectedCell();
                        break;
                }
            }
        };

        document.addEventListener('keydown', this.keyHandler);
    }

    cleanupEventHandlers() {
        if (this.keyHandler) {
            document.removeEventListener('keydown', this.keyHandler);
        }
    }

    deleteSelectedCell() {
        if (this.state.selectedCell) {
            const { key } = this.state.selectedCell;
            delete this.state.matrixData[key];
            this.updateRecordField();
            this.renderMatrix();
            this.calculateStats();
            // No notification - silent delete
        }
    }

    navigateToNextCell(currentKey, direction) {
        const [currentHeight, currentWidth] = currentKey.split('_');
        const heightIndex = this.state.heights.indexOf(currentHeight);
        const widthIndex = this.state.widths.indexOf(currentWidth);

        let newWidthIndex = widthIndex + direction;
        let newHeightIndex = heightIndex;

        // Handle wrapping
        if (newWidthIndex >= this.state.widths.length) {
            newWidthIndex = 0;
            newHeightIndex++;
        } else if (newWidthIndex < 0) {
            newWidthIndex = this.state.widths.length - 1;
            newHeightIndex--;
        }

        // Check bounds
        if (newHeightIndex >= 0 && newHeightIndex < this.state.heights.length) {
            const newHeight = this.state.heights[newHeightIndex];
            const newWidth = this.state.widths[newWidthIndex];
            const newKey = `${newHeight}_${newWidth}`;

            this.startCellEdit(newHeight, newWidth, newKey);
        }
    }

    fillSampleData() {
        const sampleData = {};
        const baseValue = 150;

        this.state.heights.forEach((height, i) => {
            this.state.widths.forEach((width, j) => {
                const key = `${height}_${width}`;
                const heightVal = parseInt(height);
                const widthVal = parseInt(width);
                const value = baseValue + (i * 25) + (j * 20) + (heightVal * widthVal * 0.0001);
                sampleData[key] = Math.round(value * 100) / 100;
            });
        });

        this.state.matrixData = sampleData;
        this.updateRecordField();
        this.renderMatrix();
        this.calculateStats();

        // No notification - silent sample data generation
    }

    clearAllData() {
        this.state.matrixData = {};
        this.updateRecordField();
        this.renderMatrix();
        this.calculateStats();

        // No notification - silent clear
    }
}

// Register the component for price matrix
registry.category("fields").add("price_matrix_visual", {
    component: PriceMatrixWidget,
});
