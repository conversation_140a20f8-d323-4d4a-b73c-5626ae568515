/** @odoo-module **/

import { Component, onMounted, onWillUpdateProps, onWillUnmount, useState, useRef } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";

/**
 * Dimension Ranges Editor Widget
 * Provides visual editing of height/width ranges for price matrices
 */
export class DimensionRangesEditor extends Component {
    static template = "canbrax_configmatrix.DimensionRangesEditor";

    setup() {
        this.state = useState({
            ranges: [],
            editingIndex: null,
            editValues: { value: '', label: '' }
        });

        this.notification = useService("notification");
        this.tableRef = useRef("rangesTable");
        this.lastKnownValue = "[]";

        onMounted(() => {
            console.warn(`[DimensionRangesEditor] Widget mounted for field ${this.props.name}`);
            console.warn(`[DimensionRangesEditor] Props:`, this.props);
            console.warn(`[DimensionRangesEditor] Record data:`, this.props.record?.data);
            this.loadRanges();

            // Try loading again after a short delay in case data wasn't ready
            setTimeout(() => {
                console.log(`[DimensionRangesEditor] Delayed reload for field ${this.props.name}`);
                this.loadRanges();
            }, 1000);

            // Set up auto-refresh to catch data changes
            this.autoRefreshInterval = setInterval(() => {
                const currentValue = this.props.record?.data[this.props.name] || "[]";
                if (currentValue !== this.lastKnownValue) {
                    console.log(`[DimensionRangesEditor] Auto-refresh detected change for field ${this.props.name}`);
                    this.lastKnownValue = currentValue;
                    this.loadRanges();
                }
            }, 2000);
        });

        onWillUnmount(() => {
            if (this.autoRefreshInterval) {
                clearInterval(this.autoRefreshInterval);
            }
        });

        // Watch for changes in the field value
        onWillUpdateProps((nextProps) => {
            if (nextProps.record.data[this.props.name] !== this.props.record.data[this.props.name]) {
                console.log(`[DimensionRangesEditor] Field ${this.props.name} value changed, reloading ranges`);
                // Update props reference and reload
                this.props = nextProps;
                this.loadRanges();
            }
        });
    }

    loadRanges() {
        try {
            if (!this.props.record || !this.props.record.data) {
                console.log(`[DimensionRangesEditor] Record or record data not available yet`);
                this.state.ranges = [];
                return;
            }

            const fieldValue = this.props.record.data[this.props.name] || "[]";
            console.warn(`[DimensionRangesEditor] Loading ranges for field ${this.props.name}:`, fieldValue);
            console.warn(`[DimensionRangesEditor] Full record data:`, this.props.record.data);

            this.lastKnownValue = fieldValue;

            // Handle Python-style single quotes by converting to valid JSON
            const jsonString = fieldValue.replace(/'/g, '"');
            console.warn(`[DimensionRangesEditor] Converted JSON string:`, jsonString);
            let rawRanges = JSON.parse(jsonString);
            console.warn(`[DimensionRangesEditor] Parsed raw ranges:`, rawRanges);

            // Convert ranges to expected format if needed
            this.state.ranges = this.normalizeRanges(rawRanges);
            console.warn(`[DimensionRangesEditor] Normalized ranges:`, this.state.ranges);

            // Force a re-render if ranges were loaded
            if (this.state.ranges.length > 0) {
                console.log(`[DimensionRangesEditor] Found ${this.state.ranges.length} ranges, forcing re-render`);
            }
        } catch (error) {
            console.error("Error loading ranges:", error, "Field value:", this.props.record.data[this.props.name]);
            this.state.ranges = [];
        }
    }

    normalizeRanges(rawRanges) {
        if (!Array.isArray(rawRanges)) {
            return [];
        }

        return rawRanges.map(range => {
            // Handle Excel import format: {min: X, max: Y, label: "Y"}
            if (range.hasOwnProperty('min') && range.hasOwnProperty('max')) {
                return {
                    value: range.max, // Use max value as the primary value
                    label: range.label || range.max.toString()
                };
            }
            // Handle editor format: {value: X, label: "X"}
            else if (range.hasOwnProperty('value')) {
                return {
                    value: range.value,
                    label: range.label || range.value.toString()
                };
            }
            // Fallback for unknown formats
            else {
                console.warn('[DimensionRangesEditor] Unknown range format:', range);
                return {
                    value: 0,
                    label: 'Unknown'
                };
            }
        });
    }

    updateRecord() {
        try {
            // Convert back to the format expected by the backend (min/max format)
            const backendRanges = this.state.ranges.map((range, index) => {
                const value = range.value;
                const prevValue = index > 0 ? this.state.ranges[index - 1].value : value;

                return {
                    min: index === 0 ? value : prevValue + 1,
                    max: value,
                    label: range.label || value.toString()
                };
            });

            const jsonString = JSON.stringify(backendRanges);
            this.props.record.update({ [this.props.name]: jsonString });
        } catch (error) {
            console.error('Failed to update record:', error);
        }
    }

    addRange() {
        // Determine next range value
        let newValue;
        if (this.state.ranges.length > 0) {
            const lastValue = Math.max(...this.state.ranges.map(r => r.value));
            newValue = lastValue + 200; // Default 200mm increment
        } else {
            newValue = 500;
        }

        const newRange = {
            value: newValue,
            label: newValue.toString()
        };

        this.state.ranges.push(newRange);
        this.sortRanges();
        this.updateRecord();

        this.notification.add(`Added value ${newValue}`, { type: "success" });
    }

    removeRange(index) {
        if (confirm('Are you sure you want to remove this range?')) {
            this.state.ranges.splice(index, 1);
            this.updateRecord();
            this.notification.add("Range removed", { type: "info" });
        }
    }

    startEdit(index) {
        const range = this.state.ranges[index];
        if (!range) {
            console.error('[DimensionRangesEditor] Range not found at index:', index);
            this.notification.add("Error: Range not found", { type: "danger" });
            return;
        }

        if (range.value === undefined || range.value === null) {
            console.error('[DimensionRangesEditor] Range value is undefined:', range);
            this.notification.add("Error: Invalid range data", { type: "danger" });
            return;
        }

        this.state.editingIndex = index;
        this.state.editValues = {
            value: range.value.toString(),
            label: range.label || range.value.toString()
        };
    }

    cancelEdit() {
        this.state.editingIndex = null;
        this.state.editValues = { value: '', label: '' };
    }

    saveEdit() {
        const index = this.state.editingIndex;
        const value = parseInt(this.state.editValues.value);
        const label = this.state.editValues.label.trim();

        // Validation
        if (isNaN(value) || value <= 0) {
            this.notification.add("Please enter a valid positive number", { type: "danger" });
            return;
        }

        if (!label) {
            this.notification.add("Label cannot be empty", { type: "danger" });
            return;
        }

        // Update range
        this.state.ranges[index] = { value, label };
        this.sortRanges();
        this.updateRecord();
        this.cancelEdit();

        this.notification.add("Value updated successfully", { type: "success" });
    }

    sortRanges() {
        this.state.ranges.sort((a, b) => a.value - b.value);
    }

    onInputChange(field, event) {
        this.state.editValues[field] = event.target.value;
    }

    onKeydown(event) {
        if (event.key === 'Enter') {
            this.saveEdit();
        } else if (event.key === 'Escape') {
            this.cancelEdit();
        }
    }

    getDimensionType() {
        return this.props.context?.dimension_type || 'dimension';
    }

    getUnit() {
        return 'mm'; // Default unit
    }

    // Debug method to manually refresh ranges
    refreshRanges() {
        console.log(`[DimensionRangesEditor] Manual refresh triggered for field ${this.props.name}`);
        this.loadRanges();
    }

    // Debug method to populate test ranges
    loadTestRanges() {
        console.log(`[DimensionRangesEditor] Loading test ranges for field ${this.props.name}`);
        this.state.ranges = [
            { value: 800, label: '800' },
            { value: 1000, label: '1000' },
            { value: 1200, label: '1200' }
        ];
        console.log(`[DimensionRangesEditor] Test ranges loaded:`, this.state.ranges);
        this.updateRecord();
    }

    // Debug method to test range normalization
    testNormalization() {
        console.log(`[DimensionRangesEditor] Testing range normalization`);

        // Test Excel import format
        const excelFormat = [
            { min: 600, max: 600, label: '600' },
            { min: 601, max: 800, label: '800' },
            { min: 801, max: 1000, label: '1000' }
        ];

        const normalized = this.normalizeRanges(excelFormat);
        console.log(`[DimensionRangesEditor] Excel format:`, excelFormat);
        console.log(`[DimensionRangesEditor] Normalized:`, normalized);

        return normalized;
    }
}

// Register the widget
registry.category("fields").add("dimension_ranges_editor", {
    component: DimensionRangesEditor,
});
