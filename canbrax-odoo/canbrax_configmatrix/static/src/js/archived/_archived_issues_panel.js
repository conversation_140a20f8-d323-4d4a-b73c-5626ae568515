/**
 * Issues Panel JavaScript
 *
 * This file contains the functionality for the issues panel:
 * - Scanning for yellow fields with values
 * - Displaying issues in a table
 * - Clearing issues
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get panel elements
    const showIssuesButton = document.getElementById('show-issues-panel');
    const hideIssuesButton = document.getElementById('hide-issues-panel');
    const refreshIssuesButton = document.getElementById('issues-refresh');
    const clearAllIssuesButton = document.getElementById('clear-all-issues');
    const issuesPanel = document.getElementById('issues-panel');
    const issuesTableBody = document.getElementById('issues-table-body');

    // Add event listeners
    if (showIssuesButton && issuesPanel) {
        showIssuesButton.addEventListener('click', function() {
            issuesPanel.style.display = 'block';
            scanForIssues();
        });
    }

    if (hideIssuesButton && issuesPanel) {
        hideIssuesButton.addEventListener('click', function() {
            issuesPanel.style.display = 'none';
        });
    }

    if (refreshIssuesButton) {
        refreshIssuesButton.addEventListener('click', scanForIssues);
    }

    if (clearAllIssuesButton) {
        clearAllIssuesButton.addEventListener('click', clearAllIssues);
    }

    /**
     * Scan for issues in the form
     */
    function scanForIssues() {
        if (!issuesTableBody) return;

        // Show loading indicator
        issuesTableBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center">
                    <i class="fas fa-spinner fa-spin"></i> Scanning for issues...
                </td>
            </tr>
        `;

        // Find all hidden fields with values
        const hiddenFields = [];

        // Function to check if a field has a meaningful value (not empty, not 0)
        function hasNonDefaultValue(field, fieldType, fieldValue) {
            if (fieldType === 'boolean') {
                return field.checked;
            } else if (fieldType === 'number') {
                // Exclude fields with value 0 as requested
                return fieldValue !== '' && fieldValue !== '0' && fieldValue !== 0;
            } else if (fieldType === 'text' || fieldType === 'date' || fieldType === 'selection') {
                return fieldValue && fieldValue.trim() !== '';
            }
            return false;
        }

        // Function to get field value
        function getFieldValue(field, fieldId, technicalName, fieldType) {
            let value = '';

            // Get value from UI
            if (fieldType === 'boolean') {
                value = field.checked ? 'true' : 'false';
            } else if (fieldType === 'number' || fieldType === 'text' || fieldType === 'date' || fieldType === 'selection') {
                value = field.value;
            }

            // Check fieldValues object if UI value is empty
            if ((!value || value === '0' || value === '') && window.fieldValues) {
                if (fieldId && window.fieldValues[fieldId] !== undefined) {
                    value = window.fieldValues[fieldId];
                } else if (technicalName && window.fieldValues[technicalName] !== undefined) {
                    value = window.fieldValues[technicalName];
                }
            }

            return value;
        }

        // Check all fields that are hidden but might have values
        document.querySelectorAll('.config-field').forEach(field => {
            const fieldId = field.getAttribute('data-field-id');
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');
            const container = field.closest('.config-field-container');

            // Check if field is hidden
            const isHidden = container && (
                container.style.display === 'none' ||
                container.classList.contains('conditional-field-pending') ||
                window.getComputedStyle(container).display === 'none'
            );

            // Get field value
            const fieldValue = getFieldValue(field, fieldId, technicalName, fieldType);

            // Check if field has a meaningful value
            const hasMeaningfulValue = hasNonDefaultValue(field, fieldType, fieldValue);

            // If field is hidden and has a meaningful value, add to the list
            if (isHidden && hasMeaningfulValue) {
                console.log(`Found hidden field ${fieldId} (${technicalName}) with value: ${fieldValue}`);
                hiddenFields.push({
                    id: fieldId,
                    name: technicalName,
                    value: fieldValue,
                    element: field
                });
            }
        });

        console.log('Scanning for hidden fields with values...');

        // For debugging only - log all fields
        if (false) { // Set to true for debugging
            document.querySelectorAll('.config-field').forEach(field => {
                const fieldId = field.getAttribute('data-field-id');
                const technicalName = field.getAttribute('data-technical-name');
                const container = field.closest('.config-field-container');
                const isVisible = container ? window.getComputedStyle(container).display !== 'none' : true;
                const fieldValue = getFieldValue(field, fieldId, technicalName, field.getAttribute('data-field-type'));
                console.log(`Field ${fieldId} (${technicalName}): Value: ${fieldValue}, Visible: ${isVisible}`);
            });
        }

            // Check if the field has a value
            let hasValue = false;
            let fieldValue = '';

            if (fieldType === 'boolean') {
                hasValue = field.checked;
                fieldValue = hasValue ? 'true' : 'false';
            } else if (fieldType === 'number' || fieldType === 'text' || fieldType === 'date' || fieldType === 'selection') {
                hasValue = field.value && field.value.trim() !== '';
                fieldValue = field.value;
            }

            // Also check the fieldValues object if it exists
            if (!hasValue && window.fieldValues) {
                if ((fieldId && window.fieldValues[fieldId] !== undefined && window.fieldValues[fieldId] !== '') ||
                    (technicalName && window.fieldValues[technicalName] !== undefined && window.fieldValues[technicalName] !== '')) {
                    hasValue = true;
                    fieldValue = window.fieldValues[technicalName] || window.fieldValues[fieldId];
                }
            }

            if (hasValue) {
                yellowFields.push({
                    id: fieldId,
                    name: technicalName,
                    value: fieldValue,
                    element: field
                });
            }
        });

        // Update the table with the results
        if (hiddenFields.length > 0) {
            let tableHtml = '';
            hiddenFields.forEach(field => {
                tableHtml += `
                    <tr>
                        <td>${field.id || 'N/A'}</td>
                        <td>${field.name || 'N/A'}</td>
                        <td>${field.value}</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger clear-field-btn"
                                    data-field-id="${field.id}"
                                    data-technical-name="${field.name}">
                                Clear
                            </button>
                        </td>
                    </tr>
                `;
            });
            issuesTableBody.innerHTML = tableHtml;

            // Add event listeners to clear buttons
            document.querySelectorAll('.clear-field-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const fieldId = this.getAttribute('data-field-id');
                    const technicalName = this.getAttribute('data-technical-name');
                    clearField(fieldId, technicalName);
                    // Re-scan after clearing
                    setTimeout(scanForIssues, 100);
                });
            });
        } else {
            issuesTableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center text-success">
                        <i class="fas fa-check-circle"></i> No issues found!
                    </td>
                </tr>
            `;
        }
    }

    /**
     * Clear a specific field
     */
    function clearField(fieldId, technicalName) {
        // Find the field element
        const field = document.querySelector(`.config-field[data-field-id="${fieldId}"]`) ||
                      document.querySelector(`.config-field[data-technical-name="${technicalName}"]`);

        if (field) {
            const fieldType = field.getAttribute('data-field-type');

            // Clear the field value in the UI
            if (fieldType === 'boolean') {
                field.checked = false;
            } else if (fieldType === 'number' || fieldType === 'text' || fieldType === 'date') {
                field.value = '';
            } else if (fieldType === 'selection') {
                field.value = '';
            }

            // Clear the value in the fieldValues object if it exists
            if (window.fieldValues) {
                if (fieldId) {
                    delete window.fieldValues[fieldId];
                }
                if (technicalName) {
                    delete window.fieldValues[technicalName];
                }
            }

            // Trigger change event to update any dependent fields
            const event = new Event('change', { bubbles: true });
            field.dispatchEvent(event);

            console.log(`Cleared field: ${technicalName || fieldId}`);
        } else {
            console.warn(`Could not find field: ${technicalName || fieldId}`);
        }
    }

    /**
     * Create and show a popup message
     */
    function showPopupMessage(message, type = 'success') {
        // Create popup element
        const popup = document.createElement('div');
        popup.className = `alert alert-${type} position-fixed`;
        popup.style.top = '20px';
        popup.style.left = '50%';
        popup.style.transform = 'translateX(-50%)';
        popup.style.zIndex = '9999';
        popup.style.padding = '15px 25px';
        popup.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
        popup.style.borderRadius = '5px';
        popup.style.maxWidth = '80%';

        // Add icon based on type
        let icon = 'check-circle';
        if (type === 'warning') icon = 'exclamation-triangle';
        if (type === 'danger') icon = 'times-circle';

        popup.innerHTML = `<i class="fas fa-${icon} me-2"></i> ${message}`;

        // Add to document
        document.body.appendChild(popup);

        // Remove after 3 seconds
        setTimeout(() => {
            popup.style.opacity = '0';
            popup.style.transition = 'opacity 0.5s';
            setTimeout(() => {
                document.body.removeChild(popup);
            }, 500);
        }, 3000);
    }

    /**
     * Clear all issues - ONLY hidden fields with values
     * This function should NEVER clear visible (green) fields
     */
    function clearAllIssues() {
        console.log('Clearing all issues (only hidden fields with values)...');

        // Count cleared fields
        let clearedCount = 0;

        // Find all hidden fields with values
        document.querySelectorAll('.config-field').forEach(field => {
            const fieldId = field.getAttribute('data-field-id');
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');
            const container = field.closest('.config-field-container');

            // Check if field is hidden - more robust detection
            let isHidden = false;

            if (container) {
                // Check direct style
                if (container.style.display === 'none') {
                    isHidden = true;
                }
                // Check computed style
                else if (window.getComputedStyle(container).display === 'none') {
                    isHidden = true;
                }
                // Check parent visibility - sometimes fields appear hidden because their parent is hidden
                else {
                    let parent = container.parentElement;
                    while (parent) {
                        if (parent.style.display === 'none' || window.getComputedStyle(parent).display === 'none') {
                            isHidden = true;
                            break;
                        }
                        parent = parent.parentElement;
                    }
                }

                // Check for conditional-field-pending class
                if (container.classList.contains('conditional-field-pending')) {
                    isHidden = true;
                }

                // Check for green background which indicates an active field
                if (!isHidden) {
                    const bgColor = window.getComputedStyle(container).backgroundColor;
                    const hasGreenBg = bgColor.includes('rgb(230, 255, 230)') ||
                                      bgColor.includes('rgb(230,255,230)') ||
                                      bgColor.includes('rgb(144, 238, 144)') ||
                                      bgColor.includes('rgb(144,238,144)');

                    // If it has a green background, it's definitely visible
                    if (hasGreenBg) {
                        isHidden = false;
                    }
                }
            } else {
                // If no container, consider it hidden
                isHidden = true;
            }

            // Get field value
            const fieldValue = getFieldValue(field, fieldId, technicalName, fieldType);

            // Check if field has a meaningful value
            const hasMeaningfulValue = hasNonDefaultValue(field, fieldType, fieldValue);

            // CRITICAL: ONLY clear if field is HIDDEN and has a meaningful value
            // DO NOT clear visible fields - this will cause data loss!
            if (isHidden && hasMeaningfulValue) {
                console.log(`Clearing hidden field ${fieldId} (${technicalName}) with value: ${fieldValue}`);

                // Clear the field value in the UI
                if (fieldType === 'boolean') {
                    field.checked = false;
                } else if (fieldType === 'number' || fieldType === 'text' || fieldType === 'date') {
                    field.value = '';
                } else if (fieldType === 'selection') {
                    field.value = '';
                }

                // Clear the value in the fieldValues object if it exists
                if (window.fieldValues) {
                    if (fieldId) {
                        delete window.fieldValues[fieldId];
                    }
                    if (technicalName) {
                        delete window.fieldValues[technicalName];
                    }
                }

                // Trigger change event to update any dependent fields
                const event = new Event('change', { bubbles: true });
                field.dispatchEvent(event);

                clearedCount++;
            }
        });

        // Show popup message with the number of fields cleared
        if (clearedCount > 0) {
            showPopupMessage(`${clearedCount} hidden field${clearedCount !== 1 ? 's' : ''} cleared successfully!`, 'success');
        }
        // No popup if no fields were cleared - avoid unnecessary notifications

        // Re-scan after clearing
        setTimeout(scanForIssues, 100);
    }
});
