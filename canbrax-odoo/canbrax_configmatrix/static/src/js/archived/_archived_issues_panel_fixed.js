/**
 * Issues Panel JavaScript - Fixed Version
 *
 * This file provides functionality for:
 * - Detection of hidden fields with values
 * - Display of hidden fields with values in an issues panel
 * - Clearing functionality that only affects hidden fields, never visible fields
 * - Automatic clearing of hidden fields on save
 * - Exclusion of fields with default values of 0
 */

/**
 * Function to check if a field has a meaningful value (not empty, not 0)
 */
function hasNonDefaultValue(field, fieldType, fieldValue) {
    if (fieldType === 'boolean') {
        return field.checked;
    } else if (fieldType === 'number') {
        // Exclude fields with value 0 as requested
        return fieldValue !== '' && fieldValue !== '0' && fieldValue !== 0;
    } else if (fieldType === 'text' || fieldType === 'date' || fieldType === 'selection') {
        return fieldValue && fieldValue.trim() !== '';
    }
    return false;
}

/**
 * Function to get field value
 */
function getFieldValue(field, fieldId, technicalName, fieldType) {
    let value = '';

    // Get value from UI
    if (fieldType === 'boolean') {
        value = field.checked ? 'true' : 'false';
    } else if (fieldType === 'number' || fieldType === 'text' || fieldType === 'date' || fieldType === 'selection') {
        value = field.value;
    }

    // Check fieldValues object if UI value is empty
    if ((!value || value === '0' || value === '') && window.fieldValues) {
        if (fieldId && window.fieldValues[fieldId] !== undefined) {
            value = window.fieldValues[fieldId];
        } else if (technicalName && window.fieldValues[technicalName] !== undefined) {
            value = window.fieldValues[technicalName];
        }
    }

    return value;
}

/**
 * Find hidden fields with values using multiple detection methods
 * @returns {Array} Array of hidden field objects with values
 */
function findHiddenFieldsWithValuesImpl() {
    const hiddenFields = [];
    console.log('Scanning for hidden fields with values using enhanced detection...');

    // Debug - count total fields
    const totalFields = document.querySelectorAll('.config-field').length;
    console.log(`Total fields found: ${totalFields}`);

    // Check all fields that are hidden but might have values
    document.querySelectorAll('.config-field').forEach(field => {
        const fieldId = field.getAttribute('data-field-id');
        const technicalName = field.getAttribute('data-technical-name');
        const fieldType = field.getAttribute('data-field-type');
        const container = field.closest('.config-field-container');

        // Skip fields without proper identification
        if (!fieldId && !technicalName) {
            console.warn('Field without ID or technical name found, skipping');
            return;
        }

        // Enhanced hidden field detection with multiple methods
        let isHidden = false;
        let hiddenReason = '';

        if (container) {
            // Method 1: Check direct style display property
            if (container.style.display === 'none') {
                isHidden = true;
                hiddenReason = 'direct style';
            }
            // Method 2: Check computed style display property
            else if (window.getComputedStyle(container).display === 'none') {
                isHidden = true;
                hiddenReason = 'computed style';
            }
            // Method 3: Check for conditional-field-pending class (yellow background)
            else if (container.classList.contains('conditional-field-pending')) {
                isHidden = true;
                hiddenReason = 'conditional-field-pending class';
            }
            // Method 4: Check parent container visibility recursively
            else {
                let parent = container.parentElement;
                while (parent) {
                    if (parent.style.display === 'none' || window.getComputedStyle(parent).display === 'none') {
                        isHidden = true;
                        hiddenReason = 'hidden parent';
                        break;
                    }
                    parent = parent.parentElement;
                }
            }

            // Method 5: Check background color - green means visible, yellow means hidden
            const bgColor = window.getComputedStyle(container).backgroundColor;

            // Check for green background which indicates an active field
            const hasGreenBg = bgColor.includes('rgb(230, 255, 230)') ||
                              bgColor.includes('rgb(230,255,230)') ||
                              bgColor.includes('rgb(144, 238, 144)') ||
                              bgColor.includes('rgb(144,238,144)') ||
                              container.classList.contains('conditional-visible');

            // Check for yellow background which indicates a hidden field
            const hasYellowBg = bgColor.includes('rgb(255, 243, 205)') ||
                               bgColor.includes('rgb(255,243,205)') ||
                               bgColor.includes('rgb(255, 235, 186)') ||
                               bgColor.includes('rgb(255,235,186)') ||
                               container.classList.contains('conditional-field-pending');

            // If it has a green background, it's definitely visible
            if (hasGreenBg) {
                isHidden = false;
                hiddenReason = '';
            }

            // If it has a yellow background, it's definitely hidden
            if (hasYellowBg) {
                isHidden = true;
                hiddenReason = 'yellow background';
            }
        } else {
            // If no container, consider it hidden
            isHidden = true;
            hiddenReason = 'no container';
        }

        // Get field value
        const fieldValue = getFieldValue(field, fieldId, technicalName, fieldType);

        // Check if field has a meaningful value (not empty, not default 0)
        const hasMeaningfulValue = hasNonDefaultValue(field, fieldType, fieldValue);

        // If field is hidden and has a meaningful value, add to the list
        if (isHidden && hasMeaningfulValue) {
            console.log(`Found hidden field ${fieldId} (${technicalName}) with value: ${fieldValue} - Hidden reason: ${hiddenReason}`);
            hiddenFields.push({
                id: fieldId,
                name: technicalName,
                value: fieldValue,
                element: field,
                hiddenReason: hiddenReason
            });
        }
    });

    console.log(`Found ${hiddenFields.length} hidden fields with values`);
    return hiddenFields;
}

// Make the findHiddenFieldsWithValues function globally available
// for the floating save button and form submission in the main template
window.findHiddenFieldsWithValues = function() {
    console.log('Global findHiddenFieldsWithValues function called');

    // If the DOM is not yet loaded, wait for it
    if (document.readyState === 'loading') {
        console.log('DOM not yet loaded, returning empty array for now');
        return [];
    }

    const result = findHiddenFieldsWithValuesImpl();
    console.log('Found ' + result.length + ' hidden fields with values');
    return result;
};

// Create a variable to store the internal clearAllIssues function
let internalClearAllIssues = null;

// Make the clearAllIssues function globally available
// This is the function that's called by the "Clear All" button
window.clearAllIssues = function() {
    console.log('Global clearAllIssues function called');

    // If the DOM is not yet loaded, wait for it
    if (document.readyState === 'loading') {
        console.log('DOM not yet loaded, cannot clear issues yet');
        return;
    }

    // Call the internal implementation when it's available
    if (internalClearAllIssues) {
        console.log('Calling internal clearAllIssues implementation');
        internalClearAllIssues();
    } else {
        console.log('Using findHiddenFieldsWithValuesImpl directly');
        const hiddenFields = findHiddenFieldsWithValuesImpl();

        if (hiddenFields.length > 0) {
            console.log(`Clearing ${hiddenFields.length} hidden fields directly`);

            // Clear each hidden field
            hiddenFields.forEach(field => {
                const fieldElement = field.element;
                const fieldType = fieldElement.getAttribute('data-field-type');

                // Clear the field value in the UI
                if (fieldType === 'boolean') {
                    fieldElement.checked = false;
                } else if (fieldType === 'number' || fieldType === 'text' || fieldType === 'date') {
                    fieldElement.value = '';
                } else if (fieldType === 'selection') {
                    fieldElement.value = '';
                }

                // Trigger change event to update any dependent fields
                const event = new Event('change', { bubbles: true });
                fieldElement.dispatchEvent(event);

                // Clear the value in the fieldValues object if it exists
                if (window.fieldValues) {
                    const fieldId = field.id;
                    const technicalName = field.name;

                    if (fieldId) {
                        delete window.fieldValues[fieldId];
                    }
                    if (technicalName) {
                        delete window.fieldValues[technicalName];
                    }
                }
            });
        }
    }
};

console.log('[DEBUG] Issues panel fixed script loaded v5');
