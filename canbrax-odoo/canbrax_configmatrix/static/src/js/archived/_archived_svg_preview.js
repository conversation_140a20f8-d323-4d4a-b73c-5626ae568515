/**
 * SVG Preview for ConfigMatrix
 *
 * This file contains the functionality for rendering SVG components in the product configurator.
 */

(function(window, document, $) {
    'use strict';

    // Check if jQuery is available
    if (typeof $ === 'undefined') {
        console.warn('[SVG-PREVIEW] jQuery not found, using fallback');
        $ = function(selector) {
            return document.querySelector(selector);
        };
        $.ready = function(callback) {
            if (document.readyState !== 'loading') {
                callback();
            } else {
                document.addEventListener('DOMContentLoaded', callback);
            }
        };
    }

    // Initialize when the DOM is loaded
    $(document).ready(function() {
        console.log('[SVG-PREVIEW] Initializing SVG preview');

        // Global field values object
        window.fieldValues = window.fieldValues || {};

        // Global components cache
        let svgComponentsCache = null;

        /**
         * Function to evaluate a condition with proper error handling
         */
        function evaluateCondition(condition) {
            if (!condition || condition === 'true') {
                return true;
            }

            console.log('[SVG-PREVIEW] Evaluating condition:', condition);
            console.log('[SVG-PREVIEW] Field values:', window.fieldValues);

            try {
                // Get field values from global scope
                const fieldValues = window.fieldValues || {};

                // Add default values for known problematic fields
                if (fieldValues.sand_slide_top_track_type === undefined) {
                    fieldValues.sand_slide_top_track_type = '';
                    console.log('[SVG-PREVIEW] Added default value for sand_slide_top_track_type');
                }

                if (fieldValues.sand_slide_frame_colour === undefined) {
                    fieldValues.sand_slide_frame_colour = '';
                    console.log('[SVG-PREVIEW] Added default value for sand_slide_frame_colour');
                }

                // Handle JSON-formatted conditions
                if (typeof condition === 'string' && condition.startsWith('__JSON__')) {
                    try {
                        // Extract the JSON part
                        const jsonStr = condition.substring(8);
                        const conditions = JSON.parse(jsonStr);

                        console.log('[SVG-PREVIEW] Parsed JSON conditions:', conditions);

                        // Group conditions by logic operator
                        const andConditions = conditions.filter(c => c.logic === 'and');
                        const orConditions = conditions.filter(c => c.logic === 'or');

                        // Evaluate AND conditions - all must be true
                        const andResult = andConditions.length === 0 ||
                            andConditions.every(c => evaluateCondition(c.condition));

                        // Evaluate OR conditions - at least one must be true
                        const orResult = orConditions.length === 0 ||
                            orConditions.some(c => evaluateCondition(c.condition));

                        console.log('[SVG-PREVIEW] AND result:', andResult, 'OR result:', orResult);

                        // Both AND and OR conditions must be satisfied
                        return andResult && orResult;
                    } catch (jsonError) {
                        console.error('[SVG-PREVIEW] Error parsing JSON condition:', jsonError);
                        return false;
                    }
                }

                // Define __JSON__ as a no-op function to prevent reference errors
                window.__JSON__ = function(obj) {
                    return obj;
                };

                // Extract all field names from the condition
                const fieldNameRegex = /\b([a-zA-Z][a-zA-Z0-9_]*)\b(?!\s*\()/g;
                let fieldMatch;
                const fieldNames = new Set();

                while ((fieldMatch = fieldNameRegex.exec(condition)) !== null) {
                    const fieldName = fieldMatch[1];
                    // Skip JavaScript keywords and common values
                    if (!['true', 'false', 'null', 'undefined', 'return', 'if', 'else', 'for', 'while', 'function', 'var', 'let', 'const'].includes(fieldName)) {
                        fieldNames.add(fieldName);
                    }
                }

                // Create a context object with all field values
                const context = {};
                for (const key in fieldValues) {
                    context[key] = fieldValues[key];
                }

                // Add missing fields with default values
                let missingFields = [];
                fieldNames.forEach(fieldName => {
                    if (context[fieldName] === undefined) {
                        console.log(`[SVG-PREVIEW] Field '${fieldName}' not found in context, setting default value`);
                        // Set a default value for missing fields
                        context[fieldName] = '';
                        missingFields.push(fieldName);
                    }
                });

                if (missingFields.length > 0) {
                    console.log(`[SVG-PREVIEW] Missing fields in condition: ${missingFields.join(', ')}`);
                }

                // Handle string comparison conditions (e.g., field == 'value')
                let safeCondition = condition;
                safeCondition = safeCondition.replace(/([a-zA-Z0-9_]+)\s*==\s*([a-zA-Z0-9_]+)(?!'|")/g, "$1 == '$2'");

                // Also handle triple equals (===)
                safeCondition = safeCondition.replace(/===\s*(['"]?)([^'";]+)(['"]?)/g, function(match, quote1, value, quote2) {
                    if (!quote1 && !quote2) {
                        return `== '${value}'`;
                    }
                    return match;
                });

                console.log('[SVG-PREVIEW] Safe condition:', safeCondition);

                // Use a safer evaluation approach with a try-catch block
                try {
                    // Create a function with the context variables
                    const evalFunc = new Function(...Object.keys(context), `
                        try {
                            return ${safeCondition};
                        } catch(e) {
                            console.error('[SVG-PREVIEW] Error in condition evaluation:', e);
                            return false;
                        }
                    `);

                    // Call the function with the context values
                    const result = evalFunc(...Object.values(context));
                    console.log('[SVG-PREVIEW] Condition result:', result);
                    return !!result;
                } catch (evalError) {
                    console.error('[SVG-PREVIEW] Error creating evaluation function:', evalError);
                    return false;
                }
            } catch (error) {
                console.error('[SVG-PREVIEW] Error evaluating condition:', error);
                return false;
            }
        }

        /**
         * Function to load SVG components from the server
         */
        function loadSvgComponents(templateId, callback) {
            console.log('[SVG-PREVIEW] Loading SVG components for template ID:', templateId);

            // If we already have the components cached, use them
            if (svgComponentsCache) {
                console.log('[SVG-PREVIEW] Using cached SVG components');
                if (callback) callback(svgComponentsCache);
                return;
            }

            // Show loading indicator in SVG container
            const svgContainer = document.getElementById('svg-container');
            if (svgContainer) {
                svgContainer.innerHTML = '<div class="text-center p-3"><i class="fas fa-spinner fa-spin fa-2x"></i><br>Loading visualization...</div>';
            }

            // Fetch SVG components from the server
            fetch('/config_matrix/get_svg_components', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    jsonrpc: "2.0",
                    method: "call",
                    params: {
                        template_id: templateId
                    },
                    id: new Date().getTime()
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('[SVG-PREVIEW] SVG components response:', data);

                if (data.result && data.result.success && data.result.components) {
                    // Cache the components
                    svgComponentsCache = data.result.components;
                    console.log('[SVG-PREVIEW] Cached', svgComponentsCache.length, 'SVG components');

                    // Call the callback with the components
                    if (callback) callback(svgComponentsCache);
                } else {
                    console.error('[SVG-PREVIEW] Error loading SVG components:', data.error || 'Unknown error');
                    if (svgContainer) {
                        svgContainer.innerHTML = '<div class="alert alert-warning">No SVG components available</div>';
                    }
                }
            })
            .catch(error => {
                console.error('[SVG-PREVIEW] Error fetching SVG components:', error);
                if (svgContainer) {
                    svgContainer.innerHTML = '<div class="alert alert-danger">Error loading SVG visualization</div>';
                }
            });
        }

        /**
         * Function to render SVG components in the product preview
         */
        function renderProductPreview() {
            console.log('[SVG-PREVIEW] Rendering product preview');

            // Get template ID
            const templateIdInput = document.querySelector('input[name="template_id"]');
            if (!templateIdInput) {
                console.error('[SVG-PREVIEW] Template ID input not found');
                return;
            }

            const templateId = parseInt(templateIdInput.value) || 0;
            console.log('[SVG-PREVIEW] Template ID:', templateId);

            // Load SVG components
            loadSvgComponents(templateId, function(components) {
                // Get the SVG container
                const svgContainer = document.getElementById('svg-container');
                if (!svgContainer) {
                    console.error('[SVG-PREVIEW] SVG container not found');
                    return;
                }

                // Clear the container
                svgContainer.innerHTML = '';

                // Sort components by z-index (ascending)
                const sortedComponents = [...components].sort((a, b) => a.z_index - b.z_index);
                console.log('[SVG-PREVIEW] Sorted components:', sortedComponents);

                // Create a div for each component with proper z-index
                sortedComponents.forEach(component => {
                    // Check if component should be visible
                    let isVisible = true;
                    if (component.condition && component.component_type === 'layer') {
                        isVisible = evaluateCondition(component.condition);
                        console.log(`[SVG-PREVIEW] Component ${component.name} visibility:`, isVisible);
                    }

                    // Only add visible components
                    if (isVisible) {
                        const componentDiv = document.createElement('div');
                        componentDiv.style.position = 'absolute';
                        componentDiv.style.zIndex = component.z_index;
                        componentDiv.style.top = '0';
                        componentDiv.style.left = '0';
                        componentDiv.style.width = '100%';
                        componentDiv.style.height = '100%';
                        componentDiv.innerHTML = component.svg_content;
                        componentDiv.setAttribute('data-component-name', component.name);
                        componentDiv.setAttribute('data-component-type', component.component_type);
                        componentDiv.setAttribute('data-z-index', component.z_index);

                        svgContainer.appendChild(componentDiv);
                        console.log(`[SVG-PREVIEW] Added component: ${component.name}, z-index: ${component.z_index}`);
                    } else {
                        console.log(`[SVG-PREVIEW] Skipped component: ${component.name}, z-index: ${component.z_index}, condition: ${component.condition}`);
                    }
                });

                // Make sure the container has position relative for absolute positioning to work
                svgContainer.style.position = 'relative';
                svgContainer.style.minHeight = '300px';

                console.log('[SVG-PREVIEW] Product preview rendered');
            });
        }

        // Make functions available globally
        window.svgPreview = {
            evaluateCondition: evaluateCondition,
            renderProductPreview: renderProductPreview,
            loadSvgComponents: loadSvgComponents
        };

        // Also expose the main function directly
        window.renderProductPreview = renderProductPreview;
        window.evaluateCondition = evaluateCondition;

        console.log('[SVG-PREVIEW] SVG preview initialized');
    });

})(window, document, window.jQuery);
