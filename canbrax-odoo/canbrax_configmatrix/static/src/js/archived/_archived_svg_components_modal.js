/**
 * SVG Components Modal
 * 
 * This script adds a "Show SVG" button to the configurator interface
 * and displays a modal with all SVG components, their conditions, and previews.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('[SVG-MODAL] Initializing SVG Components Modal');

    // Create the modal HTML
    const modalHtml = `
    <div class="modal fade" id="svgComponentsModal" tabindex="-1" aria-labelledby="svgComponentsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="svgComponentsModalLabel">SVG Components</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-12">
                            <button id="refreshSvgComponents" class="btn btn-sm btn-secondary">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-7">
                            <h6>Components List</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Component Name</th>
                                            <th>Type</th>
                                            <th>Z-Index</th>
                                            <th>Visibility Condition</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody id="svgComponentsTableBody">
                                        <tr>
                                            <td colspan="6" class="text-center">
                                                <i class="fas fa-spinner fa-spin"></i> Loading components...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <h6>SVG Preview</h6>
                            <div id="svgPreviewContainer" class="border rounded p-3 text-center bg-light" style="min-height: 300px; display: flex; align-items: center; justify-content: center;">
                                <div class="text-muted">
                                    <i class="fas fa-image fa-3x mb-2 d-block"></i>
                                    Select a component to preview
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    `;

    // Add the modal to the page
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Create the "Show SVG" button
    function addShowSvgButton() {
        // Check if the button container exists
        const buttonContainer = document.querySelector('.configurator-container .d-flex.justify-content-between');
        if (!buttonContainer) {
            console.warn('[SVG-MODAL] Button container not found');
            return;
        }

        // Check if the button already exists
        if (document.getElementById('showSvgButton')) {
            console.log('[SVG-MODAL] Show SVG button already exists');
            return;
        }

        // Create the button
        const showSvgButton = document.createElement('button');
        showSvgButton.id = 'showSvgButton';
        showSvgButton.className = 'btn btn-success';
        showSvgButton.innerHTML = 'Show SVG';
        showSvgButton.addEventListener('click', function() {
            // Load SVG components and show the modal
            loadSvgComponents();
            const modal = new bootstrap.Modal(document.getElementById('svgComponentsModal'));
            modal.show();
        });

        // Add the button to the container
        buttonContainer.appendChild(showSvgButton);
        console.log('[SVG-MODAL] Show SVG button added');
    }

    // Function to load SVG components
    function loadSvgComponents() {
        console.log('[SVG-MODAL] Loading SVG components');

        // Get template ID
        const templateIdInput = document.querySelector('input[name="template_id"]');
        if (!templateIdInput) {
            console.error('[SVG-MODAL] Template ID input not found');
            return;
        }

        const templateId = parseInt(templateIdInput.value) || 0;
        console.log('[SVG-MODAL] Template ID:', templateId);

        // Show loading indicator
        const svgComponentsTableBody = document.getElementById('svgComponentsTableBody');
        if (svgComponentsTableBody) {
            svgComponentsTableBody.innerHTML = '<tr><td colspan="6" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading SVG components...</td></tr>';
        }

        // Fetch SVG components
        fetch('/config_matrix/get_svg_components', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                jsonrpc: "2.0",
                method: "call",
                params: {
                    template_id: templateId
                },
                id: new Date().getTime()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.result && data.result.success && data.result.components) {
                displaySvgComponents(data.result.components);
            } else {
                console.error('[SVG-MODAL] Error loading SVG components:', data);
                if (svgComponentsTableBody) {
                    svgComponentsTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error loading SVG components</td></tr>';
                }
            }
        })
        .catch(error => {
            console.error('[SVG-MODAL] Error fetching SVG components:', error);
            if (svgComponentsTableBody) {
                svgComponentsTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error loading SVG components</td></tr>';
            }
        });
    }

    // Function to display SVG components
    function displaySvgComponents(components) {
        console.log('[SVG-MODAL] Displaying SVG components:', components);

        const svgComponentsTableBody = document.getElementById('svgComponentsTableBody');
        if (!svgComponentsTableBody) {
            console.error('[SVG-MODAL] SVG components table body not found');
            return;
        }

        // Sort components by z-index
        components.sort((a, b) => a.z_index - b.z_index);

        // Clear the table
        svgComponentsTableBody.innerHTML = '';

        // Get field values from global scope
        let fieldValues = {};
        if (window.fieldValues) {
            fieldValues = window.fieldValues;
        } else {
            console.warn('[SVG-MODAL] fieldValues not found in global scope, using empty object');
        }

        // Add each component to the table
        components.forEach(component => {
            const row = document.createElement('tr');

            // Evaluate condition if present
            let isVisible = true;
            if (component.condition && component.component_type === 'layer') {
                isVisible = evaluateCondition(component.condition, fieldValues);
            }

            row.innerHTML = `
                <td>${component.name}</td>
                <td>${component.component_type}</td>
                <td>${component.z_index}</td>
                <td><code>${component.condition || 'Always visible'}</code></td>
                <td>${isVisible ? '<span class="badge bg-success">Visible</span>' : '<span class="badge bg-secondary">Hidden</span>'}</td>
                <td><button class="btn btn-sm btn-outline-primary preview-svg-btn" data-component-id="${component.id}" type="button">Preview</button></td>
            `;

            svgComponentsTableBody.appendChild(row);

            // Add event listener to preview button
            const previewButton = row.querySelector('.preview-svg-btn');
            previewButton.addEventListener('click', function() {
                showSvgPreview(component, fieldValues);
            });
        });

        // Add event listener to refresh button
        const refreshButton = document.getElementById('refreshSvgComponents');
        if (refreshButton) {
            refreshButton.addEventListener('click', loadSvgComponents);
        }
    }

    // Function to show SVG preview
    function showSvgPreview(component, fieldValues) {
        console.log('[SVG-MODAL] Showing SVG preview for component:', component.name);

        const svgPreviewContainer = document.getElementById('svgPreviewContainer');
        if (!svgPreviewContainer) {
            console.error('[SVG-MODAL] SVG preview container not found');
            return;
        }

        // Process SVG content with field values
        let svgContent = component.svg_content;

        // Replace ${fieldName} with actual values
        svgContent = svgContent.replace(/\${(\w+)}/g, function(match, fieldName) {
            return fieldValues[fieldName] !== undefined
                   ? fieldValues[fieldName]
                   : match;
        });

        // Update the preview container
        svgPreviewContainer.innerHTML = `
            <div class="mb-2">
                <h6>${component.name}</h6>
                <div class="small text-muted">
                    Type: ${component.component_type}, Z-Index: ${component.z_index}
                    ${component.condition ? `<br>Condition: <code>${component.condition}</code>` : ''}
                </div>
            </div>
            <div class="svg-preview" style="background-color: #f8f9fa; padding: 10px; border-radius: 4px;">
                ${svgContent}
            </div>
        `;
    }

    // Function to evaluate a condition
    function evaluateCondition(condition, fieldValues) {
        if (!condition || condition === 'true') {
            return true;
        }

        console.log('[SVG-MODAL] Evaluating condition:', condition);
        console.log('[SVG-MODAL] Field values:', fieldValues);

        try {
            // Extract field names from the condition
            const fieldNames = condition.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || [];

            // Create a context with field values
            const context = { ...fieldValues };

            // Add missing fields with default values
            fieldNames.forEach(fieldName => {
                if (context[fieldName] === undefined) {
                    console.log(`[SVG-MODAL] Field '${fieldName}' not found in context, setting default value`);
                    context[fieldName] = '';
                }
            });

            // Create a function with the field values in scope
            const func = new Function(...Object.keys(context), `return ${condition};`);
            
            // Call the function with the field values
            return func(...Object.values(context));
        } catch (error) {
            console.error('[SVG-MODAL] Error evaluating condition:', error);
            return false;
        }
    }

    // Add the button when the page loads
    setTimeout(addShowSvgButton, 500);

    // Make functions available globally
    window.svgComponentsModal = {
        loadSvgComponents,
        displaySvgComponents,
        showSvgPreview,
        evaluateCondition
    };
});
