/**
 * SVG Panel JavaScript
 *
 * This file contains the functionality for the SVG panel in the product configurator.
 * It allows users to view and debug SVG components and their conditions.
 */

// Check if we're in an Odoo environment
if (typeof odoo !== 'undefined') {
    // If we are, define our module the Odoo way
    odoo.define('canbrax_configmatrix.svg_panel', function (require) {
        "use strict";

        console.log('[DEBUG] SVG Panel JS loading in Odoo environment');

        // Define functions in Odoo context
        function updateSvgPanel() {
            console.log('[DEBUG] updateSvgPanel called from Odoo environment');
            // Call the standalone implementation
            if (window.svgPanelFunctions && window.svgPanelFunctions.updateSvgPanel) {
                window.svgPanelFunctions.updateSvgPanel();
            }
        }

        // Make sure to export the function to window for frontend use
        window.updateSvgPanel = updateSvgPanel;

        return {
            updateSvgPanel: updateSvgPanel
        };
    });
} else {
    // If we're not in Odoo (e.g., plain frontend), define things directly
    (function(window, document, $) {
        "use strict";

        console.log('[DEBUG] SVG Panel JS loading in standalone mode');

    // Wait for document ready
    $(document).ready(function() {
        console.log('[DEBUG] SVG Panel JS loaded');

        // Check if SVG panel elements exist in the DOM
        const svgPanelElements = {
            showSvgButton: document.getElementById('show-svg-panel'),
            hideSvgButton: document.getElementById('hide-svg-panel'),
            refreshSvgButton: document.getElementById('refresh-svg'),
            svgPanel: document.getElementById('svg-panel'),
            svgComponentsTableBody: document.getElementById('svg-components-table-body'),
            svgPreviewContainer: document.getElementById('svg-preview-container')
        };

        console.log('[DEBUG] SVG Panel DOM elements:', {
            showSvgButton: svgPanelElements.showSvgButton ? 'Found' : 'Not found',
            hideSvgButton: svgPanelElements.hideSvgButton ? 'Found' : 'Not found',
            refreshSvgButton: svgPanelElements.refreshSvgButton ? 'Found' : 'Not found',
            svgPanel: svgPanelElements.svgPanel ? 'Found' : 'Not found',
            svgComponentsTableBody: svgPanelElements.svgComponentsTableBody ? 'Found' : 'Not found',
            svgPreviewContainer: svgPanelElements.svgPreviewContainer ? 'Found' : 'Not found'
        });

        // Make updateSvgPanel available globally
        window.updateSvgPanel = updateSvgPanel;

        // SVG panel event listeners
        const showSvgButton = document.getElementById('show-svg-panel');
        const hideSvgButton = document.getElementById('hide-svg-panel');
        const refreshSvgButton = document.getElementById('refresh-svg');
        const svgPanel = document.getElementById('svg-panel');

        // Make evaluateCondition function available globally for SVG panel
        window.evaluateCondition = function(condition) {
            if (!condition || condition === 'true') {
                return true;
            }

            console.log('[DEBUG] Evaluating condition:', condition);

            try {
                // Get field values from global scope
                const fieldValues = window.fieldValues || {};

                // Add default values for known problematic fields
                if (fieldValues.sand_slide_top_track_type === undefined) {
                    fieldValues.sand_slide_top_track_type = '';
                    console.log('[DEBUG] Added default value for sand_slide_top_track_type');
                }

                if (fieldValues.sand_slide_frame_colour === undefined) {
                    fieldValues.sand_slide_frame_colour = '';
                    console.log('[DEBUG] Added default value for sand_slide_frame_colour');
                }

                // Handle JSON-formatted conditions
                if (typeof condition === 'string' && condition.startsWith('__JSON__')) {
                    console.log('[DEBUG] Processing JSON condition:', condition);
                    try {
                        // Extract the JSON part
                        const jsonStr = condition.substring(8);
                        const conditions = JSON.parse(jsonStr);

                        console.log('[DEBUG] Parsed JSON conditions:', conditions);

                        // Group conditions by logic operator
                        const andConditions = conditions.filter(c => c.logic === 'and');
                        const orConditions = conditions.filter(c => c.logic === 'or');

                        console.log('[DEBUG] AND conditions:', andConditions.length, 'OR conditions:', orConditions.length);

                        // Evaluate AND conditions - all must be true
                        const andResult = andConditions.length === 0 ||
                            andConditions.every(c => window.evaluateCondition(c.condition));

                        // Evaluate OR conditions - at least one must be true
                        const orResult = orConditions.length === 0 ||
                            orConditions.some(c => window.evaluateCondition(c.condition));

                        console.log('[DEBUG] AND result:', andResult, 'OR result:', orResult);

                        // Both AND and OR conditions must be satisfied
                        return andResult && orResult;
                    } catch (jsonError) {
                        console.error('[DEBUG] Error parsing JSON condition:', jsonError);
                        // If we can't parse the JSON, treat it as a regular condition
                        // but remove the __JSON__ prefix
                        condition = condition.substring(8);
                    }
                }

                // Define __JSON__ as a no-op function to prevent reference errors
                window.__JSON__ = function(obj) {
                    console.log('[DEBUG] __JSON__ function called with:', obj);
                    return obj;
                };

                // Handle string comparison conditions (e.g., field == 'value')
                // This regex matches patterns like: field == value, field=='value', field == "value"
                const stringComparisonRegex = /([a-zA-Z0-9_]+)\s*==\s*(['"]?)([a-zA-Z0-9_]+)(['"]?)/g;
                let safeCondition = condition;

                // Replace all occurrences of string comparisons
                let match;
                const regex = new RegExp(stringComparisonRegex);
                while ((match = regex.exec(condition)) !== null) {
                    const field = match[1];
                    const hasQuotes = match[2] || match[4];
                    const value = match[3];

                    // If the value doesn't have quotes, add them
                    if (!hasQuotes) {
                        const replacement = `${field} == '${value}'`;
                        safeCondition = safeCondition.replace(match[0], replacement);
                    }
                }

                // Also handle triple equals (===)
                safeCondition = safeCondition.replace(/===\s*(['"]?)([^'";]+)(['"]?)/g, function(match, quote1, value, quote2) {
                    if (!quote1 && !quote2) {
                        return `== '${value}'`;
                    }
                    return match;
                });

                console.log('[DEBUG] Original condition:', condition);
                console.log('[DEBUG] Safe condition:', safeCondition);

                // Create a context object with all field values
                const context = {};
                for (const key in fieldValues) {
                    context[key] = fieldValues[key];
                }

                // Extract all field names from the condition
                const fieldNameRegex = /\b([a-zA-Z][a-zA-Z0-9_]*)\b(?!\s*\()/g;
                let fieldMatch;
                const fieldNames = new Set();

                while ((fieldMatch = fieldNameRegex.exec(safeCondition)) !== null) {
                    const fieldName = fieldMatch[1];
                    // Skip JavaScript keywords and common values
                    if (!['true', 'false', 'null', 'undefined', 'return', 'if', 'else', 'for', 'while', 'function', 'var', 'let', 'const'].includes(fieldName)) {
                        fieldNames.add(fieldName);
                    }
                }

                // Check if all fields in the condition exist in the context
                let missingFields = [];
                fieldNames.forEach(fieldName => {
                    if (context[fieldName] === undefined) {
                        console.log(`[DEBUG] Field '${fieldName}' not found in context, setting default value`);
                        // Set a default value for missing fields
                        context[fieldName] = '';
                        missingFields.push(fieldName);
                    }
                });

                if (missingFields.length > 0) {
                    console.log(`[DEBUG] Missing fields in condition: ${missingFields.join(', ')}`);
                }

                // Use a safer evaluation approach with a try-catch block
                try {
                    // Create a function with the context variables
                    const evalFunc = new Function(...Object.keys(context), `
                        try {
                            return ${safeCondition};
                        } catch(e) {
                            console.error('[DEBUG] Error in condition evaluation:', e);
                            return false;
                        }
                    `);

                    // Call the function with the context values
                    const result = evalFunc(...Object.values(context));
                    console.log('[DEBUG] Condition result:', result);
                    return !!result;
                } catch (evalError) {
                    console.error('[DEBUG] Error creating evaluation function:', evalError);
                    return false;
                }
            } catch (error) {
                console.error('[DEBUG] Error evaluating condition:', error);
                return false;
            }
        };

        if (showSvgButton && svgPanel) {
            console.log('[DEBUG] Adding click event to Show SVG button');
            showSvgButton.addEventListener('click', function() {
                console.log('[DEBUG] Show SVG button clicked');
                svgPanel.style.display = 'block';
                updateSvgPanel();
            });
        } else {
            console.error('[DEBUG] Show SVG button or SVG panel not found:', {
                showSvgButton: showSvgButton ? 'Found' : 'Not found',
                svgPanel: svgPanel ? 'Found' : 'Not found'
            });
        }

        if (hideSvgButton && svgPanel) {
            hideSvgButton.addEventListener('click', function() {
                console.log('[DEBUG] Hide SVG button clicked');
                svgPanel.style.display = 'none';
            });
        }

        if (refreshSvgButton) {
            refreshSvgButton.addEventListener('click', function() {
                console.log('[DEBUG] Refresh SVG button clicked');
                updateSvgPanel();
            });
        }
    });

    /**
     * Function to update the SVG panel
     */
    function updateSvgPanel() {
        console.log('[DEBUG] updateSvgPanel called');

        // Get template ID
        const templateIdInput = document.querySelector('input[name="template_id"]');
        if (!templateIdInput) {
            console.error('Template ID input not found');
            return;
        }

        const templateId = parseInt(templateIdInput.value) || 0;
        console.log('[DEBUG] Template ID:', templateId);

        // Get field values from global scope
        let fieldValues = {};
        if (window.fieldValues) {
            fieldValues = window.fieldValues;
        } else {
            console.warn('[DEBUG] fieldValues not found in global scope, using empty object');
        }

        // Show loading indicator
        const svgComponentsTableBody = document.getElementById('svg-components-table-body');
        if (svgComponentsTableBody) {
            svgComponentsTableBody.innerHTML = '<tr><td colspan="6" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading SVG components...</td></tr>';
        }

        console.log('[DEBUG] Sending request to get SVG components for template ID:', templateId);

        // Use the same approach as the base SVG loading
        fetch('/config_matrix/get_svg_components', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                jsonrpc: "2.0",
                method: "call",
                params: {
                    template_id: templateId
                },
                id: new Date().getTime()
            })
        })
        .then(response => {
            console.log('[DEBUG] Got response from server:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('[DEBUG] SVG components response:', JSON.stringify(data, null, 2));

            // Check if we have a result
            if (data.result) {
                // Check if the result has success and components
                if (data.result.success && data.result.components) {
                    console.log('[DEBUG] Found', data.result.components.length, 'SVG components');
                    console.log('[DEBUG] Components:', JSON.stringify(data.result.components, null, 2));

                    try {
                        // Update the SVG components list in the debug panel
                        updateSvgComponentsList(data.result.components);
                        console.log('[DEBUG] SVG components list updated successfully');

                        // Also update the main product preview panel
                        updateProductPreview(data.result.components);
                        console.log('[DEBUG] Product preview updated successfully');
                    } catch (error) {
                        console.error('[DEBUG] Error updating SVG components list:', error);
                        if (svgComponentsTableBody) {
                            svgComponentsTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error updating SVG components: ' +
                                error.message + '</td></tr>';
                        }
                    }
                } else {
                    // Handle error in result
                    console.error('[DEBUG] Error in SVG components response:', data.result.error || 'Unknown error');
                    if (svgComponentsTableBody) {
                        svgComponentsTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error loading SVG components: ' +
                            (data.result.error || 'Unknown error') + '</td></tr>';
                    }
                }
            } else if (data.error) {
                // Handle JSON-RPC error
                console.error('[DEBUG] JSON-RPC error:', data.error);
                if (svgComponentsTableBody) {
                    svgComponentsTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error loading SVG components: ' +
                        (data.error.data && data.error.data.message ? data.error.data.message : 'Server error') + '</td></tr>';
                }
            } else {
                // Unexpected response format
                console.error('[DEBUG] Unexpected response format:', data);
                if (svgComponentsTableBody) {
                    svgComponentsTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Unexpected response format</td></tr>';
                }
            }
        })
        .catch(error => {
            // Network or parsing error
            console.error('[DEBUG] Fetch error:', error);
            if (svgComponentsTableBody) {
                svgComponentsTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error loading SVG components: ' + error.message + '</td></tr>';
            }
        });
    }

    /**
     * Function to update the main product preview panel
     */
    function updateProductPreview(components) {
        console.log('[DEBUG] updateProductPreview called with', components.length, 'components');

        // Get the main product preview container
        const svgContainer = document.getElementById('svg-container');
        if (!svgContainer) {
            console.error('[DEBUG] SVG container not found');
            return;
        }

        // Clear the container
        svgContainer.innerHTML = '';

        // Sort components by z-index (ascending)
        const sortedComponents = [...components].sort((a, b) => a.z_index - b.z_index);

        // Create a div for each component with proper z-index
        sortedComponents.forEach(component => {
            // Check if component should be visible
            let isVisible = true;
            if (component.condition && component.component_type === 'layer') {
                // Evaluate the condition
                isVisible = window.evaluateCondition(component.condition);
            }

            // Only add visible components
            if (isVisible) {
                const componentDiv = document.createElement('div');
                componentDiv.style.position = 'absolute';
                componentDiv.style.zIndex = component.z_index;
                componentDiv.style.top = '0';
                componentDiv.style.left = '0';
                componentDiv.style.width = '100%';
                componentDiv.style.height = '100%';
                componentDiv.innerHTML = component.svg_content;
                componentDiv.setAttribute('data-component-name', component.name);
                componentDiv.setAttribute('data-component-type', component.component_type);
                componentDiv.setAttribute('data-z-index', component.z_index);

                svgContainer.appendChild(componentDiv);
                console.log(`[DEBUG] Added component to preview: ${component.name}, z-index: ${component.z_index}, visible: ${isVisible}`);
            } else {
                console.log(`[DEBUG] Skipped component in preview: ${component.name}, z-index: ${component.z_index}, condition: ${component.condition}`);
            }
        });

        // Make sure the container has position relative for absolute positioning to work
        svgContainer.style.position = 'relative';
        svgContainer.style.minHeight = '300px';

        console.log('[DEBUG] Product preview updated with', sortedComponents.length, 'components');
    }

    /**
     * Function to update the SVG components list
     */
    function updateSvgComponentsList(components) {
        console.log('[DEBUG] updateSvgComponentsList called with', components.length, 'components');

        const svgComponentsTableBody = document.getElementById('svg-components-table-body');
        if (!svgComponentsTableBody) {
            console.error('SVG components table body not found');
            return;
        }

        // Clear existing components
        svgComponentsTableBody.innerHTML = '';

        if (!components || components.length === 0) {
            svgComponentsTableBody.innerHTML = '<tr><td colspan="6" class="text-center">No SVG components found</td></tr>';
            return;
        }

        // Sort components by z-index
        components.sort((a, b) => a.z_index - b.z_index);

        // Add each component to the table
        components.forEach(component => {
            const row = document.createElement('tr');

            // Evaluate condition if present
            let isVisible = true;
            if (component.condition && component.component_type === 'layer') {
                // Use window.evaluateCondition if available
                if (window.evaluateCondition) {
                    isVisible = window.evaluateCondition(component.condition);
                } else {
                    console.warn('[DEBUG] evaluateCondition not found in global scope');
                }
            }

            row.innerHTML = `
                <td>${component.name}</td>
                <td>${component.component_type}</td>
                <td>${component.z_index}</td>
                <td><code>${component.condition || 'Always visible'}</code></td>
                <td>${isVisible ? '<span class="badge bg-success">Visible</span>' : '<span class="badge bg-secondary">Hidden</span>'}</td>
                <td><button class="btn btn-sm btn-outline-primary preview-svg-btn" data-component-id="${component.id}" type="button">Preview</button></td>
            `;

            // Add click event to the preview button
            const previewButton = row.querySelector('.preview-svg-btn');
            if (previewButton) {
                previewButton.addEventListener('click', function(event) {
                    // Prevent default action and event propagation
                    event.preventDefault();
                    event.stopPropagation();

                    console.log('[DEBUG] Preview button clicked for component:', component.name);
                    showSvgPreview(component);

                    return false; // Extra prevention for older browsers
                });
            }

            svgComponentsTableBody.appendChild(row);
        });
    }

    /**
     * Function to show SVG preview
     */
    function showSvgPreview(component) {
        console.log('[DEBUG] showSvgPreview called for component:', component.name);

        const svgPreviewContainer = document.getElementById('svg-preview-container');
        if (!svgPreviewContainer) {
            console.error('SVG preview container not found');
            return;
        }

        // Get field values from global scope
        let fieldValues = {};
        if (window.fieldValues) {
            fieldValues = window.fieldValues;
        } else {
            console.warn('[DEBUG] fieldValues not found in global scope, using empty object');
        }

        // Process SVG content with field values
        let svgContent = component.svg_content;

        // Replace ${fieldName} with actual values
        svgContent = svgContent.replace(/\${(\w+)}/g, function(match, fieldName) {
            return fieldValues[fieldName] !== undefined
                   ? fieldValues[fieldName]
                   : match;
        });

        // Update the preview container
        svgPreviewContainer.innerHTML = `
            <div class="mb-2">
                <h6>${component.name}</h6>
                <div class="small text-muted">
                    Type: ${component.component_type}, Z-Index: ${component.z_index}
                    ${component.condition ? `<br>Condition: <code>${component.condition}</code>` : ''}
                </div>
            </div>
            <div class="svg-preview" style="background-color: #f8f9fa; padding: 10px; border-radius: 4px;">
                ${svgContent}
            </div>
        `;
    }

    // Expose functions globally
    window.svgPanelFunctions = {
        updateSvgPanel: updateSvgPanel,
        updateSvgComponentsList: updateSvgComponentsList,
        showSvgPreview: showSvgPreview
    };

    // Make updateSvgPanel available globally
    window.updateSvgPanel = updateSvgPanel;

    console.log('[DEBUG] SVG Panel JS functions exposed globally');

    // Mark script as loaded
    if (window.scriptsLoaded) {
        window.scriptsLoaded.svg_panel = true;
    } else {
        window.scriptsLoaded = { svg_panel: true };
    }
    console.log("SVG Panel script loaded successfully");

})(window, document, window.jQuery || window.$ || function() {
        console.error('jQuery not found, using fallback');
        return {
            ready: function(fn) {
                if (document.readyState !== 'loading') {
                    fn();
                } else {
                    document.addEventListener('DOMContentLoaded', fn);
                }
            }
        };
    });
}
