/**
 * Complete SVG System Fix for ConfigMatrix
 * 
 * This file replaces the previous integration fix and provides a complete
 * solution for both main SVG rendering and debug panel functionality.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('[SVG-COMPLETE] Initializing complete SVG system');

    // Global state
    window.svgSystem = {
        templateId: null,
        fieldValues: {},
        components: [],
        debugPanelVisible: false,
        initialized: false
    };

    /**
     * Initialize the complete SVG system
     */
    function initializeCompleteSvgSystem() {
        // Get template ID
        const templateInput = document.querySelector('input[name="template_id"]');
        if (templateInput) {
            window.svgSystem.templateId = parseInt(templateInput.value);
            console.log('[SVG-COMPLETE] Template ID:', window.svgSystem.templateId);
        }

        // Load components and set up everything
        loadSvgComponents().then(() => {
            setupFieldListeners();
            setupDebugPanel();
            renderMainSvg();
            window.svgSystem.initialized = true;
            console.log('[SVG-COMPLETE] System fully initialized');
        });
    }

    /**
     * Load SVG components from server
     */
    async function loadSvgComponents() {
        if (!window.svgSystem.templateId) {
            console.warn('[SVG-COMPLETE] No template ID available');
            return;
        }

        try {
            const response = await fetch('/config_matrix/get_svg_components', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    jsonrpc: "2.0",
                    method: "call",
                    params: {
                        template_id: window.svgSystem.templateId
                    },
                    id: new Date().getTime()
                })
            });

            const data = await response.json();
            
            if (data.result && data.result.success && data.result.components) {
                window.svgSystem.components = data.result.components;
                console.log('[SVG-COMPLETE] Loaded', window.svgSystem.components.length, 'components');
                
                // Sort by z-index
                window.svgSystem.components.sort((a, b) => (a.z_index || 0) - (b.z_index || 0));
                
                // Store globally for other systems
                window.svgRenderer = {
                    components: window.svgSystem.components,
                    configValues: window.svgSystem.fieldValues
                };
            } else {
                console.error('[SVG-COMPLETE] Failed to load components:', data);
            }
        } catch (error) {
            console.error('[SVG-COMPLETE] Error loading components:', error);
        }
    }

    /**
     * Set up field change listeners
     */
    function setupFieldListeners() {
        const configFields = document.querySelectorAll('.config-field');
        
        configFields.forEach(field => {
            const technicalName = field.getAttribute('data-technical-name');
            
            if (technicalName) {
                // Add appropriate event listeners
                if (field.type === 'checkbox') {
                    field.addEventListener('change', function() {
                        const value = field.checked ? (field.value || 'true') : 'false';
                        updateFieldValue(technicalName, value);
                    });
                } else {
                    field.addEventListener('change', function() {
                        updateFieldValue(technicalName, field.value);
                    });
                    
                    // Real-time updates for text/number fields
                    if (field.type === 'text' || field.type === 'number') {
                        field.addEventListener('input', function() {
                            updateFieldValue(technicalName, field.value);
                        });
                    }
                }
                
                // Initialize current value
                const currentValue = getCurrentFieldValue(field);
                if (currentValue !== null) {
                    updateFieldValue(technicalName, currentValue);
                }
            }
        });

        console.log('[SVG-COMPLETE] Set up listeners for', configFields.length, 'fields');
    }

    /**
     * Get current field value
     */
    function getCurrentFieldValue(field) {
        if (field.type === 'checkbox') {
            return field.checked ? (field.value || 'true') : 'false';
        } else {
            return field.value || '';
        }
    }

    /**
     * Update field value and trigger updates
     */
    function updateFieldValue(technicalName, value) {
        // Store the value
        window.svgSystem.fieldValues[technicalName] = value;
        
        // Update global references for compatibility
        window.fieldValues = window.svgSystem.fieldValues;
        if (window.svgRenderer) {
            window.svgRenderer.configValues = window.svgSystem.fieldValues;
        }
        
        if (window.debugConditions) {
            console.log('[SVG-COMPLETE] Field updated:', technicalName, '=', value);
            console.log('[SVG-COMPLETE] Field values count:', Object.keys(window.svgSystem.fieldValues).length);
        }
        
        // Update both main SVG and debug panel
        renderMainSvg();
        updateDebugPanel();
        
        // Dispatch event for other systems
        document.dispatchEvent(new CustomEvent('fieldValueChanged', {
            detail: { fieldName: technicalName, value: value, allValues: window.svgSystem.fieldValues }
        }));
    }

    /**
     * Render the main SVG in the product preview
     */
    function renderMainSvg() {
        const svgContainer = document.getElementById('svg-container');
        if (!svgContainer || !window.svgSystem.components.length) {
            return;
        }

        console.log('[SVG-COMPLETE] Rendering main SVG');
        console.log('[SVG-COMPLETE] Current field values:', window.svgSystem.fieldValues);

        // Find base component
        const baseComponent = window.svgSystem.components.find(comp => comp.component_type === 'base');
        if (!baseComponent) {
            console.error('[SVG-COMPLETE] No base component found');
            svgContainer.innerHTML = '<div class="text-danger"><i class="fas fa-exclamation-triangle"></i> No base SVG component found</div>';
            return;
        }

        // Start with base SVG
        let svgContent = processTemplate(baseComponent.svg_content);
        
        // Ensure proper SVG structure
        if (!svgContent.includes('<svg')) {
            svgContent = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 600" width="100%" height="100%">
                ${svgContent}
            </svg>`;
        }

        // Add conditional layers
        let addedLayers = 0;
        const visibleComponents = [baseComponent];

        window.svgSystem.components.forEach(component => {
            if (component.component_type !== 'layer') {
                return;
            }

            const isVisible = evaluateCondition(component.condition);
            console.log('[SVG-COMPLETE] Component', component.name, 
                       'condition:', component.condition || 'Always visible', '=', isVisible);

            if (isVisible) {
                console.log('[SVG-COMPLETE] Adding layer:', component.name);
                visibleComponents.push(component);

                const layerSvg = processTemplate(component.svg_content);
                svgContent = svgContent.replace('</svg>', layerSvg + '</svg>');
                addedLayers++;
            }
        });

        console.log('[SVG-COMPLETE] Added', addedLayers, 'layers to main SVG');

        // Update display
        try {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = svgContent;

            if (tempDiv.firstChild) {
                svgContainer.innerHTML = '';
                svgContainer.appendChild(tempDiv.firstChild);
                console.log('[SVG-COMPLETE] Main SVG rendered successfully');
            } else {
                console.error('[SVG-COMPLETE] Failed to parse SVG content');
                svgContainer.innerHTML = '<div class="text-danger">Failed to parse SVG content</div>';
            }
        } catch (error) {
            console.error('[SVG-COMPLETE] Error rendering main SVG:', error);
            svgContainer.innerHTML = '<div class="text-danger">Error rendering SVG: ' + error.message + '</div>';
        }
    }

    /**
     * Set up debug panel functionality
     */
    function setupDebugPanel() {
        // Find the "Show SVG" button
        const showSvgButton = document.getElementById('showSvgButtonInline');
        const svgPanel = document.getElementById('svg-panel');
        const hideSvgButton = document.getElementById('hide-svg-panel');
        const refreshSvgButton = document.getElementById('svg-components-refresh');

        if (!showSvgButton || !svgPanel) {
            console.warn('[SVG-COMPLETE] Debug panel elements not found');
            return;
        }

        // Show/hide panel
        showSvgButton.addEventListener('click', function() {
            if (window.svgSystem.debugPanelVisible) {
                hideDebugPanel();
            } else {
                showDebugPanel();
            }
        });

        if (hideSvgButton) {
            hideSvgButton.addEventListener('click', hideDebugPanel);
        }

        if (refreshSvgButton) {
            refreshSvgButton.addEventListener('click', function() {
                updateDebugPanel();
                renderMainSvg();
            });
        }

        console.log('[SVG-COMPLETE] Debug panel setup complete');
    }

    /**
     * Show debug panel
     */
    function showDebugPanel() {
        const svgPanel = document.getElementById('svg-panel');
        if (svgPanel) {
            svgPanel.style.display = 'block';
            window.svgSystem.debugPanelVisible = true;
            updateDebugPanel();
            console.log('[SVG-COMPLETE] Debug panel shown');
        }
    }

    /**
     * Hide debug panel
     */
    function hideDebugPanel() {
        const svgPanel = document.getElementById('svg-panel');
        if (svgPanel) {
            svgPanel.style.display = 'none';
            window.svgSystem.debugPanelVisible = false;
            console.log('[SVG-COMPLETE] Debug panel hidden');
        }
    }

    /**
     * Update debug panel content
     */
    function updateDebugPanel() {
        if (!window.svgSystem.debugPanelVisible) {
            return;
        }

        const tableBody = document.getElementById('svg-components-table-body');
        const previewContainer = document.getElementById('svg-preview-container');
        
        if (!tableBody) {
            console.warn('[SVG-COMPLETE] Debug table not found');
            return;
        }

        console.log('[SVG-COMPLETE] Updating debug panel');

        // Clear table
        tableBody.innerHTML = '';

        // Add each component to the table
        window.svgSystem.components.forEach(component => {
            const row = document.createElement('tr');
            
            // Evaluate visibility
            let isVisible = true;
            if (component.condition && component.component_type !== 'base') {
                isVisible = evaluateCondition(component.condition);
            }

            row.innerHTML = `
                <td>${component.name || 'Unnamed'}</td>
                <td>
                    <span class="badge ${component.component_type === 'base' ? 'bg-primary' : 'bg-secondary'}">
                        ${component.component_type || 'unknown'}
                    </span>
                </td>
                <td>${component.z_index || 0}</td>
                <td><code>${component.condition || 'Always visible'}</code></td>
                <td>
                    ${isVisible ? 
                        '<span class="badge bg-success">Visible</span>' : 
                        '<span class="badge bg-secondary">Hidden</span>'
                    }
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary preview-svg-btn" type="button">
                        Preview
                    </button>
                </td>
            `;

            tableBody.appendChild(row);

            // Add preview functionality
            const previewButton = row.querySelector('.preview-svg-btn');
            previewButton.addEventListener('click', () => {
                showComponentPreview(component, previewContainer);
            });
        });

        console.log('[SVG-COMPLETE] Debug panel updated with', window.svgSystem.components.length, 'components');
    }

    /**
     * Show component preview in debug panel
     */
    function showComponentPreview(component, previewContainer) {
        if (!previewContainer) {
            console.warn('[SVG-COMPLETE] Preview container not found');
            return;
        }

        console.log('[SVG-COMPLETE] Showing preview for:', component.name);

        let svgContent = processTemplate(component.svg_content || '');

        // Wrap in SVG if needed
        if (svgContent && !svgContent.trim().startsWith('<svg')) {
            svgContent = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 600" width="100%" height="100%">
                ${svgContent}
            </svg>`;
        }

        previewContainer.innerHTML = `
            <div class="mb-2">
                <h6>${component.name || 'Unnamed Component'}</h6>
                <div class="small text-muted">
                    Type: ${component.component_type || 'unknown'}, 
                    Z-Index: ${component.z_index || 0}
                    ${component.condition ? `<br>Condition: <code>${component.condition}</code>` : ''}
                </div>
            </div>
            <div class="svg-preview" style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;">
                ${svgContent || '<div class="text-muted">No SVG content</div>'}
            </div>
        `;
    }

    /**
     * Process template variables
     */
    function processTemplate(template) {
        if (!template) return '';

        return template.replace(/\${(\w+)}/g, (match, fieldName) => {
            const value = window.svgSystem.fieldValues[fieldName];
            if (value !== undefined && value !== null && value !== '') {
                console.log('[SVG-COMPLETE] Replacing template variable:', fieldName, '=', value);
                return String(value);
            }
            return match;
        });
    }

    /**
     * Evaluate condition for component visibility
     */
    function evaluateCondition(condition) {
        if (!condition || condition === 'true' || condition === 'Always visible') {
            return true;
        }

        try {
            // Handle simple equality conditions first
            const equalityMatch = condition.match(/^\s*(\w+)\s*(==|===|!=|!==|>=|<=|>|<)\s*['"]?([^'"]*?)['"]?\s*$/);
            if (equalityMatch) {
                const [_, fieldName, operator, value] = equalityMatch;
                const fieldValue = window.svgSystem.fieldValues[fieldName];

                console.log('[SVG-COMPLETE] Evaluating:', fieldName, operator, value, 'with field value:', fieldValue);

                // Handle empty/unselected fields
                if (fieldValue === undefined || fieldValue === null || fieldValue === '') {
                    const result = (operator === '!=' || operator === '!==');
                    console.log('[SVG-COMPLETE] Empty field result:', result);
                    return result;
                }

                let result;
                switch (operator) {
                    case '==':
                    case '===':
                        result = String(fieldValue) === String(value);
                        break;
                    case '!=':
                    case '!==':
                        result = String(fieldValue) !== String(value);
                        break;
                    case '>':
                        result = Number(fieldValue) > Number(value);
                        break;
                    case '<':
                        result = Number(fieldValue) < Number(value);
                        break;
                    case '>=':
                        result = Number(fieldValue) >= Number(value);
                        break;
                    case '<=':
                        result = Number(fieldValue) <= Number(value);
                        break;
                    default:
                        result = false;
                }
                
                console.log('[SVG-COMPLETE] Condition result:', result);
                return result;
            }

            // Complex condition evaluation
            const keys = Object.keys(window.svgSystem.fieldValues);
            const values = Object.values(window.svgSystem.fieldValues);

            const evalFunction = new Function(...keys, `return ${condition};`);
            const result = evalFunction(...values);
            
            console.log('[SVG-COMPLETE] Complex condition result:', condition, '=', result);
            return result;
        } catch (error) {
            console.error('[SVG-COMPLETE] Error evaluating condition:', condition, error);
            return false;
        }
    }

    // Global functions for external access
    window.refreshSvg = function() {
        console.log('[SVG-COMPLETE] Manual refresh triggered');
        renderMainSvg();
        updateDebugPanel();
    };

    window.getSvgState = function() {
        return {
            templateId: window.svgSystem.templateId,
            fieldValues: window.svgSystem.fieldValues,
            components: window.svgSystem.components,
            visibleComponents: window.svgSystem.components.filter(comp => 
                comp.component_type === 'base' || evaluateCondition(comp.condition)
            ),
            initialized: window.svgSystem.initialized
        };
    };

    // Initialize the system
    initializeCompleteSvgSystem();

    // Also try again after a delay to ensure all DOM elements are ready
    setTimeout(() => {
        if (!window.svgSystem.initialized) {
            console.log('[SVG-COMPLETE] Retrying initialization');
            initializeCompleteSvgSystem();
        }
    }, 2000);
});
