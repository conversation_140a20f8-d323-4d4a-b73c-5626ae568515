/**
 * Enhanced Configurator Solutions
 *
 * Solution 1: Enter Key Navigation (like Tab key)
 *
 * Allows users to navigate between fields using Enter key with visual feedback
 * Note: Solution 2 (Dynamic Error Messages) is implemented in the existing
 * Use Cases tab system and configurator_templates.xml
 */

class FieldNavigationManager {
    constructor() {
        this.visibleFields = [];
        this.currentFieldIndex = -1;
        this.isInitialized = false;
        
        // Debounce field scanning to improve performance
        this.scanFieldsDebounced = this.debounce(this.scanVisibleFields.bind(this), 300);
        
        this.init();
    }
    
    init() {
        if (this.isInitialized) return;
        
        console.log('Initializing Field Navigation Manager...');
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    setup() {
        this.scanVisibleFields();
        this.attachEventListeners();
        this.createKeyboardShortcutsHint();
        this.isInitialized = true;
        
        console.log(`Field Navigation Manager initialized with ${this.visibleFields.length} visible fields`);
    }
    
    scanVisibleFields() {
        // Find all config fields that are currently visible and enabled
        const allFields = document.querySelectorAll('.config-field');
        this.visibleFields = [];
        
        allFields.forEach((field, index) => {
            if (this.isFieldNavigable(field)) {
                this.visibleFields.push({
                    element: field,
                    originalIndex: index,
                    container: field.closest('.config-field-container'),
                    fieldId: field.getAttribute('data-field-id'),
                    technicalName: field.getAttribute('data-technical-name'),
                    fieldType: field.getAttribute('data-field-type')
                });
            }
        });
        
        console.log(`Scanned ${this.visibleFields.length} navigable fields`);
    }
    
    isFieldNavigable(field) {
        // Check if field is visible and enabled
        if (!field || field.disabled || field.readOnly) {
            return false;
        }
        
        // Check if field container is visible
        const container = field.closest('.config-field-container');
        if (container) {
            const containerStyle = window.getComputedStyle(container);
            if (containerStyle.display === 'none' || containerStyle.visibility === 'hidden') {
                return false;
            }
        }
        
        // Check if field itself is visible
        const fieldStyle = window.getComputedStyle(field);
        if (fieldStyle.display === 'none' || fieldStyle.visibility === 'hidden') {
            return false;
        }
        
        // Check if field is focusable
        const tabIndex = field.getAttribute('tabindex');
        if (tabIndex === '-1') {
            return false;
        }
        
        return true;
    }
    
    attachEventListeners() {
        // Global keydown listener for Enter key navigation
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && !event.ctrlKey && !event.altKey && !event.metaKey) {
                this.handleEnterKey(event);
            }
        });
        
        // Listen for field focus to update current index
        document.addEventListener('focusin', (event) => {
            if (event.target.classList.contains('config-field')) {
                this.updateCurrentFieldIndex(event.target);
            }
        });
        
        // Listen for field changes that might affect visibility
        document.addEventListener('change', (event) => {
            if (event.target.classList.contains('config-field')) {
                // Debounced rescan after field changes
                this.scanFieldsDebounced();
            }
        });
        
        // Listen for DOM mutations that might add/remove fields
        const observer = new MutationObserver((mutations) => {
            let shouldRescan = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || 
                    (mutation.type === 'attributes' && 
                     (mutation.attributeName === 'style' || 
                      mutation.attributeName === 'class' ||
                      mutation.attributeName === 'disabled'))) {
                    shouldRescan = true;
                }
            });
            
            if (shouldRescan) {
                this.scanFieldsDebounced();
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class', 'disabled']
        });
    }
    
    handleEnterKey(event) {
        const activeElement = document.activeElement;
        
        // Only handle Enter key if we're in a config field
        if (!activeElement || !activeElement.classList.contains('config-field')) {
            return;
        }
        
        // Don't interfere with textarea or contenteditable elements
        if (activeElement.tagName === 'TEXTAREA' || 
            activeElement.contentEditable === 'true') {
            return;
        }
        
        // Don't interfere with select dropdowns that are open
        if (activeElement.tagName === 'SELECT') {
            return;
        }
        
        // Prevent default Enter behavior
        event.preventDefault();
        
        // Navigate to next or previous field
        if (event.shiftKey) {
            this.navigateToPrevious();
        } else {
            this.navigateToNext();
        }
    }
    
    updateCurrentFieldIndex(field) {
        const fieldIndex = this.visibleFields.findIndex(f => f.element === field);
        if (fieldIndex !== -1) {
            this.currentFieldIndex = fieldIndex;
        }
    }
    
    navigateToNext() {
        if (this.visibleFields.length === 0) {
            this.scanVisibleFields();
            return;
        }
        
        let nextIndex = this.currentFieldIndex + 1;
        
        // Wrap around to first field if at the end
        if (nextIndex >= this.visibleFields.length) {
            nextIndex = 0;
        }
        
        this.focusField(nextIndex);
    }
    
    navigateToPrevious() {
        if (this.visibleFields.length === 0) {
            this.scanVisibleFields();
            return;
        }
        
        let prevIndex = this.currentFieldIndex - 1;
        
        // Wrap around to last field if at the beginning
        if (prevIndex < 0) {
            prevIndex = this.visibleFields.length - 1;
        }
        
        this.focusField(prevIndex);
    }
    
    focusField(index) {
        if (index < 0 || index >= this.visibleFields.length) {
            return;
        }
        
        const fieldInfo = this.visibleFields[index];
        const field = fieldInfo.element;
        
        // Remove previous highlight
        this.clearFieldHighlight();
        
        // Focus the field
        field.focus();
        
        // Add visual highlight
        this.highlightField(field);
        
        // Update current index
        this.currentFieldIndex = index;
        
        // Scroll field into view if needed
        this.scrollFieldIntoView(field);
        
        console.log(`Navigated to field: ${fieldInfo.technicalName || fieldInfo.fieldId} (${index + 1}/${this.visibleFields.length})`);
    }
    
    highlightField(field) {
        field.classList.add('field-navigation-highlight');
        
        // Remove highlight after a short delay
        setTimeout(() => {
            field.classList.remove('field-navigation-highlight');
        }, 2000);
    }
    
    clearFieldHighlight() {
        document.querySelectorAll('.field-navigation-highlight').forEach(field => {
            field.classList.remove('field-navigation-highlight');
        });
    }
    
    scrollFieldIntoView(field) {
        // Scroll the field into view with smooth behavior
        field.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
        });
    }
    
    createKeyboardShortcutsHint() {
        // Create a subtle hint about keyboard shortcuts
        const hint = document.createElement('div');
        hint.className = 'keyboard-shortcuts-hint';
        hint.innerHTML = `
            <strong>Keyboard Navigation:</strong><br>
            <kbd>Enter</kbd> Next field &nbsp;
            <kbd>Shift+Enter</kbd> Previous field
        `;
        
        document.body.appendChild(hint);
        
        // Show hint briefly when page loads
        setTimeout(() => {
            hint.classList.add('show');
            setTimeout(() => {
                hint.classList.remove('show');
            }, 4000);
        }, 2000);
        
        // Show hint when user first uses Enter key
        let hasShownHint = false;
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && !hasShownHint && 
                event.target.classList.contains('config-field')) {
                hint.classList.add('show');
                hasShownHint = true;
                setTimeout(() => {
                    hint.classList.remove('show');
                }, 3000);
            }
        });
    }
    
    // Utility function for debouncing
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Public API methods
    getVisibleFieldsCount() {
        return this.visibleFields.length;
    }
    
    getCurrentFieldInfo() {
        if (this.currentFieldIndex >= 0 && this.currentFieldIndex < this.visibleFields.length) {
            return this.visibleFields[this.currentFieldIndex];
        }
        return null;
    }
    
    refreshFields() {
        this.scanVisibleFields();
    }
}

// Initialize the Field Navigation Manager when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for other configurator scripts to initialize
    setTimeout(() => {
        window.fieldNavigationManager = new FieldNavigationManager();
    }, 1000);
});

// Also initialize if DOM is already loaded
if (document.readyState !== 'loading') {
    setTimeout(() => {
        if (!window.fieldNavigationManager) {
            window.fieldNavigationManager = new FieldNavigationManager();
        }
    }, 1000);
}

// Note: Solution 2 (Dynamic Error Messages) is implemented in the existing
// Use Cases tab system in the backend and enhanced template evaluation
// in configurator_templates.xml - no additional JavaScript needed here.
