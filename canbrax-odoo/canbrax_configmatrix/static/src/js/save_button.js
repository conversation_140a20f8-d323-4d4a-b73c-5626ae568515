/**
 * Simple Save Fix
 *
 * This script makes three simple changes:
 * 1. Removes the validation popup when saving
 * 2. Makes the floating save button always visible
 * 3. Ensures ALL fields (green and yellow) are saved without filtering
 */

// Wait for document ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('[Simple Save Fix] Script loaded');

    // Wait a bit to ensure the original scripts have run
    setTimeout(function() {
        applySimpleSaveFix();
    }, 500);

    function applySimpleSaveFix() {
        console.log('[Simple Save Fix] Applying fixes...');

        // 1. Override the validateNumberFields function to always return true
        if (window.validateNumberFields) {
            console.log('[Simple Save Fix] Overriding validateNumberFields function');
            window.validateNumberFields = function() {
                console.log('[Simple Save Fix] validateNumberFields called - always returning true');
                return true;
            };
        }

        // 2. Make the floating save button always visible
        const floatingSaveBtn = document.getElementById('floating-save-btn');
        if (floatingSaveBtn) {
            console.log('[Simple Save Fix] Making floating save button always visible');

            // Make the button always visible
            floatingSaveBtn.style.opacity = '1';
            floatingSaveBtn.style.pointerEvents = 'auto';

            // Position it in the bottom right corner
            floatingSaveBtn.style.position = 'fixed';
            floatingSaveBtn.style.bottom = '20px';
            floatingSaveBtn.style.right = '20px';
            floatingSaveBtn.style.zIndex = '1000';

            // Override any scroll event handlers
            window.addEventListener('scroll', function() {
                floatingSaveBtn.style.opacity = '1';
                floatingSaveBtn.style.pointerEvents = 'auto';
            }, true);
        }

        // 3. CRITICAL: Override any functions that might filter out fields
        // Note: Removed clearAllIssues override to allow auto-clearing of hidden fields
        // The auto-clear functionality needs these functions to work properly

        // 4. Override form submission to ensure all fields are included and generate config data
        const configForm = document.getElementById('config-form');
        if (configForm) {
            console.log('[Simple Save Fix] Overriding form submission handler');

            configForm.addEventListener('submit', function(event) {
                console.log('[Simple Save Fix] Form submission - ensuring all fields are included');

                // Make sure all hidden fields are included
                document.querySelectorAll('.conditional-field-pending').forEach(function(row) {
                    // Make sure the row is visible for form submission
                    row.style.display = '';

                    // Make sure any inputs in this row are enabled
                    row.querySelectorAll('input, select, textarea').forEach(function(input) {
                        input.disabled = false;
                    });
                });

                // Generate config data using the globally accessible function
                if (window.generateConfigData && typeof window.generateConfigData === 'function') {
                    try {
                        const configData = window.generateConfigData();
                        const configDataField = document.getElementById('config-data-field');
                        if (configDataField) {
                            configDataField.value = configData;
                            console.log('[Simple Save Fix] Config data generated and set');
                        } else {
                            console.warn('[Simple Save Fix] Config data field not found');
                        }
                    } catch (error) {
                        console.error('[Simple Save Fix] Error generating config data:', error);
                    }
                } else {
                    console.warn('[Simple Save Fix] generateConfigData function not available');
                }

                // Let the form submit normally
                return true;
            }, true);
        }

        console.log('[Simple Save Fix] Fixes applied successfully');
    }
});
