/**
 * SVG Renderer Fix
 *
 * This script fixes issues with SVG conditional layers not loading when related questions are answered.
 * It enhances the existing SVG rendering functionality by:
 * 1. Improving field value tracking
 * 2. Ensuring SVG components are properly updated when field values change
 * 3. Adding better error handling and debugging
 */

document.addEventListener('DOMContentLoaded', function() {
    // Global variables
    let svgComponents = [];
    let fieldValues = {};
    let templateId = null;
    let svgContainer = null;

    // Field name mapping - maps condition field names to technical names
    const fieldNameMapping = {
        // Add mappings here as needed
        'sand_slide_top_track_type': 'top_track_type',
        'sand_slide_frame_colour': 'frame_colour',
        'location': 'location'
    };

    // Initialize the fix
    function initialize() {
        // Get the SVG container
        svgContainer = document.getElementById('svg-container');
        if (!svgContainer) {
            console.error('[SVG-FIX] SVG container not found');
            return;
        }

        // Get the template ID
        const templateIdInput = document.querySelector('input[name="template_id"]');
        if (!templateIdInput) {
            console.error('[SVG-FIX] Template ID input not found');
            return;
        }
        templateId = parseInt(templateIdInput.value) || 0;

        // Load SVG components
        loadSvgComponents();

        // Add event listeners to all configuration fields
        addFieldEventListeners();
    }

    // Load SVG components from the server
    function loadSvgComponents() {
        // Show loading indicator
        if (svgContainer) {
            svgContainer.innerHTML = '<div class="text-center p-3"><i class="fas fa-spinner fa-spin"></i> Loading visualization...</div>';
        }

        // Fetch SVG components
        fetch('/config_matrix/get_svg_components', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                jsonrpc: "2.0",
                method: "call",
                params: {
                    template_id: templateId
                },
                id: new Date().getTime()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.result && data.result.success && data.result.components) {
                svgComponents = data.result.components;

                // Collect current field values
                collectFieldValues();

                // Render SVG with current field values
                renderSvg();
            } else {
                console.error('[SVG-FIX] Error loading SVG components:', data);
                if (svgContainer) {
                    svgContainer.innerHTML = '<div class="alert alert-danger">Error loading SVG components</div>';
                }
            }
        })
        .catch(error => {
            console.error('[SVG-FIX] Error fetching SVG components:', error);
            if (svgContainer) {
                svgContainer.innerHTML = '<div class="alert alert-danger">Error loading SVG components</div>';
            }
        });
    }

    // Add event listeners to all configuration fields
    function addFieldEventListeners() {
        // Find all configuration fields
        const configFields = document.querySelectorAll('.config-field');

        // Add event listeners to each field
        configFields.forEach(field => {
            // Get field information
            const fieldId = field.getAttribute('data-field-id');
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');

            if (!fieldId || !technicalName) {
                console.warn('[SVG-FIX] Field missing ID or technical name:', field);
                return;
            }

            // Add change event listener
            field.addEventListener('change', function() {
                // Get the field value
                let value = getFieldValue(field, fieldType);

                // Update the field value in the global object
                fieldValues[technicalName] = value;

                // Update the SVG visualization
                renderSvg();
            });
        });
    }

    // Get the value of a field based on its type
    function getFieldValue(field, fieldType) {
        switch (fieldType) {
            case 'boolean':
                return field.checked;
            case 'number':
                return parseFloat(field.value) || 0;
            case 'selection':
                // Check if this is an unselected option
                if (!field.value || field.value === '-- Select an option --' || field.selectedIndex === 0) {
                    return '';
                }
                return field.value;
            default:
                return field.value;
        }
    }

    // Collect current values of all fields
    function collectFieldValues() {
        // Clear existing values
        fieldValues = {};

        // Find all configuration fields
        const configFields = document.querySelectorAll('.config-field');

        // Collect values from each field
        configFields.forEach(field => {
            // Get field information
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');
            const fieldId = field.getAttribute('data-field-id');

            if (!technicalName) {
                return;
            }

            // Get the field value
            const value = getFieldValue(field, fieldType);

            // Store the value - ensure empty selections are stored as empty strings
            if (fieldType === 'selection' && (value === null || value === undefined || value === '-- Select an option --')) {
                fieldValues[technicalName] = '';
            } else {
                fieldValues[technicalName] = value;
            }
        });

        // Add mapped field values for SVG conditions
        for (const [conditionName, technicalName] of Object.entries(fieldNameMapping)) {
            if (fieldValues[technicalName] !== undefined) {
                fieldValues[conditionName] = fieldValues[technicalName];
            }
        }

        // Add special values for common condition values
        fieldValues['no'] = 'no';
        fieldValues['yes'] = 'yes';
        fieldValues['bbq'] = 'bbq';
        fieldValues['black_custom_matt_gn248a'] = 'black_custom_matt_gn248a';

        // Make field values available globally
        window.fieldValues = fieldValues;
    }

    // Render the SVG with current field values
    function renderSvg() {
        if (!svgContainer) {
            console.error('[SVG-FIX] SVG container not found');
            return;
        }

        if (svgComponents.length === 0) {
            console.warn('[SVG-FIX] No SVG components available');
            svgContainer.innerHTML = '<div class="alert alert-warning">No SVG components available</div>';
            return;
        }

        // Find base component
        const baseComponent = svgComponents.find(comp => comp.component_type === 'base');
        if (!baseComponent) {
            console.error('[SVG-FIX] No base SVG component found');
            svgContainer.innerHTML = '<div class="alert alert-warning">No base SVG component found</div>';
            return;
        }

        // Start with base SVG content
        let svgContent = processTemplate(baseComponent.svg_content);

        // Add other layers based on conditions
        let addedLayers = 0;
        svgComponents.forEach(component => {
            if (component.component_type !== 'layer') {
                return;
            }

            // Evaluate condition if present
            if (component.condition) {
                const conditionResult = evaluateCondition(component.condition);
                if (!conditionResult) {
                    return;
                }
            }

            // Process template with current config values
            let layerSvg = processTemplate(component.svg_content);

            // Check if the layer content is a complete SVG or just a fragment
            if (layerSvg.trim().startsWith('<svg')) {
                // Extract the content inside the SVG tags
                const svgMatch = layerSvg.match(/<svg[^>]*>([\s\S]*)<\/svg>/i);
                if (svgMatch && svgMatch[1]) {
                    layerSvg = svgMatch[1];
                }
            }

            // Add to SVG content - before the closing </svg> tag
            svgContent = svgContent.replace('</svg>', layerSvg + '</svg>');
            addedLayers++;
        });

        // Update the SVG display - use a safe method to insert SVG
        try {
            // Create a temporary div to parse the SVG
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = svgContent;

            // Clear the container
            svgContainer.innerHTML = '';

            // Append the SVG element
            if (tempDiv.firstChild) {
                svgContainer.appendChild(tempDiv.firstChild);
            } else {
                console.error('[SVG-FIX] Failed to parse SVG content');
                svgContainer.innerHTML = '<div class="alert alert-warning">Failed to parse SVG content</div>';
            }
        } catch (e) {
            console.error('[SVG-FIX] Error rendering SVG:', e);
            svgContainer.innerHTML = '<div class="alert alert-warning">Error rendering SVG</div>';
        }
    }

    // Process template with field values
    function processTemplate(template) {
        if (!template) {
            console.warn('[SVG-FIX] Empty SVG template');
            return '';
        }

        // Replace ${fieldName} with actual values
        return template.replace(/\${(\w+)}/g, function(match, fieldName) {
            const value = fieldValues[fieldName];
            return value !== undefined ? value : match;
        });
    }

    // Evaluate a condition with field values
    function evaluateCondition(condition) {
        if (!condition || condition === 'true') {
            return true;
        }

        try {
            // Handle equality conditions (e.g., "field == value")
            const equalityMatch = condition.match(/^\s*(\w+)\s*(==|===|!=|!==|>=|<=|>|<)\s*['"]?([^'"]*?)['"]?\s*$/);
            if (equalityMatch) {
                const [_, fieldName, operator, value] = equalityMatch;

                // Check if we need to use a mapped field name
                let actualFieldName = fieldName;
                for (const [conditionName, technicalName] of Object.entries(fieldNameMapping)) {
                    if (fieldName === conditionName && fieldValues[technicalName] !== undefined) {
                        actualFieldName = technicalName;
                        break;
                    }
                }

                const fieldValue = fieldValues[fieldName];

                // Special case for option values - check if the field value equals the option value
                // For example, if condition is "top_track_type == no", we need to check if the field value is "no"

                // If the field is empty/undefined/null and we're checking for equality with a value,
                // the condition should be false (component hidden)
                if ((fieldValue === undefined || fieldValue === null || fieldValue === '') &&
                    (operator === '==' || operator === '===')) {
                    return false;
                }

                // If the field is empty/undefined/null and we're checking for inequality with a value,
                // the condition should be true (component visible)
                if ((fieldValue === undefined || fieldValue === null || fieldValue === '') &&
                    (operator === '!=' || operator === '!==')) {
                    return true;
                }

                switch (operator) {
                    case '==':
                    case '===':
                        return String(fieldValue) === String(value);
                    case '!=':
                    case '!==':
                        return String(fieldValue) !== String(value);
                    case '>':
                        return Number(fieldValue) > Number(value);
                    case '<':
                        return Number(fieldValue) < Number(value);
                    case '>=':
                        return Number(fieldValue) >= Number(value);
                    case '<=':
                        return Number(fieldValue) <= Number(value);
                }
            }

            // Extract field names from the condition
            const fieldNames = condition.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || [];

            // Create a context with field values
            const context = { ...fieldValues };

            // Add missing fields with default values
            fieldNames.forEach(fieldName => {
                if (context[fieldName] === undefined) {
                    context[fieldName] = '';
                }
            });

            // Create a function with the field values in scope
            const func = new Function(...Object.keys(context), `return ${condition};`);

            // Call the function with the field values
            return func(...Object.values(context));
        } catch (e) {
            console.error('[SVG-FIX] Error evaluating condition:', e, 'with values:', fieldValues);
            return false;
        }
    }

    // Initialize after a short delay to ensure DOM is ready
    setTimeout(initialize, 1000);

    // Make functions available globally
    window.svgRendererFix = {
        initialize,
        loadSvgComponents,
        renderSvg,
        evaluateCondition,
        fieldValues
    };
});
