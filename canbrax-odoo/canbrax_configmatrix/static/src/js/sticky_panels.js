/**
 * Sticky Columns Fix
 * 
 * This script ensures that the left and right columns stick properly
 * by forcing layout recalculation and handling edge cases.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Wait for all content to load
    setTimeout(function() {
        initializeStickyColumns();
    }, 500);
    
    // Also initialize after window load
    window.addEventListener('load', function() {
        setTimeout(initializeStickyColumns, 100);
    });
});

function initializeStickyColumns() {
    const leftColumn = document.querySelector('.sticky-column.left-column');
    const rightColumn = document.querySelector('.sticky-column.right-column');
    const mainRow = document.querySelector('.row.d-flex');
    
    if (!leftColumn || !rightColumn || !mainRow) {
        console.error('[STICKY-FIX] Required elements not found');
        return;
    }
    

    // Force layout recalculation
    function forceReflow() {
        leftColumn.style.display = 'none';
        rightColumn.style.display = 'none';
        leftColumn.offsetHeight; // Force reflow
        rightColumn.offsetHeight; // Force reflow
        leftColumn.style.display = '';
        rightColumn.style.display = '';
    }
    
    // Apply sticky positioning with fallback
    function applyStickyPositioning() {
        // Ensure parent containers support sticky
        const containers = [
            mainRow,
            mainRow.parentElement,
            mainRow.parentElement?.parentElement
        ].filter(Boolean);
        
        containers.forEach(container => {
            if (container) {
                container.style.overflow = 'visible';
                container.style.position = 'relative';
            }
        });
        
        // Apply sticky styles
        [leftColumn, rightColumn].forEach(column => {
            if (column) {
                column.style.position = 'sticky';
                column.style.top = '10px';
                column.style.zIndex = '1000';
                column.style.height = 'fit-content';
                column.style.maxHeight = 'calc(100vh - 20px)';
                column.style.overflowY = 'auto';
                
            }
        });
        
        // Force reflow
        forceReflow();
    }
    
    // Test if sticky is working
    function testStickySupport() {
        const testElement = document.createElement('div');
        testElement.style.position = 'sticky';
        const isSupported = testElement.style.position === 'sticky';
        return isSupported;
    }
    
    // Apply the fix
    if (testStickySupport()) {
        applyStickyPositioning();
        
        // Monitor scroll to verify sticky is working
        let scrollTimeout;
        window.addEventListener('scroll', function() {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(function() {
                const leftRect = leftColumn.getBoundingClientRect();
                const rightRect = rightColumn.getBoundingClientRect();
                
                // Scroll position monitoring for debugging if needed
            }, 100);
        });
        
    } else {
        console.warn('[STICKY-FIX] Sticky position not supported, using fallback');
        // Could implement a JavaScript-based sticky fallback here
    }
    
    // Add visual indicator for debugging (temporary)
    if (window.location.search.includes('debug=sticky')) {
        [leftColumn, rightColumn].forEach(column => {
            const indicator = document.createElement('div');
            indicator.textContent = '📌 STICKY ACTIVE';
            indicator.style.cssText = `
                position: absolute;
                top: -20px;
                left: 5px;
                background: #007bff;
                color: white;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 10px;
                z-index: 9999;
            `;
            column.style.position = 'relative';
            column.appendChild(indicator);
        });
    }
}

// Export for debugging
window.stickyFix = {
    initialize: initializeStickyColumns
};
