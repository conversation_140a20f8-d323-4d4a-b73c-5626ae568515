/**
 * Hidden Fields Tracking and Clearing System - FIXED VERSION
 *
 * This file implements a working system to clear hidden fields with values.
 * Based on your debugging, we know:
 * - Hidden fields have class 'conditional-field-pending'
 * - Values are stored in window.fieldValues[technicalName]
 * - We need to clear the UI element AND the stored value
 */

/**
 * Find hidden fields with values - ENHANCED VERSION
 * Works regardless of "show hidden fields" toggle state
 */
function findHiddenFieldsWithValuesImpl() {
    const hiddenFields = [];

    // Find all field containers with visibility conditions
    document.querySelectorAll('.config-field-container[data-visibility-condition]').forEach(container => {
        const technicalName = container.getAttribute('data-technical-name');
        const fieldId = container.getAttribute('data-field-id');
        const condition = container.getAttribute('data-visibility-condition');

        if (!technicalName || !condition || condition === 'true') return;

        // Evaluate the visibility condition to determine if field should be visible
        let isVisible = false;
        try {
            if (typeof window.evaluateCondition === 'function') {
                isVisible = window.evaluateCondition(condition);
            } else {
                // Fallback evaluation
                isVisible = true; // Default to visible if we can't evaluate
            }
        } catch (error) {
            console.warn(`Error evaluating condition for ${technicalName}:`, error);
            isVisible = true; // Default to visible on error
        }

        // If field is hidden (condition not met), check if it has a value
        if (!isVisible) {
            // Get value from window.fieldValues (primary source)
            let fieldValue = '';
            if (window.fieldValues && window.fieldValues[technicalName] !== undefined) {
                fieldValue = window.fieldValues[technicalName];
            }

            // Check if field has a meaningful value
            const hasValue = fieldValue !== undefined && fieldValue !== null && fieldValue !== '' && fieldValue !== 0;

            if (hasValue) {
                // Find the actual input element
                const fieldElement = container.querySelector('.config-field') ||
                                   document.querySelector(`#field_${fieldId}`) ||
                                   document.querySelector(`.config-field[data-field-id="${fieldId}"]`);

                hiddenFields.push({
                    id: fieldId,
                    name: technicalName,
                    value: fieldValue,
                    element: fieldElement,
                    container: container,
                    hiddenReason: 'condition_not_met',
                    condition: condition
                });
            }
        }
    });

    return hiddenFields;
}

// Make the findHiddenFieldsWithValues function globally available
window.findHiddenFieldsWithValues = function() {
    return findHiddenFieldsWithValuesImpl();
};

// WORKING clear function that actually clears the values
window.clearAllIssues = function() {
    try {
        const hiddenFields = window.findHiddenFieldsWithValues();
        let clearedCount = 0;

        if (hiddenFields.length > 0) {
            console.log(`Clearing ${hiddenFields.length} hidden fields with values...`);

            hiddenFields.forEach(field => {
                console.log(`Clearing: ${field.name} = ${field.value}`);
                
                // Method 1: Clear from window.fieldValues (MOST IMPORTANT)
                if (window.fieldValues && window.fieldValues[field.name] !== undefined) {
                    delete window.fieldValues[field.name];
                }
                if (window.fieldValues && field.id && window.fieldValues[field.id] !== undefined) {
                    delete window.fieldValues[field.id];
                }
                
                // Method 2: Clear the UI element
                if (field.element) {
                    const fieldType = field.element.getAttribute('data-field-type');
                    if (fieldType === 'boolean') {
                        field.element.checked = false;
                    } else {
                        field.element.value = '';
                    }
                    
                    // Trigger change event to update any dependencies
                    field.element.dispatchEvent(new Event('change', { bubbles: true }));
                }
                
                // Method 3: Clear hidden form field
                if (field.id) {
                    const hiddenField = document.getElementById('hidden_field_' + field.id);
                    if (hiddenField) {
                        hiddenField.value = '';
                    }
                }
                
                clearedCount++;
            });

            console.log(`Successfully cleared ${clearedCount} fields`);
            
            // Update visibility after clearing
            if (typeof window.updateFieldVisibility === 'function') {
                setTimeout(() => {
                    window.updateFieldVisibility();
                }, 100);
            }
        } else {
            console.log('No hidden fields with values found');
        }
        
        return clearedCount;
    } catch (error) {
        console.error('Error clearing fields:', error);
        return 0;
    }
};

// Simplified setFieldValue
window.setFieldValue = function(fieldName, value) {
    // Update window.fieldValues
    if (window.fieldValues) {
        window.fieldValues[fieldName] = value;
    }
    
    // Find and update UI element
    const fieldElement = document.querySelector(`[data-technical-name="${fieldName}"]`)?.querySelector('.config-field') ||
                        document.querySelector(`.config-field[data-technical-name="${fieldName}"]`);
    
    if (fieldElement) {
        const fieldType = fieldElement.getAttribute('data-field-type');
        if (fieldType === 'boolean') {
            fieldElement.checked = value === true || value === 'true' || value === 'on';
        } else {
            fieldElement.value = value;
        }
        
        // Trigger change event
        fieldElement.dispatchEvent(new Event('change', { bubbles: true }));
    }
};

// Enhanced updateFieldVisibility that works with the main system
window.updateFieldVisibility = function() {
    // Call the main updateFieldVisibility function if it exists
    if (typeof updateFieldVisibility === 'function') {
        updateFieldVisibility();
    } else {
        console.log('Main updateFieldVisibility function not found');
    }
};

// Function to manually trigger dependency chain clearing
window.clearDependencyChain = function() {
    console.log('Manually triggering dependency chain clearing...');

    // Find all hidden fields with values
    const hiddenFields = window.findHiddenFieldsWithValues();

    if (hiddenFields.length > 0) {
        console.log(`Found ${hiddenFields.length} hidden fields with values to clear`);

        // Clear each hidden field
        hiddenFields.forEach(field => {
            console.log(`Clearing hidden field: ${field.name} = ${field.value}`);

            // Clear from window.fieldValues
            if (window.fieldValues && window.fieldValues[field.name] !== undefined) {
                delete window.fieldValues[field.name];
            }

            // Clear UI element
            if (field.element) {
                const fieldType = field.element.getAttribute('data-field-type');
                if (fieldType === 'boolean') {
                    field.element.checked = false;
                } else {
                    field.element.value = '';
                }

                // Trigger change event
                field.element.dispatchEvent(new Event('change', { bubbles: true }));
            }

            // Clear hidden form field
            if (field.id) {
                const hiddenField = document.getElementById('hidden_field_' + field.id);
                if (hiddenField) {
                    hiddenField.value = '';
                }
            }
        });

        // Trigger visibility update to handle cascading effects
        setTimeout(() => {
            if (typeof window.updateFieldVisibility === 'function') {
                window.updateFieldVisibility();
            }
        }, 100);

        return hiddenFields.length;
    } else {
        console.log('No hidden fields with values found');
        return 0;
    }
};

document.addEventListener('DOMContentLoaded', function() {
    // Get panel elements
    const showIssuesButton = document.getElementById('show-issues-panel');
    const hideIssuesButton = document.getElementById('hide-issues-panel');
    const refreshIssuesButton = document.getElementById('issues-refresh');
    const clearAllIssuesButton = document.getElementById('clear-all-issues');
    const issuesPanel = document.getElementById('issues-panel');
    const issuesTableBody = document.getElementById('issues-table-body');

    // Add event listeners
    if (showIssuesButton && issuesPanel) {
        showIssuesButton.addEventListener('click', function() {
            issuesPanel.style.display = 'block';
            scanForIssues();
        });
    }

    if (hideIssuesButton && issuesPanel) {
        hideIssuesButton.addEventListener('click', function() {
            issuesPanel.style.display = 'none';
        });
    }

    if (refreshIssuesButton) {
        refreshIssuesButton.addEventListener('click', scanForIssues);
    }

    if (clearAllIssuesButton) {
        clearAllIssuesButton.addEventListener('click', function() {
            const clearedCount = clearAllIssues();
            showPopupMessage(`Cleared ${clearedCount} hidden fields with values`, 'success');
            setTimeout(scanForIssues, 200);
        });
    }

    // Add event listener for the new dependency chain clearing button
    const clearDependencyChainButton = document.getElementById('clear-dependency-chain');
    if (clearDependencyChainButton) {
        clearDependencyChainButton.addEventListener('click', function() {
            const clearedCount = window.clearDependencyChain();
            showPopupMessage(`Dependency chain cleared: ${clearedCount} fields`, 'info');
            setTimeout(scanForIssues, 200);
        });
    }

    /**
     * Create and show a popup message
     */
    function showPopupMessage(message, type = 'success') {
        const popup = document.createElement('div');
        popup.className = `alert alert-${type} position-fixed`;
        popup.style.cssText = `
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 9999;
            padding: 15px 25px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            border-radius: 5px;
            max-width: 80%;
        `;

        let icon = 'check-circle';
        if (type === 'warning') icon = 'exclamation-triangle';
        if (type === 'danger') icon = 'times-circle';

        popup.innerHTML = `<i class="fas fa-${icon} me-2"></i> ${message}`;
        document.body.appendChild(popup);

        setTimeout(() => {
            popup.style.opacity = '0';
            popup.style.transition = 'opacity 0.5s';
            setTimeout(() => {
                if (document.body.contains(popup)) {
                    document.body.removeChild(popup);
                }
            }, 500);
        }, 3000);
    }

    /**
     * Scan for issues using the working detection
     */
    function scanForIssues() {
        if (!issuesTableBody) return;

        issuesTableBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center">
                    <i class="fas fa-spinner fa-spin"></i> Scanning for issues...
                </td>
            </tr>
        `;

        setTimeout(() => {
            try {
                const hiddenFields = window.findHiddenFieldsWithValues();

                if (hiddenFields.length > 0) {
                    let tableHtml = '';
                    hiddenFields.forEach(field => {
                        const reasonBadge = field.hiddenReason === 'condition_not_met' ?
                            '<span class="badge bg-warning">Condition Not Met</span>' :
                            '<span class="badge bg-secondary">Hidden</span>';

                        tableHtml += `
                            <tr>
                                <td>${field.id || 'N/A'}</td>
                                <td>${field.name || 'N/A'}</td>
                                <td><strong>${field.value}</strong><br><small>${reasonBadge}</small></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-danger clear-field-btn"
                                            data-field-name="${field.name}">
                                        Clear
                                    </button>
                                </td>
                            </tr>
                        `;
                    });
                    issuesTableBody.innerHTML = tableHtml;

                    document.querySelectorAll('.clear-field-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const fieldName = this.getAttribute('data-field-name');
                            clearField(fieldName);
                            showPopupMessage(`Field "${fieldName}" cleared successfully!`, 'success');
                            setTimeout(scanForIssues, 100);
                        });
                    });
                } else {
                    issuesTableBody.innerHTML = `
                        <tr>
                            <td colspan="4" class="text-center text-success">
                                <i class="fas fa-check-circle"></i> No issues found!
                            </td>
                        </tr>
                    `;
                }
            } catch (error) {
                console.error('Error scanning for issues:', error);
                issuesTableBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle"></i> Error scanning for issues: ${error.message}
                        </td>
                    </tr>
                `;
            }
        }, 100);
    }

    /**
     * Clear a specific field
     */
    function clearField(fieldName, showMessage = false) {
        // Clear from window.fieldValues
        if (window.fieldValues && window.fieldValues[fieldName] !== undefined) {
            delete window.fieldValues[fieldName];
        }
        
        // Find and clear UI element
        const container = document.querySelector(`[data-technical-name="${fieldName}"]`);
        if (container) {
            const fieldElement = container.querySelector('.config-field');
            const fieldId = container.getAttribute('data-field-id');
            
            if (fieldElement) {
                const fieldType = fieldElement.getAttribute('data-field-type');
                if (fieldType === 'boolean') {
                    fieldElement.checked = false;
                } else {
                    fieldElement.value = '';
                }
                fieldElement.dispatchEvent(new Event('change', { bubbles: true }));
            }
            
            // Clear hidden form field
            if (fieldId) {
                const hiddenField = document.getElementById('hidden_field_' + fieldId);
                if (hiddenField) {
                    hiddenField.value = '';
                }
            }
        }

        if (showMessage) {
            showPopupMessage(`Field "${fieldName}" cleared successfully!`, 'success');
        }
    }
});
