/**
 * Product Configurator JavaScript
 *
 * This file contains the functionality for the product configurator,
 * including the floating save button and SVG panel.
 */

odoo.define('canbrax_configmatrix.floating_panels', function (require) {
    'use strict';

    // Wait for document ready
    $(document).ready(function() {
        console.log('[DEBUG] Product Configurator JS loaded');

        // Set up floating save button
        setupFloatingSaveButton();

        // Set up SVG panel button
        setupSvgPanelButton();
    });

    /**
     * Set up floating save button
     */
    function setupFloatingSaveButton() {
        console.log('[DEBUG] Setting up floating save button');

        const floatingSaveButton = document.getElementById('floating-save-btn');
        const saveConfigButton = document.getElementById('save-config-btn');

        if (floatingSaveButton && saveConfigButton) {
            // We're not adding a click handler here because it's already handled in the template
            console.log('[DEBUG] Floating save button found, visibility will be managed by scroll events');

            // Show/hide floating button based on scroll position
            function updateFloatingButtonVisibility() {
                const saveButtonRect = saveConfigButton.getBoundingClientRect();
                const isVisible = (
                    saveButtonRect.top >= 0 && saveButtonRect.bottom <= window.innerHeight
                );

                // Only show floating button when the main button is not visible
                floatingSaveButton.style.opacity = isVisible ? '0' : '1';
                floatingSaveButton.style.pointerEvents = isVisible ? 'none' : 'auto';
            }

            // Add scroll event listener
            window.addEventListener('scroll', updateFloatingButtonVisibility);

            // Trigger scroll event once to set initial state
            window.dispatchEvent(new Event('scroll'));
        }
    }

    /**
     * Set up SVG panel button
     */
    function setupSvgPanelButton() {
        console.log('[DEBUG] Setting up SVG panel button');

        const showSvgButton = document.getElementById('show-svg-panel');
        const hideSvgButton = document.getElementById('hide-svg-panel');
        const svgPanel = document.getElementById('svg-panel');

        if (showSvgButton && svgPanel) {
            showSvgButton.addEventListener('click', function() {
                console.log('[DEBUG] Show SVG button clicked');

                // Fetch SVG components data
                fetchSvgComponents();

                // Show the panel
                svgPanel.style.display = 'block';
            });
        }

        if (hideSvgButton && svgPanel) {
            hideSvgButton.addEventListener('click', function() {
                console.log('[DEBUG] Hide SVG button clicked');
                svgPanel.style.display = 'none';
            });
        }
    }

    /**
     * Fetch SVG components data
     */
    function fetchSvgComponents() {
        const templateId = document.querySelector('input[name="template_id"]').value;

        fetch('/config_matrix/get_svg_components', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                jsonrpc: "2.0",
                method: "call",
                params: {
                    template_id: templateId
                },
                id: new Date().getTime()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.result && data.result.success && data.result.components) {
                displaySvgComponents(data.result.components);
            }
        })
        .catch(error => {
            console.error('Error fetching SVG components:', error);
        });
    }

    /**
     * Display SVG components in the panel
     */
    function displaySvgComponents(components) {
        const tableBody = document.getElementById('svg-components-table-body');
        if (!tableBody) return;

        // Clear existing rows
        tableBody.innerHTML = '';

        // Sort components by z-index
        components.sort((a, b) => (a.z_index || 0) - (b.z_index || 0));

        // Add rows for each component
        components.forEach(component => {
            const row = document.createElement('tr');

            // Name column
            const nameCell = document.createElement('td');
            nameCell.textContent = component.name || '';
            row.appendChild(nameCell);

            // Type column
            const typeCell = document.createElement('td');
            typeCell.textContent = component.component_type || '';
            row.appendChild(typeCell);

            // Z-Index column
            const zIndexCell = document.createElement('td');
            zIndexCell.textContent = component.z_index || '';
            row.appendChild(zIndexCell);

            // Condition column
            const conditionCell = document.createElement('td');
            conditionCell.textContent = component.visibility_condition || 'Always';
            row.appendChild(conditionCell);

            // Visible column
            const visibleCell = document.createElement('td');
            const visibleIcon = document.createElement('i');
            visibleIcon.className = 'fas fa-' + (component.is_visible ? 'check text-success' : 'times text-danger');
            visibleCell.appendChild(visibleIcon);
            row.appendChild(visibleCell);

            // Add click event to show SVG preview
            row.addEventListener('click', function() {
                showSvgPreview(component);
            });

            // Add row to table
            tableBody.appendChild(row);
        });
    }

    /**
     * Show SVG preview for a component
     */
    function showSvgPreview(component) {
        const previewContainer = document.getElementById('svg-preview-container');
        if (!previewContainer) return;

        if (component.svg_content) {
            previewContainer.innerHTML = component.svg_content;
        } else {
            previewContainer.innerHTML = '<div class="text-center p-3"><i class="fas fa-image fa-3x mb-2"></i><br/>No SVG content available</div>';
        }
    }

    return {
        setupFloatingSaveButton: setupFloatingSaveButton,
        setupSvgPanelButton: setupSvgPanelButton
    };
});
