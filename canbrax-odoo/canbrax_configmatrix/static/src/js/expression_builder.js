/** @odoo-module **/

import { registry } from "@web/core/registry";
import { Component, useState, onWillStart, onMounted } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

/**
 * Enhanced Expression Builder for Option Visibility Conditions
 *
 * This provides a user-friendly interface for building complex expressions
 * while generating the technical field names behind the scenes.
 */
export class ExpressionBuilder {
    constructor() {
        this.fields = [];
        this.conditions = [];
        this.currentMatrixId = null;
    }

    /**
     * Initialize the expression builder
     */
    async init(matrixId, rpc) {
        this.currentMatrixId = matrixId;
        this.rpc = rpc;

        if (matrixId) {
            await this.loadFields(matrixId);
        }

        this.setupEventListeners();
        this.populateFieldDropdown();
    }

    /**
     * Load fields for the current matrix
     */
    async loadFields(matrixId) {
        try {
            this.fields = await this.rpc('/web/dataset/call_kw', {
                model: 'config.matrix.field',
                method: 'search_read',
                args: [[['matrix_id', '=', matrixId]]],
                kwargs: {
                    fields: ['id', 'name', 'technical_name', 'field_type', 'selection_options']
                }
            });
        } catch (error) {
            console.error('Error loading fields:', error);
            this.fields = [];
        }
    }

    /**
     * Populate the field dropdown with user-friendly names
     */
    populateFieldDropdown() {
        const fieldSelect = document.getElementById('builder-field');
        if (!fieldSelect) return;

        // Clear existing options except the first one
        fieldSelect.innerHTML = '<option value="">-- Select Field --</option>';

        this.fields.forEach(field => {
            const option = document.createElement('option');
            option.value = field.technical_name;
            option.textContent = field.name;
            option.setAttribute('data-field-type', field.field_type);
            option.setAttribute('data-field-id', field.id);
            fieldSelect.appendChild(option);
        });
    }

    /**
     * Setup event listeners for the builder interface
     */
    setupEventListeners() {
        // Add condition button
        const addBtn = document.getElementById('add-condition-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.addCondition());
        }

        // Clear expression button
        const clearBtn = document.getElementById('clear-expression-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearExpression());
        }

        // Field selection change
        const fieldSelect = document.getElementById('builder-field');
        if (fieldSelect) {
            fieldSelect.addEventListener('change', () => this.onFieldChange());
        }

        // Value input enter key
        const valueInput = document.getElementById('builder-value');
        if (valueInput) {
            valueInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.addCondition();
                }
            });
        }

        // Edit last condition button
        const editLastBtn = document.getElementById('edit-last-condition-btn');
        if (editLastBtn) {
            editLastBtn.addEventListener('click', () => this.editLastCondition());
        }

        // Remove last condition button
        const removeLastBtn = document.getElementById('remove-last-condition-btn');
        if (removeLastBtn) {
            removeLastBtn.addEventListener('click', () => this.removeLastCondition());
        }

        // Parse existing expression button
        const parseBtn = document.getElementById('parse-existing-btn');
        if (parseBtn) {
            parseBtn.addEventListener('click', () => this.parseExistingExpression());
        }
    }

    /**
     * Handle field selection change
     */
    onFieldChange() {
        const fieldSelect = document.getElementById('builder-field');
        const valueInput = document.getElementById('builder-value');

        if (!fieldSelect || !valueInput) return;

        const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
        const fieldType = selectedOption.getAttribute('data-field-type');

        // Update value input placeholder based on field type
        switch (fieldType) {
            case 'boolean':
                valueInput.placeholder = 'true or false';
                break;
            case 'number':
                valueInput.placeholder = 'Enter number (e.g., 1000)';
                break;
            case 'selection':
                valueInput.placeholder = 'Enter option value';
                break;
            case 'date':
                valueInput.placeholder = 'YYYY-MM-DD';
                break;
            default:
                valueInput.placeholder = 'Enter value';
        }
    }

    /**
     * Add a new condition to the expression
     */
    addCondition() {
        const fieldSelect = document.getElementById('builder-field');
        const operatorSelect = document.getElementById('builder-operator');
        const valueInput = document.getElementById('builder-value');
        const logicSelect = document.getElementById('builder-logic');

        if (!fieldSelect || !operatorSelect || !valueInput || !logicSelect) return;

        const fieldTechnicalName = fieldSelect.value;
        const fieldDisplayName = fieldSelect.options[fieldSelect.selectedIndex].text;
        const operator = operatorSelect.value;
        const operatorDisplay = operatorSelect.options[operatorSelect.selectedIndex].text;
        const value = valueInput.value.trim();
        const logic = logicSelect.value;

        // Validation
        if (!fieldTechnicalName) {
            alert('Please select a field');
            return;
        }

        if (!value && !['is_set', 'is_not_set'].includes(operator)) {
            alert('Please enter a value');
            return;
        }

        // Get field info
        const field = this.fields.find(f => f.technical_name === fieldTechnicalName);
        if (!field) {
            alert('Field not found');
            return;
        }

        // Create condition object
        const condition = {
            fieldTechnicalName: fieldTechnicalName,
            fieldDisplayName: fieldDisplayName,
            operator: operator,
            operatorDisplay: operatorDisplay,
            value: value,
            logic: this.conditions.length > 0 ? logic : null, // First condition has no logic
            fieldType: field.field_type
        };

        // Add to conditions array
        this.conditions.push(condition);

        // Update displays
        this.updateHumanReadableDisplay();
        this.updateTechnicalExpression();
        this.updateConditionButtons();

        // Clear inputs
        fieldSelect.value = '';
        valueInput.value = '';
        valueInput.placeholder = 'Enter value';
    }

    /**
     * Clear all conditions
     */
    clearExpression() {
        this.conditions = [];
        this.updateHumanReadableDisplay();
        this.updateTechnicalExpression();
        this.updateConditionButtons();
    }

    /**
     * Edit the last condition
     */
    editLastCondition() {
        if (this.conditions.length === 0) return;

        const lastCondition = this.conditions[this.conditions.length - 1];

        // Populate the form with the last condition
        const fieldSelect = document.getElementById('builder-field');
        const operatorSelect = document.getElementById('builder-operator');
        const valueInput = document.getElementById('builder-value');
        const logicSelect = document.getElementById('builder-logic');

        if (fieldSelect) fieldSelect.value = lastCondition.fieldTechnicalName;
        if (operatorSelect) operatorSelect.value = lastCondition.operator;
        if (valueInput) valueInput.value = lastCondition.value;
        if (logicSelect && lastCondition.logic) logicSelect.value = lastCondition.logic;

        // Remove the last condition
        this.conditions.pop();
        this.updateHumanReadableDisplay();
        this.updateTechnicalExpression();
        this.updateConditionButtons();
    }

    /**
     * Remove the last condition
     */
    removeLastCondition() {
        if (this.conditions.length === 0) return;

        this.conditions.pop();
        this.updateHumanReadableDisplay();
        this.updateTechnicalExpression();
        this.updateConditionButtons();
    }

    /**
     * Parse existing expression from the technical field
     */
    parseExistingExpression() {
        const expressionField = document.querySelector('input[name="expression"]');
        if (!expressionField || !expressionField.value) {
            alert('No existing expression to parse');
            return;
        }

        const expression = expressionField.value.trim();

        // Simple parsing for basic expressions
        // This is a basic implementation - you might want to enhance it
        try {
            this.conditions = [];

            // Split by 'and' and 'or' operators
            const parts = expression.split(/\s+(and|or)\s+/i);

            for (let i = 0; i < parts.length; i += 2) {
                const conditionPart = parts[i].trim();
                const logic = i > 0 ? parts[i - 1].toLowerCase() : null;

                // Parse individual condition (field operator value)
                const condition = this.parseConditionPart(conditionPart, logic);
                if (condition) {
                    this.conditions.push(condition);
                }
            }

            this.updateHumanReadableDisplay();
            this.updateConditionButtons();

            if (this.conditions.length > 0) {
                alert(`Parsed ${this.conditions.length} condition(s) from the expression`);
            } else {
                alert('Could not parse the expression. You may need to rebuild it manually.');
            }

        } catch (error) {
            console.error('Error parsing expression:', error);
            alert('Error parsing expression. Please rebuild it manually.');
        }
    }

    /**
     * Parse a single condition part
     */
    parseConditionPart(conditionPart, logic) {
        // Remove parentheses
        conditionPart = conditionPart.replace(/^\(|\)$/g, '');

        // Try to match different patterns
        const patterns = [
            /^(.+?)\s*(==|!=|>=|<=|>|<)\s*'([^']+)'$/, // String values
            /^(.+?)\s*(==|!=|>=|<=|>|<)\s*([0-9.]+)$/, // Numeric values
            /^(.+?)\s*(==|!=)\s*(True|False)$/, // Boolean values
            /^(.+?)\s+(in|not in)\s+\[(.+)\]$/, // List values
        ];

        for (const pattern of patterns) {
            const match = conditionPart.match(pattern);
            if (match) {
                const fieldTechnicalName = match[1].trim();
                const operator = match[2];
                let value = match[3];

                // Find the field to get display name
                const field = this.fields.find(f => f.technical_name === fieldTechnicalName);
                if (!field) continue; // Skip if field not found

                // Handle different value types
                if (operator === 'in' || operator === 'not in') {
                    // Parse list values
                    value = value.split(',').map(v => v.trim().replace(/'/g, '')).join(', ');
                } else if (value === 'True' || value === 'False') {
                    value = value.toLowerCase();
                }

                return {
                    fieldTechnicalName: fieldTechnicalName,
                    fieldDisplayName: field.name,
                    operator: operator,
                    operatorDisplay: this.getOperatorDisplay(operator),
                    value: value,
                    logic: logic,
                    fieldType: field.field_type
                };
            }
        }

        return null;
    }

    /**
     * Get operator display text
     */
    getOperatorDisplay(operator) {
        const operatorMap = {
            '==': 'equals',
            '!=': 'not equals',
            '>': 'greater than',
            '<': 'less than',
            '>=': 'greater or equal',
            '<=': 'less or equal',
            'in': 'in list',
            'not in': 'not in list',
            'contains': 'contains',
            'not_contains': 'does not contain'
        };
        return operatorMap[operator] || operator;
    }

    /**
     * Update the human-readable expression display
     */
    updateHumanReadableDisplay() {
        const display = document.getElementById('human-readable-expression');
        if (!display) return;

        if (this.conditions.length === 0) {
            display.innerHTML = '<em class="text-muted">No conditions added yet</em>';
            return;
        }

        let html = '';
        this.conditions.forEach((condition, index) => {
            if (index > 0 && condition.logic) {
                html += ` <span class="badge bg-secondary">${condition.logic.toUpperCase()}</span> `;
            }

            html += `<span class="badge bg-primary">${condition.fieldDisplayName}</span> `;
            html += `<span class="text-muted">${condition.operatorDisplay}</span> `;
            html += `<span class="badge bg-success">${condition.value}</span>`;
        });

        display.innerHTML = html;
    }

    /**
     * Update condition management buttons visibility
     */
    updateConditionButtons() {
        const editLastBtn = document.getElementById('edit-last-condition-btn');
        const removeLastBtn = document.getElementById('remove-last-condition-btn');

        const hasConditions = this.conditions.length > 0;

        if (editLastBtn) {
            editLastBtn.style.display = hasConditions ? 'inline-block' : 'none';
        }
        if (removeLastBtn) {
            removeLastBtn.style.display = hasConditions ? 'inline-block' : 'none';
        }
    }

    /**
     * Update the technical expression field
     */
    updateTechnicalExpression() {
        const expressionField = document.querySelector('input[name="expression"]');
        if (!expressionField) return;

        if (this.conditions.length === 0) {
            expressionField.value = '';
            return;
        }

        let expression = '';
        this.conditions.forEach((condition, index) => {
            if (index > 0 && condition.logic) {
                expression += ` ${condition.logic} `;
            }

            // Format value based on field type
            let formattedValue = condition.value;
            if (condition.fieldType === 'text' || condition.fieldType === 'selection') {
                // Handle list operators
                if (condition.operator === 'in' || condition.operator === 'not in') {
                    const valueList = condition.value.split(',').map(v => `'${v.trim()}'`).join(', ');
                    formattedValue = `[${valueList}]`;
                } else {
                    formattedValue = `'${condition.value}'`;
                }
            } else if (condition.fieldType === 'boolean') {
                formattedValue = condition.value.toLowerCase() === 'true' ? 'True' : 'False';
            }

            // Handle special operators
            if (condition.operator === 'contains') {
                expression += `'${condition.value}' in ${condition.fieldTechnicalName}`;
            } else if (condition.operator === 'not_contains') {
                expression += `'${condition.value}' not in ${condition.fieldTechnicalName}`;
            } else {
                expression += `${condition.fieldTechnicalName} ${condition.operator} ${formattedValue}`;
            }
        });

        expressionField.value = expression;

        // Trigger change event to update the model
        expressionField.dispatchEvent(new Event('change', { bubbles: true }));
    }

    /**
     * Parse existing expression to populate the builder
     */
    parseExpression(expression) {
        // This is a simplified parser - you might want to enhance it
        // For now, just set the technical expression
        const expressionField = document.querySelector('input[name="expression"]');
        if (expressionField && expression) {
            expressionField.value = expression;
        }
    }
}

// Global instance
window.expressionBuilder = new ExpressionBuilder();

// Enhanced initialization for Odoo forms
function initializeExpressionBuilder() {
    // Try multiple ways to get the matrix ID
    let matrixId = null;

    // Method 1: From matrix_id field
    const matrixField = document.querySelector('input[name="matrix_id"], select[name="matrix_id"]');
    if (matrixField && matrixField.value) {
        matrixId = parseInt(matrixField.value);
    }

    // Method 2: From option_id field (get matrix from option)
    if (!matrixId) {
        const optionField = document.querySelector('input[name="option_id"], select[name="option_id"]');
        if (optionField && optionField.value) {
            // We'll need to fetch the matrix ID from the option
            fetchMatrixFromOption(parseInt(optionField.value));
            return;
        }
    }

    if (matrixId && window.expressionBuilder) {
        // Get RPC service from Odoo
        const rpc = getRpcService();
        if (rpc) {
            window.expressionBuilder.init(matrixId, rpc);
        }
    }
}

// Get RPC service from Odoo
function getRpcService() {
    try {
        // Try different ways to get RPC service
        if (window.odoo && window.odoo.rpc) {
            return window.odoo.rpc;
        }
        if (window.odoo && window.odoo.__DEBUG__ && window.odoo.__DEBUG__.services && window.odoo.__DEBUG__.services.rpc) {
            return window.odoo.__DEBUG__.services.rpc;
        }
        // Fallback to legacy method
        if (window.openerp && window.openerp.web && window.openerp.web.rpc) {
            return window.openerp.web.rpc;
        }
        return null;
    } catch (error) {
        console.error('Error getting RPC service:', error);
        return null;
    }
}

// Fetch matrix ID from option
async function fetchMatrixFromOption(optionId) {
    const rpc = getRpcService();
    if (!rpc) return;

    try {
        const result = await rpc('/web/dataset/call_kw', {
            model: 'config.matrix.option',
            method: 'read',
            args: [[optionId], ['matrix_id']],
            kwargs: {}
        });

        if (result && result.length > 0 && result[0].matrix_id) {
            const matrixId = result[0].matrix_id[0];
            window.expressionBuilder.init(matrixId, rpc);
        }
    } catch (error) {
        console.error('Error fetching matrix from option:', error);
    }
}

// Initialize when the form loads
document.addEventListener('DOMContentLoaded', function() {
    // Wait for Odoo to fully load the form
    setTimeout(initializeExpressionBuilder, 1000);

    // Also try when the page becomes visible (in case of tab switching)
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            setTimeout(initializeExpressionBuilder, 500);
        }
    });
});

// Re-initialize when form changes (for Odoo's dynamic loading)
function setupMutationObserver() {
    if (window.MutationObserver && document.body) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    // Check if expression builder container was added
                    const builderContainer = document.getElementById('expression-builder-container');
                    if (builderContainer && !builderContainer.hasAttribute('data-initialized')) {
                        builderContainer.setAttribute('data-initialized', 'true');
                        setTimeout(initializeExpressionBuilder, 500);
                    }
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
}

// Setup observer when DOM is ready
document.addEventListener('DOMContentLoaded', setupMutationObserver);

// Fallback: setup observer when body is available
if (document.body) {
    setupMutationObserver();
} else {
    // Wait for body to be available
    const bodyCheckInterval = setInterval(() => {
        if (document.body) {
            clearInterval(bodyCheckInterval);
            setupMutationObserver();
        }
    }, 100);
}
