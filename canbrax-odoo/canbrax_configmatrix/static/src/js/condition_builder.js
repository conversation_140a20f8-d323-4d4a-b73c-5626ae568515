/** @odoo-module **/

import { registry } from "@web/core/registry";
import { Component, useState, onWillStart, onMounted } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";
import { standardWidgetProps } from "@web/views/widgets/standard_widget_props";

/**
 * Condition Builder Component
 *
 * A component for building visibility conditions and formulas
 * with a user-friendly interface.
 */
export default class ConditionBuilder extends Component {
    static template = "canbrax_configmatrix.ConditionBuilder";
    static props = {
        ...standardWidgetProps,
        templateId: { type: Number, optional: true },
        initialCondition: { type: String, optional: true },
        onConditionChange: { type: Function, optional: true },
    };
    setup() {
        this.state = useState({
            condition: this.props.initialCondition || "",
            fields: [],
            operators: [
                { value: "==", label: "equals" },
                { value: "!=", label: "not equals" },
                { value: ">", label: "greater than" },
                { value: "<", label: "less than" },
                { value: ">=", label: "greater than or equal" },
                { value: "<=", label: "less than or equal" },
                { value: "in", label: "in list" },
                { value: "not in", label: "not in list" },
            ],
            logicalOperators: [
                { value: "and", label: "AND" },
                { value: "or", label: "OR" },
            ],
            selectedField: "",
            selectedOperator: "==",
            fieldValue: "",
            loading: true,
        });

        // Use try/catch to handle services that might not be available
        try {
            this.orm = useService("orm");
        } catch (error) {
            console.warn("ORM service not available:", error);
            this.orm = {
                call: () => Promise.resolve([]),
            };
        }

        try {
            this.notification = useService("notification");
        } catch (error) {
            console.warn("Notification service not available:", error);
            this.notification = {
                add: (message) => console.log("Notification:", message),
            };
        }

        onWillStart(async () => {
            await this.loadFields();
        });

        onMounted(() => {
            this.parseCondition();
        });
    }

    async loadFields() {
        try {
            this.state.loading = true;

            // Load fields from the template
            const templateId = this.props.templateId;
            if (!templateId) {
                throw new Error("No template ID provided");
            }

            const fields = await this.orm.call(
                "config.matrix.template",
                "get_available_fields",
                [templateId]
            );

            this.state.fields = fields;
            this.state.loading = false;
        } catch (error) {
            this.state.loading = false;
            this.notification.add(error.message || "Failed to load fields", {
                type: "danger",
            });
        }
    }

    parseCondition() {
        // Parse the existing condition to populate the builder
        // This is a simplified implementation
        const condition = this.state.condition;
        if (!condition) return;

        // Simple parsing for basic conditions like "field == value"
        const equalsMatch = condition.match(/(\w+)\s*==\s*["']?([^"']+)["']?/);
        if (equalsMatch) {
            this.state.selectedField = equalsMatch[1];
            this.state.selectedOperator = "==";
            this.state.fieldValue = equalsMatch[2];
        }

        // Add more parsing logic for other operators as needed
    }

    buildCondition() {
        const { selectedField, selectedOperator, fieldValue } = this.state;

        if (!selectedField || !selectedOperator) {
            return "";
        }

        // Find the field to determine its type
        const field = this.state.fields.find(f => f.technical_name === selectedField);
        if (!field) return "";

        let formattedValue = fieldValue;

        // Format value based on field type
        if (field.field_type === 'text' || field.field_type === 'selection') {
            formattedValue = `"${fieldValue}"`;
        } else if (field.field_type === 'boolean') {
            formattedValue = fieldValue === 'true' ? 'True' : 'False';
        }

        // Build the condition
        let condition = "";

        if (selectedOperator === "in" || selectedOperator === "not in") {
            // Handle list operators
            const valueList = fieldValue.split(",").map(v => {
                if (field.field_type === 'text' || field.field_type === 'selection') {
                    return `"${v.trim()}"`;
                }
                return v.trim();
            }).join(", ");

            condition = `${selectedField} ${selectedOperator} [${valueList}]`;
        } else {
            // Handle standard operators
            condition = `${selectedField} ${selectedOperator} ${formattedValue}`;
        }

        return condition;
    }

    onAddCondition() {
        const newCondition = this.buildCondition();
        if (!newCondition) return;

        if (this.state.condition) {
            // Add logical operator if there's already a condition
            this.state.condition = `(${this.state.condition}) and (${newCondition})`;
        } else {
            this.state.condition = newCondition;
        }

        // Reset inputs
        this.state.selectedField = "";
        this.state.selectedOperator = "==";
        this.state.fieldValue = "";

        // Notify parent
        this.props.onConditionChange(this.state.condition);
    }

    onClearCondition() {
        this.state.condition = "";
        this.state.selectedField = "";
        this.state.selectedOperator = "==";
        this.state.fieldValue = "";

        // Notify parent
        this.props.onConditionChange("");
    }
}

// Register with both components and fields registries for better compatibility
registry.category("components").add("condition_builder", ConditionBuilder);
registry.category("fields").add("condition_builder", {
    component: ConditionBuilder,
});
