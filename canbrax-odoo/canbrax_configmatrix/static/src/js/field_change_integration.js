/**
 * Field Change Integration for Cascading Dependencies
 * 
 * This file automatically integrates the cascading dependency system with
 * field changes in the configurator. It hooks into the DOM and triggers
 * the cascading clearing system whenever a field value changes.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Field change integration loaded');
    
    // Wait for the configurator to be fully loaded
    setTimeout(() => {
        initializeFieldChangeIntegration();
    }, 1000);
});

// Legacy function - now redirects to enhanced version
function initializeFieldChangeIntegration() {
    console.log('Legacy initializeFieldChangeIntegration called, redirecting to enhanced version');
    initializeFieldChangeIntegrationEnhanced();
}

// Legacy function - now redirects to enhanced version
function handleFieldChange(fieldElement) {
    handleFieldChangeEnhanced(fieldElement);
}

function observeForNewFields() {
    // Create a MutationObserver to watch for dynamically added fields
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // Check if the added node or its children contain config fields
                    const newFields = node.classList && node.classList.contains('config-field')
                        ? [node]
                        : node.querySelectorAll('.config-field');

                    if (newFields.length > 0) {
                        console.log(`Found ${newFields.length} new config fields, adding listeners`);
                        addEventListenersToFields(newFields);
                    }
                }
            });
        });
    });

    // Start observing the document for changes
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    console.log('MutationObserver set up for dynamic field detection');
}

function addEventListenersToFields(fields) {
    fields.forEach(field => {
        // Skip if already has listeners
        if (field._hasChangeListeners) return;

        // Add change event listener
        field.addEventListener('change', function(event) {
            handleFieldChange(event.target);
        });

        // Add input event listener for real-time updates on text/number fields
        if (field.type === 'text' || field.type === 'number') {
            field.addEventListener('input', function(event) {
                // Debounce input events to avoid excessive processing
                clearTimeout(field._inputTimeout);
                field._inputTimeout = setTimeout(() => {
                    handleFieldChange(event.target);
                }, 500);
            });
        }

        // Mark as having listeners to avoid duplicates
        field._hasChangeListeners = true;
    });
}

// Enhanced field change detection for various field types
function detectFieldValue(fieldElement) {
    const fieldType = fieldElement.getAttribute('data-field-type') || fieldElement.type;

    switch (fieldType) {
        case 'boolean':
        case 'checkbox':
            return fieldElement.checked;
        case 'selection':
        case 'select':
            return fieldElement.value;
        case 'number':
            const numValue = parseFloat(fieldElement.value);
            return isNaN(numValue) ? null : numValue;
        case 'text':
        case 'char':
        default:
            return fieldElement.value || null;
    }
}

// Enhanced field change handler with better error handling
function handleFieldChangeEnhanced(fieldElement) {
    try {
        const fieldName = fieldElement.getAttribute('data-technical-name');
        const fieldType = fieldElement.getAttribute('data-field-type');

        if (!fieldName) {
            console.warn('Field change detected but no technical name found:', fieldElement);
            return;
        }

        const newValue = detectFieldValue(fieldElement);
        const oldValue = window.fieldValues ? window.fieldValues[fieldName] : undefined;

        // Only process if value actually changed
        if (oldValue === newValue) {
            return;
        }

        console.log(`Field change detected: ${fieldName} = ${newValue} (was: ${oldValue})`);

        // Update global field values immediately
        updateGlobalFieldValues(fieldName, newValue, fieldType);

        // Update hidden form field
        updateHiddenFormField(fieldElement, newValue);

        // Trigger configurator field change with proper error handling
        triggerConfiguratorFieldChange(fieldName, newValue);

    } catch (error) {
        console.error('Error in field change handler:', error);
    }
}

function updateGlobalFieldValues(fieldName, newValue, fieldType) {
    if (!window.fieldValues) {
        window.fieldValues = {};
    }

    // Handle empty/null values
    if (newValue === '' || newValue === null || newValue === undefined ||
        (fieldType === 'boolean' && newValue === false)) {
        delete window.fieldValues[fieldName];
    } else {
        window.fieldValues[fieldName] = newValue;
    }
}

function updateHiddenFormField(fieldElement, newValue) {
    const fieldId = fieldElement.getAttribute('data-field-id');
    if (fieldId) {
        const hiddenField = document.getElementById(`hidden_field_${fieldId}`);
        if (hiddenField) {
            hiddenField.value = newValue || '';
        }
    }
}

function triggerConfiguratorFieldChange(fieldName, newValue) {
    // Priority 1: Use configurator's field change handler
    if (typeof window.onConfiguratorFieldChange === 'function') {
        try {
            window.onConfiguratorFieldChange(fieldName, newValue);
            return;
        } catch (error) {
            console.error('Error in configurator field change:', error);
        }
    }

    // Priority 2: Use direct configurator instance if available
    if (window.configuratorInstance && typeof window.configuratorInstance.onFieldChange === 'function') {
        try {
            const field = window.configuratorInstance.allFields.get(fieldName);
            if (field) {
                window.configuratorInstance.onFieldChange(field, newValue);
                return;
            }
        } catch (error) {
            console.error('Error in direct configurator field change:', error);
        }
    }

    // Priority 3: Fallback to visibility update only
    if (typeof window.updateFieldVisibility === 'function') {
        try {
            window.updateFieldVisibility();
        } catch (error) {
            console.error('Error in visibility update fallback:', error);
        }
    }

    console.warn(`No configurator field change handler available for field: ${fieldName}`);
}

// Initialize with enhanced detection
function initializeFieldChangeIntegrationEnhanced() {
    console.log('Initializing enhanced field change integration...');

    // Find all possible field selectors
    const fieldSelectors = [
        '.config-field',
        '[data-technical-name]',
        '.js_config_field',
        'input[data-field-type]',
        'select[data-field-type]',
        'textarea[data-field-type]'
    ];

    let allFields = [];
    fieldSelectors.forEach(selector => {
        const fields = document.querySelectorAll(selector);
        allFields = allFields.concat(Array.from(fields));
    });

    // Remove duplicates
    allFields = allFields.filter((field, index, self) =>
        self.findIndex(f => f === field) === index
    );

    console.log(`Found ${allFields.length} total config fields`);

    // Add enhanced event listeners
    addEventListenersToFields(allFields);

    // Set up mutation observer for dynamic fields
    observeForNewFields();

    // Expose enhanced handler globally
    window.handleFieldChange = handleFieldChangeEnhanced;

    console.log('Enhanced field change integration initialized successfully');
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Field change integration loaded');

    // Wait for the configurator to be fully loaded
    setTimeout(() => {
        initializeFieldChangeIntegrationEnhanced();
    }, 1000);

    // Also try to initialize when configurator is ready
    if (typeof window.addEventListener !== 'undefined') {
        window.addEventListener('configurator-ready', () => {
            console.log('Configurator ready event detected, re-initializing field integration');
            initializeFieldChangeIntegrationEnhanced();
        });
    }
});

// Expose functions globally for external use
window.fieldChangeIntegration = {
    initialize: initializeFieldChangeIntegrationEnhanced,
    handleFieldChange: handleFieldChangeEnhanced,
    addListenersToFields: addEventListenersToFields
};
}