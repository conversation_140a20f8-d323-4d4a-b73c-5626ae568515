/**
 * Price Matrix Handler - v1.0
 * 
 * This script handles price matrix functionality for the configurator.
 * It automatically detects height and width fields and gets prices from price matrices.
 */

(function() {
    'use strict';

    // Global variables
    let templateId = null;
    let configurationPriceElement = null;
    let fieldValues = {};

    // Initialize the price matrix handler
    function initialize() {
        // Get the configuration price element
        configurationPriceElement = document.getElementById('configuration-price-matrix');
        if (!configurationPriceElement) {
            console.error('[PRICE_MATRIX] Configuration price element not found');
            return;
        }

        // Get the template ID
        const templateIdInput = document.querySelector('input[name="template_id"]');
        if (!templateIdInput) {
            console.error('[PRICE_MATRIX] Template ID input not found');
            return;
        }
        templateId = parseInt(templateIdInput.value) || 0;

        // Add event listeners to all configuration fields
        addFieldEventListeners();

        console.log('[PRICE_MATRIX] Price matrix handler initialized');
    }

    // Add event listeners to all configuration fields
    function addFieldEventListeners() {
        // Find all configuration fields
        const configFields = document.querySelectorAll('.config-field');

        // Add event listeners to each field
        configFields.forEach(field => {
            // Get field information
            const fieldId = field.getAttribute('data-field-id');
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');

            if (!fieldId || !technicalName) {
                console.warn('[PRICE_MATRIX] Field missing ID or technical name:', field);
                return;
            }

            // Add change event listener
            field.addEventListener('change', async function() {
                // Get the field value
                let value = getFieldValue(field, fieldType);

                // Update the field value in the global object
                fieldValues[technicalName] = value;

                // Update price from matrix
                await updatePriceFromMatrix();
            });
        });
    }

    // Get the value of a field based on its type
    function getFieldValue(field, fieldType) {
        switch (fieldType) {
            case 'boolean':
                return field.checked;
            case 'number':
                return parseFloat(field.value) || 0;
            case 'selection':
                // Check if this is an unselected option
                if (!field.value || field.value === '-- Select an option --' || field.selectedIndex === 0) {
                    return '';
                }
                return field.value;
            default:
                return field.value;
        }
    }

    // Collect current values of all fields
    function collectFieldValues() {
        // Clear existing values
        fieldValues = {};

        // Find all configuration fields
        const configFields = document.querySelectorAll('.config-field');

        // Collect values from each field
        configFields.forEach(field => {
            // Get field information
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');

            if (!technicalName) {
                return;
            }

            // Get the field value
            const value = getFieldValue(field, fieldType);

            // Store the value
            fieldValues[technicalName] = value;
        });
    }

    // Get price from price matrix
    async function getPriceFromMatrix() {
        try {
            // Generate configuration data
            const configData = JSON.stringify(fieldValues);
            
            const response = await fetch('/config_matrix/get_price_from_matrix', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    jsonrpc: "2.0",
                    method: "call",
                    params: {
                        template_id: templateId,
                        config_data: configData
                    },
                    id: new Date().getTime()
                })
            });

            const data = await response.json();
            
            if (data.result && data.result.success) {
                return data.result.price;
            } else {
                console.warn('[PRICE_MATRIX] No price found from matrix:', data.result?.error || 'Unknown error');
                return 0;
            }
        } catch (error) {
            console.error('[PRICE_MATRIX] Error getting price from matrix:', error);
            return 0;
        }
    }

    // Update price from matrix based on current field values
    async function updatePriceFromMatrix() {
        if (!configurationPriceElement) {
            return false;
        }

        // Collect current field values
        collectFieldValues();

        // Check if we have any field values
        const filledFields = Object.values(fieldValues).filter(v => v && v !== '').length;
        if (filledFields < 2) {
            console.log('[PRICE_MATRIX] Not enough fields filled to get price from matrix');
            return false;
        }

        console.log('[PRICE_MATRIX] Getting price from matrix with configuration data');
        
        const matrixPrice = await getPriceFromMatrix();
        
        if (matrixPrice !== null) {
            // Update the configuration price with matrix price
            configurationPriceElement.textContent = formatCurrency(matrixPrice);
            console.log(`[PRICE_MATRIX] Updated price to: ${formatCurrency(matrixPrice)}`);
            
            // Update hidden input fields
            const fieldPriceMatrix = document.getElementById('field-price-matrix');
            if (fieldPriceMatrix) {
                fieldPriceMatrix.value = matrixPrice;
                console.log(`[PRICE_MATRIX] Updated field-price-matrix input: ${matrixPrice}`);
            }
            
            return true; // Indicate that we found a matrix price
        }

        // If no matrix price found, keep the existing BOM price calculation
        console.log('[PRICE_MATRIX] No matrix price found, using BOM calculation');
        return false; // Indicate that we didn't find a matrix price
    }

    // Format currency
    function formatCurrency(amount) {
        return '$' + parseFloat(amount).toFixed(2);
    }

    // Make functions available globally
    window.priceMatrixHandler = {
        initialize,
        updatePriceFromMatrix,
        getPriceFromMatrix
    };

    // Initialize after a short delay to ensure DOM is ready
    setTimeout(initialize, 500);

})(); 