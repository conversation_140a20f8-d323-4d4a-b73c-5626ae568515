/**
 * SVG Components Inline Display
 *
 * This script adds a "Show SVG" button next to "Show Conditions" and displays
 * SVG components in an inline panel similar to the conditions panel.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('[SVG-INLINE] Initializing SVG Components Inline Display');

    // Create the SVG components panel HTML
    const svgPanelHtml = `
    <div id="svgComponentsPanel" class="card mb-4" style="display: none;">
        <div class="card-header py-2">
            <h5 class="mb-0 fs-6">SVG Components</h5>
        </div>
        <div class="card-body p-2">
            <div class="row mb-2">
                <div class="col-12">
                    <button id="refreshSvgComponentsInline" class="btn btn-sm btn-secondary">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-sm table-bordered">
                    <thead class="table-light">
                        <tr>
                            <th>Component Name</th>
                            <th>Type</th>
                            <th>Z-Index</th>
                            <th>Visibility Condition</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody id="svgComponentsTableBodyInline">
                        <tr>
                            <td colspan="6" class="text-center">
                                <i class="fas fa-spinner fa-spin"></i> Loading components...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div id="svgPreviewContainerInline" class="mt-3 border rounded p-3 text-center bg-light" style="min-height: 200px; display: flex; align-items: center; justify-content: center;">
                <div class="text-muted">
                    <i class="fas fa-image fa-3x mb-2 d-block"></i>
                    Select a component to preview
                </div>
            </div>
        </div>
    </div>
    `;

    // Initialize the SVG button
    function initializeSvgButton() {
        // Find the existing button
        const showSvgButton = document.getElementById('showSvgButtonInline');
        if (!showSvgButton) {
            console.warn('[SVG-INLINE] Show SVG button not found');
            return;
        }

        // Add event listener to the button
        showSvgButton.addEventListener('click', function() {
            // Toggle SVG components panel
            const svgPanel = document.getElementById('svgComponentsPanel');
            if (svgPanel) {
                const isVisible = svgPanel.style.display !== 'none';
                svgPanel.style.display = isVisible ? 'none' : 'block';

                // Load components if panel is being shown
                if (!isVisible) {
                    loadSvgComponentsInline();
                }
            }
        });

        console.log('[SVG-INLINE] SVG button initialized');

        // Add the SVG components panel to the right column
        addSvgComponentsPanel();
    }

    // Add the SVG components panel to the right column
    function addSvgComponentsPanel() {
        // Find the right column (Components card)
        const componentsCard = document.querySelector('.col-md-3 .card:nth-child(2)');
        if (!componentsCard) {
            console.warn('[SVG-INLINE] Components card not found');
            return;
        }

        // Insert the SVG components panel before the Components card
        componentsCard.insertAdjacentHTML('beforebegin', svgPanelHtml);
        console.log('[SVG-INLINE] SVG components panel added');

        // Add event listener to refresh button
        const refreshButton = document.getElementById('refreshSvgComponentsInline');
        if (refreshButton) {
            refreshButton.addEventListener('click', loadSvgComponentsInline);
        }
    }

    // Function to load SVG components
    function loadSvgComponentsInline() {
        console.log('[SVG-INLINE] Loading SVG components');

        // Get template ID
        const templateIdInput = document.querySelector('input[name="template_id"]');
        if (!templateIdInput) {
            console.error('[SVG-INLINE] Template ID input not found');
            return;
        }

        const templateId = parseInt(templateIdInput.value) || 0;
        console.log('[SVG-INLINE] Template ID:', templateId);

        // Show loading indicator
        const svgComponentsTableBody = document.getElementById('svgComponentsTableBodyInline');
        if (svgComponentsTableBody) {
            svgComponentsTableBody.innerHTML = '<tr><td colspan="6" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading SVG components...</td></tr>';
        }

        // Fetch SVG components
        fetch('/config_matrix/get_svg_components', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                jsonrpc: "2.0",
                method: "call",
                params: {
                    template_id: templateId
                },
                id: new Date().getTime()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.result && data.result.success && data.result.components) {
                displaySvgComponentsInline(data.result.components);
            } else {
                console.error('[SVG-INLINE] Error loading SVG components:', data);
                if (svgComponentsTableBody) {
                    svgComponentsTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error loading SVG components</td></tr>';
                }
            }
        })
        .catch(error => {
            console.error('[SVG-INLINE] Error fetching SVG components:', error);
            if (svgComponentsTableBody) {
                svgComponentsTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error loading SVG components</td></tr>';
            }
        });
    }

    // Function to display SVG components
    function displaySvgComponentsInline(components) {
        console.log('[SVG-INLINE] Displaying SVG components:', components);

        const svgComponentsTableBody = document.getElementById('svgComponentsTableBodyInline');
        if (!svgComponentsTableBody) {
            console.error('[SVG-INLINE] SVG components table body not found');
            return;
        }

        // Sort components by z-index
        components.sort((a, b) => a.z_index - b.z_index);

        // Clear the table
        svgComponentsTableBody.innerHTML = '';

        // Get field values from global scope
        let fieldValues = {};
        if (window.fieldValues) {
            fieldValues = window.fieldValues;
        } else {
            console.warn('[SVG-INLINE] fieldValues not found in global scope, using empty object');
        }

        // Add each component to the table
        components.forEach(component => {
            const row = document.createElement('tr');

            // Evaluate condition if present
            let isVisible = true;
            if (component.condition && component.component_type === 'layer') {
                isVisible = evaluateCondition(component.condition, fieldValues);
            }

            row.innerHTML = `
                <td>${component.name}</td>
                <td>${component.component_type}</td>
                <td>${component.z_index}</td>
                <td><code>${component.condition || 'Always visible'}</code></td>
                <td>${isVisible ? '<span class="badge bg-success">Visible</span>' : '<span class="badge bg-secondary">Hidden</span>'}</td>
                <td><button class="btn btn-sm btn-outline-primary preview-svg-btn-inline" type="button">Preview</button></td>
            `;

            svgComponentsTableBody.appendChild(row);

            // Add event listener to preview button
            const previewButton = row.querySelector('.preview-svg-btn-inline');
            previewButton.addEventListener('click', function() {
                showSvgPreviewInline(component, fieldValues);
            });
        });
    }

    // Function to show SVG preview
    function showSvgPreviewInline(component, fieldValues) {
        console.log('[SVG-INLINE] Showing SVG preview for component:', component.name);

        const svgPreviewContainer = document.getElementById('svgPreviewContainerInline');
        if (!svgPreviewContainer) {
            console.error('[SVG-INLINE] SVG preview container not found');
            return;
        }

        // Process SVG content with field values
        let svgContent = component.svg_content;

        // Replace ${fieldName} with actual values
        svgContent = svgContent.replace(/\${(\w+)}/g, function(match, fieldName) {
            return fieldValues[fieldName] !== undefined
                   ? fieldValues[fieldName]
                   : match;
        });

        // Check if the content is a complete SVG or just a fragment
        let wrappedSvgContent = svgContent;
        if (!svgContent.trim().startsWith('<svg')) {
            // Wrap the fragment in an SVG tag
            wrappedSvgContent = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 600" width="100%" height="100%">
                ${svgContent}
            </svg>`;
            console.log('[SVG-INLINE] Wrapped SVG fragment in SVG tags');
        }

        // Update the preview container
        svgPreviewContainer.innerHTML = `
            <div class="mb-2">
                <h6>${component.name}</h6>
                <div class="small text-muted">
                    Type: ${component.component_type}, Z-Index: ${component.z_index}
                    ${component.condition ? `<br>Condition: <code>${component.condition}</code>` : ''}
                </div>
            </div>
            <div class="svg-preview" style="background-color: #f8f9fa; padding: 10px; border-radius: 4px;">
                ${wrappedSvgContent}
            </div>
        `;
    }

    // Function to evaluate a condition
    function evaluateCondition(condition, fieldValues) {
        if (!condition || condition === 'true') {
            return true;
        }

        try {
            // Extract field names from the condition
            const fieldNames = condition.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || [];

            // Create a context with field values
            const context = { ...fieldValues };

            // Add missing fields with default values
            fieldNames.forEach(fieldName => {
                if (context[fieldName] === undefined) {
                    console.log(`[SVG-INLINE] Field '${fieldName}' not found in context, setting default value`);
                    context[fieldName] = '';
                }
            });

            // Create a function with the field values in scope
            const func = new Function(...Object.keys(context), `return ${condition};`);

            // Call the function with the field values
            return func(...Object.values(context));
        } catch (error) {
            console.error('[SVG-INLINE] Error evaluating condition:', error);
            return false;
        }
    }

    // Initialize when the page loads
    setTimeout(initializeSvgButton, 500);

    // Make functions available globally
    window.svgComponentsInline = {
        initializeSvgButton,
        loadSvgComponentsInline,
        displaySvgComponentsInline,
        showSvgPreviewInline,
        evaluateCondition
    };
});
