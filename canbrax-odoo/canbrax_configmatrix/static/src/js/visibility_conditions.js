'use strict';

(function() {

    // Debug toggle - set to true to enable detailed logging
    window.debugConditions = false;

    // Function to toggle debug mode
    window.toggleDebug = function() {
        window.debugConditions = !window.debugConditions;
        console.log('Debug mode:', window.debugConditions ? 'ON' : 'OFF');
        return window.debugConditions;
    };

    // Initialize field values object
    const fieldValues = {};

    // PERFORMANCE FIX: Track original field values to detect actual changes
    const originalFieldValues = {};

    // CRITICAL FIX: Add missing variables and functions
    let calculatedFieldsDefinitions = [];
    let calculatedFieldsCache = null;
    let lastFieldValuesHash = null;

    // CRITICAL FIX: Add flags to prevent infinite loops
    let isUpdatingDynamicDefaults = false;
    let isUpdatingFieldVisibility = false;

    // PERFORMANCE FIX: Prevent duplicate updateFieldVisibility calls
    let visibilityUpdateTimer = null;
    const VISIBILITY_UPDATE_DEBOUNCE = 10; // Very short debounce to merge rapid calls

    // PERFORMANCE FIX: Separate immediate and heavy operations

    // PERFORMANCE FIX: Debounced visibility update to prevent duplicates
    function updateFieldVisibilityDebounced() {
        // Clear existing timer
        if (visibilityUpdateTimer) {
            clearTimeout(visibilityUpdateTimer);
        }

        // Set new timer with very short delay to merge rapid calls
        visibilityUpdateTimer = setTimeout(() => {
            console.log('[PERF] Debounced visibility update triggered');
            updateFieldVisibility();
            visibilityUpdateTimer = null;
        }, VISIBILITY_UPDATE_DEBOUNCE);
    }

    // Immediate operations that should run synchronously for good UX
    function updateImmediateUI() {
        updateFieldVisibilityDebounced(); // Use debounced version
        updateErrorMessages();
    }

    // Heavy operations that can run asynchronously
    async function updateHeavyOperationsAsync() {
        try {
            // Use setTimeout to make these truly asynchronous
            await new Promise(resolve => setTimeout(resolve, 0));

            console.log('[PERF] Starting heavy operations async...');

            // Run operations in batches to prevent blocking
            await Promise.all([
                new Promise(resolve => { updateDynamicHelp(); resolve(); }),
                new Promise(resolve => { updateFieldConstraints(); resolve(); })
            ]);

            // Second batch
            await Promise.all([
                new Promise(resolve => { updateVisibilityConditionsDisplay(); resolve(); }),
                new Promise(resolve => { updateDebugPanel(); resolve(); })
            ]);

            // Final batch
            await new Promise(resolve => { updateCalculatedFieldsPanel(); resolve(); });

            console.log('[PERF] Heavy operations completed');
        } catch (error) {
            console.error('[PERF] Error in heavy operations:', error);
        }
    }

    // Function to get field value
    function getFieldValue(technicalName) {
        return fieldValues[technicalName];
    }

    // Function to set field value
    function setFieldValue(technicalName, value) {
        fieldValues[technicalName] = value;
        // Invalidate calculated fields cache when field values change
        invalidateCalculatedFieldsCache();
    }

    // CRITICAL FIX: Add missing invalidateCalculatedFieldsCache function
    function invalidateCalculatedFieldsCache() {
        calculatedFieldsCache = null;
        lastFieldValuesHash = null;
    }

    // CRITICAL FIX: Add missing loadCalculatedFields function
    async function loadCalculatedFields() {
        try {
            // Get template ID from URL or global variable
            const urlParams = new URLSearchParams(window.location.search);
            const templateId = urlParams.get('template_id') || urlParams.get('matrix_id');

            if (!templateId) {
                console.warn('No template ID found for loading calculated fields');
                calculatedFieldsDefinitions = [];
                return;
            }

            // Make AJAX call to load calculated fields
            const response = await fetch('/web/dataset/call_kw', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    jsonrpc: '2.0',
                    method: 'call',
                    params: {
                        model: 'config.matrix.calculated.field',
                        method: 'get_calculated_fields_for_template',
                        args: [parseInt(templateId)],
                        kwargs: {}
                    },
                    id: Math.random()
                })
            });

            const result = await response.json();

            if (result.result && Array.isArray(result.result)) {
                // Sort by sequence to ensure proper dependency order
                calculatedFieldsDefinitions = result.result
                    .map(field => ({
                        name: field.name,
                        formula: field.formula,
                        description: field.description || '',
                        sequence: field.sequence || 10,
                        category: field.category || 'basic',
                        is_pricing_matrix: field.is_pricing_matrix || false,
                    }))
                    .sort((a, b) => a.sequence - b.sequence);

                console.log(`Loaded ${calculatedFieldsDefinitions.length} calculated field definitions`);
            } else {
                calculatedFieldsDefinitions = [];
                console.log('No calculated fields found for template');
            }
        } catch (error) {
            console.error('Error loading calculated fields:', error);
            calculatedFieldsDefinitions = [];
        }
    }

    // CRITICAL FIX: Add missing calculateDynamicFields function
    function calculateDynamicFields(fieldValues) {
        // Create a hash of the field values to check if we need to recalculate
        const currentHash = JSON.stringify(fieldValues);

        // Return cached results if field values haven't changed
        if (calculatedFieldsCache && lastFieldValuesHash === currentHash) {
            return calculatedFieldsCache;
        }

        const results = {};

        // Add basic fallback calculations
        calculateBasicFields(fieldValues, results);

        if (calculatedFieldsDefinitions && calculatedFieldsDefinitions.length > 0) {
            // Create evaluation context
            const context = { ...fieldValues, ...results };

            // Add math functions
            context.Math = Math;
            context.min = Math.min;
            context.max = Math.max;
            context.abs = Math.abs;
            context.round = Math.round;
            context.ceil = Math.ceil;
            context.floor = Math.floor;
            context.sqrt = Math.sqrt;

            // Add parseFloat function for JavaScript compatibility
            context.parseFloat = (value) => {
                if (value === null || value === undefined || value === '') return 0;
                try {
                    return parseFloat(String(value));
                } catch {
                    return 0;
                }
            };

            // Calculate server-defined fields in sequence order with multiple passes
            // to handle dependencies
            const maxPasses = 5; // Prevent infinite loops
            let hasChanges = true;
            let pass = 0;

            while (hasChanges && pass < maxPasses) {
                hasChanges = false;
                pass++;

                calculatedFieldsDefinitions.forEach((calcField, index) => {
                    try {
                        // Update context with latest results
                        Object.assign(context, results);

                        // Create a safe context that returns null for undefined variables
                        const safeContext = new Proxy(context, {
                            get: (target, prop) => {
                                if (prop in target) {
                                    return target[prop];
                                }
                                // Return null for missing calculated fields to prevent ReferenceError
                                if (prop.startsWith('_CALCULATED_')) {
                                    return null;
                                }
                                return null;
                            }
                        });

                        // Build argument list and values
                        const argNames = Object.keys(safeContext);
                        const argValues = argNames.map(name => safeContext[name]);

                        // Evaluate the formula with better error handling
                        const safeFormula = `
                            try {
                                const result = (${calcField.formula});
                                return result === undefined ? null : result;
                            } catch (e) {
                                if (e instanceof ReferenceError) {
                                    console.warn('ReferenceError in ${calcField.name}:', e.message);
                                    return null;
                                }
                                console.error('Error in ${calcField.name}:', e);
                                return null;
                            }
                        `;
                        
                        const func = new Function(...argNames, safeFormula);
                        const result = func(...argValues);
                        
                        // Only update if the result has changed
                        if (results[calcField.name] !== result) {
                            results[calcField.name] = result;
                            hasChanges = true;
                        }

                    } catch (error) {
                        console.error(`Error calculating ${calcField.name}:`, error);
                        results[calcField.name] = null;
                    }
                });
            }

            if (pass >= maxPasses) {
                console.warn('Maximum calculation passes reached. Some calculated fields may not be fully resolved.');
            }
        }

        // Cache the results
        calculatedFieldsCache = results;
        lastFieldValuesHash = currentHash;

        return results;
    }

    // CRITICAL FIX: Add missing calculateBasicFields function
    function calculateBasicFields(fieldValues, results) {
        // Basic fallback calculations for backward compatibility
        // This ensures basic fields exist even if server-defined fields fail

        // Initialize common calculated fields with default values
        const commonCalculatedFields = [
            '_CALCULATED_smallest_door_height',
            '_CALCULATED_door_split_type',
            '_CALCULATED_height_calculation_method',
            '_CALCULATED_manual_left_height',
            '_CALCULATED_manual_right_height',
            '_CALCULATED_is_even_split',
            '_CALCULATED_is_uneven_split',
            '_CALCULATED_is_manual_mode',
            '_CALCULATED_halfway_point',
            '_CALCULATED_manual_formula'
        ];

        // Set default values to prevent ReferenceError
        commonCalculatedFields.forEach(fieldName => {
            if (!(fieldName in results)) {
                results[fieldName] = null;
            }
        });

        // Add basic calculations based on field values
        try {
            // Calculate smallest door height from available height fields
            const heightFields = Object.keys(fieldValues).filter(key => 
                key.toLowerCase().includes('height') && 
                fieldValues[key] && 
                !isNaN(parseFloat(fieldValues[key]))
            );

            if (heightFields.length > 0) {
                const heights = heightFields.map(key => parseFloat(fieldValues[key])).filter(h => h > 0);
                if (heights.length > 0) {
                    results['_CALCULATED_smallest_door_height'] = Math.min(...heights);
                }
            }

            // Determine door split type based on available fields
            if (fieldValues.door_split_type) {
                results['_CALCULATED_door_split_type'] = fieldValues.door_split_type;
            } else if (fieldValues.split_type) {
                results['_CALCULATED_door_split_type'] = fieldValues.split_type;
            }

            // Determine height calculation method
            if (fieldValues.height_calculation_method) {
                results['_CALCULATED_height_calculation_method'] = fieldValues.height_calculation_method;
            } else if (fieldValues.calculation_method) {
                results['_CALCULATED_height_calculation_method'] = fieldValues.calculation_method;
            }

        } catch (error) {
            console.warn('Error in calculateBasicFields:', error);
        }
    }

    // CRITICAL FIX: Add validation function for browser console testing
    window.validateVisibilityConditionsJS = function () {
        console.log('='.repeat(50));
        console.log('[VALIDATION] Testing visibility_conditions.js fixes...');
        console.log('='.repeat(50));

        const tests = {
            loadCalculatedFieldsExists: typeof loadCalculatedFields === 'function',
            calculateDynamicFieldsExists: typeof calculateDynamicFields === 'function',
            calculateBasicFieldsExists: typeof calculateBasicFields === 'function',
            updateDynamicDefaultsExists: typeof updateDynamicDefaults === 'function',
            updateFieldVisibilityExists: typeof updateFieldVisibility === 'function',
            infiniteLoopProtectionDefaults: typeof isUpdatingDynamicDefaults !== 'undefined',
            infiniteLoopProtectionVisibility: typeof isUpdatingFieldVisibility !== 'undefined',
            calculatedFieldsDefinitionsExists: Array.isArray(calculatedFieldsDefinitions),
            nullReferenceProtection: true // We added null checks to prevent the .checked error
        };

        console.log('[VALIDATION] Function availability:');
        Object.entries(tests).forEach(([test, passed]) => {
            console.log(`  ${test}: ${passed ? '✅ PASS' : '❌ FAIL'}`);
        });

        // Test infinite loop protection for updateDynamicDefaults
        console.log('[VALIDATION] Testing infinite loop protection...');
        const originalDefaultsFlag = isUpdatingDynamicDefaults;
        isUpdatingDynamicDefaults = true;

        try {
            updateDynamicDefaults(); // Should be skipped
            console.log('  updateDynamicDefaults protection: ✅ WORKING');
        } catch (error) {
            console.log('  updateDynamicDefaults protection: ❌ FAILED -', error.message);
        }

        // Restore flag
        isUpdatingDynamicDefaults = originalDefaultsFlag;

        // Test infinite loop protection for updateFieldVisibility
        const originalVisibilityFlag = isUpdatingFieldVisibility;
        isUpdatingFieldVisibility = true;

        try {
            updateFieldVisibility(); // Should be skipped
            console.log('  updateFieldVisibility protection: ✅ WORKING');
        } catch (error) {
            console.log('  updateFieldVisibility protection: ❌ FAILED -', error.message);
        }

        // Restore flag
        isUpdatingFieldVisibility = originalVisibilityFlag;

        // Test calculated fields
        console.log('[VALIDATION] Testing calculated fields...');
        try {
            const testResult = calculateDynamicFields({ test_field: 100 });
            console.log('  Calculate dynamic fields: ✅ WORKING');
            console.log('  Result:', testResult);
        } catch (error) {
            console.log('  Calculate dynamic fields: ❌ FAILED -', error.message);
        }

        // Test null reference protection
        console.log('[VALIDATION] Testing null reference protection...');
        try {
            // This should not throw an error even if show-all-fields doesn't exist
            updateFieldVisibility();
            console.log('  Null reference protection: ✅ WORKING');
        } catch (error) {
            console.log('  Null reference protection: ❌ FAILED -', error.message);
        }

        const allPassed = Object.values(tests).every(t => t);
        console.log('='.repeat(50));
        console.log(`[VALIDATION] Overall: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
        console.log('='.repeat(50));

        return tests;
    };

    // Add debug function for calculated fields
    window.debugCalculatedFields = function() {
        console.log('='.repeat(50));
        console.log('[DEBUG] Calculated Fields Debug Info');
        console.log('='.repeat(50));
        
        console.log('Calculated Fields Definitions:', calculatedFieldsDefinitions);
        console.log('Current Field Values:', fieldValues);
        
        if (calculatedFieldsCache) {
            console.log('Cached Results:', calculatedFieldsCache);
        }
        
        console.log('='.repeat(50));


        console.log('[VALIDATION] Function availability:');
        Object.entries(tests).forEach(([test, passed]) => {
            console.log(`  ${test}: ${passed ? '✅ PASS' : '❌ FAIL'}`);
        });

        // Test infinite loop protection for updateDynamicDefaults
        console.log('[VALIDATION] Testing infinite loop protection...');
        const originalDefaultsFlag = isUpdatingDynamicDefaults;
        isUpdatingDynamicDefaults = true;

        try {
            updateDynamicDefaults(); // Should be skipped
            console.log('  updateDynamicDefaults protection: ✅ WORKING');
        } catch (error) {
            console.log('  updateDynamicDefaults protection: ❌ FAILED -', error.message);
        }

        // Restore flag
        isUpdatingDynamicDefaults = originalDefaultsFlag;

        // Test infinite loop protection for updateFieldVisibility
        const originalVisibilityFlag = isUpdatingFieldVisibility;
        isUpdatingFieldVisibility = true;

        try {
            updateFieldVisibility(); // Should be skipped
            console.log('  updateFieldVisibility protection: ✅ WORKING');
        } catch (error) {
            console.log('  updateFieldVisibility protection: ❌ FAILED -', error.message);
        }

        // Restore flag
        isUpdatingFieldVisibility = originalVisibilityFlag;

        // Test calculated fields
        console.log('[VALIDATION] Testing calculated fields...');
        try {
            const testResult = calculateDynamicFields({ test_field: 100 });
            console.log('  Calculate dynamic fields: ✅ WORKING');
            console.log('  Result:', testResult);
        } catch (error) {
            console.log('  Calculate dynamic fields: ❌ FAILED -', error.message);
        }

        // Test null reference protection
        console.log('[VALIDATION] Testing null reference protection...');
        try {
            // This should not throw an error even if show-all-fields doesn't exist
            updateFieldVisibility();
            console.log('  Null reference protection: ✅ WORKING');
        } catch (error) {
            console.log('  Null reference protection: ❌ FAILED -', error.message);
        }

        const allPassed = Object.values(tests).every(t => t);
        console.log('='.repeat(50));
        console.log(`[VALIDATION] Overall: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
        console.log('='.repeat(50));

        return tests;
    };

    // Simple manual function for browser console
    window.clearAllIssuesNow = function() {
        if (typeof window.clearAllIssues === 'function') {
            window.clearAllIssues();
            console.log('Manual clear-all triggered from console');
        } else {
            console.error('clearAllIssues function not available');
        }
    };

    // Function to clear logged condition errors (useful for debugging)
    window.clearConditionErrorLogs = function() {
        window.loggedConditionErrors = new Set();
        window.loggedJSONErrors = new Set();
        console.log('Condition error logs cleared');
    };

    // Function to clean Python-style expressions for JavaScript
    function cleanExpressionForJS(expression) {
        // Handle edge cases
        if (!expression || typeof expression !== 'string') {
            return expression;
        }
        
        // Split the expression into parts: outside strings and inside strings
        const parts = [];
        let currentPart = '';
        let inString = false;
        let stringDelimiter = '';
        let i = 0;
        
        while (i < expression.length) {
            const char = expression[i];
            
            if (!inString && (char === '"' || char === "'")) {
                // Start of string
                if (currentPart) {
                    parts.push({ type: 'code', content: currentPart });
                    currentPart = '';
                }
                inString = true;
                stringDelimiter = char;
                currentPart = char;
            } else if (inString && char === stringDelimiter) {
                // End of string
                currentPart += char;
                parts.push({ type: 'string', content: currentPart });
                currentPart = '';
                inString = false;
                stringDelimiter = '';
            } else {
                // Regular character
                currentPart += char;
            }
            i++;
        }
        
        // Add any remaining content
        if (currentPart) {
            parts.push({ type: inString ? 'string' : 'code', content: currentPart });
        }
        
        // Process only the code parts (not strings)
        const processedParts = parts.map(part => {
            if (part.type === 'string') {
                return part.content; // Keep strings unchanged
            } else {
                // Apply Python to JavaScript conversion only to code parts
                return part.content
                    .replace(/\band\b/g, '&&')
                    .replace(/\bor\b/g, '||')
                    .replace(/\bnot\b/g, '!')
                    .replace(/\bTrue\b/g, 'true')
                    .replace(/\bFalse\b/g, 'false')
                    .replace(/\bNone\b/g, 'null');
            }
        });
        
        return processedParts.join('');
    }

    // Function to evaluate dynamic min/max values
    function evaluateDynamicValue(expression) {
        if (!expression) return null;

        // If it's just a number, return it
        const numValue = parseFloat(expression);
        if (!isNaN(numValue)) {
            return numValue;
        }

        // Handle ternary operator: condition ? value1 : value2
        if (expression.includes('?') && expression.includes(':')) {
            const parts = expression.split('?');
            if (parts.length === 2) {
                const condition = parts[0].trim();
                const valueParts = parts[1].split(':');
                if (valueParts.length === 2) {
                    const trueValue = parseFloat(valueParts[0].trim());
                    const falseValue = parseFloat(valueParts[1].trim());

                    // Evaluate condition
                    if (evaluateSimpleCondition(condition)) {
                        return trueValue;
                    } else {
                        return falseValue;
                    }
                }
            }
        }

        return null;
    }

    // Function to evaluate simple conditions like "field > 2510"
    function evaluateSimpleCondition(condition) {
        try {
            // Replace field names with their values
            let evalCondition = condition;
            for (const [fieldName, value] of Object.entries(fieldValues)) {
                if (evalCondition.includes(fieldName)) {
                    evalCondition = evalCondition.replace(new RegExp(fieldName, 'g'), value);
                }
            }

            // Simple operator evaluation
            if (evalCondition.includes('>')) {
                const parts = evalCondition.split('>');
                if (parts.length === 2) {
                    const left = parseFloat(parts[0].trim());
                    const right = parseFloat(parts[1].trim());
                    return left > right;
                }
            } else if (evalCondition.includes('<')) {
                const parts = evalCondition.split('<');
                if (parts.length === 2) {
                    const left = parseFloat(parts[0].trim());
                    const right = parseFloat(parts[1].trim());
                    return left < right;
                }
            } else if (evalCondition.includes('==')) {
                const parts = evalCondition.split('==');
                if (parts.length === 2) {
                    const left = parts[0].trim();
                    const right = parts[1].trim();
                    return left === right;
                }
            }
        } catch (error) {
            // Silently handle simple condition errors to reduce console spam
            if (window.debugConditions) {
                console.warn('Simple condition evaluation error:', condition, error.message);
            }
        }
        return false;
    }

    // Function to update field constraints based on current values (display removed per customer request)
    function updateFieldConstraints() {
        try {
            document.querySelectorAll('.config-field[data-field-type="number"]').forEach(field => {
                const minExpr = field.getAttribute('data-min');
                const maxExpr = field.getAttribute('data-max');

                if (minExpr) {
                    const minValue = evaluateDynamicValue(minExpr);
                    if (minValue !== null) {
                        field.setAttribute('min', minValue);
                    }
                }

                if (maxExpr) {
                    const maxValue = evaluateDynamicValue(maxExpr);
                    if (maxValue !== null) {
                        field.setAttribute('max', maxValue);
                    }
                }
            });

            // Constraints display removed per customer request
        } catch (error) {
            console.error("Field constraints update error:", error);
        }
    }

    // Function to update constraints display text - removed per customer request

    // Function to update dynamic help text
    function updateDynamicHelp() {
        try {
            // Find all containers with dynamic help enabled
            const helpContainers = document.querySelectorAll('[data-use-dynamic-help="true"]');

            helpContainers.forEach((container, index) => {
                const template = container.getAttribute('data-dynamic-help-template');

                if (template) {
                    const dynamicHelp = evaluateTemplate(template);
                    const dynamicHelpDiv = container.querySelector('.dynamic-help-text');
                    const staticHelpDiv = container.querySelector('.static-help-text');

                    if (dynamicHelpDiv && staticHelpDiv) {
                        dynamicHelpDiv.innerHTML = dynamicHelp;
                        dynamicHelpDiv.style.display = 'block';
                        staticHelpDiv.style.display = 'none';
                    }
                }
            });
        } catch (error) {
            console.error("Dynamic help update error:", error);
        }
    }

    // Function to update dynamic default values
    function updateDynamicDefaults(excludeEvent = null) {
        // CRITICAL FIX: Prevent infinite loop
        if (isUpdatingDynamicDefaults) {
            console.log('[PERF] Skipping updateDynamicDefaults - already in progress');
            return;
        }

        isUpdatingDynamicDefaults = true;

        try {
            // Get the field ID that triggered the event (if any)
            const excludeFieldId = excludeEvent ? excludeEvent.target.getAttribute('data-field-id') : null;

            // Find all containers with dynamic defaults enabled
            const defaultContainers = document.querySelectorAll('[data-use-dynamic-default="true"]');

            defaultContainers.forEach((container, index) => {
                const template = container.getAttribute('data-dynamic-default-template');
                const fieldId = container.getAttribute('data-field-id');
                const technicalName = container.getAttribute('data-technical-name');

                // Skip if this is the field that triggered the change event
                if (excludeFieldId && fieldId === excludeFieldId) {
                    console.log(`[PERF] Skipping dynamic default for field ${fieldId} - it triggered the change event`);
                    return;
                }

                if (template && fieldId && technicalName) {
                    const currentValue = fieldValues[technicalName];
                    const dynamicDefault = evaluateTemplate(template);

                    // Set dynamic default if:
                    // 1. Field is empty, OR
                    // 2. Dynamic default has changed and is different from current value
                    if ((!currentValue || currentValue === '') ||
                        (dynamicDefault && dynamicDefault !== '' && dynamicDefault !== currentValue)) {

                        // Find the field element and update it
                        const fieldElement = document.querySelector(`[data-field-id="${fieldId}"].config-field`);
                        if (fieldElement) {
                            const fieldType = fieldElement.getAttribute('data-field-type');

                            if (fieldType === 'selection') {
                                fieldElement.value = dynamicDefault;
                            } else if (fieldType === 'boolean') {
                                fieldElement.checked = dynamicDefault === 'true' || dynamicDefault === true;
                            } else {
                                fieldElement.value = dynamicDefault;
                            }

                            // Update the field values object
                            setFieldValue(technicalName, dynamicDefault);

                            // Update hidden form field
                            const hiddenField = document.getElementById('hidden_field_' + fieldId);
                            if (hiddenField) {
                                hiddenField.value = dynamicDefault;
                            }

                            // CRITICAL FIX: Don't trigger change event to prevent infinite recursion
                            // Instead, manually update visibility and other dependencies
                            updateDynamicHelp();
                            updateErrorMessages();
                        }
                    }
                }
            });
        } catch (error) {
            console.error('Error updating dynamic defaults:', error);
        } finally {
            // CRITICAL FIX: Always reset the flag to prevent permanent blocking
            isUpdatingDynamicDefaults = false;
        }
    }

    // Function to evaluate template expressions
    function evaluateTemplate(template) {
        if (!template) return '';

        try {
            const context = { ...fieldValues };

            // Add calculated fields to the context
            try {
                const calculatedFields = calculateDynamicFields(fieldValues);
                Object.assign(context, calculatedFields);
            } catch (error) {
                console.warn('Error calculating fields for template evaluation:', error);
            }

            // Create optimized field name mapping with priority system
            const fieldNameMapping = { ...context };

            // Priority: exact > numeric > alphanumeric
            const priorityMap = new Map();

            for (const [key, value] of Object.entries(context)) {
                const exact = key;
                const numeric = key.match(/^(.+?)_(\d+)$/);
                const alphanumeric = key.match(/^(.+?)[_-]([a-zA-Z0-9_]+)$/);

                let baseName, priority;

                if (numeric) {
                    baseName = numeric[1];
                    priority = 1 + parseInt(numeric[2]) / 1000; // 1.001, 1.002, etc.
                } else if (alphanumeric) {
                    baseName = alphanumeric[1];
                    priority = 2; // Fixed priority for alphanumeric
                } else {
                    baseName = exact;
                    priority = 0; // Highest priority for exact match
                }

                // Only update if this is a better priority or doesn't exist
                if (!priorityMap.has(baseName) || priority < priorityMap.get(baseName).priority) {
                    priorityMap.set(baseName, { value, priority });
                }
            }

            // Add base name mappings
            for (const [baseName, { value }] of priorityMap) {
                if (baseName !== context[baseName]) { // Avoid duplicate mapping
                    fieldNameMapping[baseName] = value;
                }
            }

            // Add mathematical functions
            Object.assign(fieldNameMapping, {
                min: Math.min,
                max: Math.max,
                abs: Math.abs,
                round: Math.round
            });

            let result = template;

            // Replace {expression} patterns
            result = result.replace(/\{([^}]+)\}/g, (match, expression) => {
                try {
                    const cleanExpression = cleanExpressionForJS(expression);
                    const func = new Function(...Object.keys(fieldNameMapping), `return ${cleanExpression};`);
                    return String(func(...Object.values(fieldNameMapping)));
                } catch (e) {
                    console.warn(`Error evaluating expression: ${expression}`, e);
                    return `[${expression}]`;
                }
            });

            return result;
        } catch (error) {
            console.error(`Error evaluating template: ${template}`, error);
            return template;
        }
    }

    // Function to evaluate error conditions and update error messages
    function updateErrorMessages() {
        document.querySelectorAll('.error-message-container').forEach(container => {
            const fieldId = container.getAttribute('data-field-id');
            const useDynamic = container.getAttribute('data-use-dynamic-error') === 'true';
            const template = container.getAttribute('data-dynamic-error-template');
            const errorCondition = container.getAttribute('data-error-condition');
            const useCase = container.getAttribute('data-use-case');

            let showError = false;
            let errorMessage = '';

            // Check if error condition is met
            if (errorCondition && errorCondition.trim() !== '') {
                try {
                    showError = evaluateCondition(errorCondition);
                } catch (error) {
                    console.error('Error evaluating error condition for field', fieldId, ':', error);
                    showError = false;
                }
            }

            if (showError) {
                if (useDynamic && template) {
                    // Generate dynamic error message using the same template evaluation as help text
                    errorMessage = evaluateTemplate(template);

                    // Update the dynamic error text
                    const dynamicSpan = container.querySelector('.dynamic-error-text');
                    const staticSpan = container.querySelector('.static-error-text');

                    if (dynamicSpan) {
                        dynamicSpan.innerHTML = errorMessage;
                        dynamicSpan.style.display = 'inline';
                    }
                    if (staticSpan) {
                        staticSpan.style.display = 'none';
                    }
                } else {
                    // Show static error text
                    const dynamicSpan = container.querySelector('.dynamic-error-text');
                    const staticSpan = container.querySelector('.static-error-text');

                    if (dynamicSpan) {
                        dynamicSpan.style.display = 'none';
                    }
                    if (staticSpan) {
                        staticSpan.style.display = 'inline';
                    }
                }

                // Always show the error container when condition is met
                // Remove any Bootstrap dismissal classes that might hide it
                const alertDiv = container.querySelector('.alert');
                if (alertDiv) {
                    alertDiv.classList.remove('d-none');
                    alertDiv.style.display = '';
                }
                container.style.display = 'block';

                // Add a subtle animation to draw attention to persistent errors
                if (!container.classList.contains('error-visible')) {
                    container.classList.add('error-visible');
                    container.style.animation = 'errorPulse 0.5s ease-in-out';
                    setTimeout(() => {
                        container.style.animation = '';
                    }, 500);
                }
            } else {
                // Hide the error container only when condition is not met
                container.style.display = 'none';
                container.classList.remove('error-visible');
            }
        });
    }

    // Function to evaluate a single condition
    function evaluateSingleCondition(condition) {
        try {
            // Check for "in" and "not in" operators with array syntax
            const inListRegex = /^([a-zA-Z0-9_]+)\s+(in|not\s+in)\s+\[(.*)\]$/i;
            const inListMatch = condition.match(inListRegex);

            if (inListMatch) {
                const [_, fieldName, operator, valueList] = inListMatch;
                const fieldValue = fieldValues[fieldName];

                // Parse the array values
                const listValues = valueList.split(',').map(item => {
                    // Remove quotes and trim whitespace
                    return item.trim().replace(/^['"]|['"]$/g, '');
                });

                // Check if the field value is in the list
                const isInList = listValues.includes(fieldValue);
                const result = operator.toLowerCase() === 'in' ? isInList : !isInList;

                return result;
            }

            // Extract field names from the condition to add missing ones
            const fieldNames = condition.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || [];

            // Create a safe evaluation context with field values
            const evalContext = {};

            // Add all field values to the context
            for (const [key, value] of Object.entries(fieldValues)) {
                evalContext[key] = value;
            }

            // Add calculated fields to the context
            try {
                const calculatedFields = calculateDynamicFields(fieldValues);
                for (const [key, value] of Object.entries(calculatedFields)) {
                    evalContext[key] = value;
                }
            } catch (error) {
                console.warn('Error calculating fields for visibility evaluation:', error);
            }

            // Add missing fields with default values to prevent ReferenceError
            fieldNames.forEach(fieldName => {
                if (evalContext[fieldName] === undefined) {
                    evalContext[fieldName] = ''; // Default to empty string for missing fields
                }
            });

            // Add Math functions to the context
            evalContext.Math = Math;
            evalContext.min = Math.min;
            evalContext.max = Math.max;
            evalContext.abs = Math.abs;
            evalContext.round = Math.round;
            evalContext.floor = Math.floor;
            evalContext.ceil = Math.ceil;

            // Clean the condition for JavaScript
            const cleanCondition = cleanExpressionForJS(condition);

            // Create a function with the context variables
            const evalFunc = new Function(...Object.keys(evalContext), `return ${cleanCondition};`);

            // Call the function with the context values
            const result = evalFunc(...Object.values(evalContext));
            return result;
        } catch (error) {
            // Only log syntax errors once per condition to avoid spam
            if (!window.loggedConditionErrors) {
                window.loggedConditionErrors = new Set();
            }
            if (!window.loggedConditionErrors.has(condition)) {
                console.warn('Visibility condition syntax error:', condition.substring(0, 100) + (condition.length > 100 ? '...' : ''), error.message);
                window.loggedConditionErrors.add(condition);
            }
            return true; // Default to visible on error
        }
    }

    // Function to evaluate visibility condition
    function evaluateCondition(conditionStr) {
        // Check if this is a JSON condition
        if (conditionStr && conditionStr.startsWith('__JSON__')) {
            try {
                // Extract the JSON part
                const jsonStr = conditionStr.substring(8);
                const conditions = JSON.parse(jsonStr);

                // Group conditions by logic operator
                const andConditions = conditions.filter(c => c.logic === 'and');
                const orConditions = conditions.filter(c => c.logic === 'or');

                // Evaluate AND conditions - all must be true
                const andResult = andConditions.length === 0 ||
                    andConditions.every(c => evaluateSingleCondition(c.condition));

                // Evaluate OR conditions - at least one must be true
                const orResult = orConditions.length === 0 ||
                    orConditions.some(c => evaluateSingleCondition(c.condition));

                // Both AND and OR conditions must be satisfied
                return andResult && orResult;
            } catch (error) {
                // Only log JSON parsing errors once to avoid spam
                if (!window.loggedJSONErrors) {
                    window.loggedJSONErrors = new Set();
                }
                const errorKey = conditionStr.substring(0, 50);
                if (!window.loggedJSONErrors.has(errorKey)) {
                    console.warn('JSON condition parsing error:', errorKey + '...', error.message);
                    window.loggedJSONErrors.add(errorKey);
                }
                return true; // Default to visible on error
            }
        } else {
            // Regular condition
            return evaluateSingleCondition(conditionStr);
        }
    }

    // Function to update field visibility
    function updateFieldVisibility() {
        const timestamp = new Date().toISOString().substring(11, 23);
        console.log(`[${timestamp}] updateFieldVisibility >> (called from:`, new Error().stack.split('\n')[2].trim(), ')');
        // CRITICAL FIX: Prevent infinite loop
        if (isUpdatingFieldVisibility) {
            console.log('[PERF] Skipping updateFieldVisibility - already in progress');
            return;
        }

        isUpdatingFieldVisibility = true;

        try {
            // CRITICAL FIX: Add null check for show-all-fields element
            const showAllFieldsElement = document.getElementById('show-all-fields');
            const showAllFields = showAllFieldsElement ? showAllFieldsElement.checked : false;

            // Track if any color fields changed visibility
            let colorFieldsVisibilityChanged = false;

            // Track fields that become hidden to clear their values
            const fieldsToClean = [];
            // Track fields that become visible to restore default values
            const fieldsToRestore = [];

        // Update field containers
        document.querySelectorAll('.config-field-container').forEach(container => {
            const condition = container.getAttribute('data-visibility-condition');
            const isConditional = container.getAttribute('data-is-conditional') === 'true';
            const fieldId = container.getAttribute('data-field-id');
            const technicalName = container.getAttribute('data-technical-name');

            if (condition && condition !== 'true') {
                const conditionMet = evaluateCondition(condition);

                // Store previous visibility state
                const wasVisible = container.style.display !== 'none';

                // Determine visibility based on toggle and condition
                let isVisible = showAllFields || conditionMet;

                // Check if this is a color field that changed visibility
                if (globalColorFields.some(f => f.id === fieldId)) {
                    colorFieldsVisibilityChanged = true;
                }

                // ENHANCED: Detect fields that become hidden and have values
                if (wasVisible && !conditionMet && technicalName) {
                    // Field was visible but now condition is not met
                    const fieldValue = window.fieldValues && window.fieldValues[technicalName];
                    const hasValue = fieldValue !== undefined && fieldValue !== null && fieldValue !== '' && fieldValue !== 0;

                    if (hasValue) {
                        console.log(`Field ${technicalName} became hidden but has value: ${fieldValue}`);
                        fieldsToClean.push({
                            technicalName: technicalName,
                            fieldId: fieldId,
                            container: container,
                            value: fieldValue
                        });
                    }
                }

                // ENHANCED: Detect fields that become visible and need default values
                if (!wasVisible && isVisible && technicalName) {
                    // Field was hidden but now condition is met
                    const fieldValue = window.fieldValues && window.fieldValues[technicalName];
                    const hasNoValue = fieldValue === undefined || fieldValue === null || fieldValue === '' || fieldValue === 0;

                    if (hasNoValue) {
                        // Check if this field actually has a default value before restoring
                        const fieldElement = container.querySelector('.config-field');
                        if (fieldElement) {
                            const defaultValue = fieldElement.getAttribute('data-default-value');
                            const fieldType = fieldElement.getAttribute('data-field-type');
                            
                            let shouldRestore = false;
                            
                            if (fieldType === 'boolean') {
                                // For boolean fields, check if there's a default value
                                shouldRestore = defaultValue === 'true' || defaultValue === true;
                            } else if (fieldType === 'selection') {
                                // For selection fields, check if there's a default option
                                shouldRestore = defaultValue && defaultValue !== '';
                            } else {
                                // For text, number, date fields, check if there's a default value
                                shouldRestore = defaultValue && defaultValue !== '';
                            }
                            
                            if (shouldRestore) {
                                console.log(`Field ${technicalName} became visible but has no value, restoring default: ${defaultValue}`);
                                fieldsToRestore.push({
                                    technicalName: technicalName,
                                    fieldId: fieldId,
                                    container: container
                                });
                            }
                        }
                    }
                }

                // If showing all fields, still highlight those with unmet conditions
                if (showAllFields && !conditionMet) {
                    // Add a class to indicate this is a conditional field that's not yet available
                    container.classList.add('conditional-field-pending');
                } else {
                    container.classList.remove('conditional-field-pending');
                }

                container.style.display = isVisible ? 'table-row' : 'none';

                // Add green highlight for conditional fields that are visible due to met conditions
                if (conditionMet) {
                    container.classList.add('conditional-visible');
                    // Add light green background to the entire row
                    container.style.backgroundColor = '#e6ffe6';
                } else {
                    container.classList.remove('conditional-visible');
                    container.style.backgroundColor = '';
                }
            }
        });

        // ENHANCED: Clear values of fields that became hidden
        if (fieldsToClean.length > 0) {
            console.log(`Auto-clearing ${fieldsToClean.length} fields that became hidden:`, fieldsToClean.map(f => f.technicalName));

            fieldsToClean.forEach(fieldInfo => {
                // Clear from window.fieldValues
                if (window.fieldValues && window.fieldValues[fieldInfo.technicalName] !== undefined) {
                    delete window.fieldValues[fieldInfo.technicalName];
                }

                // Clear UI element
                const fieldElement = fieldInfo.container.querySelector('.config-field');
                if (fieldElement) {
                    const fieldType = fieldElement.getAttribute('data-field-type');
                    if (fieldType === 'boolean') {
                        fieldElement.checked = false;
                    } else {
                        fieldElement.value = '';
                    }
                }

                // Clear hidden form field
                const hiddenField = document.getElementById('hidden_field_' + fieldInfo.fieldId);
                if (hiddenField) {
                    hiddenField.value = '';
                }
            });
        }

        // ENHANCED: Restore default values for fields that became visible
        if (fieldsToRestore.length > 0) {
            console.log(`Restoring defaults for ${fieldsToRestore.length} fields that became visible:`, fieldsToRestore.map(f => f.technicalName));

            fieldsToRestore.forEach(fieldInfo => {
                const fieldElement = fieldInfo.container.querySelector('.config-field');
                if (fieldElement) {
                    const fieldType = fieldElement.getAttribute('data-field-type');
                    const fieldId = fieldElement.getAttribute('data-field-id');
                    const technicalName = fieldElement.getAttribute('data-technical-name');
                    const defaultValue = fieldElement.getAttribute('data-default-value');

                    let defaultVal = '';

                    if (fieldType === 'boolean') {
                        // For boolean fields, check if default is true
                        defaultVal = defaultValue === 'true' || defaultValue === true;
                        fieldElement.checked = defaultVal;
                    } else if (fieldType === 'selection') {
                        // For selection fields, try to find the default option
                        if (defaultValue) {
                            // Try to find option with this value
                            const defaultOption = Array.from(fieldElement.options).find(opt => 
                                opt.value === defaultValue || opt.getAttribute('data-option-value') === defaultValue
                            );
                            if (defaultOption && defaultOption.style.display !== 'none') {
                                fieldElement.value = defaultOption.value;
                                defaultVal = defaultOption.value;
                            }
                        }
                        // Note: Removed auto-selection of first option to preserve original logic
                    } else {
                        // For text, number, date fields
                        defaultVal = defaultValue || '';
                        fieldElement.value = defaultVal;
                    }

                    // Update field values object only if we have a valid default value
                    if (technicalName && defaultVal !== '' && defaultVal !== false) {
                        setFieldValue(technicalName, defaultVal);
                    }

                    // Update hidden form field
                    const hiddenField = document.getElementById('hidden_field_' + fieldId);
                    if (hiddenField) {
                        hiddenField.value = defaultVal;
                    }

                    // PERFORMANCE FIX: Don't trigger change event immediately to prevent cascade lag
                    // Instead, we'll handle dependencies in the cascade update below
                }
            });
        }

            // PERFORMANCE FIX: Use debounced cascade update to prevent multiple rapid calls
        if (fieldsToClean.length > 0 || fieldsToRestore.length > 0) {
            console.log(`[PERF] Scheduling cascade update for ${fieldsToClean.length + fieldsToRestore.length} field changes`);
            setTimeout(() => {
                updateFieldVisibilityDebounced(); // Use debounced version
            }, 50);
        }

        // Update selection options
        document.querySelectorAll('select.config-field').forEach(select => {
            const technicalName = select.getAttribute('data-technical-name');
            const defaultValue = select.getAttribute('data-default-value');
            let hasVisibleOptions = false;
            let selectedOptionValue = '';

            Array.from(select.options).forEach(option => {
                const condition = option.getAttribute('data-visibility-condition');
                if (condition && condition !== 'true') {
                    const isVisible = evaluateCondition(condition);
                    // Hide unavailable options completely instead of showing them as disabled
                    option.style.display = isVisible ? '' : 'none';
                    // Remove disabled state since we're hiding the option entirely
                    option.disabled = false;

                    // Track if we have any visible options
                    if (isVisible) {
                        hasVisibleOptions = true;
                        if (option.selected) {
                            selectedOptionValue = option.value;
                        }
                    }

                    // If the option is selected but not visible, clear the selection
                    if (option.selected && !isVisible) {
                        option.selected = false;
                        // Clear the field value since the selected option is now hidden
                        if (technicalName && window.fieldValues) {
                            delete window.fieldValues[technicalName];
                        }
                    }
                } else {
                    // Options without conditions are always visible
                    hasVisibleOptions = true;
                    if (option.selected) {
                        selectedOptionValue = option.value;
                    }
                }
            });

            // If no options are visible, clear the field value
            if (!hasVisibleOptions && technicalName) {
                if (window.fieldValues && window.fieldValues[technicalName] !== undefined) {
                    delete window.fieldValues[technicalName];
                }
                select.value = '';
                
                // Clear hidden form field
                const fieldId = select.getAttribute('data-field-id');
                const hiddenField = document.getElementById('hidden_field_' + fieldId);
                if (hiddenField) {
                    hiddenField.value = '';
                }
            } else if (selectedOptionValue) {
                // If we have a valid selected option, update the field value
                if (technicalName) {
                    setFieldValue(technicalName, selectedOptionValue);
                }
            } else if (hasVisibleOptions && technicalName && (defaultValue === '' || defaultValue)) {
                // Only restore default if field actually has a default value and no current selection
                const currentValue = window.fieldValues && window.fieldValues[technicalName];
                if (!currentValue || currentValue === '') {
                    // Try to find default option
                    const defaultOption = Array.from(select.options).find(opt => 
                        (opt.value === defaultValue || opt.getAttribute('data-option-value') === defaultValue) &&
                        opt.style.display !== 'none'
                    );
                    
                    if (defaultOption) {
                        defaultOption.selected = true;
                        select.value = defaultOption.value;
                        setFieldValue(technicalName, defaultOption.value);
                        
                        // Update hidden form field
                        const fieldId = select.getAttribute('data-field-id');
                        const hiddenField = document.getElementById('hidden_field_' + fieldId);
                        if (hiddenField) {
                            hiddenField.value = defaultOption.value;
                        }
                    }
                }
            }
        });

        // If color fields visibility changed, update the global color selector
        if (colorFieldsVisibilityChanged) {
            // Re-identify color fields and update the selector
            identifyColorFields();
            populateGlobalColorSelector();
        }

        } catch (error) {
            console.error('❌ Error in updateFieldVisibility:', error);
            // Don't throw the error to prevent breaking the entire system
        } finally {
            // CRITICAL FIX: Always reset the flag to prevent permanent blocking
            isUpdatingFieldVisibility = false;
        }
    }

    // Function to update visibility conditions display
    function updateVisibilityConditionsDisplay() {
        // CRITICAL FIX: Add null check for show-visibility-conditions element
        const showConditionsElement = document.getElementById('show-visibility-conditions');
        const showConditions = showConditionsElement ? showConditionsElement.checked : false;

        // Toggle field visibility conditions
        document.querySelectorAll('.visibility-conditions-info').forEach(container => {
            if (showConditions) {
                container.classList.add('show');

                // Update the condition display with current evaluation
                const conditionExpr = container.querySelector('.condition-expression');
                if (conditionExpr) {
                    const condition = conditionExpr.getAttribute('data-condition');
                    if (condition && condition !== 'true') {
                        const isConditionMet = evaluateCondition(condition);
                        const conditionItem = conditionExpr.closest('.visibility-condition-item');
                        if (conditionItem) {
                            conditionItem.style.borderLeftColor = isConditionMet ? '#28a745' : '#dc3545';
                            conditionItem.style.backgroundColor = isConditionMet ? '#f8fff8' : '#fff8f8';
                        }

                        // Add evaluation result
                        let evaluationText = conditionExpr.textContent;
                        if (!evaluationText.includes(' → ')) {
                            conditionExpr.textContent = evaluationText + ` → ${isConditionMet ? 'TRUE' : 'FALSE'}`;
                        }
                    }
                }
            } else {
                container.classList.remove('show');
            }
        });

        // Update searchable dropdown option conditions
        document.querySelectorAll('.searchable-dropdown-item .option-condition-text').forEach(conditionText => {
            if (showConditions) {
                conditionText.style.display = 'block';

                // Update condition evaluation
                const condition = conditionText.getAttribute('data-condition');
                if (condition && condition !== 'true') {
                    const isConditionMet = evaluateCondition(condition);
                    conditionText.className = `option-condition-text ${isConditionMet ? 'condition-met' : 'condition-not-met'}`;

                    // Check if this is a multiple condition (JSON format)
                    if (condition.startsWith('__JSON__')) {
                        try {
                            const jsonStr = condition.substring(8);
                            const conditions = JSON.parse(jsonStr);

                            let metCount = 0;
                            let totalCount = conditions.length;
                            let detailedText = '';

                            conditions.forEach((c, index) => {
                                const conditionMet = evaluateSingleCondition(c.condition);
                                if (conditionMet) {
                                    metCount++;
                                }

                                const logicText = index > 0 ? ` ${c.logic.toUpperCase()} ` : '';
                                const statusIcon = conditionMet ? '✓' : '✗';
                                const shortCondition = c.condition.length > 20 ? c.condition.substring(0, 20) + '...' : c.condition;
                                detailedText += `${logicText}${statusIcon} ${shortCondition}`;
                            });

                            conditionText.textContent = `${metCount}/${totalCount} conditions met → ${isConditionMet ? 'TRUE' : 'FALSE'}`;
                            conditionText.setAttribute('title', `Details: ${detailedText}`);

                        } catch (e) {
                            // Fallback for invalid JSON
                            let currentText = conditionText.textContent.trim();
                            if (!currentText.includes(' → ')) {
                                conditionText.textContent = currentText + ` → ${isConditionMet ? 'TRUE' : 'FALSE'}`;
                            }
                        }
                    } else {
                        // Single condition
                        let currentText = conditionText.textContent.trim();
                        if (!currentText.includes(' → ')) {
                            conditionText.textContent = currentText + ` → ${isConditionMet ? 'TRUE' : 'FALSE'}`;
                        }
                        const statusIcon = isConditionMet ? '✓' : '✗';
                        conditionText.setAttribute('title', `Condition: ${statusIcon} ${condition}`);
                    }
                } else {
                    conditionText.className = 'option-condition-text';
                }
            } else {
                conditionText.style.display = 'none';
            }
        });

        // Update regular dropdown options with conditions
        document.querySelectorAll('select.config-field').forEach(select => {
            Array.from(select.options).forEach(option => {
                if (showConditions) {
                    const condition = option.getAttribute('data-visibility-condition');
                    const originalText = option.textContent.split(' [')[0]; // Remove any existing condition text

                    let displayText;
                    let isConditionMet = true; // Default for options with no conditions

                    if (condition && condition !== 'true') {
                        isConditionMet = evaluateCondition(condition);
                        const statusText = isConditionMet ? 'TRUE' : 'FALSE';

                        // Check if this is a multiple condition (JSON format)
                        if (condition.startsWith('__JSON__')) {
                            try {
                                const jsonStr = condition.substring(8);
                                const conditions = JSON.parse(jsonStr);

                                // Evaluate each condition and count met/total
                                let metCount = 0;
                                let totalCount = conditions.length;
                                let conditionDetails = [];

                                conditions.forEach((c, index) => {
                                    const conditionMet = evaluateSingleCondition(c.condition);
                                    if (conditionMet) {
                                        metCount++;
                                    }

                                    // Create detailed status for tooltip
                                    const logicText = index > 0 ? c.logic.toUpperCase() : "";
                                    const statusIcon = conditionMet ? "✓" : "✗";
                                    const shortCondition = c.condition.length > 25 ? c.condition.substring(0, 25) + "..." : c.condition;
                                    conditionDetails.push(`${logicText} ${statusIcon} ${shortCondition}`.trim());
                                });

                                displayText = `${metCount}/${totalCount} conditions met → ${statusText}`;

                                // Add detailed tooltip
                                const detailsText = conditionDetails.join(' | ');
                                option.setAttribute('title', `Conditions: ${detailsText}`);

                            } catch (e) {
                                // Fallback for invalid JSON
                                displayText = `1 condition → ${statusText}`;
                            }
                        } else {
                            // Single condition
                            displayText = `1/1 conditions met → ${statusText}`;
                            const conditionMet = isConditionMet;
                            const statusIcon = conditionMet ? "✓" : "✗";
                            const shortCondition = condition.length > 30 ? condition.substring(0, 30) + "..." : condition;
                            option.setAttribute('title', `Condition: ${statusIcon} ${shortCondition}`);
                        }
                    } else {
                        // No conditions - always visible
                        displayText = `0/0 conditions met → TRUE`;
                        isConditionMet = true;
                    }

                    option.textContent = `${originalText} [${displayText}]`;

                    // Style the option based on condition status
                    if (isConditionMet) {
                        option.style.color = '#28a745';
                        option.style.fontWeight = 'bold';
                    } else {
                        option.style.color = '#dc3545';
                        option.style.fontWeight = 'normal';
                    }
                } else {
                    // Reset option text to original when not showing conditions
                    const originalText = option.textContent.split(' [')[0];
                    option.textContent = originalText;
                    option.style.color = '';
                    option.style.fontWeight = '';
                }
            });
        });

        // Legacy support for old option visibility info elements
        document.querySelectorAll('.option-visibility-info').forEach(info => {
            if (showConditions) {
                info.style.display = 'block';

                // Update option condition evaluation
                const condition = info.getAttribute('data-condition');
                if (condition && condition !== 'true') {
                    const isConditionMet = evaluateCondition(condition);
                    info.style.color = isConditionMet ? '#28a745' : '#dc3545';
                    info.style.fontWeight = isConditionMet ? 'bold' : 'normal';
                }
            } else {
                info.style.display = 'none';
            }
        });
    }

    // Function to initialize all field values with defaults
    function initializeAllFieldsWithDefaults() {
        // First, initialize all fields with default values to prevent ReferenceError
        document.querySelectorAll('.config-field').forEach(field => {
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');

            if (technicalName && fieldValues[technicalName] === undefined) {
                // Set default values based on field type
                if (fieldType === 'boolean') {
                    fieldValues[technicalName] = false;
                } else if (fieldType === 'number') {
                    fieldValues[technicalName] = 0;
                } else {
                    fieldValues[technicalName] = '';
                }
                // Invalidate cache when setting default values
                invalidateCalculatedFieldsCache();
                // Only log if debugging is enabled
                if (window.debugConditions) {
                    console.log(`Initialized field ${technicalName} with default value:`, fieldValues[technicalName]);
                }
            }
        });
    }

    // Function to initialize field values
    function initializeFieldValues() {
        // First initialize all fields with defaults
        initializeAllFieldsWithDefaults();

        document.querySelectorAll('.config-field').forEach(field => {
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');

            if (technicalName) {
                let value;

                if (fieldType === 'boolean') {
                    value = field.checked;
                } else if (fieldType === 'number') {
                    // For number fields (which are now text inputs), handle empty or invalid values
                    if (field.value === '' || field.value === '-' || field.value === '.') {
                        // If the field is empty or has just a minus sign or decimal point
                        // Leave it as is for now - it will be validated on blur
                        value = field.value;
                    } else {
                        // Otherwise parse as a number
                        value = parseFloat(field.value) || 0;

                        // Check min/max constraints but don't auto-correct
                        const min = parseFloat(field.getAttribute('data-min'));
                        const max = parseFloat(field.getAttribute('data-max'));

                        // Keep the original value even if it's outside the range
                        // The validation will show warnings but won't auto-correct
                        if (!isNaN(min) && value < min) {
                            console.log(`Field ${field.getAttribute('data-field-id')} value ${value} is below minimum ${min} - keeping original value`);
                        }
                        if (!isNaN(max) && value > max) {
                            console.log(`Field ${field.getAttribute('data-field-id')} value ${value} is above maximum ${max} - keeping original value`);
                        }

                        // Format the number with appropriate decimal places
                        const decimalPrecision = parseInt(field.getAttribute('data-decimal-precision')) || 0;

                        if (decimalPrecision > 0 && !isNaN(value)) {
                            field.value = value.toFixed(decimalPrecision);
                        } else if (decimalPrecision === 0 && !isNaN(value)) {
                            // For whole numbers, ensure no decimal places
                            field.value = Math.round(value).toString();
                        }
                    }
                } else if (fieldType === 'selection') {
                    // All selection fields are now regular select elements
                    const selectedOption = field.options[field.selectedIndex];
                    value = selectedOption ? (selectedOption.getAttribute('data-option-value') || selectedOption.value) : '';
                } else if (fieldType === 'date') {
                    value = field.value; // Date in YYYY-MM-DD format
                } else {
                    value = field.value;
                }

                setFieldValue(technicalName, value);

                // Initialize hidden field for form submission
                const fieldId = field.getAttribute('data-field-id');
                const hiddenField = document.getElementById('hidden_field_' + fieldId);
                if (hiddenField) {
                    hiddenField.value = value;
                }
            }
        });
    }

    // All fields are required by default

    // Function to expand instructional image in modal
    function expandInstructionalImage(imageUrl, fieldId) {
        const modal = new bootstrap.Modal(document.getElementById('instructionalImageModal'));
        const expandedImage = document.getElementById('expandedInstructionalImage');

        // Set the image source
        expandedImage.src = imageUrl;

        // Store the field ID on the modal for reference
        document.getElementById('instructionalImageModal').setAttribute('data-field-id', fieldId);

        // Show the modal
        modal.show();
    }

    // Function to handle instructional images
    function setupInstructionalImages() {
        // Handle click on "Show Instructions" links
        document.querySelectorAll('.show-instructions-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const fieldId = this.getAttribute('data-field-id');
                const showInPreview = this.getAttribute('data-show-in-preview') === 'true';

                // Create the image URL
                const imageUrl = '/web/image/config.matrix.field/' + fieldId + '/instructional_image';

                // Toggle the preview panel
                const previewContainer = document.getElementById('instructional-image-preview');
                const previewContent = document.getElementById('instructional-image-preview-content');

                // Check if we're already showing this image
                const isShowing = previewContainer.style.display === 'block' &&
                                 previewContainer.getAttribute('data-current-field') === fieldId;

                if (isShowing) {
                    // Hide the preview if it's already showing
                    previewContainer.style.display = 'none';
                    previewContainer.removeAttribute('data-current-field');
                } else {
                    // Show in preview panel if enabled
                    if (showInPreview) {
                        // Create a new image element
                        const img = document.createElement('img');
                        img.src = imageUrl;
                        img.className = 'img-fluid instructional-image';
                        img.style.maxHeight = '200px';
                        img.style.border = '1px solid #dee2e6';
                        img.style.borderRadius = '4px';
                        img.style.cursor = 'pointer';

                        // Add click handler to expand the image
                        img.addEventListener('click', function() {
                            expandInstructionalImage(imageUrl, fieldId);
                        });

                        // Clear previous content and add the new image
                        previewContent.innerHTML = '';
                        previewContent.appendChild(img);
                        previewContainer.style.display = 'block';
                        previewContainer.setAttribute('data-current-field', fieldId);
                    } else {
                        // If not showing in preview, just expand the image directly
                        expandInstructionalImage(imageUrl, fieldId);
                    }
                }
            });
        });

        // Fix modal close behavior
        const instructionalModal = document.getElementById('instructionalImageModal');
        if (instructionalModal) {
            instructionalModal.addEventListener('hidden.bs.modal', function() {
                // Ensure the page isn't stuck after closing the modal
                document.body.classList.remove('modal-open');
                const modalBackdrops = document.getElementsByClassName('modal-backdrop');
                while (modalBackdrops.length > 0) {
                    modalBackdrops[0].parentNode.removeChild(modalBackdrops[0]);
                }
            });
        }
    }

    // Function to add event listeners
    function addEventListeners() {
        document.querySelectorAll('.config-field').forEach(field => {
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');

            if (technicalName) {
                // PERFORMANCE FIX: Capture original value on focus
                field.addEventListener('focus', function () {
                    // Get current value based on field type
                    let currentValue;
                    if (fieldType === 'boolean') {
                        currentValue = this.checked;
                    } else if (fieldType === 'number') {
                        currentValue = parseFloat(this.value) || 0;
                    } else if (fieldType === 'selection') {
                        const selectedOption = this.options[this.selectedIndex];
                        currentValue = selectedOption ? (selectedOption.getAttribute('data-option-value') || selectedOption.value) : '';
                    } else if (fieldType === 'date') {
                        currentValue = this.value;
                    } else {
                        currentValue = this.value;
                    }

                    // Store original value
                    originalFieldValues[technicalName] = currentValue;
                    console.log(`[PERF] Captured original value for ${technicalName}:`, currentValue);
                });

                field.addEventListener('change', function(event) {
                    let value;

                    if (fieldType === 'boolean') {
                        value = this.checked;
                    } else if (fieldType === 'number') {
                        value = parseFloat(this.value) || 0;
                    } else if (fieldType === 'selection') {
                        // All selection fields are now regular select elements
                        const selectedOption = this.options[this.selectedIndex];
                        value = selectedOption ? (selectedOption.getAttribute('data-option-value') || selectedOption.value) : '';
                    } else if (fieldType === 'date') {
                        value = this.value; // Date in YYYY-MM-DD format
                    } else {
                        value = this.value;
                    }

                    setFieldValue(technicalName, value);

                    // Update hidden field for form submission
                    const fieldId = this.getAttribute('data-field-id');
                    const hiddenField = document.getElementById('hidden_field_' + fieldId);
                    if (hiddenField) {
                        hiddenField.value = value;
                    }

                    // PERFORMANCE FIX: Separate immediate and heavy operations
                    console.log(`[PERF] Change event triggered for field: ${technicalName}`);

                    // Immediate UI updates (synchronous for good UX)
                    updateImmediateUI();

                    // Heavy operations (asynchronous to prevent blocking)
                    // Note: Pass event to exclude triggering field from dynamic defaults
                    updateHeavyOperationsAsync().then(() => {
                        // Run dynamic defaults after other heavy operations
                        updateDynamicDefaults(event);
                    });

                    // Note: Auto-cascade removed to prevent console chaos

                });

                // For text and number fields, also listen for input events
                if (fieldType === 'text' || fieldType === 'number') {
                    // Add keydown listener for number fields to prevent decimal input when precision is 0
                    if (fieldType === 'number') {
                        field.addEventListener('keydown', function(e) {
                            const decimalPrecision = parseInt(this.getAttribute('data-decimal-precision')) || 0;

                            // If decimal precision is 0, prevent decimal point input
                            if (decimalPrecision === 0 && (e.key === '.' || e.key === ',')) {
                                e.preventDefault();
                                return false;
                            }
                        });
                    }

                    field.addEventListener('input', function(event) {
                        let value;

                        if (fieldType === 'number') {
                            // Get decimal precision setting
                            const decimalPrecision = parseInt(this.getAttribute('data-decimal-precision')) || 0;

                            // For number fields (which are now text inputs with numeric validation)
                            if (decimalPrecision === 0) {
                                // Only allow whole numbers (digits and minus sign)
                                this.value = this.value.replace(/[^0-9-]/g, '');
                            } else {
                                // Allow decimal point for fields with decimal precision
                                this.value = this.value.replace(/[^0-9.-]/g, '');
                            }

                            // Don't convert to 0 immediately if the field is empty or being edited
                            if (this.value === '' || this.value === '-' || (decimalPrecision > 0 && this.value === '.')) {
                                value = this.value;
                            } else {
                                // Try to parse as float, but don't convert to 0 if invalid
                                const parsed = parseFloat(this.value);
                                value = !isNaN(parsed) ? parsed : this.value;
                            }
                        } else {
                            value = this.value;
                        }

                        // Store the value immediately (for real-time field access)
                        setFieldValue(technicalName, value);

                        // Update hidden field for form submission immediately
                        const fieldId = this.getAttribute('data-field-id');
                        const hiddenField = document.getElementById('hidden_field_' + fieldId);
                        if (hiddenField) {
                            hiddenField.value = value;
                        }

                        // PERFORMANCE FIX: Heavy UI updates are now handled in blur event
                        // This keeps input responsive while deferring expensive operations

                    });

                    // Add blur event to validate when focus leaves the field
                    if (fieldType === 'text') {
                        field.addEventListener('blur', function() {
                            // On blur, validate but don't auto-correct empty fields
                            if (this.value === '' || this.value === '-' || this.value === '.') {
                                // Don't auto-correct empty fields, just show validation message
                                this.style.borderColor = '#dc3545';
                                this.classList.add('is-invalid');

                                const fieldId = this.getAttribute('data-field-id');
                                const validationMessage = document.getElementById('validation_message_' + fieldId);
                                if (validationMessage) {
                                    const messageText = validationMessage.querySelector('.message-text');
                                    if (messageText) {
                                        messageText.textContent = `This field is required. Please enter a value.`;
                                    }
                                    validationMessage.style.display = 'block';
                                    validationMessage.className = validationMessage.className.replace('alert-warning', 'alert-danger');
                                }
                                return; // Don't process further
                            } else {
                                const fieldId = this.getAttribute('data-field-id');
                                const validationMessage = document.getElementById('validation_message_' + fieldId);

                                // Clear any existing validation message content but don't hide it
                                if (validationMessage) {
                                    const messageText = validationMessage.querySelector('.message-text');
                                    if (messageText) {
                                        messageText.textContent = '';
                                    }
                                }

                                // Clear validation styling if value is valid
                                this.style.borderColor = '';
                                this.classList.remove('is-invalid');
                                if (validationMessage) {
                                    validationMessage.style.display = 'none';
                                }
                            }

                            // PERFORMANCE FIX: Check if value actually changed before triggering updates
                            const currentValue = this.value;
                            const originalValue = originalFieldValues[technicalName];

                            if (currentValue === originalValue) {
                                console.log(`[PERF] No change detected for text field ${technicalName}, skipping updates`);
                                return; // Skip updates if no change
                            }

                            console.log(`[PERF] Value changed for text field ${technicalName}: "${originalValue}" → "${currentValue}"`);

                            // Immediate UI updates (synchronous for good UX)
                            updateImmediateUI();

                            // Heavy operations (asynchronous to prevent blocking)
                            updateHeavyOperationsAsync();
                        });
                    } else if (fieldType === 'number') {
                        field.addEventListener('blur', function() {
                            // On blur, validate but don't auto-correct empty fields
                            if (this.value === '' || this.value === '-' || this.value === '.') {
                                // Don't auto-correct empty fields, just show validation message
                                this.style.borderColor = '#dc3545';
                                this.classList.add('is-invalid');

                                const fieldId = this.getAttribute('data-field-id');
                                const validationMessage = document.getElementById('validation_message_' + fieldId);
                                if (validationMessage) {
                                    const messageText = validationMessage.querySelector('.message-text');
                                    if (messageText) {
                                        messageText.textContent = `This field is required. Please enter a value.`;
                                    }
                                    validationMessage.style.display = 'block';
                                    validationMessage.className = validationMessage.className.replace('alert-warning', 'alert-danger');
                                }
                                return; // Don't process further
                            } else {
                                // Parse the value
                                let value = parseFloat(this.value);

                                // Apply min/max constraints using dynamically evaluated values
                                const minExpr = this.getAttribute('data-min');
                                const maxExpr = this.getAttribute('data-max');
                                const min = minExpr ? evaluateDynamicValue(minExpr) : null;
                                const max = maxExpr ? evaluateDynamicValue(maxExpr) : null;
                                const fieldId = this.getAttribute('data-field-id');
                                const validationMessage = document.getElementById('validation_message_' + fieldId);

                                console.log(`Field ${fieldId} validation - min: ${min}, max: ${max}, value: ${value}`);

                                // Clear any existing validation message content but don't hide it
                                if (validationMessage) {
                                    const messageText = validationMessage.querySelector('.message-text');
                                    if (messageText) {
                                        messageText.textContent = '';
                                    }
                                }

                                if (min !== null && value < min) {
                                    const originalValue = value;
                                    // Don't auto-correct the value, just show error message
                                    this.style.borderColor = '#dc3545';
                                    this.classList.add('is-invalid');

                                    // Show validation error message
                                    if (validationMessage) {
                                        const messageText = validationMessage.querySelector('.message-text');
                                        if (messageText) {
                                            messageText.textContent = `Value ${originalValue} is below minimum (${min}). Please enter a valid value.`;
                                        }
                                        validationMessage.style.display = 'block';
                                        validationMessage.className = validationMessage.className.replace('alert-warning', 'alert-danger');
                                    }
                                    return; // Don't update field values with invalid data
                                }
                                if (max !== null && value > max) {
                                    const originalValue = value;
                                    // Don't auto-correct the value, just show error message
                                    this.style.borderColor = '#dc3545';
                                    this.classList.add('is-invalid');

                                    // Show validation error message
                                    if (validationMessage) {
                                        const messageText = validationMessage.querySelector('.message-text');
                                        if (messageText) {
                                            messageText.textContent = `Value ${originalValue} is above maximum (${max}). Please enter a valid value.`;
                                        }
                                        validationMessage.style.display = 'block';
                                        validationMessage.className = validationMessage.className.replace('alert-warning', 'alert-danger');
                                    }
                                    return; // Don't update field values with invalid data
                                }

                                // Clear validation styling if value is valid
                                this.style.borderColor = '';
                                this.classList.remove('is-invalid');
                                if (validationMessage) {
                                    validationMessage.style.display = 'none';
                                }

                                // Format the number with appropriate decimal places
                                const decimalPrecision = parseInt(this.getAttribute('data-decimal-precision')) || 0;

                                // Format with the correct number of decimal places
                                if (decimalPrecision > 0) {
                                    this.value = value.toFixed(decimalPrecision);
                                } else if (decimalPrecision === 0) {
                                    // For whole numbers, ensure no decimal places
                                    this.value = Math.round(value).toString();
                                } else {
                                    this.value = value;
                                }
                            }

                            // Update the value after validation
                            const value = parseFloat(this.value) || 0;
                            setFieldValue(technicalName, value);

                            // Update hidden field
                            const fieldId = this.getAttribute('data-field-id');
                            const hiddenField = document.getElementById('hidden_field_' + fieldId);
                            if (hiddenField) {
                                hiddenField.value = value;
                            }

                            // PERFORMANCE FIX: Check if value actually changed before triggering updates
                            const currentValue = parseFloat(this.value) || 0;
                            const originalValue = originalFieldValues[technicalName];

                            if (currentValue === originalValue) {
                                console.log(`[PERF] No change detected for number field ${technicalName}, skipping updates`);
                                return; // Skip updates if no change
                            }

                            console.log(`[PERF] Value changed for number field ${technicalName}: ${originalValue} → ${currentValue}`);

                            // Immediate UI updates (synchronous for good UX)
                            updateImmediateUI();

                            // Heavy operations (asynchronous to prevent blocking)
                            updateHeavyOperationsAsync();
                        });

                        // Add increment/decrement buttons functionality
                        const fieldId = field.getAttribute('data-field-id');
                        const incrementBtn = field.nextElementSibling?.querySelector('.number-increment');
                        const decrementBtn = field.nextElementSibling?.querySelector('.number-decrement');

                        if (incrementBtn) {
                            incrementBtn.addEventListener('click', function() {
                                const step = parseFloat(field.getAttribute('data-step')) || 1;
                                const currentValue = parseFloat(field.value) || 0;
                                const max = parseFloat(field.getAttribute('data-max'));

                                let newValue = currentValue + step;
                                if (!isNaN(max) && newValue > max) {
                                    newValue = max;
                                }

                                // Format with the correct number of decimal places
                                const decimalPrecision = parseInt(field.getAttribute('data-decimal-precision')) || 0;
                                if (decimalPrecision === 0) {
                                    field.value = Math.round(newValue).toString();
                                } else {
                                    field.value = newValue.toFixed(decimalPrecision);
                                }

                                // Trigger change event
                                field.dispatchEvent(new Event('change', { bubbles: true }));
                            });
                        }

                        if (decrementBtn) {
                            decrementBtn.addEventListener('click', function() {
                                const step = parseFloat(field.getAttribute('data-step')) || 1;
                                const currentValue = parseFloat(field.value) || 0;
                                const min = parseFloat(field.getAttribute('data-min'));

                                let newValue = currentValue - step;
                                if (!isNaN(min) && newValue < min) {
                                    newValue = min;
                                }

                                // Format with the correct number of decimal places
                                const decimalPrecision = parseInt(field.getAttribute('data-decimal-precision')) || 0;
                                if (decimalPrecision === 0) {
                                    field.value = Math.round(newValue).toString();
                                } else {
                                    field.value = newValue.toFixed(decimalPrecision);
                                }

                                // Trigger change event
                                field.dispatchEvent(new Event('change', { bubbles: true }));
                            });
                        }
                    }
                }
            }
        });
    }



    // Function to update the debug panel
    function updateDebugPanel() {
        // Update field values with calculated values
        const fieldValuesDebug = document.getElementById('field-values-debug');
        if (fieldValuesDebug) {
            // Create enhanced field values object with calculations
            const enhancedFieldValues = { ...fieldValues };

            // Calculate dynamic fields using the calculated fields engine
            try {
                const calculatedFields = calculateDynamicFields(fieldValues);
                Object.assign(enhancedFieldValues, calculatedFields);
            } catch (error) {
                console.error('Error calculating dynamic fields:', error);
                // Fallback to basic calculations for backward compatibility
                calculateBasicFields(fieldValues, enhancedFieldValues);
            }

            // All calculated fields now come from server-defined JSON imports





            fieldValuesDebug.textContent = JSON.stringify(enhancedFieldValues, null, 2);
        }

        // Update visibility conditions
        const conditionsTableBody = document.getElementById('conditions-table-body');
        if (conditionsTableBody) {
            conditionsTableBody.innerHTML = '';

            // Add field conditions
            document.querySelectorAll('.config-field-container').forEach(container => {
                const fieldId = container.getAttribute('data-field-id');
                const technicalName = container.getAttribute('data-technical-name');
                const condition = container.getAttribute('data-visibility-condition');
                const isVisible = !container.classList.contains('field-condition-hidden');

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>Field: ${technicalName || fieldId}</td>
                    <td><code>${condition}</code></td>
                    <td>${isVisible ? '✅' : '❌'}</td>
                `;
                conditionsTableBody.appendChild(row);
            });

            // Add option conditions with enhanced evaluation
            document.querySelectorAll('select.config-field').forEach(select => {
                const fieldId = select.getAttribute('data-field-id');
                const technicalName = select.getAttribute('data-technical-name');

                Array.from(select.options).forEach(option => {
                    const optionId = option.getAttribute('data-option-id');
                    const optionValue = option.getAttribute('data-option-value');
                    const condition = option.getAttribute('data-visibility-condition');

                    if (condition && condition !== 'true') {
                        const isVisible = option.style.display !== 'none';

                        // Try to evaluate the condition to show the result
                        let conditionResult = 'Unknown';
                        let conditionError = '';
                        try {
                            if (typeof evaluateCondition === 'function') {
                                const result = evaluateCondition(condition);
                                conditionResult = result ? 'TRUE' : 'FALSE';
                            }
                        } catch (error) {
                            conditionResult = 'ERROR';
                            conditionError = error.message;
                        }

                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>Option: ${optionValue || optionId} (in ${technicalName || fieldId})</td>
                            <td><code style="font-size: 11px; word-break: break-all;">${condition}</code></td>
                            <td>
                                ${isVisible ? '✅' : '❌'}
                                <br><small>Eval: <strong>${conditionResult}</strong></small>
                                ${conditionError ? `<br><small style="color: red;">Error: ${conditionError}</small>` : ''}
                            </td>
                        `;
                        conditionsTableBody.appendChild(row);
                    }
                });
            });


        }
    }

    // Function to update the calculated fields panel
    function updateCalculatedFieldsPanel() {
        const tableBody = document.getElementById('calculated-fields-table-body');
        const priceMatricesTableBody = document.getElementById('price-matrices-table-body');
        if (!tableBody) return;

        // Clear existing content
        tableBody.innerHTML = '';

        // Calculate dynamic fields to get current values
        let calculatedFields = {};
        try {
            calculatedFields = calculateDynamicFields(fieldValues);
        } catch (error) {
            console.error('Error calculating fields for panel:', error);
        }

        // Filter only calculated fields (those starting with _CALCULATED_)
        const calculatedFieldsOnly = Object.entries(calculatedFields)
            .filter(([key, value]) => key.startsWith('_CALCULATED_'))
            .sort(([a], [b]) => a.localeCompare(b)); // Sort alphabetically

        if (calculatedFieldsOnly.length === 0) {
            // Show empty state
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="4" class="text-center text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    No calculated fields found. Import calculated fields or configure some values to see results.
                </td>
            `;
            tableBody.appendChild(row);
            return;
        }

        if (priceMatricesTableBody) {
            priceMatricesTableBody.innerHTML = '';
        }
        
        // Track price matrix fields
        let priceMatrixFieldsCount = 0;
        
        // Add each calculated field to the table
        calculatedFieldsOnly.forEach(([fieldName, value]) => {
            // Find the field definition for additional info
            const fieldDef = calculatedFieldsDefinitions.find(def => def.name === fieldName);

            // Determine value type and format
            let valueType = typeof value;
            let displayValue = value;

            if (value === null || value === undefined) {
                displayValue = '<span class="text-muted">null</span>';
                valueType = 'null';
            } else if (typeof value === 'number') {
                displayValue = value.toLocaleString();
                valueType = Number.isInteger(value) ? 'integer' : 'decimal';
            } else if (typeof value === 'boolean') {
                displayValue = value ? '<span class="text-success">true</span>' : '<span class="text-danger">false</span>';
                valueType = 'boolean';
            } else {
                displayValue = String(value);
                valueType = 'string';
            }

            // Only add to price matrices table if field has defined height/width in pricing matrix
            if (priceMatricesTableBody && fieldDef && fieldDef.is_pricing_matrix) {
                const rowPriceMatrices = document.createElement('tr');
                rowPriceMatrices.innerHTML = `
                    <td>
                        <code class="text-primary">${fieldName}</code>
                    </td>
                    <td class="text-end">
                        ${displayValue}
                    </td>
                `;
                priceMatricesTableBody.appendChild(rowPriceMatrices);
                priceMatrixFieldsCount++;
            }
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <code class="text-primary">${fieldName}</code>
                </td>
                <td>
                    ${displayValue}
                </td>
                <td>
                    <span class="badge bg-secondary">${valueType}</span>
                </td>
                <td>
                    <small class="text-muted">
                        ${fieldDef ? fieldDef.description || 'No description' : 'Field definition not found'}
                    </small>
                </td>
            `;
            tableBody.appendChild(row);
        });

        // Show message if no price matrix fields found
        if (priceMatricesTableBody && priceMatrixFieldsCount === 0) {
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `
                <td colspan="2" class="text-center text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    No price matrix fields found. Set Price Matrix for fields to appear here.
                </td>
            `;
            priceMatricesTableBody.appendChild(emptyRow);
        }

        // Show/hide the Price Matrices card based on whether there are price matrix fields
        const priceMatricesCard = document.querySelector('.card:has(#price-matrices-table-body)');
        if (priceMatricesCard) {
            if (priceMatrixFieldsCount === 0) {
                priceMatricesCard.style.display = 'none';
            } else {
                priceMatricesCard.style.display = 'block';
            }
        }

    }

    // Function to validate all number fields
    function validateNumberFields() {
        let isValid = true;

        // Don't hide existing validation messages

        // Check all number fields (now text inputs with data-field-type="number")
        document.querySelectorAll('input[data-field-type="number"].config-field').forEach(field => {
            // Skip fields that are hidden (not visible to the user)
            if (field.closest('.config-field-container') &&
                field.closest('.config-field-container').style.display === 'none') {
                return;
            }

            // Skip fields that are currently being edited (empty, just a minus sign, or decimal point)
            if (field.value === '' || field.value === '-' || field.value === '.') {
                // Don't auto-correct empty fields, just mark as invalid
                field.style.borderColor = '#dc3545';
                field.classList.add('is-invalid');
                setTimeout(() => {
                    field.style.borderColor = '';
                    field.classList.remove('is-invalid');
                }, 2000);
                isValid = false;

                // Show validation message for empty fields
                const fieldId = field.getAttribute('data-field-id');
                const validationMessage = document.getElementById('validation_message_' + fieldId);
                if (validationMessage) {
                    const messageText = validationMessage.querySelector('.message-text');
                    if (messageText) {
                        messageText.textContent = `This field is required. Please enter a value.`;
                    }
                    validationMessage.style.display = 'block';
                    validationMessage.className = validationMessage.className.replace('alert-warning', 'alert-danger');
                }
                return;
            }

            const value = parseFloat(field.value) || 0;
            const min = parseFloat(field.getAttribute('data-min'));
            const max = parseFloat(field.getAttribute('data-max'));

            // Get validation message element
            const fieldId = field.getAttribute('data-field-id');
            const validationMessage = document.getElementById('validation_message_' + fieldId);

            // Clear any existing validation message content but don't hide it
            if (validationMessage) {
                const messageText = validationMessage.querySelector('.message-text');
                if (messageText) {
                    messageText.textContent = '';
                }
            }

            // Apply constraints if defined
            if (!isNaN(min) && value < min) {
                const originalValue = value;
                // Don't auto-correct the value, just show error message
                field.style.borderColor = '#dc3545';
                field.classList.add('is-invalid');
                isValid = false;

                // Show validation error message
                if (validationMessage) {
                    const messageText = validationMessage.querySelector('.message-text');
                    if (messageText) {
                        messageText.textContent = `Value ${originalValue} is below minimum (${min}). Please enter a valid value.`;
                    }
                    validationMessage.style.display = 'block';
                    validationMessage.className = validationMessage.className.replace('alert-warning', 'alert-danger');
                }
            } else if (!isNaN(max) && value > max) {
                const originalValue = value;
                // Don't auto-correct the value, just show error message
                field.style.borderColor = '#dc3545';
                field.classList.add('is-invalid');
                isValid = false;

                // Show validation error message
                if (validationMessage) {
                    const messageText = validationMessage.querySelector('.message-text');
                    if (messageText) {
                        messageText.textContent = `Value ${originalValue} is above maximum (${max}). Please enter a valid value.`;
                    }
                    validationMessage.style.display = 'block';
                    validationMessage.className = validationMessage.className.replace('alert-warning', 'alert-danger');
                }
            } else {
                // Clear validation styling if value is valid
                field.style.borderColor = '';
                field.classList.remove('is-invalid');
                if (validationMessage) {
                    validationMessage.style.display = 'none';
                }
            }

            // Format the number with appropriate decimal places
            const decimalPrecision = parseInt(field.getAttribute('data-decimal-precision')) || 0;

            if (decimalPrecision > 0) {
                field.value = parseFloat(field.value).toFixed(decimalPrecision);
            } else if (decimalPrecision === 0) {
                // For whole numbers, ensure no decimal places
                field.value = Math.round(parseFloat(field.value)).toString();
            }
        });

        return isValid;
    }

    // Global color management
    let globalColorFields = [];
    let globalColorOptions = [];
    let isApplyingGlobalColor = false;
    let currentGlobalColor = '';

    // Function to identify and track color fields
    function identifyColorFields() {
        // Reset the array but keep the current global color
        globalColorFields = [];
        globalColorOptions = [];

        // Find all selection fields that might be color fields
        document.querySelectorAll('select.config-field').forEach(select => {
            const technicalName = select.getAttribute('data-technical-name');
            const fieldId = select.getAttribute('data-field-id');
            const fieldContainer = document.getElementById('field_container_' + fieldId);

            if (!technicalName || !fieldContainer) return;

            // Check if this is a color field by looking at the field name or label
            const fieldLabel = fieldContainer.querySelector('label');
            const labelText = fieldLabel ? fieldLabel.textContent.trim().toLowerCase() : '';

            // Check if this is a color field (contains "color" or "colour" in the name)
            const isColorField =
                technicalName.toLowerCase().includes('color') ||
                technicalName.toLowerCase().includes('colour') ||
                labelText.includes('color') ||
                labelText.includes('colour');

            if (isColorField) {
                globalColorFields.push({
                    id: fieldId,
                    technicalName: technicalName,
                    element: select,
                    label: labelText
                });

                // Collect all unique color options
                Array.from(select.options).forEach(option => {
                    if (option.value && option.text) {
                        const existingOption = globalColorOptions.find(o => o.value === option.value);
                        if (!existingOption) {
                            globalColorOptions.push({
                                value: option.value,
                                text: option.text
                            });
                        }
                    }
                });
            }
        });

        return globalColorFields.length > 0;
    }

    // Function to populate the global color selector
    function populateGlobalColorSelector() {
        const globalColorSelect = document.getElementById('global-color-value');
        if (!globalColorSelect) return;

        // Save the current selection if any
        const previousValue = globalColorSelect.value || currentGlobalColor;

        // Clear existing options (except the first placeholder)
        while (globalColorSelect.options.length > 1) {
            globalColorSelect.remove(1);
        }

        // Sort options alphabetically
        globalColorOptions.sort((a, b) => a.text.localeCompare(b.text));

        // Add options to the global selector
        globalColorOptions.forEach(option => {
            const optElement = document.createElement('option');
            optElement.value = option.value;
            optElement.text = option.text;
            globalColorSelect.add(optElement);
        });

        // Show or hide the global color selector based on whether we found color fields
        const globalColorCard = document.getElementById('global-color-selector');
        if (globalColorCard) {
            globalColorCard.style.display = globalColorFields.length > 0 ? 'block' : 'none';
        }

        // Restore the previous selection if it exists in the new options
        if (previousValue) {
            // Check if the option still exists
            const optionExists = Array.from(globalColorSelect.options).some(opt => opt.value === previousValue);
            if (optionExists) {
                globalColorSelect.value = previousValue;
            }
        }
    }

    // Function to apply the global color to all color fields
    function applyGlobalColor(colorValue) {
        if (isApplyingGlobalColor) return;

        // Set flag to prevent recursive calls
        isApplyingGlobalColor = true;

        // Store the current global color
        currentGlobalColor = colorValue;

        if (colorValue) {
            // Apply to each color field
            globalColorFields.forEach(field => {
                const select = field.element;

                // Find the option with this value
                const option = Array.from(select.options).find(opt => opt.value === colorValue);

                // Only apply if the option exists in this select and is visible
                if (option && option.style.display !== 'none') {
                    // Set the selected option
                    select.value = colorValue;

                    // Trigger the change event
                    const event = new Event('change', { bubbles: true });
                    select.dispatchEvent(event);

                }
            });
        }

        // Reset flag
        isApplyingGlobalColor = false;
    }

    // Function to setup the global color selector
    function setupGlobalColorSelector() {
        // First identify all color fields
        const hasColorFields = identifyColorFields();

        if (hasColorFields) {
            // Populate the global color selector
            populateGlobalColorSelector();

            // Add event listener to the global color selector
            const globalColorSelect = document.getElementById('global-color-value');
            if (globalColorSelect) {
                globalColorSelect.addEventListener('change', function() {
                    applyGlobalColor(this.value);
                });
            }
        }
    }

    // Function to setup section collapse functionality
    function setupSectionCollapse() {
        // Add event listeners to all section collapse toggle buttons
        document.querySelectorAll('.section-collapse-toggle').forEach(button => {
            button.addEventListener('click', function() {
                // Get the target section ID from the data-bs-target attribute
                const targetId = this.getAttribute('data-bs-target');
                const targetSection = document.querySelector(targetId);
                const icon = this.querySelector('i');

                if (targetSection && icon) {
                    // Toggle the collapse classes
                    const isCurrentlyCollapsed = targetSection.classList.contains('show');

                    if (isCurrentlyCollapsed) {
                        // Currently expanded, so collapse it
                        targetSection.classList.remove('show');
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    } else {
                        // Currently collapsed, so expand it
                        targetSection.classList.add('show');
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-up');
                    }
                }
            });
        });
    }

    // Function to setup searchable select functionality
    function setupSearchableSelects() {
        document.querySelectorAll('.searchable-select').forEach(select => {
            const wrapper = select.parentElement;

            // Create search input overlay
            const searchInput = document.createElement('input');
            searchInput.type = 'text';
            searchInput.className = 'form-control form-control-sm search-overlay';
            searchInput.placeholder = 'Type to search...';
            searchInput.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                z-index: 10;
                background: white;
                display: none;
            `;
            wrapper.appendChild(searchInput);

            let isSearchMode = false;
            let allOptions = Array.from(select.options);

            // Show search when select is clicked
            select.addEventListener('mousedown', function(e) {
                if (!isSearchMode) {
                    e.preventDefault();
                    isSearchMode = true;
                    searchInput.style.display = 'block';
                    searchInput.focus();
                    searchInput.value = '';

                    // Show all options initially - ensure minimum of 2 to maintain searchable appearance
                    this.size = Math.max(2, Math.min(allOptions.length, 10));
                    this.style.position = 'relative';
                    this.style.zIndex = '5';
                }
            });

            // Handle search input
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                // Clear current options
                select.innerHTML = '';

                // Filter and add matching options
                allOptions.forEach(option => {
                    if (!searchTerm || option.text.toLowerCase().includes(searchTerm)) {
                        const newOption = option.cloneNode(true);
                        select.appendChild(newOption);
                    }
                });

                // Update select size - ensure minimum of 2 to maintain searchable appearance
                select.size = Math.max(2, Math.min(select.options.length, 10));

                // If no matches, show "no results"
                if (select.options.length === 0) {
                    const noResults = document.createElement('option');
                    noResults.textContent = 'No matches found';
                    noResults.disabled = true;
                    select.appendChild(noResults);
                    select.size = 2; // Ensure "no results" is visible
                }
            });

            // Handle option selection
            select.addEventListener('change', function() {
                if (isSearchMode && this.value) {
                    const selectedValue = this.value;

                    // Close search mode
                    isSearchMode = false;
                    searchInput.style.display = 'none';
                    this.size = 1;
                    this.style.position = '';
                    this.style.zIndex = '';

                    // Restore all options
                    this.innerHTML = '';
                    allOptions.forEach(option => {
                        this.appendChild(option.cloneNode(true));
                    });

                    // Set selected value properly
                    this.value = selectedValue;

                    // Manually trigger the field update logic that would normally happen in the main change handler
                    const technicalName = this.getAttribute('data-technical-name');
                    const fieldType = this.getAttribute('data-field-type');

                    if (technicalName) {
                        // Get the selected option value
                        const selectedOption = this.options[this.selectedIndex];
                        const value = selectedOption ? (selectedOption.getAttribute('data-option-value') || selectedOption.value) : '';

                        // Update field value
                        if (typeof setFieldValue === 'function') {
                            setFieldValue(technicalName, value);
                        }

                        // Update hidden field for form submission
                        const fieldId = this.getAttribute('data-field-id');
                        const hiddenField = document.getElementById('hidden_field_' + fieldId);
                        if (hiddenField) {
                            hiddenField.value = value;
                        }

                        // Trigger all the necessary updates
                        if (typeof updateFieldVisibility === 'function') updateFieldVisibility();
                        if (typeof updateDynamicHelp === 'function') updateDynamicHelp();
                        if (typeof updateDynamicDefaults === 'function') updateDynamicDefaults();
                        if (typeof updateErrorMessages === 'function') updateErrorMessages();
                        if (typeof updateFieldConstraints === 'function') updateFieldConstraints();
                        if (typeof updateVisibilityConditionsDisplay === 'function') updateVisibilityConditionsDisplay();
                        if (typeof updateDebugPanel === 'function') updateDebugPanel();
                        if (typeof updateCalculatedFieldsPanel === 'function') updateCalculatedFieldsPanel();


                    }
                }
            });

            // Handle escape key and blur
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const currentValue = select.value;
                    isSearchMode = false;
                    this.style.display = 'none';
                    select.size = 1;
                    select.style.position = '';
                    select.style.zIndex = '';

                    // Restore all options
                    select.innerHTML = '';
                    allOptions.forEach(option => {
                        select.appendChild(option.cloneNode(true));
                    });

                    // Restore the selected value
                    if (currentValue) {
                        select.value = currentValue;
                    }
                } else if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                    // Transfer focus to select for arrow navigation
                    select.focus();
                }
            });

            searchInput.addEventListener('blur', function() {
                // Delay to allow for option selection
                setTimeout(() => {
                    if (isSearchMode) {
                        const currentValue = select.value;
                        isSearchMode = false;
                        this.style.display = 'none';
                        select.size = 1;
                        select.style.position = '';
                        select.style.zIndex = '';

                        // Restore all options
                        select.innerHTML = '';
                        allOptions.forEach(option => {
                            select.appendChild(option.cloneNode(true));
                        });

                        // Restore the selected value
                        if (currentValue) {
                            select.value = currentValue;

                            // Trigger field updates if value was set
                            const technicalName = select.getAttribute('data-technical-name');
                            if (technicalName && typeof setFieldValue === 'function') {
                                const selectedOption = select.options[select.selectedIndex];
                                const value = selectedOption ? (selectedOption.getAttribute('data-option-value') || selectedOption.value) : '';
                                setFieldValue(technicalName, value);

                                // Update hidden field
                                const fieldId = select.getAttribute('data-field-id');
                                const hiddenField = document.getElementById('hidden_field_' + fieldId);
                                if (hiddenField) {
                                    hiddenField.value = value;
                                }

                                // Trigger updates
                                if (typeof updateFieldVisibility === 'function') updateFieldVisibility();
                                if (typeof updateDynamicHelp === 'function') updateDynamicHelp();
                                if (typeof updateDynamicDefaults === 'function') updateDynamicDefaults();
                                if (typeof updateErrorMessages === 'function') updateErrorMessages();
                                if (typeof updateFieldConstraints === 'function') updateFieldConstraints();
                                if (typeof updateVisibilityConditionsDisplay === 'function') updateVisibilityConditionsDisplay();
                                if (typeof updateDebugPanel === 'function') updateDebugPanel();
                            }
                        }
                    }
                }, 200);
            });

            // Handle select blur
            select.addEventListener('blur', function() {
                setTimeout(() => {
                    if (isSearchMode) {
                        const currentValue = this.value;
                        isSearchMode = false;
                        searchInput.style.display = 'none';
                        this.size = 1;
                        this.style.position = '';
                        this.style.zIndex = '';

                        // Restore all options
                        this.innerHTML = '';
                        allOptions.forEach(option => {
                            this.appendChild(option.cloneNode(true));
                        });

                        // Restore the selected value
                        if (currentValue) {
                            this.value = currentValue;

                            // Trigger field updates if value was set
                            const technicalName = this.getAttribute('data-technical-name');
                            if (technicalName && typeof setFieldValue === 'function') {
                                const selectedOption = this.options[this.selectedIndex];
                                const value = selectedOption ? (selectedOption.getAttribute('data-option-value') || selectedOption.value) : '';
                                setFieldValue(technicalName, value);

                                // Update hidden field
                                const fieldId = this.getAttribute('data-field-id');
                                const hiddenField = document.getElementById('hidden_field_' + fieldId);
                                if (hiddenField) {
                                    hiddenField.value = value;
                                }

                                // Trigger updates
                                if (typeof updateFieldVisibility === 'function') updateFieldVisibility();
                                if (typeof updateDynamicHelp === 'function') updateDynamicHelp();
                                if (typeof updateDynamicDefaults === 'function') updateDynamicDefaults();
                                if (typeof updateErrorMessages === 'function') updateErrorMessages();
                                if (typeof updateFieldConstraints === 'function') updateFieldConstraints();
                                if (typeof updateVisibilityConditionsDisplay === 'function') updateVisibilityConditionsDisplay();
                                if (typeof updateDebugPanel === 'function') updateDebugPanel();
                            }
                        }
                    }
                }, 200);
            });
        });
    }

    // Function to update conditions debug panel
    function updateConditionsDebugPanel() {
        const tableBody = document.getElementById('conditions-debug-table-body');

        if (!tableBody) return;

        // Clear existing content
        tableBody.innerHTML = '';

        // Find all fields/options with multiple visibility conditions
        const multiConditionElements = [];

        // First, let's find all elements that might have visibility conditions
        // Look for any elements with visibility-related attributes
        const allElementsWithConditions = document.querySelectorAll('[data-visibility-condition], [data-visibility-conditions], option[style*="display"]');

        // Group conditions by option value to find multiple conditions for the same option
        const optionConditionsMap = new Map();

        // Look through all select options and check for visibility conditions
        document.querySelectorAll('select option').forEach(option => {
            const optionValue = option.value;
            const optionText = option.textContent.trim();
            const select = option.closest('select');

            if (!select || !optionValue) return;

            const fieldId = select.getAttribute('data-field-id');
            const fieldLabel = select.closest('.form-group')?.querySelector('label')?.textContent?.trim() || fieldId;

            // Try to extract question number from the field label or surrounding elements
            let questionNumber = null;
            const formGroup = select.closest('.form-group');
            if (formGroup) {
                // Look for question number in various places
                const questionSpan = formGroup.querySelector('.question-number');
                const labelText = formGroup.querySelector('label')?.textContent || '';

                if (questionSpan) {
                    questionNumber = questionSpan.textContent.trim();
                } else {
                    // Try to extract number from label text (e.g., "68. Field Name" or "Question 68:")
                    const numberMatch = labelText.match(/^(\d+)[\.\:\s]/) || labelText.match(/Question\s+(\d+)/i);
                    if (numberMatch) {
                        questionNumber = numberMatch[1];
                    }
                }
            }

            // If we still don't have a question number, try to find it in the DOM structure
            if (!questionNumber) {
                const container = select.closest('[data-question-number]') || select.closest('[data-question]');
                if (container) {
                    questionNumber = container.getAttribute('data-question-number') || container.getAttribute('data-question');
                }
            }

            // Question number detection (debug logs removed for performance)

            // Look for visibility condition attribute (could be different formats)
            const visibilityCondition = option.getAttribute('data-visibility-condition') ||
                                      option.getAttribute('data-visibility-conditions') ||
                                      option.getAttribute('data-condition');

            // Option condition processing (debug log removed)

            if (visibilityCondition) {
                const key = `${fieldId}_${optionValue}`;

                // Check if this is a JSON format condition
                let conditions = [];
                if (visibilityCondition.startsWith('__JSON__')) {
                    try {
                        // Extract JSON part after __JSON__
                        const jsonPart = visibilityCondition.substring(8); // Remove '__JSON__'
                        const parsedConditions = JSON.parse(jsonPart);

                        if (Array.isArray(parsedConditions)) {
                            // Extract just the condition strings
                            conditions = parsedConditions.map(c => c.condition || c);
                            // JSON condition parsing (debug log removed)
                        } else {
                            conditions = [visibilityCondition];
                        }
                    } catch (e) {
                        console.warn('Error parsing JSON condition:', e);
                        conditions = [visibilityCondition];
                    }
                } else {
                    conditions = [visibilityCondition];
                }

                if (!optionConditionsMap.has(key)) {
                    optionConditionsMap.set(key, {
                        type: 'option',
                        fieldId: fieldId,
                        fieldLabel: fieldLabel,
                        questionNumber: questionNumber,
                        optionId: optionValue, // Use option value as the ID
                        optionText: optionText,
                        optionValue: optionValue,
                        conditions: [],
                        element: option,
                        isVisible: !option.style.display || option.style.display !== 'none'
                    });
                }

                // Add these conditions to the list
                optionConditionsMap.get(key).conditions.push(...conditions);
            }
        });

        // Also check for elements that might have multiple visibility conditions stored differently
        // Look for elements with data attributes that might contain multiple conditions
        document.querySelectorAll('[data-visibility-conditions]').forEach(element => {
            const conditionsAttr = element.getAttribute('data-visibility-conditions');
            // Element with data-visibility-conditions found (debug log removed)

            if (conditionsAttr) {
                try {
                    const conditions = JSON.parse(conditionsAttr);
                    if (Array.isArray(conditions) && conditions.length > 1) {
                        const fieldId = element.getAttribute('data-field-id') || element.id || 'unknown';
                        const fieldLabel = element.closest('.form-group')?.querySelector('label')?.textContent?.trim() || fieldId;

                        if (element.tagName === 'OPTION') {
                            const key = `${fieldId}_${element.value}`;

                            // Try to get question number for this option too
                            let questionNumber = null;
                            const formGroup = element.closest('.form-group');
                            if (formGroup) {
                                const questionSpan = formGroup.querySelector('.question-number');
                                const labelText = formGroup.querySelector('label')?.textContent || '';

                                if (questionSpan) {
                                    questionNumber = questionSpan.textContent.trim();
                                } else {
                                    const numberMatch = labelText.match(/^(\d+)[\.\:\s]/) || labelText.match(/Question\s+(\d+)/i);
                                    if (numberMatch) {
                                        questionNumber = numberMatch[1];
                                    }
                                }
                            }

                            optionConditionsMap.set(key, {
                                type: 'option',
                                fieldId: fieldId,
                                fieldLabel: fieldLabel,
                                questionNumber: questionNumber,
                                optionId: element.value, // Use option value as the ID
                                optionText: element.textContent.trim(),
                                optionValue: element.value,
                                conditions: conditions,
                                element: element,
                                isVisible: !element.style.display || element.style.display !== 'none'
                            });
                        } else {
                            multiConditionElements.push({
                                type: 'field',
                                fieldId: fieldId,
                                fieldLabel: fieldLabel,
                                conditions: conditions,
                                element: element,
                                isVisible: !element.style.display || element.style.display !== 'none'
                            });
                        }
                    }
                } catch (e) {
                    console.warn('Error parsing visibility conditions for element:', element, e);
                }
            }
        });

        // Convert map to array and filter for multiple conditions OR complex single conditions
        optionConditionsMap.forEach((optionData, key) => {
            // Checking option conditions (debug log removed)

            // Check if it has multiple conditions OR a single complex condition with OR operators
            const hasMultipleConditions = optionData.conditions.length > 1;
            const hasSingleComplexCondition = optionData.conditions.length === 1 &&
                optionData.conditions[0].includes(' || ') &&
                optionData.conditions[0].includes(' && ');

            if (hasMultipleConditions || hasSingleComplexCondition) {
                // For single complex conditions, break them down into parts
                if (hasSingleComplexCondition) {
                    const complexCondition = optionData.conditions[0];
                    // Complex single condition found (debug log removed)

                    // Split by || to get the main parts (UNEVEN and EVEN)
                    const orParts = complexCondition.split(' || ');
                    const brokenDownConditions = [];

                    orParts.forEach((part, index) => {
                        const partName = part.includes('door_split_type == "uneven"') ? 'UNEVEN' :
                                       part.includes('door_split_type == "even"') ? 'EVEN' :
                                       `Part ${index + 1}`;

                        // Remove outer parentheses and split by &&
                        const cleanPart = part.replace(/^\(|\)$/g, '');
                        const andConditions = cleanPart.split(' && ');

                        andConditions.forEach((subCond, subIndex) => {
                            let conditionName = '';
                            if (subCond.includes('door_split_type')) {
                                conditionName = `${partName}: Split Type Check`;
                            } else if (subCond.includes('>= 1053')) {
                                conditionName = `${partName}: Lock Height >= 1053`;
                            } else if (subCond.includes('< (') && subCond.includes('/2 + 32')) {
                                conditionName = `${partName}: Lock Height < (Height/2 + 32)`;
                            } else if (subCond.includes('<= (') && subCond.includes('- 1137')) {
                                conditionName = `${partName}: Lock Height <= (Height - 1137)`;
                            } else {
                                conditionName = `${partName}: Condition ${subIndex + 1}`;
                            }

                            brokenDownConditions.push({
                                name: conditionName,
                                condition: subCond.trim(),
                                partName: partName
                            });
                        });
                    });

                    // Replace the single complex condition with broken down conditions
                    optionData.conditions = brokenDownConditions.map(c => c.condition);
                    optionData.conditionNames = brokenDownConditions.map(c => c.name);
                    optionData.isComplexSingleCondition = true;
                    optionData.originalComplexCondition = complexCondition;

                    // Store OR group information for proper evaluation
                    optionData.orGroups = [];
                    orParts.forEach((part, index) => {
                        const partName = part.includes('door_split_type == "uneven"') ? 'UNEVEN' :
                                       part.includes('door_split_type == "even"') ? 'EVEN' :
                                       `Part ${index + 1}`;

                        const cleanPart = part.replace(/^\(|\)$/g, '');
                        const andConditions = cleanPart.split(' && ');

                        optionData.orGroups.push({
                            name: partName,
                            conditionIndices: andConditions.map((_, subIndex) => {
                                return brokenDownConditions.findIndex(c =>
                                    c.partName === partName &&
                                    c.condition === andConditions[subIndex].trim()
                                );
                            }).filter(idx => idx !== -1)
                        });
                    });
                }

                multiConditionElements.push(optionData);
                // Option added (debug log removed)
            } else {
                // Option skipped (debug log removed)
            }
        });

        // Multi-condition elements processing complete (debug log removed)

        if (multiConditionElements.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        No fields or options with multiple visibility conditions found.
                        <br><small>This panel shows elements that require multiple conditions to be TRUE.</small>
                    </td>
                </tr>
            `;
            return;
        }

        // Function to explain step-by-step calculation
        function explainCalculation(evaluatedCondition) {
            try {
                // Try to create a simplified version by resolving ternary operations
                let simplifiedCondition = evaluatedCondition;

                // Replace ternary operations with their resolved values
                const ternaryRegex = /\(([^?]+)\s*\?\s*([^:]+)\s*:\s*([^)]+)\)/g;
                simplifiedCondition = simplifiedCondition.replace(ternaryRegex, (match, conditionPart, truePart, falsePart) => {
                    try {
                        const conditionResult = eval(conditionPart);
                        return conditionResult ? truePart.trim() : falsePart.trim();
                    } catch (e) {
                        return match; // Keep original if evaluation fails
                    }
                });

                // If we have a simplified version, show the calculation steps
                if (simplifiedCondition !== evaluatedCondition && simplifiedCondition.includes('<') || simplifiedCondition.includes('>') || simplifiedCondition.includes('=')) {
                    // Extract the main comparison
                    const comparisonMatch = simplifiedCondition.match(/(\d+(?:\.\d+)?)\s*([<>=!]+)\s*\((.+)\)/);
                    if (comparisonMatch) {
                        const [, leftValue, operator, rightExpression] = comparisonMatch;

                        try {
                            const rightResult = eval(rightExpression);
                            const finalResult = eval(simplifiedCondition);

                            // Convert operator to English
                            const operatorText = {
                                '<=': 'is less than or equal to',
                                '>=': 'is greater than or equal to',
                                '<': 'is less than',
                                '>': 'is greater than',
                                '==': 'is equal to',
                                '!=': 'is not equal to'
                            }[operator] || operator;

                            return `
                                <div class="mt-2 p-3" style="background: #f8fff8; border-left: 4px solid #28a745; font-size: 0.85em; border-radius: 4px;">
                                    <strong>🧮 Calculate the Final Result</strong>
                                    <div style="margin: 8px 0; padding: 8px; background: #ffffff; border-radius: 4px;">
                                        <strong>Now the expression becomes:</strong><br>
                                        <code>${simplifiedCondition}</code><br><br>

                                        <strong>Calculate the right-hand side:</strong><br>
                                        <code>${rightExpression}</code> = <strong>${rightResult}</strong><br><br>

                                        <strong>Final comparison:</strong><br>
                                        ${leftValue} ${operatorText} ${rightResult} → <strong>${finalResult ? 'TRUE' : 'FALSE'}</strong>
                                    </div>
                                </div>
                            `;
                        } catch (e) {
                            return ''; // Skip if calculation fails
                        }
                    }
                }

                return '';
            } catch (e) {
                return '';
            }
        }

        // Function to explain ternary operations step by step
        function explainTernary(condition, evaluatedCondition) {
            const ternaryRegex = /\(([^?]+)\s*\?\s*([^:]+)\s*:\s*([^)]+)\)/g;
            let explanation = '';
            let match;

            while ((match = ternaryRegex.exec(evaluatedCondition)) !== null) {
                const [fullMatch, conditionPart, truePart, falsePart] = match;

                try {
                    // Evaluate the condition part
                    const conditionResult = eval(conditionPart);
                    const selectedValue = conditionResult ? truePart.trim() : falsePart.trim();

                    // Extract numbers for clearer explanation
                    const conditionMatch = conditionPart.match(/(\d+(?:\.\d+)?)\s*(<=|>=|<|>|==|!=)\s*(\d+(?:\.\d+)?)/);
                    let detailedExplanation = '';

                    if (conditionMatch) {
                        const [, leftValue, operator, rightValue] = conditionMatch;
                        const operatorText = {
                            '<=': 'less than or equal to',
                            '>=': 'greater than or equal to',
                            '<': 'less than',
                            '>': 'greater than',
                            '==': 'equal to',
                            '!=': 'not equal to'
                        }[operator] || operator;

                        detailedExplanation = `
                            <div style="margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                                <strong>📝 Step 1: Simplify the Technical Syntax</strong><br>
                                The condition uses a ternary operator (?:), which can be confusing. Let's rewrite it in plain language.<br><br>

                                The ternary operation <code>(${conditionPart} ? ${truePart.trim()} : ${falsePart.trim()})</code> means:<br>
                                <em>"If ${leftValue} is ${operatorText} ${rightValue}, use ${truePart.trim()}; otherwise, use ${falsePart.trim()}."</em><br><br>

                                <strong>🔍 Step 2: Check the Condition</strong><br>
                                Is ${leftValue} ${operatorText} ${rightValue}?<br>
                                ${leftValue} ${operator} ${rightValue} → <strong>${conditionResult ? 'TRUE' : 'FALSE'}</strong><br><br>

                                <strong>✅ Step 3: Select the Value</strong><br>
                                Since ${leftValue} is ${conditionResult ? '' : 'NOT '}${operatorText} ${rightValue}, we use the <strong>${conditionResult ? 'first' : 'second'}</strong> value: <strong>${selectedValue}</strong>
                            </div>
                        `;
                    } else {
                        // Fallback for complex conditions
                        detailedExplanation = `
                            <div style="margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                                <strong>📝 Ternary Operation Explanation</strong><br>
                                The ternary operation <code>(${conditionPart} ? ${truePart.trim()} : ${falsePart.trim()})</code> means:<br>
                                <em>"If the condition is true, use ${truePart.trim()}; otherwise, use ${falsePart.trim()}."</em><br><br>

                                <strong>Result:</strong> The condition <code>${conditionPart}</code> is <strong>${conditionResult ? 'TRUE' : 'FALSE'}</strong><br>
                                So we use: <strong>${selectedValue}</strong>
                            </div>
                        `;
                    }

                    explanation += `
                        <div class="mt-2 p-3" style="background: #f0f8ff; border-left: 4px solid #007bff; font-size: 0.85em; border-radius: 4px;">
                            <strong>🧮 Ternary Operation Breakdown</strong>
                            ${detailedExplanation}
                        </div>
                    `;
                } catch (e) {
                    // If evaluation fails, just note that there's a ternary
                    explanation += `
                        <div class="mt-2 p-2" style="background: #fff8dc; border-left: 3px solid #ffc107; font-size: 0.8em;">
                            <strong>Ternary Operation Found:</strong><br>
                            <code>${fullMatch}</code><br>
                            <small>Unable to evaluate automatically</small>
                        </div>
                    `;
                }
            }

            return explanation;
        }

        // Process each multi-condition element
        multiConditionElements.forEach(item => {
            const conditionResults = item.conditions.map((condition, index) => {
                let result = false;
                let error = null;
                let evaluatedCondition = condition;
                let ternaryExplanation = '';

                try {
                    result = evaluateCondition(condition);

                    // Create a version with actual values substituted
                    evaluatedCondition = condition;

                    // Get current field values
                    const currentValues = window.fieldValues || {};

                    // Replace field names with their actual values
                    Object.keys(currentValues).forEach(fieldName => {
                        const value = currentValues[fieldName];
                        if (value !== null && value !== undefined && value !== '') {
                            // Use word boundaries to avoid partial replacements
                            const regex = new RegExp(`\\b${fieldName}\\b`, 'g');
                            evaluatedCondition = evaluatedCondition.replace(regex, value);
                        }
                    });

                    // Check for ternary operations and explain them
                    if (evaluatedCondition.includes('?') && evaluatedCondition.includes(':')) {
                        ternaryExplanation = explainTernary(condition, evaluatedCondition);

                        // Add step-by-step calculation after ternary resolution
                        ternaryExplanation += explainCalculation(evaluatedCondition);
                    }

                } catch (e) {
                    error = e.message;
                }

                return {
                    index: index + 1,
                    condition: condition,
                    evaluatedCondition: evaluatedCondition,
                    ternaryExplanation: ternaryExplanation,
                    result: result,
                    error: error
                };
            });

            // Handle OR group evaluation for complex single conditions
            let allConditionsTrue, hasErrors, orGroupResults;
            hasErrors = conditionResults.some(cr => cr.error);

            if (item.isComplexSingleCondition && item.orGroups) {
                // Evaluate each OR group separately
                orGroupResults = item.orGroups.map(group => {
                    const groupConditionResults = group.conditionIndices.map(idx => conditionResults[idx]);
                    const groupAllTrue = groupConditionResults.every(cr => cr && cr.result === true);
                    const groupHasErrors = groupConditionResults.some(cr => cr && cr.error);

                    return {
                        name: group.name,
                        allTrue: groupAllTrue,
                        hasErrors: groupHasErrors,
                        trueCount: groupConditionResults.filter(cr => cr && cr.result === true).length,
                        totalCount: groupConditionResults.length,
                        conditionResults: groupConditionResults
                    };
                });

                // For OR logic: true if ANY group is completely true
                allConditionsTrue = orGroupResults.some(group => group.allTrue);
            } else {
                // Standard evaluation: all conditions must be true
                allConditionsTrue = conditionResults.every(cr => cr.result === true);
            }

            // Create condition details HTML
            const conditionDetailsHtml = conditionResults.map(cr => {
                const statusClass = cr.error ? 'text-danger' : (cr.result ? 'text-success' : 'text-danger');
                const statusIcon = cr.error ? 'fa-exclamation-triangle' : (cr.result ? 'fa-check' : 'fa-times');
                const statusText = cr.error ? 'ERROR' : (cr.result ? 'TRUE' : 'FALSE');

                // Use custom condition name if available (for complex single conditions)
                const conditionLabel = item.conditionNames && item.conditionNames[cr.index - 1]
                    ? item.conditionNames[cr.index - 1]
                    : `Condition ${cr.index}`;

                return `
                    <div class="mb-2 p-2 border rounded" style="font-size: 0.85em;">
                        <div class="d-flex justify-content-between align-items-start">
                            <strong>${conditionLabel}:</strong>
                            <span class="${statusClass}">
                                <i class="fas ${statusIcon} me-1"></i>${statusText}
                            </span>
                        </div>
                        <code class="d-block mt-1 text-wrap" style="font-size: 0.8em; background: #f8f9fa; padding: 4px; border-radius: 3px;">
                            ${cr.condition}
                        </code>
                        ${cr.evaluatedCondition !== cr.condition ? `
                            <div class="mt-1">
                                <small class="text-muted">With values:</small>
                                <code class="d-block text-wrap" style="font-size: 0.75em; background: #e9ecef; padding: 3px; border-radius: 3px; color: #495057;">
                                    ${cr.evaluatedCondition}
                                </code>
                            </div>
                        ` : ''}
                        ${cr.ternaryExplanation ? cr.ternaryExplanation : ''}
                        ${cr.error ? `<div class="text-danger mt-1"><small>Error: ${cr.error}</small></div>` : ''}
                    </div>
                `;
            }).join('');

            // Overall status
            const overallStatusClass = hasErrors ? 'text-warning' : (allConditionsTrue ? 'text-success' : 'text-danger');
            const overallStatusIcon = hasErrors ? 'fa-exclamation-triangle' : (allConditionsTrue ? 'fa-check-circle' : 'fa-times-circle');
            const overallStatusText = hasErrors ? 'ERROR' : (allConditionsTrue ? 'VISIBLE' : 'HIDDEN');

            // Create display name with question information for options
            let displayName;
            if (item.type === 'option') {
                const questionInfo = item.questionNumber ? `Question ${item.questionNumber}` : item.fieldLabel;
                displayName = `${questionInfo}, Option ${item.optionId || item.fieldId} → "${item.optionText}"`;
            } else {
                displayName = item.fieldLabel;
            }

            // Create a more informative identifier
            const identifier = item.questionNumber ?
                `Q${item.questionNumber}` :
                `${item.type === 'option' ? 'Option' : 'Field'} ID: ${item.fieldId}`;

            // Add special header for complex single conditions
            let complexConditionHeader = '';
            if (item.isComplexSingleCondition) {
                complexConditionHeader = `
                    <div class="alert alert-info mb-2" style="font-size: 0.8em; padding: 8px;">
                        <i class="fas fa-info-circle me-1"></i>
                        <strong>Complex Single Condition Breakdown</strong><br>
                        <small>This option has a single condition with OR logic that has been broken down for analysis:</small><br>
                        <code style="font-size: 0.7em; word-break: break-all;">${item.originalComplexCondition}</code>
                    </div>
                `;
            }

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <strong>${displayName}</strong>
                    <br><small class="text-muted">${identifier}</small>
                </td>
                <td>
                    ${complexConditionHeader}
                    ${conditionDetailsHtml}
                </td>
                <td class="text-center">
                    ${item.isComplexSingleCondition && orGroupResults ? `
                        <div class="mb-1">
                            <strong>OR Group Results:</strong>
                        </div>
                        ${orGroupResults.map(group => `
                            <div class="mb-1">
                                <span class="badge ${group.allTrue ? 'bg-success' : 'bg-danger'}" style="font-size: 0.7em;">
                                    ${group.name}: ${group.trueCount}/${group.totalCount}
                                </span>
                            </div>
                        `).join('')}
                        <div class="mt-2">
                            <span class="badge ${allConditionsTrue ? 'bg-success' : 'bg-danger'}">
                                ${orGroupResults.some(g => g.allTrue) ? 'OR Satisfied' : 'OR Not Satisfied'}
                            </span>
                        </div>
                    ` : `
                        <div class="mb-1">
                            <span class="badge ${allConditionsTrue ? 'bg-success' : 'bg-danger'}">
                                ${conditionResults.filter(cr => cr.result).length}/${conditionResults.length} TRUE
                            </span>
                        </div>
                    `}
                    <small class="text-muted">
                        ${hasErrors ? 'Has Errors' : 'All evaluated'}
                    </small>
                </td>
                <td class="text-center">
                    <div class="${overallStatusClass}">
                        <i class="fas ${overallStatusIcon} fa-lg"></i>
                        <br><strong>${overallStatusText}</strong>
                    </div>
                </td>
            `;

            tableBody.appendChild(row);
        });
    }

    // Initialize when the DOM is loaded
    document.addEventListener('DOMContentLoaded', async function() {
        initializeFieldValues();
        addEventListeners();
        setupInstructionalImages();
        setupGlobalColorSelector();
        setupSectionCollapse();
        setupSearchableSelects();

        // Load calculated fields definitions first, then calculate initial values
        try {
            await loadCalculatedFields();

            // Now calculate initial dynamic fields with loaded definitions
            const initialCalculatedFields = calculateDynamicFields(fieldValues);

            // Update dynamic help text with calculated values
            updateDynamicHelp();
            updateDynamicDefaults();
            updateErrorMessages();
            updateFieldVisibility();
        } catch (error) {
            console.error('❌ Error during initial calculated fields setup:', error);
        }

        // Set the toggle to off by default (only show fields with met conditions)
        const showAllFieldsToggle = document.getElementById('show-all-fields');
        if (showAllFieldsToggle) {
            showAllFieldsToggle.checked = false;

            // Add event listener to the toggle
            showAllFieldsToggle.addEventListener('change', function() {
                updateFieldVisibilityDebounced(); // Use debounced version
            });
        }

        // Set up the visibility conditions toggle
        const showVisibilityConditionsToggle = document.getElementById('show-visibility-conditions');
        if (showVisibilityConditionsToggle) {
            showVisibilityConditionsToggle.checked = false;

            // Add event listener to the toggle
            showVisibilityConditionsToggle.addEventListener('change', function() {
                updateVisibilityConditionsDisplay();
            });
        }

        updateFieldVisibility();
        updateDynamicHelp();
        updateDynamicDefaults();
        updateErrorMessages();
        updateFieldConstraints();
        updateVisibilityConditionsDisplay();

        // Add form submission validation
        const configForm = document.getElementById('config-form');
        if (configForm) {
            configForm.addEventListener('submit', function(event) {
                // Always prevent default form submission to handle it manually
                event.preventDefault();

                // Always proceed with form submission, regardless of validation state

                // Clear hidden fields with values before submitting
                if (typeof window.findHiddenFieldsWithValues === 'function') {
                    const hiddenFields = window.findHiddenFieldsWithValues();

                    // If there are hidden fields with values, clear them
                    if (hiddenFields.length > 0) {

                        // Clear only the hidden fields with values
                        hiddenFields.forEach(field => {
                            const fieldElement = field.element;
                            const fieldId = field.id;
                            const technicalName = field.name;
                            const fieldType = fieldElement.getAttribute('data-field-type');

                            // Clear the field value in the UI
                            if (fieldType === 'boolean') {
                                fieldElement.checked = false;
                            } else if (fieldType === 'number' || fieldType === 'text' || fieldType === 'date') {
                                fieldElement.value = '';
                            } else if (fieldType === 'selection') {
                                fieldElement.value = '';
                            }

                            // Trigger change event to update any dependent fields
                            const event = new Event('change', { bubbles: true });
                            fieldElement.dispatchEvent(event);

                            // Clear the value in the fieldValues object if it exists
                            if (window.fieldValues) {
                                if (fieldId) {
                                    delete window.fieldValues[fieldId];
                                }
                                if (technicalName) {
                                    delete window.fieldValues[technicalName];
                                }
                            }
                        });

                        // Show a single popup message
                        const messageDiv = document.createElement('div');
                        messageDiv.className = 'alert alert-warning alert-dismissible fade show position-fixed';
                        messageDiv.style.top = '20px';
                        messageDiv.style.right = '20px';
                        messageDiv.style.zIndex = '2000';
                        messageDiv.innerHTML = `
                            <strong>Notice:</strong> ${hiddenFields.length} hidden field${hiddenFields.length !== 1 ? 's' : ''} cleared automatically.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        `;
                        document.body.appendChild(messageDiv);

                        // Auto-dismiss message after 3 seconds
                        setTimeout(() => {
                            messageDiv.remove();
                        }, 3000);
                    }
                } else {
                    console.error('findHiddenFieldsWithValues function not found. Make sure issues_panel_new.js is loaded.');
                }

                // Now manually submit the form

                // Check if we should use AJAX or regular form submission
                const saveConfigBtn = document.getElementById('save-config-btn');
                if (event.submitter === saveConfigBtn) {
                    // Regular form submission - this was triggered by the Save Configuration button
                    configForm.submit();
                } else {
                    // Use AJAX submission similar to the floating save button
                    const formData = new FormData(configForm);

                    // Add configuration price matrix value if available
                    // const configurationPriceMatrix = document.getElementById('configuration-price-matrix');
                    // if (configurationPriceMatrix && configurationPriceMatrix.textContent) {
                    //     // Extract the price value from the text content (remove currency symbol and formatting)
                    //     const priceText = configurationPriceMatrix.textContent.trim();
                    //     const priceValue = priceText.replace(/[^\d.,]/g, '').replace(',', '.');
                    //     if (priceValue && !isNaN(parseFloat(priceValue))) {
                    //         formData.append('configuration_price_matrix', priceValue);
                    //         console.log('[SAVE] Added configuration price matrix value:', priceValue);
                    //     }
                    // }

                    // Add a parameter to indicate this is an AJAX save
                    formData.append('ajax_save', 'true');

                    // Send the AJAX request
                    fetch(configForm.action, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        // If this was a new configuration, update the config_id in the form and URL
                        if (data.success && data.config_id) {
                            const configIdInput = configForm.querySelector('input[name="config_id"]');
                            const hasConfigId = configIdInput && configIdInput.value;

                            if (!hasConfigId) {
                                // Check if we already have a config_id input
                                if (configIdInput) {
                                    configIdInput.value = data.config_id;
                                } else {
                                    // Create a new config_id input
                                    const newConfigIdInput = document.createElement('input');
                                    newConfigIdInput.type = 'hidden';
                                    newConfigIdInput.name = 'config_id';
                                    newConfigIdInput.value = data.config_id;
                                    configForm.appendChild(newConfigIdInput);
                                }

                                // Update the URL to include the config_id parameter
                                const currentUrl = new URL(window.location);
                                currentUrl.searchParams.set('config_id', data.config_id);
                                window.history.replaceState({}, '', currentUrl);
                            }
                        }

                        // Show a success message
                        const messageDiv = document.createElement('div');
                        messageDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
                        messageDiv.style.top = '20px';
                        messageDiv.style.right = '20px';
                        messageDiv.style.zIndex = '2000';
                        messageDiv.innerHTML = `
                            <strong>Success!</strong> Configuration saved.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        `;
                        document.body.appendChild(messageDiv);

                        // Auto-dismiss message after 3 seconds
                        setTimeout(() => {
                            messageDiv.remove();
                        }, 3000);
                    })
                    .catch(error => {
                        console.error('Error saving configuration:', error);

                        // Show an error message
                        const messageDiv = document.createElement('div');
                        messageDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
                        messageDiv.style.top = '20px';
                        messageDiv.style.right = '20px';
                        messageDiv.style.zIndex = '2000';
                        messageDiv.innerHTML = `
                            <strong>Error!</strong> Could not save configuration.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        `;
                        document.body.appendChild(messageDiv);

                        // Auto-dismiss message after 3 seconds
                        setTimeout(() => {
                            messageDiv.remove();
                        }, 3000);
                    });
                }
            });
        }

        // Debug panel event listeners
        const showDebugButton = document.getElementById('show-debug-panel');
        const hideDebugButton = document.getElementById('hide-debug-panel');
        const refreshDebugButton = document.getElementById('refresh-debug');
        const debugPanel = document.getElementById('debug-panel');
        const saveConfigBtn = document.getElementById('save-config-btn');
        const floatingSaveBtn = document.getElementById('floating-save-btn');

        // Add a direct click handler to the Save Configuration button
        if (saveConfigBtn) {
            saveConfigBtn.addEventListener('click', function(event) {
                // Try to directly call the clearAllIssues function if it exists in the global scope
                if (typeof window.clearAllIssues === 'function') {
                    window.clearAllIssues();
                    return; // Skip the rest of the function
                }

                // Fallback to using the findHiddenFieldsWithValues function
                if (typeof window.findHiddenFieldsWithValues === 'function') {
                    const hiddenFields = window.findHiddenFieldsWithValues();

                    // If there are hidden fields with values, clear them
                    if (hiddenFields.length > 0) {

                        // Clear only the hidden fields with values
                        hiddenFields.forEach(field => {
                            const fieldElement = field.element;
                            const fieldId = field.id;
                            const technicalName = field.name;
                            const fieldType = fieldElement.getAttribute('data-field-type');

                            // Clear the field value in the UI
                            if (fieldType === 'boolean') {
                                fieldElement.checked = false;
                            } else if (fieldType === 'number' || fieldType === 'text' || fieldType === 'date') {
                                fieldElement.value = '';
                            } else if (fieldType === 'selection') {
                                fieldElement.value = '';
                            }

                            // Trigger change event to update any dependent fields
                            const event = new Event('change', { bubbles: true });
                            fieldElement.dispatchEvent(event);

                            // Clear the value in the fieldValues object if it exists
                            if (window.fieldValues) {
                                if (fieldId) {
                                    delete window.fieldValues[fieldId];
                                }
                                if (technicalName) {
                                    delete window.fieldValues[technicalName];
                                }
                            }
                        });

                        // Show a single popup message
                        const messageDiv = document.createElement('div');
                        messageDiv.className = 'alert alert-warning alert-dismissible fade show position-fixed';
                        messageDiv.style.top = '20px';
                        messageDiv.style.right = '20px';
                        messageDiv.style.zIndex = '2000';
                        messageDiv.innerHTML = `
                            <strong>Notice:</strong> ${hiddenFields.length} hidden field${hiddenFields.length !== 1 ? 's' : ''} cleared automatically.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        `;
                        document.body.appendChild(messageDiv);

                        // Auto-dismiss message after 3 seconds
                        setTimeout(() => {
                            messageDiv.remove();
                        }, 3000);
                    }
                } else {
                    console.error('Direct click handler - findHiddenFieldsWithValues function not found');
                }

                // Let the form submission continue normally
            });
        }

        if (showDebugButton && debugPanel) {
            showDebugButton.addEventListener('click', function() {
                debugPanel.style.display = 'block';
                updateDebugPanel();
            });
        }

        if (hideDebugButton && debugPanel) {
            hideDebugButton.addEventListener('click', function() {
                debugPanel.style.display = 'none';
            });
        }

        if (refreshDebugButton) {
            refreshDebugButton.addEventListener('click', updateDebugPanel);
        }

        // Conditions Debug Panel event handlers
        const showConditionsDebugButton = document.getElementById('show-conditions-debug-panel');
        const hideConditionsDebugButton = document.getElementById('hide-conditions-debug-panel');
        const refreshConditionsDebugButton = document.getElementById('refresh-conditions-debug');
        const conditionsDebugPanel = document.getElementById('conditions-debug-panel');

        if (showConditionsDebugButton && conditionsDebugPanel) {
            showConditionsDebugButton.addEventListener('click', function() {
                conditionsDebugPanel.style.display = 'block';
                updateConditionsDebugPanel();
            });
        }

        if (hideConditionsDebugButton && conditionsDebugPanel) {
            hideConditionsDebugButton.addEventListener('click', function() {
                conditionsDebugPanel.style.display = 'none';
            });
        }

        if (refreshConditionsDebugButton) {
            refreshConditionsDebugButton.addEventListener('click', updateConditionsDebugPanel);
        }

        // Calculated Fields Panel event handlers
        const showCalculatedFieldsButton = document.getElementById('show-calculated-fields-panel');
        const hideCalculatedFieldsButton = document.getElementById('hide-calculated-fields-panel');
        const refreshCalculatedFieldsButton = document.getElementById('refresh-calculated-fields');
        const calculatedFieldsPanel = document.getElementById('calculated-fields-panel');

        if (showCalculatedFieldsButton && calculatedFieldsPanel) {
            showCalculatedFieldsButton.addEventListener('click', function() {
                calculatedFieldsPanel.style.display = 'block';
                updateCalculatedFieldsPanel();
            });
        }

        if (hideCalculatedFieldsButton && calculatedFieldsPanel) {
            hideCalculatedFieldsButton.addEventListener('click', function() {
                calculatedFieldsPanel.style.display = 'none';
            });
        }

        if (refreshCalculatedFieldsButton) {
            refreshCalculatedFieldsButton.addEventListener('click', updateCalculatedFieldsPanel);
        }

        // Floating save button functionality
        if (floatingSaveBtn && saveConfigBtn) {
            floatingSaveBtn.addEventListener('click', function() {
                // Always proceed with save, regardless of validation state

                // First try to directly call the clearAllIssues function if it exists in the global scope
                let hiddenFields = [];

                if (typeof window.clearAllIssues === 'function') {
                    window.clearAllIssues();
                } else {
                    // Fallback to using the findHiddenFieldsWithValues function

                    if (typeof window.findHiddenFieldsWithValues === 'function') {
                        hiddenFields = window.findHiddenFieldsWithValues();
                    } else {
                        console.error('findHiddenFieldsWithValues function not found. Make sure issues_panel_new.js is loaded.');
                    }

                    // If there are hidden fields with values, clear them
                    if (hiddenFields.length > 0) {

                    // Clear only the hidden fields with values
                    hiddenFields.forEach(field => {
                        const fieldElement = field.element;
                        const fieldId = field.id;
                        const technicalName = field.name;
                        const fieldType = fieldElement.getAttribute('data-field-type');

                        // Clear the field value in the UI
                        if (fieldType === 'boolean') {
                            fieldElement.checked = false;
                        } else if (fieldType === 'number' || fieldType === 'text' || fieldType === 'date') {
                            fieldElement.value = '';
                        } else if (fieldType === 'selection') {
                            fieldElement.value = '';
                        }

                        // Trigger change event to update any dependent fields
                        const event = new Event('change', { bubbles: true });
                        fieldElement.dispatchEvent(event);

                        // Clear the value in the fieldValues object if it exists
                        if (window.fieldValues) {
                            if (fieldId) {
                                delete window.fieldValues[fieldId];
                            }
                            if (technicalName) {
                                delete window.fieldValues[technicalName];
                            }
                        }
                    });

                    // Show a single popup message
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'alert alert-warning alert-dismissible fade show position-fixed';
                    messageDiv.style.top = '20px';
                    messageDiv.style.right = '20px';
                    messageDiv.style.zIndex = '2000';
                    messageDiv.innerHTML = `
                        <strong>Notice:</strong> ${hiddenFields.length} hidden field${hiddenFields.length !== 1 ? 's' : ''} cleared automatically.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    document.body.appendChild(messageDiv);

                    // Auto-dismiss message after 3 seconds
                    setTimeout(() => {
                        messageDiv.remove();
                    }, 3000);
                    }
                }

                // Save the configuration via AJAX instead of form submission
                const form = document.querySelector('form');
                const formData = new FormData(form);

                // Add configuration price matrix value if available
                // const configurationPriceMatrix = document.getElementById('configuration-price-matrix');
                // if (configurationPriceMatrix && configurationPriceMatrix.textContent) {
                //     // Extract the price value from the text content (remove currency symbol and formatting)
                //     const priceText = configurationPriceMatrix.textContent.trim();
                //     const priceValue = priceText.replace(/[^\d.,]/g, '').replace(',', '.');
                //     if (priceValue && !isNaN(parseFloat(priceValue))) {
                //         formData.append('configuration_price_matrix', priceValue);
                //         console.log('[SAVE] Added configuration price matrix value:', priceValue);
                //     }
                // }

                // Add a parameter to indicate this is an AJAX save
                formData.append('ajax_save', 'true');

                // Check if we have a config_id already
                const configIdInput = form.querySelector('input[name="config_id"]');
                const hasConfigId = configIdInput && configIdInput.value;



                // Show a loading indicator on the button
                const originalIcon = floatingSaveBtn.innerHTML;
                floatingSaveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                floatingSaveBtn.disabled = true;

                // Send the AJAX request
                fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // Show success indicator
                    floatingSaveBtn.innerHTML = '<i class="fas fa-check"></i>';
                    floatingSaveBtn.style.backgroundColor = '#28a745';

                    // If this was a new configuration, update the config_id in the form and URL
                    if (data.success && data.config_id && !hasConfigId) {

                        // Check if we already have a config_id input
                        if (configIdInput) {
                            configIdInput.value = data.config_id;
                        } else {
                            // Create a new config_id input
                            const newConfigIdInput = document.createElement('input');
                            newConfigIdInput.type = 'hidden';
                            newConfigIdInput.name = 'config_id';
                            newConfigIdInput.value = data.config_id;
                            form.appendChild(newConfigIdInput);
                        }

                        // Update the URL to include the config_id parameter
                        const currentUrl = new URL(window.location);
                        currentUrl.searchParams.set('config_id', data.config_id);
                        window.history.replaceState({}, '', currentUrl);
                    }

                    // Show a success message
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
                    messageDiv.style.top = '20px';
                    messageDiv.style.right = '20px';
                    messageDiv.style.zIndex = '2000';
                    messageDiv.innerHTML = `
                        <strong>Success!</strong> Configuration saved.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    document.body.appendChild(messageDiv);

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        floatingSaveBtn.innerHTML = originalIcon;
                        floatingSaveBtn.style.backgroundColor = '#645CB3';
                        floatingSaveBtn.disabled = false;

                        // Auto-dismiss message after 5 seconds
                        setTimeout(() => {
                            messageDiv.remove();
                        }, 3000);
                    }, 2000);
                })
                .catch(error => {
                    // Show error indicator
                    floatingSaveBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                    floatingSaveBtn.style.backgroundColor = '#dc3545';

                    // Show an error message
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
                    messageDiv.style.top = '20px';
                    messageDiv.style.right = '20px';
                    messageDiv.style.zIndex = '2000';
                    messageDiv.innerHTML = `
                        <strong>Error!</strong> Could not save configuration.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    document.body.appendChild(messageDiv);

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        floatingSaveBtn.innerHTML = originalIcon;
                        floatingSaveBtn.style.backgroundColor = '#645CB3';
                        floatingSaveBtn.disabled = false;

                        // Auto-dismiss message after 5 seconds
                        setTimeout(() => {
                            messageDiv.remove();
                        }, 3000);
                    }, 2000);

                    console.error('Error saving configuration:', error);
                });
            });

            // Always show floating button regardless of scroll position
            function updateFloatingButtonVisibility() {
                // Always keep the floating save button visible
                floatingSaveBtn.style.opacity = '1';
                floatingSaveBtn.style.pointerEvents = 'auto';
            }

            window.addEventListener('scroll', updateFloatingButtonVisibility);

            // Also update when debug panel or SVG panel is shown/hidden
            if (showDebugButton) {
                showDebugButton.addEventListener('click', function() {
                    setTimeout(updateFloatingButtonVisibility, 100);
                });
            }

            if (hideDebugButton) {
                hideDebugButton.addEventListener('click', function() {
                    setTimeout(updateFloatingButtonVisibility, 100);
                });
            }

            // Add event listeners for SVG panel buttons
            const showSvgButton = document.getElementById('showSvgButtonInline');
            const hideSvgButton = document.getElementById('hide-svg-panel');

            if (showSvgButton) {
                showSvgButton.addEventListener('click', function() {
                    setTimeout(updateFloatingButtonVisibility, 100);
                });
            }

            if (hideSvgButton) {
                hideSvgButton.addEventListener('click', function() {
                    setTimeout(updateFloatingButtonVisibility, 100);
                });
            }

            // Trigger scroll event once to set initial state
            window.dispatchEvent(new Event('scroll'));
        }

    });

})();
