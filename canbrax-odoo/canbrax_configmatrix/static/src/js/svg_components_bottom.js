/**
 * SVG Components Bottom Panel
 *
 * This script adds functionality to the "Show SVG" button to display
 * SVG components in a panel at the bottom of the page, similar to the debug panel.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('[SVG-BOTTOM] Initializing SVG Components Bottom Panel');

    // Initialize the SVG button
    function initializeSvgButton() {
        // Find the button
        const showSvgButton = document.getElementById('showSvgButtonInline');
        const svgPanel = document.getElementById('svg-panel');

        if (!showSvgButton || !svgPanel) {
            console.warn('[SVG-BOTTOM] Show SVG button or panel not found');
            return;
        }

        // Add event listener to the button
        showSvgButton.addEventListener('click', function() {
            // Toggle SVG components panel
            const isVisible = svgPanel.style.display !== 'none';
            svgPanel.style.display = isVisible ? 'none' : 'block';

            // Load components if panel is being shown
            if (!isVisible) {
                loadSvgComponents();
            }
        });

        // Add event listener to hide button
        const hideSvgButton = document.getElementById('hide-svg-panel');
        if (hideSvgButton) {
            hideSvgButton.addEventListener('click', function() {
                svgPanel.style.display = 'none';
            });
        }

        // Add event listener to refresh button
        const refreshSvgButton = document.getElementById('refresh-svg');
        if (refreshSvgButton) {
            refreshSvgButton.addEventListener('click', loadSvgComponents);
        }

        console.log('[SVG-BOTTOM] SVG button initialized');
    }

    // Function to load SVG components
    function loadSvgComponents() {
        console.log('[SVG-BOTTOM] Loading SVG components');

        // Get template ID
        const templateIdInput = document.querySelector('input[name="template_id"]');
        if (!templateIdInput) {
            console.error('[SVG-BOTTOM] Template ID input not found');
            return;
        }

        const templateId = parseInt(templateIdInput.value) || 0;
        console.log('[SVG-BOTTOM] Template ID:', templateId);

        // Show loading indicator
        const svgComponentsTableBody = document.getElementById('svg-components-table-body');
        if (svgComponentsTableBody) {
            svgComponentsTableBody.innerHTML = '<tr><td colspan="6" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading SVG components...</td></tr>';
        }

        // Fetch SVG components
        fetch('/config_matrix/get_svg_components', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                jsonrpc: "2.0",
                method: "call",
                params: {
                    template_id: templateId
                },
                id: new Date().getTime()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.result && data.result.success && data.result.components) {
                displaySvgComponents(data.result.components);
            } else {
                console.error('[SVG-BOTTOM] Error loading SVG components:', data);
                if (svgComponentsTableBody) {
                    svgComponentsTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error loading SVG components</td></tr>';
                }
            }
        })
        .catch(error => {
            console.error('[SVG-BOTTOM] Error fetching SVG components:', error);
            if (svgComponentsTableBody) {
                svgComponentsTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error loading SVG components</td></tr>';
            }
        });
    }

    // Function to display SVG components
    function displaySvgComponents(components) {
        console.log('[SVG-BOTTOM] Displaying SVG components:', components);

        const svgComponentsTableBody = document.getElementById('svg-components-table-body');
        if (!svgComponentsTableBody) {
            console.error('[SVG-BOTTOM] SVG components table body not found');
            return;
        }

        // Sort components by z-index
        components.sort((a, b) => a.z_index - b.z_index);

        // Clear the table
        svgComponentsTableBody.innerHTML = '';

        // Get field values from global scope
        let fieldValues = {};
        if (window.fieldValues) {
            fieldValues = { ...window.fieldValues };

            // Log field values count for debugging (massive object logging disabled)
            if (window.debugConditions) {
                console.log('[SVG-BOTTOM] Field values count:', Object.keys(fieldValues).length);
            }

            // Check for empty selection fields and set them to empty string explicitly
            for (const [key, value] of Object.entries(fieldValues)) {
                if (value === null || value === undefined) {
                    fieldValues[key] = '';
                    console.log(`[SVG-BOTTOM] Setting empty field ${key} to empty string`);
                }
            }
        } else {
            console.warn('[SVG-BOTTOM] fieldValues not found in global scope, using empty object');
        }

        // Add each component to the table
        components.forEach(component => {
            const row = document.createElement('tr');

            // Evaluate condition if present
            let isVisible = true;
            if (component.condition) {
                // For base components, always show as visible
                if (component.component_type === 'base') {
                    isVisible = true;
                } else {
                    isVisible = evaluateCondition(component.condition, fieldValues);
                }
                console.log(`[SVG-BOTTOM] Component ${component.name} with condition "${component.condition}" is ${isVisible ? 'visible' : 'hidden'}`);
            }

            row.innerHTML = `
                <td>${component.name}</td>
                <td>${component.component_type}</td>
                <td>${component.z_index}</td>
                <td><code>${component.condition || 'Always visible'}</code></td>
                <td>${isVisible ? '<span class="badge bg-success">Visible</span>' : '<span class="badge bg-secondary">Hidden</span>'}</td>
                <td><button class="btn btn-sm btn-outline-primary preview-svg-btn" type="button">Preview</button></td>
            `;

            svgComponentsTableBody.appendChild(row);

            // Add event listener to preview button
            const previewButton = row.querySelector('.preview-svg-btn');
            previewButton.addEventListener('click', function() {
                showSvgPreview(component, fieldValues);
            });
        });
    }

    // Function to show SVG preview
    function showSvgPreview(component, fieldValues) {
        console.log('[SVG-BOTTOM] Showing SVG preview for component:', component.name);

        const svgPreviewContainer = document.getElementById('svg-preview-container');
        if (!svgPreviewContainer) {
            console.error('[SVG-BOTTOM] SVG preview container not found');
            return;
        }

        // Process SVG content with field values
        let svgContent = component.svg_content;

        // Replace ${fieldName} with actual values
        svgContent = svgContent.replace(/\${(\w+)}/g, function(match, fieldName) {
            return fieldValues[fieldName] !== undefined
                   ? fieldValues[fieldName]
                   : match;
        });

        // Check if the content is a complete SVG or just a fragment
        let wrappedSvgContent = svgContent;
        if (!svgContent.trim().startsWith('<svg')) {
            // Wrap the fragment in an SVG tag
            wrappedSvgContent = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 600" width="100%" height="100%">
                ${svgContent}
            </svg>`;
            console.log('[SVG-BOTTOM] Wrapped SVG fragment in SVG tags');
        }

        // Update the preview container
        svgPreviewContainer.innerHTML = `
            <div class="mb-2">
                <h6>${component.name}</h6>
                <div class="small text-muted">
                    Type: ${component.component_type}, Z-Index: ${component.z_index}
                    ${component.condition ? `<br>Condition: <code>${component.condition}</code>` : ''}
                </div>
            </div>
            <div class="svg-preview" style="background-color: #f8f9fa; padding: 10px; border-radius: 4px;">
                ${wrappedSvgContent}
            </div>
        `;
    }

    // Function to evaluate a condition
    function evaluateCondition(condition, fieldValues) {
        console.log(`[SVG-BOTTOM] Evaluating condition: "${condition}" with field values:`, fieldValues);

        if (!condition || condition === 'true' || condition === 'Always visible') {
            console.log('[SVG-BOTTOM] Condition is empty, true, or "Always visible", returning true');
            return true;
        }

        try {
            // Check if we need to use the global evaluateCondition function
            if (window.evaluateCondition && typeof window.evaluateCondition === 'function') {
                console.log('[SVG-BOTTOM] Using global evaluateCondition function');
                return window.evaluateCondition(condition);
            }

            // Handle equality conditions (e.g., "field == value")
            const equalityMatch = condition.match(/^\s*(\w+)\s*(==|===|!=|!==|>=|<=|>|<)\s*['"]?([^'"]*?)['"]?\s*$/);
            if (equalityMatch) {
                const [_, fieldName, operator, value] = equalityMatch;
                const fieldValue = fieldValues[fieldName];

                console.log(`[SVG-BOTTOM] Equality condition detected: ${fieldName} ${operator} ${value}`);
                console.log(`[SVG-BOTTOM] Field value: ${fieldValue}`);

                // If the field is empty/undefined/null and we're checking for equality with a value,
                // the condition should be false (component hidden)
                if ((fieldValue === undefined || fieldValue === null || fieldValue === '') &&
                    (operator === '==' || operator === '===')) {
                    console.log(`[SVG-BOTTOM] Field ${fieldName} is empty/unselected, condition is false`);
                    return false;
                }

                // If the field is empty/undefined/null and we're checking for inequality with a value,
                // the condition should be true (component visible)
                if ((fieldValue === undefined || fieldValue === null || fieldValue === '') &&
                    (operator === '!=' || operator === '!==')) {
                    console.log(`[SVG-BOTTOM] Field ${fieldName} is empty/unselected, condition is true`);
                    return true;
                }

                switch (operator) {
                    case '==':
                    case '===':
                        return String(fieldValue) === String(value);
                    case '!=':
                    case '!==':
                        return String(fieldValue) !== String(value);
                    case '>':
                        return Number(fieldValue) > Number(value);
                    case '<':
                        return Number(fieldValue) < Number(value);
                    case '>=':
                        return Number(fieldValue) >= Number(value);
                    case '<=':
                        return Number(fieldValue) <= Number(value);
                }
            }

            // Extract field names from the condition
            const fieldNames = condition.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || [];

            // Create a context with field values
            const context = { ...fieldValues };

            // Add missing fields with default values
            fieldNames.forEach(fieldName => {
                if (context[fieldName] === undefined) {
                    console.log(`[SVG-BOTTOM] Field '${fieldName}' not found in context, setting default value`);
                    context[fieldName] = '';
                }
            });

            // Create a function with the field values in scope
            const conditionCode = `return ${condition};`;
            console.log(`[SVG-BOTTOM] Evaluating code: ${conditionCode} with context:`, context);

            const func = new Function(...Object.keys(context), conditionCode);

            // Call the function with the field values
            const result = func(...Object.values(context));
            console.log(`[SVG-BOTTOM] Condition result: ${result}`);
            return result;
        } catch (error) {
            console.error('[SVG-BOTTOM] Error evaluating condition:', error);
            // For debugging, let's try to make most conditions visible
            return true;
        }
    }

    // Function to refresh SVG components
    function refreshSvgComponents() {
        console.log('[SVG-BOTTOM] Refreshing SVG components');
        const templateId = document.getElementById('template_id')?.value;
        if (templateId) {
            loadSvgComponents(templateId);
        } else {
            console.error('[SVG-BOTTOM] Template ID not found');
        }
    }

    // Initialize when the page loads
    setTimeout(initializeSvgButton, 500);

    // Add event listener to the refresh button
    document.addEventListener('DOMContentLoaded', function() {
        const refreshButton = document.getElementById('svg-components-refresh');
        if (refreshButton) {
            refreshButton.addEventListener('click', refreshSvgComponents);
        }

        // Also refresh when field values change
        document.querySelectorAll('.config-field').forEach(field => {
            field.addEventListener('change', function() {
                // Wait a bit for field values to update
                setTimeout(refreshSvgComponents, 300);
            });
        });
    });

    // Make functions available globally
    window.svgComponentsBottom = {
        initializeSvgButton,
        loadSvgComponents,
        displaySvgComponents,
        showSvgPreview,
        evaluateCondition,
        refreshSvgComponents
    };
});
