/**
 * Direct Split JavaScript
 * This file is loaded directly in the template to handle split functionality
 */
(function() {
    'use strict';
    
    console.log("Loading direct split module");

    // Patch Element.prototype.removeChild to prevent errors
    try {
        if (Element.prototype.removeChild) {
            const originalRemoveChild = Element.prototype.removeChild;
            Element.prototype.removeChild = function(child) {
                try {
                    if (child && this.contains(child)) {
                        return originalRemoveChild.call(this, child);
                    } else {
                        console.warn("Prevented removeChild on non-existent child");
                        return null;
                    }
                } catch (error) {
                    console.error("Error in removeChild:", error);
                    return null;
                }
            };
        }
    } catch (error) {
        console.error("Failed to patch removeChild method:", error);
    }

    // Function to handle split button click
    function handleSplitButtonClick(event) {
        try {
            if (!event) return;
            event.preventDefault();
            
            console.log("Split button clicked via direct_split.js");

            var button = event.currentTarget;
            if (!button) return;
            
            var lineId = button.dataset.lineId;
            if (!lineId) {
                console.error("No line ID found in button data");
                return;
            }

            // Confirmation dialog
            if (!confirm('This will split the product into individual items with identical configurations. Continue?')) {
                return;
            }

            // Disable the button to prevent multiple clicks
            button.disabled = true;
            button.classList.add('disabled');

            // Show loading spinner
            button.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';

            // Get CSRF token
            var csrfToken = '';
            var tokenElements = document.getElementsByName('csrf_token');
            if (tokenElements.length > 0) {
                csrfToken = tokenElements[0].value;
            }

            // Call the server to split the line - using fetch instead of $.ajax
            fetch('/config_matrix/split_line', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    'order_line_id': lineId,
                    'csrf_token': csrfToken
                })
            })
            .then(function(response) {
                return response.json();
            })
            .then(function(result) {
                if (result.success) {
                    // Show success message before reload
                    var successAlert = document.createElement('div');
                    successAlert.className = 'alert alert-success mt-2';
                    successAlert.textContent = 'Product has been split into individual items with identical configurations.';
                    
                    if (button.parentNode) {
                        button.parentNode.appendChild(successAlert);
                    }

                    // Reload the page after a short delay
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    // Show error message
                    button.disabled = false;
                    button.classList.remove('disabled');
                    button.innerHTML = '<i class="fa fa-object-ungroup me-1"></i>Split';

                    // Display error in a more user-friendly way
                    var errorAlert = document.createElement('div');
                    errorAlert.className = 'alert alert-danger mt-2';
                    errorAlert.textContent = result.error || 'Could not split the line. Please try again.';
                    
                    if (button.parentNode) {
                        button.parentNode.appendChild(errorAlert);

                        // Auto-remove the alert after 5 seconds - WITH EXTREME SAFETY
                        var errorAlertRef = errorAlert; // Keep reference for closure
                        setTimeout(function() {
                            try {
                                if (errorAlertRef && errorAlertRef.parentNode) {
                                    var parent = errorAlertRef.parentNode;
                                    if (parent && parent.contains && parent.contains(errorAlertRef)) {
                                        parent.removeChild(errorAlertRef);
                                    }
                                }
                            } catch (error) {
                                console.warn("Could not remove error alert, but prevented error from showing:", error);
                            }
                        }, 5000);
                    }
                }
            })
            .catch(function(error) {
                console.error('Error splitting line:', error);

                // Re-enable the button and show error
                button.disabled = false;
                button.classList.remove('disabled');
                button.innerHTML = '<i class="fa fa-object-ungroup me-1"></i>Split';

                // Display error in a more user-friendly way
                var errorAlert = document.createElement('div');
                errorAlert.className = 'alert alert-danger mt-2';
                errorAlert.textContent = 'Could not split the line. Please try again.';
                
                if (button.parentNode) {
                    button.parentNode.appendChild(errorAlert);

                    // Auto-remove the alert after 5 seconds - WITH EXTREME SAFETY
                    var errorAlertRef = errorAlert; // Keep reference for closure
                    setTimeout(function() {
                        try {
                            if (errorAlertRef && errorAlertRef.parentNode) {
                                var parent = errorAlertRef.parentNode;
                                if (parent && parent.contains && parent.contains(errorAlertRef)) {
                                    parent.removeChild(errorAlertRef);
                                }
                            }
                        } catch (error) {
                            console.warn("Could not remove error alert, but prevented error from showing:", error);
                        }
                    }, 5000);
                }
            });
        } catch (error) {
            console.error("Error in split button click handler:", error);
        }
    }

    // Initialize when the DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        try {
            console.log("Direct split: DOM loaded");

            // Add event listeners to split buttons
            var splitButtons = document.querySelectorAll('.js_config_split_line');
            console.log("Split buttons found:", splitButtons.length);

            for (var i = 0; i < splitButtons.length; i++) {
                if (!splitButtons[i]) continue;
                
                console.log("Adding click listener to split button:", splitButtons[i]);
                splitButtons[i].addEventListener('click', handleSplitButtonClick);
            }
        } catch (error) {
            console.error("Error initializing direct split module:", error);
        }
    });
})();
