/**
 * Split Modal JavaScript
 * Handles the split quantity modal dialog
 */
(function() {
    'use strict';

    console.log("Loading split modal module");

    // Wait for the DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        try {
            console.log("Split modal: DOM loaded");
            
            // Only initialize if we're on a page with split buttons
            const splitButtons = document.querySelectorAll('.js_config_split_line');
            console.log("Split buttons found:", splitButtons.length);
            
            if (splitButtons.length > 0) {
                // Initialize the split modal functionality
                initializeSplitModal();
            } else {
                console.log("No split buttons found, skipping modal initialization");
            }
        } catch (error) {
            console.error("Error initializing split modal:", error);
        }
    });

    /**
     * Initialize the split modal functionality
     */
    function initializeSplitModal() {
        try {
            // Create the modal HTML if it doesn't exist
            createSplitModal();

            // Add event listeners for the split buttons
            setupSplitButtons();
        } catch (error) {
            console.error("Error in initializeSplitModal:", error);
        }
    }

    /**
     * Create the split modal HTML
     */
    function createSplitModal() {
        console.log("Creating split modal");

        try {
            // Check if the modal already exists
            if (document.getElementById('splitQuantityModal')) {
                console.log("Split modal already exists, not creating again");
                return;
            }

            console.log("Split modal doesn't exist, creating it now");

            // Create the modal HTML
            const modalHTML = `
                <div class="modal fade" id="splitQuantityModal" tabindex="-1" aria-labelledby="splitQuantityModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="splitQuantityModalLabel">Split Configuration</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p>Choose how to split this configuration:</p>
                                <div class="mb-3">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="radio" name="splitType" id="splitTypeIndividual" value="individual" checked>
                                        <label class="form-check-label" for="splitTypeIndividual">
                                            Split into individual items (quantity of 1 each)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="splitType" id="splitTypeCustom" value="custom">
                                        <label class="form-check-label" for="splitTypeCustom">
                                            Split into a specific number of items
                                        </label>
                                    </div>
                                    <div id="customSplitOptions" class="mt-3 ps-4" style="display: none;">
                                        <div class="form-group">
                                            <label for="splitQuantity">Number of items to split into:</label>
                                            <input type="number" class="form-control" id="splitQuantity" min="2" value="2">
                                            <small class="form-text text-muted">
                                                The original quantity will be distributed as evenly as possible.
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" id="splitLineId" value="">
                                <input type="hidden" id="splitOriginalQty" value="">
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" id="confirmSplitBtn">Split</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Append the modal to the body
            const modalContainer = document.createElement('div');
            modalContainer.innerHTML = modalHTML;
            document.body.appendChild(modalContainer);

            // Add event listeners for the modal
            setupModalEvents();
        } catch (error) {
            console.error("Error creating split modal:", error);
        }
    }

    /**
     * Setup event listeners for the modal
     */
    function setupModalEvents() {
        try {
            console.log("Setting up modal events");

            // Show/hide custom split options based on radio selection
            const radioButtons = document.querySelectorAll('input[name="splitType"]');
            console.log("Radio buttons found:", radioButtons.length);

            if (radioButtons.length === 0) {
                console.warn("No radio buttons found for split type");
                return;
            }

            radioButtons.forEach(radio => {
                if (!radio) return;
                
                console.log("Adding event listener to radio button:", radio.id);
                radio.addEventListener('change', function() {
                    console.log("Radio button changed:", this.value);
                    const customOptions = document.getElementById('customSplitOptions');
                    if (!customOptions) return;
                    
                    if (this.value === 'custom') {
                        customOptions.style.display = 'block';
                    } else {
                        customOptions.style.display = 'none';
                    }
                });
            });

            // Handle confirm button click
            const confirmBtn = document.getElementById('confirmSplitBtn');
            console.log("Confirm button found:", confirmBtn);

            if (!confirmBtn) {
                console.warn("Confirm button not found");
                return;
            }

            confirmBtn.addEventListener('click', function() {
                try {
                    console.log("Confirm button clicked");

                    const splitLineIdElem = document.getElementById('splitLineId');
                    if (!splitLineIdElem) {
                        console.error("Split line ID element not found");
                        return;
                    }
                    
                    const lineId = splitLineIdElem.value;
                    
                    const splitTypeElem = document.querySelector('input[name="splitType"]:checked');
                    if (!splitTypeElem) {
                        console.error("Split type element not found");
                        return;
                    }
                    
                    const splitType = splitTypeElem.value;
                    console.log("Split type:", splitType, "Line ID:", lineId);

                    if (splitType === 'individual') {
                        // Split into individual items
                        console.log("Splitting into individual items");
                        splitLine(lineId);
                    } else {
                        // Split into custom number of items
                        const splitQuantityElem = document.getElementById('splitQuantity');
                        if (!splitQuantityElem) {
                            console.error("Split quantity element not found");
                            return;
                        }
                        
                        const splitQuantity = splitQuantityElem.value;
                        console.log("Splitting into", splitQuantity, "items");
                        splitLine(lineId, splitQuantity);
                    }

                    // Close the modal
                    try {
                        const modalElement = document.getElementById('splitQuantityModal');
                        if (!modalElement) {
                            console.error("Modal element not found");
                            return;
                        }
                        
                        console.log("Modal element:", modalElement);
                        
                        // Check if bootstrap is available
                        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                            const modal = bootstrap.Modal.getInstance(modalElement);
                            console.log("Bootstrap modal instance:", modal);
                            if (modal) {
                                modal.hide();
                            } else {
                                console.error("Could not get modal instance");
                                modalElement.style.display = 'none';
                            }
                        } else {
                            console.warn("Bootstrap not available, hiding modal directly");
                            modalElement.style.display = 'none';
                        }
                    } catch (error) {
                        console.error("Error closing modal:", error);
                    }
                } catch (error) {
                    console.error("Error handling confirm button click:", error);
                }
            });
        } catch (error) {
            console.error("Error setting up modal events:", error);
        }
    }

    /**
     * Setup event listeners for the split buttons
     */
    function setupSplitButtons() {
        try {
            console.log("Setting up split button event listeners");

            // Add direct event listeners to each split button
            const splitButtons = document.querySelectorAll('.js_config_split_line');
            if (splitButtons.length === 0) {
                console.warn("No split buttons found");
                return;
            }
            
            splitButtons.forEach(button => {
                if (!button) return;
                
                console.log("Found split button:", button);
                button.addEventListener('click', handleSplitButtonClick);
            });

            // Also add a delegated event listener for buttons added dynamically
            document.addEventListener('click', function(event) {
                try {
                    const splitButton = event.target ? event.target.closest('.js_config_split_line') : null;
                    if (splitButton) {
                        console.log("Split button clicked via delegation:", splitButton);
                        handleSplitButtonClick(event);
                    }
                } catch (error) {
                    console.error("Error in delegated click handler:", error);
                }
            });
        } catch (error) {
            console.error("Error setting up split buttons:", error);
        }
    }

    /**
     * Handle split button click
     */
    function handleSplitButtonClick(event) {
        try {
            if (!event) return;
            
            event.preventDefault();
            console.log("Split button click handler executed");

            const splitButton = event.target ? event.target.closest('.js_config_split_line') : null;
            if (!splitButton) {
                console.error("Split button not found in event target");
                return;
            }
            
            const lineId = splitButton.dataset.lineId;
            const originalQty = splitButton.dataset.quantity || 2;

            console.log("Split button data:", { lineId, originalQty });

            // Check if elements exist before setting values
            const splitLineIdElem = document.getElementById('splitLineId');
            const splitOriginalQtyElem = document.getElementById('splitOriginalQty');
            const splitQuantityInput = document.getElementById('splitQuantity');
            const splitModalElem = document.getElementById('splitQuantityModal');
            
            if (!splitLineIdElem || !splitOriginalQtyElem || !splitQuantityInput || !splitModalElem) {
                console.error("Required modal elements not found, falling back to direct split");
                // Fall back to direct split if modal elements aren't found
                splitLine(lineId);
                return;
            }

            // Set the line ID and original quantity in the modal
            splitLineIdElem.value = lineId;
            splitOriginalQtyElem.value = originalQty;

            // Set min value for split quantity
            splitQuantityInput.min = 2;
            splitQuantityInput.max = originalQty;
            splitQuantityInput.value = Math.min(originalQty, 2);

            // Check if bootstrap is available
            if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                // Show the modal
                try {
                    const modal = new bootstrap.Modal(splitModalElem);
                    modal.show();
                } catch (error) {
                    console.error("Error showing modal:", error);
                    // Fall back to direct split
                    splitLine(lineId);
                }
            } else {
                console.warn("Bootstrap not available, falling back to direct split");
                splitLine(lineId);
            }
        } catch (error) {
            console.error("Error handling split button click:", error);
            // Try to get the line ID from the event and fall back to direct split
            try {
                if (event && event.target) {
                    const splitButton = event.target.closest('.js_config_split_line');
                    if (splitButton && splitButton.dataset.lineId) {
                        splitLine(splitButton.dataset.lineId);
                    }
                }
            } catch (innerError) {
                console.error("Error in fallback split:", innerError);
            }
        }
    }

    /**
     * Split a line into individual or custom number of items
     */
    function splitLine(lineId, splitQuantity = null) {
        try {
            if (!lineId) {
                console.error("No line ID provided for splitting");
                return;
            }
            
            // Show loading indicator
            const button = document.querySelector(`.js_config_split_line[data-line-id="${lineId}"]`);
            if (button) {
                button.disabled = true;
                button.classList.add('disabled');
                button.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
            }

            // Prepare request data
            const requestData = {
                'order_line_id': lineId,
                'csrf_token': getCSRFToken(),
            };

            // Add split quantity if provided
            if (splitQuantity) {
                requestData.split_quantity = splitQuantity;
            }

            // Call the server to split the line
            fetch('/config_matrix/split_line', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                },
                body: JSON.stringify(requestData),
            })
            .then(response => response.json())
            .then(result => handleSplitResult(result, button, splitQuantity))
            .catch(error => handleSplitError(error, button));
        } catch (error) {
            console.error("Error in splitLine function:", error);
            
            // Try to re-enable the button if possible
            try {
                if (lineId) {
                    const button = document.querySelector(`.js_config_split_line[data-line-id="${lineId}"]`);
                    if (button) {
                        button.disabled = false;
                        button.classList.remove('disabled');
                        button.innerHTML = '<i class="fa fa-object-ungroup me-1"></i>Split';
                    }
                }
            } catch (innerError) {
                console.error("Error re-enabling button:", innerError);
            }
        }
    }

    /**
     * Handle the result of a split operation
     */
    function handleSplitResult(result, button, splitQuantity) {
        try {
            if (!result) {
                console.error("No result received from split operation");
                return;
            }
            
            if (result.success) {
                // Show success message before reload
                const successAlert = document.createElement('div');
                successAlert.className = 'alert alert-success mt-2';

                if (splitQuantity) {
                    successAlert.textContent = `Product has been split into ${splitQuantity} items with identical configurations.`;
                } else {
                    successAlert.textContent = 'Product has been split into individual items with identical configurations.';
                }

                if (button && button.parentNode) {
                    button.parentNode.appendChild(successAlert);
                } else {
                    // If button not found, add to a visible container
                    const container = document.querySelector('.website-portal-container') || document.body;
                    if (container) {
                        container.prepend(successAlert);
                    }
                }

                // Reload the page after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                // Show error message
                if (button) {
                    button.disabled = false;
                    button.classList.remove('disabled');
                    button.innerHTML = '<i class="fa fa-object-ungroup me-1"></i>Split';

                    // Display error in a more user-friendly way
                    const errorAlert = document.createElement('div');
                    errorAlert.className = 'alert alert-danger mt-2';
                    errorAlert.textContent = result.error || 'Could not split the line. Please try again.';
                    
                    if (button.parentNode) {
                        button.parentNode.appendChild(errorAlert);

                        // Auto-remove the alert after 5 seconds
                        setTimeout(() => {
                            if (errorAlert && errorAlert.parentNode) {
                                errorAlert.parentNode.removeChild(errorAlert);
                            }
                        }, 5000);
                    }
                }
            }
        } catch (error) {
            console.error("Error handling split result:", error);
            
            // Try to re-enable the button if possible
            if (button) {
                try {
                    button.disabled = false;
                    button.classList.remove('disabled');
                    button.innerHTML = '<i class="fa fa-object-ungroup me-1"></i>Split';
                } catch (innerError) {
                    console.error("Error re-enabling button:", innerError);
                }
            }
        }
    }

    /**
     * Handle errors in the split operation
     */
    function handleSplitError(error, button) {
        try {
            console.error('Error splitting line:', error);

            // Re-enable the button and show error
            if (button) {
                button.disabled = false;
                button.classList.remove('disabled');
                button.innerHTML = '<i class="fa fa-object-ungroup me-1"></i>Split';

                // Display error in a more user-friendly way
                const errorAlert = document.createElement('div');
                errorAlert.className = 'alert alert-danger mt-2';
                errorAlert.textContent = 'Could not split the line. Please try again.';
                
                if (button.parentNode) {
                    button.parentNode.appendChild(errorAlert);

                    // Auto-remove the alert after 5 seconds - WITH EXTRA SAFETY CHECKS
                    setTimeout(() => {
                        try {
                            if (errorAlert && errorAlert.parentNode) {
                                errorAlert.parentNode.removeChild(errorAlert);
                            }
                        } catch (innerError) {
                            console.error("Error removing error alert:", innerError);
                        }
                    }, 5000);
                }
            }
        } catch (outerError) {
            console.error("Error in handleSplitError:", outerError);
        }
    }

    /**
     * Get CSRF token from the page
     */
    function getCSRFToken() {
        try {
            const tokenElement = document.querySelector('meta[name="csrf-token"]');
            return tokenElement ? tokenElement.getAttribute('content') : '';
        } catch (error) {
            console.error("Error getting CSRF token:", error);
            return '';
        }
    }

    // Expose functions to global scope for use in other modules - WITH SAFETY WRAPPER
    try {
        window.splitModalFunctions = {
            splitLine: function(lineId, splitQuantity) {
                try {
                    return splitLine(lineId, splitQuantity);
                } catch (error) {
                    console.error("Error in splitLine wrapper:", error);
                }
            },
            handleSplitResult: function(result, button, splitQuantity) {
                try {
                    return handleSplitResult(result, button, splitQuantity);
                } catch (error) {
                    console.error("Error in handleSplitResult wrapper:", error);
                }
            },
            handleSplitError: function(error, button) {
                try {
                    return handleSplitError(error, button);
                } catch (innerError) {
                    console.error("Error in handleSplitError wrapper:", innerError);
                }
            }
        };
    } catch (error) {
        console.error("Error exposing split modal functions:", error);
    }
})();