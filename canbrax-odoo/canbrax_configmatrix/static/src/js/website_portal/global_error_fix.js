/**
 * Global Error Fix Script
 * This script adds a global error handler to prevent the specific error
 * "Cannot read properties of null (reading 'remove')"
 */
(function() {
    'use strict';

    console.log("Loading global error fix script");

    // Override Element.prototype.removeChild to add safety checks
    const originalRemoveChild = Element.prototype.removeChild;
    Element.prototype.removeChild = function(child) {
        try {
            if (child && this.contains(child)) {
                return originalRemoveChild.call(this, child);
            } else {
                console.warn("Prevented removeChild on non-existent child");
                return null;
            }
        } catch (error) {
            console.error("Error in removeChild:", error);
            return null;
        }
    };

    // Override setTimeout to catch errors in callbacks
    const originalSetTimeout = window.setTimeout;
    window.setTimeout = function(callback, delay) {
        if (typeof callback === 'function') {
            const wrappedCallback = function() {
                try {
                    callback();
                } catch (error) {
                    console.error("Caught error in setTimeout callback:", error);
                }
            };
            return originalSetTimeout(wrappedCallback, delay);
        } else {
            return originalSetTimeout(callback, delay);
        }
    };

    // Add global promise rejection handler
    window.addEventListener('unhandledrejection', function(event) {
        console.error('Unhandled promise rejection:', event.reason);
        // If it's the specific error we're targeting, prevent it from bubbling up
        if (event.reason && event.reason.message && 
            event.reason.message.includes("Cannot read properties of null (reading 'remove')")) {
            event.preventDefault();
            console.warn("Suppressed 'Cannot read properties of null (reading remove)' error");
        }
    });

    // Add global error handler
    window.addEventListener('error', function(event) {
        // Check if it's our specific error
        if (event.message && event.message.includes("Cannot read properties of null (reading 'remove')")) {
            console.warn("Suppressed 'Cannot read properties of null (reading remove)' error");
            event.preventDefault(); // Stop error from propagating
            return true; // Prevents the browser from logging the error
        }
    }, true); // Use capturing to catch the error before other handlers
})();
