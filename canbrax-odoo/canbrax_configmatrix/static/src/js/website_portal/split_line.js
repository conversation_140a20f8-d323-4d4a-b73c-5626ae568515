/**
 * Split Line JavaScript
 * This file defines the odoo.splitLine function
 */
(function() {
    'use strict';
    
    // Define the global odoo object if it doesn't exist
    window.odoo = window.odoo || {};
    
    /**
     * Handle split button click
     */
    window.odoo.splitLine = function(button) {
        try {
            if (!button) {
                console.error("Button not provided to odoo.splitLine");
                return;
            }
            
            console.log("Split button clicked via odoo.splitLine");

            var lineId = button.dataset.lineId;
            console.log("Line ID:", lineId);

            // Confirmation dialog
            if (!confirm('This will split the product into individual items with identical configurations. Continue?')) {
                return;
            }

            // Disable the button to prevent multiple clicks
            button.disabled = true;
            button.classList.add('disabled');

            // Show loading spinner
            button.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';

            // Get CSRF token
            var csrfToken = '';
            var tokenElements = document.getElementsByName('csrf_token');
            if (tokenElements.length > 0) {
                csrfToken = tokenElements[0].value;
            }

            // Create the request
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '/config_matrix/split_line', true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

            // Handle response
            xhr.onload = function() {
                if (xhr.status === 200) {
                    var result;
                    try {
                        result = JSON.parse(xhr.responseText);
                    } catch (e) {
                        console.error('Error parsing response:', e);
                        handleError();
                        return;
                    }

                    if (result.success) {
                        // Show success message before reload
                        var successAlert = document.createElement('div');
                        successAlert.className = 'alert alert-success mt-2';
                        successAlert.textContent = 'Product has been split into individual items with identical configurations.';
                        
                        if (button.parentNode) {
                            button.parentNode.appendChild(successAlert);
                        }

                        // Reload the page after a short delay
                        setTimeout(function() {
                            window.location.reload();
                        }, 1500);
                    } else {
                        handleError(result.error);
                    }
                } else {
                    handleError();
                }
            };

            // Handle network errors
            xhr.onerror = function() {
                console.error('Network error occurred');
                handleError();
            };

            // Function to handle errors
            function handleError(errorMessage) {
                try {
                    // Re-enable the button and show error
                    button.disabled = false;
                    button.classList.remove('disabled');
                    button.innerHTML = '<i class="fa fa-object-ungroup me-1"></i>Split';

                    // Display error in a more user-friendly way
                    var errorAlert = document.createElement('div');
                    errorAlert.className = 'alert alert-danger mt-2';
                    errorAlert.textContent = errorMessage || 'Could not split the line. Please try again.';
                    
                    if (button.parentNode) {
                        button.parentNode.appendChild(errorAlert);

                        // Auto-remove the alert after 5 seconds - WITH SAFETY CHECKS
                        setTimeout(function() {
                            try {
                                if (errorAlert && errorAlert.parentNode) {
                                    errorAlert.parentNode.removeChild(errorAlert);
                                }
                            } catch (error) {
                                console.error("Error removing error alert:", error);
                            }
                        }, 5000);
                    }
                } catch (error) {
                    console.error("Error in handleError:", error);
                }
            }

            // Send the request
            xhr.send(JSON.stringify({
                'order_line_id': lineId,
                'csrf_token': csrfToken
            }));
        } catch (error) {
            console.error("Error in odoo.splitLine:", error);
            // Try to re-enable the button
            try {
                if (button) {
                    button.disabled = false;
                    button.classList.remove('disabled');
                    button.innerHTML = '<i class="fa fa-object-ungroup me-1"></i>Split';
                }
            } catch (innerError) {
                console.error("Error re-enabling button:", innerError);
            }
        }
    };
})();