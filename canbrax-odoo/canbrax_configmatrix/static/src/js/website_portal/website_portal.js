/**
 * Website Portal JavaScript
 * Vanilla JavaScript implementation that doesn't rely on Odoo modules
 */
(function() {
    'use strict';

    console.log("Loading website portal module");

    // Wait for the DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        console.log("Website portal: DOM loaded");
        
        // Only initialize if we have the portal container
        const portalContainer = document.querySelector('.website-portal-container');
        if (!portalContainer) {
            console.log("Website portal container not found, skipping initialization");
            return;
        }
        
        console.log("Website portal container found, initializing");
        
        // Initialize the website portal functionality
        initializeWebsitePortal();
    });

    /**
     * Initialize all website portal functionality
     */
    function initializeWebsitePortal() {
        try {
            // Initialize progress bar
            initializeProgressBar();

            // Setup field visibility
            setupFieldVisibility();

            // Add event listeners
            setupEventListeners();
        } catch (error) {
            console.error("Error initializing website portal:", error);
        }
    }

    /**
     * Initialize the progress bar based on completed fields
     */
    function initializeProgressBar() {
        const progressBar = document.querySelector('.js_config_progress_bar');
        if (!progressBar) return;

        const fields = document.querySelectorAll('.js_config_field');
        let completedFields = 0;

        fields.forEach(field => {
            const value = field.value;
            if (field.type === 'checkbox') {
                if (field.checked) completedFields++;
            } else {
                if (value && value.trim() !== '') completedFields++;
            }
        });

        const progressPercent = fields.length > 0 ? Math.round((completedFields / fields.length) * 100) : 0;
        progressBar.style.width = progressPercent + '%';
        progressBar.setAttribute('aria-valuenow', progressPercent);
        progressBar.textContent = progressPercent + '%';
    }

    /**
     * Setup visibility conditions for fields
     */
    function setupFieldVisibility() {
        // Get all fields with visibility conditions
        const fields = document.querySelectorAll('[data-visibility-condition]');
        if (!fields.length) return;

        updateFieldVisibility();

        // Add event listeners to fields that might affect visibility
        document.querySelectorAll('.js_config_field').forEach(field => {
            field.addEventListener('change', updateFieldVisibility);
        });
    }

    /**
     * Update the visibility of fields based on their conditions
     */
    function updateFieldVisibility() {
        const fields = document.querySelectorAll('[data-visibility-condition]');
        if (!fields.length) return;

        fields.forEach(field => {
            const condition = field.dataset.visibilityCondition;
            let isVisible = false;

            try {
                // Create a context with the current field values
                const context = {};
                document.querySelectorAll('.js_config_field').forEach(input => {
                    const fieldName = input.dataset.fieldName;

                    if (fieldName) {
                        if (input.type === 'checkbox') {
                            context[fieldName] = input.checked;
                        } else {
                            context[fieldName] = input.value;
                        }
                    }
                });

                // Evaluate the condition
                isVisible = new Function('context', `with(context) { return ${condition}; }`)(context);
            } catch (error) {
                console.error('Error evaluating visibility condition:', error);
                isVisible = true; // Default to visible on error
            }

            // Update visibility
            const container = field.closest('.website-form-group');
            if (!container) return;
            
            if (isVisible) {
                container.classList.remove('d-none');
            } else {
                container.classList.add('d-none');
            }
        });

        // Update progress bar after visibility changes
        initializeProgressBar();
    }

    /**
     * Setup all event listeners
     */
    function setupEventListeners() {
        // Configuration summary toggle buttons
        document.querySelectorAll('.js_toggle_config_summary').forEach(button => {
            button.addEventListener('click', onToggleConfigSummary);
        });

        // Also add a document-level event listener for dynamically added elements
        document.addEventListener('click', function(event) {
            if (event.target.closest('.js_toggle_config_summary')) {
                onToggleConfigSummary(event);
            }
        });

        // Config field changes
        document.querySelectorAll('.js_config_field').forEach(field => {
            field.addEventListener('change', onConfigFieldChange);
        });

        // Section tab clicks
        document.querySelectorAll('.js_section_tab').forEach(tab => {
            tab.addEventListener('click', onSectionTabClick);
        });

        // Form submission
        const configForm = document.querySelector('.js_config_form');
        if (configForm) {
            configForm.addEventListener('submit', onConfigFormSubmit);
        }
    }

    /**
     * Handle toggle of configuration summary
     */
    function onToggleConfigSummary(event) {
        event.preventDefault();

        // Get the toggle button and find related elements
        const button = event.target.closest('.js_toggle_config_summary');
        if (!button) return;

        const icon = button.querySelector('i');
        const summaryContainer = button.closest('.config-summary');
        if (!summaryContainer) return;
        
        const content = summaryContainer.querySelector('.config-summary-content');
        if (!content) return;

        // Toggle visibility
        content.classList.toggle('d-none');

        // Toggle icon
        if (icon) {
            icon.classList.toggle('fa-chevron-down');
            icon.classList.toggle('fa-chevron-up');
        }
    }

    /**
     * Handle configuration field change
     */
    function onConfigFieldChange(event) {
        const field = event.currentTarget;
        if (!field) return;

        // Update progress bar
        initializeProgressBar();

        // Update field visibility based on conditions
        updateFieldVisibility();
    }

    /**
     * Handle click on section tab
     */
    function onSectionTabClick(event) {
        event.preventDefault();
        const tab = event.currentTarget;
        if (!tab) return;
        
        const sectionId = tab.dataset.sectionId;
        if (!sectionId) return;

        // Update active tab
        document.querySelectorAll('.js_section_tab').forEach(t => {
            t.classList.remove('active');
        });
        tab.classList.add('active');

        // Show corresponding section content
        document.querySelectorAll('.js_section_content').forEach(section => {
            section.classList.add('d-none');
        });
        
        const contentSection = document.querySelector(`.js_section_content[data-section-id="${sectionId}"]`);
        if (contentSection) {
            contentSection.classList.remove('d-none');
        }
    }

    /**
     * Handle form submission
     */
    function onConfigFormSubmit(event) {
        const form = event.currentTarget;
        if (!form) return;

        // Always consider the form valid to avoid validation issues
        const formValid = true;

        if (!formValid) {
            event.preventDefault();
            // Scroll to the first error
            const firstError = form.querySelector('.is-invalid');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        } else {
            // Show loading spinner on submit button
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                const originalBtnHtml = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Processing...';

                // If all is valid, allow form submission
                // But add a 5 second timeout to re-enable the button in case of network issues
                setTimeout(() => {
                    if (submitBtn) {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalBtnHtml;
                    }
                }, 5000);
            }
        }
    }
})();