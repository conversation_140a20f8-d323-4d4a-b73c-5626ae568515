/**
 * Portal Start Order JavaScript
 * Handles the start order page functionality
 */

// Global functions for direct onclick handlers
function incrementCount(inputId) {
    var input = document.getElementById(inputId);
    if (input) {
        var value = parseInt(input.value) || 0;
        if (value < 20) {
            input.value = value + 1;
        }
    }
}

function decrementCount(inputId) {
    var input = document.getElementById(inputId);
    if (input) {
        var value = parseInt(input.value) || 0;
        if (value > 0) {
            input.value = value - 1;
        }
    }
}

function continueWithOrder() {
    console.log("Continue button clicked");

    // Validate order name
    var orderName = document.getElementById('order_name').value.trim();
    if (!orderName) {
        alert('Please enter a name for your order.');
        document.getElementById('order_name').focus();
        return;
    }

    // Get door and screen counts
    var doorCount = parseInt(document.getElementById('door_count').value) || 0;
    var screenCount = parseInt(document.getElementById('screen_count').value) || 0;

    // Check if at least one category has items
    if (doorCount <= 0 && screenCount <= 0) {
        alert('Please select at least 1 door or screen to continue.');
        return;
    }

    // Determine which category to use based on which has more items
    var category;
    var count;

    if (doorCount > 0 && (doorCount >= screenCount || screenCount <= 0)) {
        category = 'door';
        count = doorCount;
    } else {
        category = 'screen';
        count = screenCount;
    }

    // Navigate to the configure grid page with both counts
    var projectId = document.getElementById('hidden_project_id').value || '0';
    var url = '/my/configure_grid?category=' + category +
        '&count=' + count +
        '&door_count=' + doorCount +
        '&screen_count=' + screenCount +
        '&project_id=' + projectId +
        '&order_name=' + encodeURIComponent(orderName);

    console.log("Navigating to: " + url);
    window.location.href = url;
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log("Portal Start Order: DOM loaded");

    // Set up event listener for the continue button as a backup
    var continueBtn = document.getElementById('continue-btn');
    if (continueBtn) {
        continueBtn.addEventListener('click', function() {
            continueWithOrder();
        });
    }
});
