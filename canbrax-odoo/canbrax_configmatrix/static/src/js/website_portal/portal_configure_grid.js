/**
 * Portal Configure Grid JavaScript
 * Handles the configuration grid functionality
 */
(function() {
    'use strict';

    // Global configurations object
    var configurations = {};

    // Wait for the DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        console.log("Portal Configure Grid: DOM loaded");

        // Check if we're on the configure grid page
        if (document.querySelector('.config-slot')) {
            console.log("On configure grid page, initializing event listeners");

            // Initialize event listeners
            initEventListeners();

            // Check for configured slot in URL (returning from configurator)
            var urlParams = new URLSearchParams(window.location.search);
            var configuredSlot = urlParams.get('configured_slot');
            var configId = urlParams.get('config_id');

            if (configuredSlot && configId) {
                updateConfigurationStatus(configuredSlot, configId);
            }
        } else {
            console.log("Not on configure grid page, skipping initialization");
        }
    });

    /**
     * Initialize all event listeners
     */
    function initEventListeners() {
        // Product selection change
        var productSelects = document.querySelectorAll('.product-select');
        productSelects.forEach(function(select) {
            select.addEventListener('change', onProductSelectChange);
        });

        // Configure button click
        var configureButtons = document.querySelectorAll('.configure-btn');
        configureButtons.forEach(function(button) {
            button.addEventListener('click', onConfigureButtonClick);
        });

        // Slot checkbox change
        var slotCheckboxes = document.querySelectorAll('.slot-checkbox');
        slotCheckboxes.forEach(function(checkbox) {
            checkbox.addEventListener('change', onSlotCheckboxChange);
        });

        // Copy to all button click
        var copyToAllButton = document.getElementById('copy-to-all');
        if (copyToAllButton) {
            copyToAllButton.addEventListener('click', onCopyToAllClick);
        }

        // Copy to selected button click
        var copyToSelectedButton = document.getElementById('copy-to-selected');
        if (copyToSelectedButton) {
            copyToSelectedButton.addEventListener('click', onCopyToSelectedClick);
        }

        // Add to cart button click
        var addToCartButton = document.getElementById('add-to-cart');
        if (addToCartButton) {
            addToCartButton.addEventListener('click', onAddToCartClick);
        }

        // Save button click
        var saveButton = document.getElementById('save-button');
        if (saveButton) {
            saveButton.addEventListener('click', onSaveButtonClick);
        }
    }

    /**
     * Update the configuration status for a slot
     */
    function updateConfigurationStatus(slotId, configId) {
        var slot = document.querySelector('.config-slot[data-slot-id="' + slotId + '"]');
        if (!slot) return;

        var details = slot.querySelector('.product-details');
        if (!details) return;

        // Update configuration status
        if (configurations[slotId]) {
            configurations[slotId].configured = true;
            configurations[slotId].config_id = configId;

            // Update UI
            var textElement = details.querySelector('.text-muted');
            if (textElement) {
                textElement.textContent = 'Configured';
                textElement.classList.remove('text-muted');
                textElement.classList.add('text-success');
            }

            // Update buttons
            updateCopyButtons();
            updateAddToCartButton();
        }
    }

    /**
     * Copy configuration from one slot to others
     */
    function copyConfiguration(sourceConfigId, targetSlotIds) {
        if (!Array.isArray(targetSlotIds)) {
            targetSlotIds = [targetSlotIds];
        }

        // Create CSRF token
        var csrfToken = '';
        var tokenElements = document.getElementsByName('csrf_token');
        if (tokenElements.length > 0) {
            csrfToken = tokenElements[0].value;
        }

        // Call the server to copy the configuration
        var xhr = new XMLHttpRequest();
        xhr.open('POST', '/my/copy_configuration', true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

        xhr.onload = function() {
            if (xhr.status === 200) {
                var result;
                try {
                    result = JSON.parse(xhr.responseText);
                    if (result.jsonrpc) {
                        result = result.result;
                    }
                } catch (e) {
                    console.error('Error parsing response:', e);
                    alert('Failed to copy configuration. Please try again.');
                    return;
                }

                if (result && result.success) {
                    // Update UI for each target slot
                    targetSlotIds.forEach(function(targetSlotId) {
                        var slot = document.querySelector('.config-slot[data-slot-id="' + targetSlotId + '"]');
                        if (!slot) return;

                        var details = slot.querySelector('.product-details');
                        if (!details) return;

                        // Update configuration status
                        if (configurations[targetSlotId]) {
                            configurations[targetSlotId].configured = true;
                            configurations[targetSlotId].config_id = sourceConfigId; // This will be updated with the actual new config ID

                            // Update UI
                            var textElement = details.querySelector('.text-muted');
                            if (textElement) {
                                textElement.textContent = 'Configured (copied)';
                                textElement.classList.remove('text-muted');
                                textElement.classList.add('text-success');
                            }
                        }
                    });

                    // Update buttons
                    updateAddToCartButton();

                    // Show success message
                    alert('Configuration copied successfully to ' + targetSlotIds.length + ' slots.');
                } else {
                    alert('Failed to copy configuration: ' + (result && result.error || 'Unknown error'));
                }
            } else {
                alert('Failed to copy configuration. Please try again.');
            }
        };

        xhr.onerror = function() {
            alert('Failed to copy configuration. Please try again.');
        };

        xhr.send(JSON.stringify({
            'jsonrpc': '2.0',
            'method': 'call',
            'params': {
                'source_config_id': sourceConfigId,
                'target_slot_ids': targetSlotIds
            },
            'id': Math.floor(Math.random() * 1000000000)
        }));
    }

    /**
     * Update copy buttons state
     */
    function updateCopyButtons() {
        var hasConfigured = false;
        var hasSelected = document.querySelectorAll('.slot-checkbox:checked').length > 0;

        for (var slotId in configurations) {
            if (configurations[slotId].configured) {
                hasConfigured = true;
                break;
            }
        }

        var copyToAllButton = document.getElementById('copy-to-all');
        var copyToSelectedButton = document.getElementById('copy-to-selected');

        if (copyToAllButton) {
            copyToAllButton.disabled = !hasConfigured;
        }

        if (copyToSelectedButton) {
            copyToSelectedButton.disabled = !hasConfigured || !hasSelected;
        }
    }

    /**
     * Update add to cart and save buttons state
     */
    function updateAddToCartButton() {
        var hasConfigured = false;
        var hasSelected = false;

        for (var slotId in configurations) {
            if (configurations[slotId].configured) {
                hasConfigured = true;
            }
            if (configurations[slotId]) {
                hasSelected = true;
            }
        }

        var addToCartButton = document.getElementById('add-to-cart');
        if (addToCartButton) {
            addToCartButton.disabled = !hasConfigured;
        }

        var saveButton = document.getElementById('save-button');
        if (saveButton) {
            saveButton.disabled = !hasSelected;
        }
    }

    /**
     * Handle product selection change
     */
    function onProductSelectChange(event) {
        var select = event.target;
        var slotId = select.getAttribute('data-slot-id');
        var productId = select.value;
        var slot = document.querySelector('.config-slot[data-slot-id="' + slotId + '"]');
        var configBtn = slot.querySelector('.configure-btn');

        if (productId) {
            configBtn.disabled = false;

            // Update product details
            var productName = select.options[select.selectedIndex].text;
            var details = slot.querySelector('.product-details');

            details.innerHTML =
                '<div class="selected-product">' +
                '<img src="/web/image/product.template/' + productId + '/image_1024" alt="' + productName + '" class="img-fluid mb-3" style="max-height: 100px;"/>' +
                '<h5>' + productName + '</h5>' +
                '<p class="text-muted">Not configured yet</p>' +
                '</div>';

            // Store product info
            configurations[slotId] = {
                product_id: productId,
                matrix_id: select.options[select.selectedIndex].getAttribute('data-matrix-id'),
                configured: false,
                config_id: null
            };

            // Immediately try to get matrix_id if it's not already available
            if (!configurations[slotId].matrix_id) {
                getProductMatrixId(productId, function(matrixId) {
                    if (matrixId) {
                        configurations[slotId].matrix_id = matrixId;
                        console.log("Retrieved matrix_id for product: " + productId + ", matrix_id: " + matrixId);
                    }
                });
            }

            // Enable copy buttons if at least one product is selected
            updateCopyButtons();

            // Automatically add product to cart
            addProductToCart(productId, slotId);

        } else {
            configBtn.disabled = true;
            slot.querySelector('.product-details').innerHTML =
                '<div class="placeholder-content">' +
                '<img src="/canbrax_configmatrix/static/src/img/placeholder.png" alt="Select a product" class="img-fluid mb-3" style="max-height: 100px;"/>' +
                '<p>Select a product to configure</p>' +
                '</div>';

            // Remove from configurations
            if (configurations[slotId]) {
                delete configurations[slotId];
            }

            updateCopyButtons();
        }

        // Update Add to Cart button
        updateAddToCartButton();
    }

    /**
     * Add product to cart automatically
     */
    function addProductToCart(productId, slotId) {
        // Get project ID from the form
        var configData = document.getElementById('configuration-data');
        var projectId = configData ? configData.querySelector('input[name="project_id"]').value : 0;

        if (!projectId) {
            console.error("No project ID found, cannot add product to cart");
            return;
        }

        // Add product to cart with project reference
        fetch('/shop/cart/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: 'product_id=' + productId +
                 '&add_qty=1' +
                 '&project_id=' + projectId +
                 '&slot_id=' + slotId
        })
        .then(response => {
            console.log("Product added to cart for project: " + projectId + ", slot: " + slotId);
        })
        .catch(error => {
            console.error('Error adding product to cart:', error);
        });
    }

    /**
     * Get the matrix_id for a product
     */
    function getProductMatrixId(productId, callback) {
        fetch('/config_matrix/get_product_matrix_id', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                'jsonrpc': '2.0',
                'method': 'call',
                'params': {
                    'product_id': productId
                },
                'id': Math.floor(Math.random() * 1000000000)
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.result && data.result.matrix_id) {
                callback(data.result.matrix_id);
            } else {
                callback(null);
            }
        })
        .catch(error => {
            console.error("Error fetching matrix_id:", error);
            callback(null);
        });
    }

    /**
     * Handle configure button click
     */
    function onConfigureButtonClick(event) {
        var button = event.currentTarget;
        var slotId = button.getAttribute('data-slot-id');
        var config = configurations[slotId];

        if (config) {
            // Show loading indicator
            button.disabled = true;
            button.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i>Preparing...';

            // First, check if we need to create a matrix for this product
            if (!config.matrix_id) {
                console.log("No matrix_id found for product, attempting to create one");

                // Try to get the matrix ID again first
                getProductMatrixId(config.product_id, function(matrixId) {
                    if (matrixId) {
                        // Matrix ID found, update and navigate
                        config.matrix_id = matrixId;
                        button.disabled = false;
                        button.innerHTML = '<i class="fa fa-cogs me-1"></i>Configure';
                        navigateToConfigurator(config, slotId);
                    } else {
                        // Create a new matrix
                        createMatrixAndNavigate(config, slotId, button);
                    }
                });
            } else {
                // Matrix ID already exists, navigate directly
                navigateToConfigurator(config, slotId);
            }
        } else {
            console.error("No configuration found for slot:", slotId);
        }
    }

    /**
     * Create a matrix for the product and then navigate
     */
    function createMatrixAndNavigate(config, slotId, button) {
        fetch('/config_matrix/create_product_matrix', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                'jsonrpc': '2.0',
                'method': 'call',
                'params': {
                    'product_id': config.product_id
                },
                'id': Math.floor(Math.random() * 1000000000)
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.result && data.result.success) {
                // Update the matrix_id
                config.matrix_id = data.result.matrix_id;
                console.log("Created new matrix_id: " + config.matrix_id + " for product: " + config.product_id);

                // Reset button
                if (button) {
                    button.disabled = false;
                    button.innerHTML = '<i class="fa fa-cogs me-1"></i>Configure';
                }

                // Navigate to configurator
                navigateToConfigurator(config, slotId);
            } else {
                // Show error
                if (button) {
                    button.disabled = false;
                    button.innerHTML = '<i class="fa fa-cogs me-1"></i>Configure';
                }
                alert('Could not create configuration matrix for this product. Please try another product.');
            }
        })
        .catch(error => {
            console.error("Error creating matrix:", error);

            // Reset button
            if (button) {
                button.disabled = false;
                button.innerHTML = '<i class="fa fa-cogs me-1"></i>Configure';
            }

            alert('Could not create configuration matrix for this product. Please try another product.');
        });
    }

    /**
     * Navigate to the configurator with the config
     */
    function navigateToConfigurator(config, slotId) {
        // Ensure we have all required parameters
        if (!config.product_id) {
            console.error("Missing product_id for navigation");
            alert("Error: Missing product ID. Please try again.");
            return;
        }

        // If matrix_id is missing, try to create one
        if (!config.matrix_id) {
            console.log("Missing matrix_id for navigation, creating one");

            // Show loading indicator
            var button = document.querySelector('.configure-btn[data-slot-id="' + slotId + '"]');
            if (button) {
                button.disabled = true;
                button.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i>Creating matrix...';
            }

            // Create a matrix for this product
            createMatrixAndNavigate(config, slotId, button);
            return;
        }

        // Build the configurator URL
        var url = '/config_matrix/website_configurator?' +
                 'product_id=' + encodeURIComponent(config.product_id) +
                 '&template_id=' + encodeURIComponent(config.matrix_id);

        // Add config_id if available
        if (config.config_id) {
            url += '&config_id=' + encodeURIComponent(config.config_id);
        }

        // Add callback URL to return to this page with info about which slot was configured
        var currentUrl = window.location.pathname + window.location.search;
        if (!currentUrl.includes('configured_slot=')) {
            var separator = currentUrl.includes('?') ? '&' : '?';
            var returnUrl = currentUrl + separator + 'configured_slot=' + encodeURIComponent(slotId);
            url += '&callback=' + encodeURIComponent(returnUrl);
        }

        console.log("Navigating to configurator URL:", url);
        window.location.href = url;
    }

    /**
     * Handle slot checkbox change
     */
    function onSlotCheckboxChange(event) {
        updateCopyButtons();
    }

    /**
     * Handle copy to all button click
     */
    function onCopyToAllClick(event) {
        // Find the first configured slot
        var sourceSlotId = null;
        var sourceConfig = null;

        for (var slotId in configurations) {
            if (configurations[slotId].configured) {
                sourceSlotId = slotId;
                sourceConfig = configurations[slotId];
                break;
            }
        }

        if (sourceSlotId && sourceConfig) {
            // Copy to all other slots
            var targetSlotIds = [];

            document.querySelectorAll('.product-select').forEach(function(select) {
                var targetSlotId = select.getAttribute('data-slot-id');

                if (targetSlotId != sourceSlotId) {
                    select.value = sourceConfig.product_id;

                    // Trigger change event
                    var event = new Event('change', { bubbles: true });
                    select.dispatchEvent(event);

                    targetSlotIds.push(targetSlotId);
                }
            });

            if (targetSlotIds.length > 0) {
                // Copy configuration
                copyConfiguration(sourceConfig.config_id, targetSlotIds);
            }
        }
    }

    /**
     * Handle copy to selected button click
     */
    function onCopyToSelectedClick(event) {
        // Find the first configured slot
        var sourceSlotId = null;
        var sourceConfig = null;

        for (var slotId in configurations) {
            if (configurations[slotId].configured) {
                sourceSlotId = slotId;
                sourceConfig = configurations[slotId];
                break;
            }
        }

        if (sourceSlotId && sourceConfig) {
            // Get selected slots
            var targetSlotIds = [];

            document.querySelectorAll('.slot-checkbox:checked').forEach(function(checkbox) {
                var targetSlotId = checkbox.getAttribute('data-slot-id');

                if (targetSlotId != sourceSlotId) {
                    targetSlotIds.push(targetSlotId);

                    // Update product selection
                    var select = document.querySelector('.product-select[data-slot-id="' + targetSlotId + '"]');
                    if (select) {
                        select.value = sourceConfig.product_id;

                        // Trigger change event
                        var event = new Event('change', { bubbles: true });
                        select.dispatchEvent(event);
                    }
                }
            });

            if (targetSlotIds.length > 0) {
                // Copy configuration to selected slots
                copyConfiguration(sourceConfig.config_id, targetSlotIds);
            }
        }
    }

    /**
     * Handle save button click - adds products to cart as separate items
     */
    function onSaveButtonClick(event) {
        // Prepare data for all selected products
        var productData = [];

        for (var slotId in configurations) {
            var config = configurations[slotId];

            if (config && config.product_id) {
                productData.push({
                    product_id: config.product_id,
                    quantity: 1
                });
            }
        }

        if (productData.length > 0) {
            // Show loading indicator
            var saveButton = document.getElementById('save-button');
            if (saveButton) {
                saveButton.disabled = true;
                saveButton.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i>Saving...';
            }

            // Add each product to cart separately
            var addedCount = 0;
            var totalCount = productData.length;

            function addNextProduct(index) {
                if (index >= productData.length) {
                    // All products added, redirect to cart
                    window.location.href = '/shop/cart';
                    return;
                }

                var product = productData[index];

                // Add product to cart
                fetch('/shop/cart/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: 'product_id=' + product.product_id + '&add_qty=' + product.quantity
                })
                .then(response => {
                    addedCount++;
                    // Update button text
                    if (saveButton) {
                        saveButton.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i>Saving (' + addedCount + '/' + totalCount + ')';
                    }
                    // Add next product
                    addNextProduct(index + 1);
                })
                .catch(error => {
                    console.error('Error adding product to cart:', error);
                    // Continue with next product anyway
                    addNextProduct(index + 1);
                });
            }

            // Start adding products
            addNextProduct(0);
        } else {
            alert('Please select at least one product to save.');
        }
    }

    /**
     * Handle add to cart button click - adds configured products to cart
     */
    function onAddToCartClick(event) {
        // Prepare data for configured products
        var slotData = [];

        for (var slotId in configurations) {
            var config = configurations[slotId];

            if (config.configured && config.config_id) {
                slotData.push({
                    slot_id: slotId,
                    config_id: config.config_id,
                    product_id: config.product_id
                });
            }
        }

        if (slotData.length > 0) {
            // Show loading indicator
            var addToCartButton = document.getElementById('add-to-cart');
            if (addToCartButton) {
                addToCartButton.disabled = true;
                addToCartButton.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i>Adding...';
            }

            // Add to cart
            var configData = document.getElementById('configuration-data');
            var projectId = configData ? configData.querySelector('input[name="project_id"]').value : 0;
            var category = configData ? configData.querySelector('input[name="category"]').value : '';

            // Add each configured product to cart separately
            var addedCount = 0;
            var totalCount = slotData.length;

            function addNextConfiguredProduct(index) {
                if (index >= slotData.length) {
                    // All products added, redirect to cart
                    window.location.href = '/shop/cart';
                    return;
                }

                var slotItem = slotData[index];

                // Add product to cart with configuration
                fetch('/shop/cart/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: 'product_id=' + slotItem.product_id +
                          '&add_qty=1' +
                          '&config_id=' + slotItem.config_id +
                          '&slot_id=' + slotItem.slot_id +
                          '&project_id=' + projectId
                })
                .then(response => {
                    addedCount++;
                    // Update button text
                    if (addToCartButton) {
                        addToCartButton.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i>Adding (' + addedCount + '/' + totalCount + ')';
                    }
                    // Add next product
                    addNextConfiguredProduct(index + 1);
                })
                .catch(error => {
                    console.error('Error adding configured product to cart:', error);
                    // Continue with next product anyway
                    addNextConfiguredProduct(index + 1);
                });
            }

            // Start adding products
            addNextConfiguredProduct(0);
        } else {
            alert('Please configure at least one product before adding to cart.');
        }
    }
})();