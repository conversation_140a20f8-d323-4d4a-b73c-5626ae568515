/** @odoo-module **/

import { Component, onMounted, onWillUnmount, useState, useRef } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";

/**
 * Interactive Matrix Visual Editor Component
 * Provides Excel-like editing experience directly in the form
 */
export class InteractiveMatrixEditor extends Component {
    static template = "canbrax_configmatrix.InteractiveMatrixEditor";

    setup() {
        this.state = useState({
            heights: [],
            widths: [],
            matrixData: {},
            selectedCell: null,
            editingCell: null,
            editValue: '',
            stats: { total: 0, filled: 0, completion: 0 }
        });

        this.matrixRef = useRef("matrixGrid");
        this.notification = useService("notification");

        onMounted(() => {
            this.loadMatrixData();
            this.setupEventHandlers();
            // Auto-refresh when form data changes
            this.autoRefreshInterval = setInterval(() => {
                this.loadMatrixData();
            }, 1000);
        });

        onWillUnmount(() => {
            this.cleanupEventHandlers();
            if (this.autoRefreshInterval) {
                clearInterval(this.autoRefreshInterval);
            }
        });
    }

    loadMatrixData() {
        try {
            // Get data from the form fields by accessing the record
            const record = this.env.model?.root;
            if (!record) return;

            const heightsText = record.data.heights_text || '1000,1200,1500,1800,2000';
            const widthsText = record.data.widths_text || '600,800,1000,1200,1500';
            const matrixDataJson = record.data.matrix_data_json || '{}';

            const newHeights = heightsText.split(',').map(h => h.trim()).filter(h => h);
            const newWidths = widthsText.split(',').map(w => w.trim()).filter(w => w);
            const newMatrixData = JSON.parse(matrixDataJson);

            // Only update if data has changed
            if (JSON.stringify(newHeights) !== JSON.stringify(this.state.heights) ||
                JSON.stringify(newWidths) !== JSON.stringify(this.state.widths) ||
                JSON.stringify(newMatrixData) !== JSON.stringify(this.state.matrixData)) {

                this.state.heights = newHeights;
                this.state.widths = newWidths;
                this.state.matrixData = newMatrixData;

                this.calculateStats();
                this.renderMatrix();
            }
        } catch (error) {
            console.error('Failed to load matrix data:', error);
        }
    }

    calculateStats() {
        const total = this.state.heights.length * this.state.widths.length;
        const filled = Object.keys(this.state.matrixData).filter(key => this.state.matrixData[key]).length;
        const completion = total > 0 ? Math.round((filled / total) * 100) : 0;

        this.state.stats = { total, filled, completion };
    }

    renderMatrix() {
        if (!this.matrixRef.el) return;

        const table = this.matrixRef.el;

        // Clear existing content
        table.innerHTML = '';

        if (this.state.heights.length === 0 || this.state.widths.length === 0) {
            table.innerHTML = '<tr><td colspan="100%" class="text-center text-muted p-4">Enter heights and widths in fields above to see the matrix</td></tr>';
            return;
        }

        // Create header row
        const headerRow = document.createElement('tr');
        const cornerCell = document.createElement('th');
        cornerCell.className = 'matrix-corner-cell';
        cornerCell.textContent = 'H\\W';
        cornerCell.style.cssText = 'background: #f8f9fa; position: sticky; left: 0; z-index: 10; min-width: 80px; text-align: center; font-weight: bold;';
        headerRow.appendChild(cornerCell);

        this.state.widths.forEach(width => {
            const th = document.createElement('th');
            th.className = 'matrix-header-cell';
            th.textContent = width;
            th.style.cssText = 'background: #f8f9fa; text-align: center; min-width: 90px; font-weight: bold; position: sticky; top: 0; z-index: 5;';
            headerRow.appendChild(th);
        });

        table.appendChild(headerRow);

        // Create data rows
        this.state.heights.forEach(height => {
            const row = document.createElement('tr');

            // Row header
            const rowHeader = document.createElement('th');
            rowHeader.className = 'matrix-row-header';
            rowHeader.textContent = height;
            rowHeader.style.cssText = 'background: #f8f9fa; position: sticky; left: 0; z-index: 9; text-align: center; font-weight: bold;';
            row.appendChild(rowHeader);

            // Data cells
            this.state.widths.forEach(width => {
                const cell = document.createElement('td');
                const cellKey = `${height}_${width}`;
                const cellValue = this.state.matrixData[cellKey] || '';

                cell.className = 'matrix-data-cell';
                cell.dataset.height = height;
                cell.dataset.width = width;
                cell.dataset.key = cellKey;
                cell.style.cssText = 'text-align: center; cursor: pointer; padding: 8px; min-width: 90px; border: 1px solid #dee2e6; transition: all 0.2s ease;';

                if (cellValue) {
                    cell.textContent = this.formatCellValue(cellValue);
                    cell.classList.add('has-value');
                    cell.style.backgroundColor = '#e8f5e8';
                    cell.style.fontWeight = '500';
                } else {
                    cell.textContent = '-';
                    cell.classList.add('empty-value');
                    cell.style.backgroundColor = '#ffffff';
                    cell.style.color = '#6c757d';
                }

                // Add hover effect
                cell.addEventListener('mouseenter', () => {
                    cell.style.backgroundColor = '#b3d7ff';
                    cell.style.borderColor = '#007bff';
                });

                cell.addEventListener('mouseleave', () => {
                    if (!cell.classList.contains('selected')) {
                        if (cellValue) {
                            cell.style.backgroundColor = '#e8f5e8';
                        } else {
                            cell.style.backgroundColor = '#ffffff';
                        }
                        cell.style.borderColor = '#dee2e6';
                    }
                });

                // Add click handlers
                cell.addEventListener('click', () => this.onCellClick(height, width, cellKey));
                cell.addEventListener('dblclick', () => this.onCellDoubleClick(height, width, cellKey));

                row.appendChild(cell);
            });

            table.appendChild(row);
        });
    }

    formatCellValue(value) {
        if (typeof value === 'number') {
            return value.toFixed(4);
        }
        return value.toString();
    }

    onCellClick(height, width, cellKey) {
        // Clear previous selection
        this.clearSelection();

        // Set new selection
        this.state.selectedCell = { height, width, key: cellKey };

        // Update visual selection
        const cell = this.matrixRef.el.querySelector(`[data-key="${cellKey}"]`);
        if (cell) {
            cell.classList.add('selected');
            cell.style.backgroundColor = '#007bff';
            cell.style.color = 'white';
            cell.style.borderColor = '#0056b3';
        }
    }

    onCellDoubleClick(height, width, cellKey) {
        this.startCellEdit(height, width, cellKey);
    }

    startCellEdit(height, width, cellKey) {
        this.state.editingCell = { height, width, key: cellKey };
        this.state.editValue = this.state.matrixData[cellKey] || '';

        const cell = this.matrixRef.el.querySelector(`[data-key="${cellKey}"]`);
        if (cell) {
            cell.classList.add('editing');

            // Create input element
            const input = document.createElement('input');
            input.type = 'number';
            input.step = '0.01';
            input.className = 'matrix-cell-input';
            input.value = this.state.editValue;
            input.style.cssText = 'width: 100%; border: 2px solid #28a745; padding: 4px; text-align: center; background: white; font-size: 12px; border-radius: 2px;';

            // Replace cell content with input
            cell.innerHTML = '';
            cell.appendChild(input);

            // Focus and select
            input.focus();
            input.select();

            // Handle input events
            input.addEventListener('blur', () => this.confirmCellEdit(cellKey));
            input.addEventListener('keydown', (e) => this.handleInputKeydown(e, cellKey));
        }
    }

    handleInputKeydown(event, cellKey) {
        switch(event.key) {
            case 'Enter':
                event.preventDefault();
                this.confirmCellEdit(cellKey);
                break;
            case 'Escape':
                event.preventDefault();
                this.cancelCellEdit(cellKey);
                break;
            case 'Tab':
                event.preventDefault();
                this.confirmCellEdit(cellKey);
                this.navigateToNextCell(cellKey, event.shiftKey ? -1 : 1);
                break;
        }
    }

    confirmCellEdit(cellKey) {
        const cell = this.matrixRef.el.querySelector(`[data-key="${cellKey}"]`);
        const input = cell?.querySelector('.matrix-cell-input');

        if (input) {
            const newValue = parseFloat(input.value) || 0;

            // Update matrix data
            if (newValue === 0) {
                delete this.state.matrixData[cellKey];
            } else {
                this.state.matrixData[cellKey] = newValue;
            }

            // Update the parent form field
            this.updateFormField();

            // Re-render the cell
            this.endCellEdit(cellKey);
            this.calculateStats();

            // Show success feedback
            this.notification.add(`Updated cell ${cellKey} to ${newValue}`, { type: "success" });
        }
    }

    cancelCellEdit(cellKey) {
        this.endCellEdit(cellKey);
    }

    endCellEdit(cellKey) {
        this.state.editingCell = null;
        this.state.editValue = '';

        const cell = this.matrixRef.el.querySelector(`[data-key="${cellKey}"]`);
        if (cell) {
            cell.classList.remove('editing');

            // Restore cell content
            const cellValue = this.state.matrixData[cellKey] || '';
            if (cellValue) {
                cell.textContent = this.formatCellValue(cellValue);
                cell.classList.remove('empty-value');
                cell.classList.add('has-value');
                cell.style.backgroundColor = '#e8f5e8';
                cell.style.color = 'black';
                cell.style.fontWeight = '500';
            } else {
                cell.textContent = '-';
                cell.classList.remove('has-value');
                cell.classList.add('empty-value');
                cell.style.backgroundColor = '#ffffff';
                cell.style.color = '#6c757d';
                cell.style.fontWeight = 'normal';
            }
            cell.style.borderColor = '#dee2e6';
        }
    }

    navigateToNextCell(currentKey, direction) {
        const [currentHeight, currentWidth] = currentKey.split('_');
        const heightIndex = this.state.heights.indexOf(currentHeight);
        const widthIndex = this.state.widths.indexOf(currentWidth);

        let newWidthIndex = widthIndex + direction;
        let newHeightIndex = heightIndex;

        // Handle wrapping
        if (newWidthIndex >= this.state.widths.length) {
            newWidthIndex = 0;
            newHeightIndex++;
        } else if (newWidthIndex < 0) {
            newWidthIndex = this.state.widths.length - 1;
            newHeightIndex--;
        }

        // Check bounds
        if (newHeightIndex >= 0 && newHeightIndex < this.state.heights.length) {
            const newHeight = this.state.heights[newHeightIndex];
            const newWidth = this.state.widths[newWidthIndex];
            const newKey = `${newHeight}_${newWidth}`;

            this.startCellEdit(newHeight, newWidth, newKey);
        }
    }

    updateFormField() {
        // Update the JSON field in the parent form
        try {
            const jsonString = JSON.stringify(this.state.matrixData);
            const record = this.env.model?.root;
            if (record) {
                record.update({ matrix_data_json: jsonString });
            }
        } catch (error) {
            console.error('Failed to update form field:', error);
        }
    }

    clearSelection() {
        this.matrixRef.el?.querySelectorAll('.matrix-data-cell.selected').forEach(cell => {
            cell.classList.remove('selected');
            const cellValue = this.state.matrixData[cell.dataset.key] || '';
            if (cellValue) {
                cell.style.backgroundColor = '#e8f5e8';
                cell.style.color = 'black';
            } else {
                cell.style.backgroundColor = '#ffffff';
                cell.style.color = '#6c757d';
            }
            cell.style.borderColor = '#dee2e6';
        });
        this.state.selectedCell = null;
    }

    setupEventHandlers() {
        // Add keyboard handlers for the matrix
        this.keyHandler = (event) => {
            if (!this.matrixRef.el?.contains(event.target)) return;

            // Handle matrix navigation
            if (this.state.selectedCell && !this.state.editingCell) {
                switch(event.key) {
                    case 'Enter':
                    case 'F2':
                        event.preventDefault();
                        const { height, width, key } = this.state.selectedCell;
                        this.startCellEdit(height, width, key);
                        break;
                    case 'Delete':
                    case 'Backspace':
                        event.preventDefault();
                        this.deleteSelectedCell();
                        break;
                }
            }
        };

        document.addEventListener('keydown', this.keyHandler);
    }

    cleanupEventHandlers() {
        if (this.keyHandler) {
            document.removeEventListener('keydown', this.keyHandler);
        }
    }

    deleteSelectedCell() {
        if (this.state.selectedCell) {
            const { key } = this.state.selectedCell;
            delete this.state.matrixData[key];
            this.updateFormField();
            this.renderMatrix();
            this.calculateStats();
            this.notification.add(`Cleared cell ${key}`, { type: "info" });
        }
    }

    fillSampleData() {
        const sampleData = {};
        const baseValue = 100;

        this.state.heights.forEach((height, i) => {
            this.state.widths.forEach((width, j) => {
                const key = `${height}_${width}`;
                const value = baseValue * (1 + i * 0.2 + j * 0.15);
                sampleData[key] = Math.round(value * 100) / 100;
            });
        });

        this.state.matrixData = sampleData;
        this.updateFormField();
        this.renderMatrix();
        this.calculateStats();

        this.notification.add("Sample data generated successfully", { type: "success" });
    }

    clearAllData() {
        this.state.matrixData = {};
        this.updateFormField();
        this.renderMatrix();
        this.calculateStats();

        this.notification.add("Matrix data cleared", { type: "info" });
    }
}



// Register the component
registry.category("fields").add("interactive_matrix", {
    component: InteractiveMatrixEditor,
});
