odoo.define('canbrax_configmatrix.svg_renderer', function (require) {
    "use strict";

    var Widget = require('web.Widget');

    var ProductSvgRenderer = Widget.extend({
        template: 'SvgRenderer',

        init: function (parent, options) {
            this._super.apply(this, arguments);
            this.template_id = options.template_id;
            this.components = [];
            this.configValues = {};
            this.svgContainer = null;

            // Debug info
            console.log('Initializing SVG Renderer with template ID:', this.template_id);
        },

        willStart: function () {
            var self = this;
            console.log('SVG Renderer willStart');
            return this._super.apply(this, arguments).then(function () {
                return self._loadComponents();
            });
        },

        start: function () {
            console.log('SVG Renderer start');
            this.svgContainer = this.$el.find('.svg-container');
            if (this.components.length > 0) {
                this._renderSvg();
            } else {
                console.warn('No SVG components loaded yet');
                // Show loading indicator
                this.svgContainer.html('<div class="text-center p-3"><i class="fas fa-spinner fa-spin"></i> Loading visualization...</div>');
                // Try loading components again
                this._loadComponents().then(() => {
                    if (this.components.length > 0) {
                        this._renderSvg();
                    } else {
                        this._showError('No SVG components available');
                    }
                });
            }
            return this._super.apply(this, arguments);
        },

        _loadComponents: function () {
            var self = this;
            console.log('Loading SVG components for template ID:', this.template_id);

            // Make AJAX request to get SVG components
            return $.ajax({
                url: '/config_matrix/get_svg_components',
                type: 'POST',
                dataType: 'json',
                contentType: 'application/json',
                data: JSON.stringify({
                    template_id: this.template_id
                })
            }).then(function (response) {
                // Parse response if it's a string
                var result = response;
                if (typeof response === 'string') {
                    try {
                        result = JSON.parse(response);
                    } catch (e) {
                        console.error('Error parsing SVG components response:', e);
                        self._showError('Invalid response format');
                        return;
                    }
                }

                console.log('SVG components response:', result);

                if (result.success) {
                    self.components = result.components || [];
                    console.log('Loaded', self.components.length, 'SVG components');
                } else {
                    console.error("Failed to load SVG components:", result.error);
                    self._showError(result.error || 'Failed to load SVG components');
                }
            }).catch(function(error) {
                console.error('AJAX error loading SVG components:', error);
                self._showError('Failed to load SVG components');
            });
        },

        updateConfiguration: function (values) {
            console.log('Updating SVG configuration with values:', values);
            this.configValues = values || {};
            this._renderSvg();
        },

        _renderSvg: function () {
            console.log('Rendering SVG with', this.components.length, 'components');

            if (!this.svgContainer) {
                console.error('SVG container not found');
                return;
            }

            if (this.components.length === 0) {
                console.warn('No SVG components available');
                this._showError('No SVG components available');
                return;
            }

            // Find base component
            var baseComponent = this.components.find(function(comp) {
                return comp.component_type === 'base';
            });

            if (!baseComponent) {
                console.error("No base SVG component found");
                this._showError('No base SVG component found');
                return;
            }

            console.log('Using base component:', baseComponent.name);

            // Start with base SVG content
            var svgContent = this._processTemplate(baseComponent.svg_content);

            // Add other layers based on conditions
            var addedLayers = 0;
            this.components.forEach(function(component) {
                if (component.component_type !== 'layer') {
                    return;
                }

                // Evaluate condition if present
                if (component.condition) {
                    var conditionResult = this._evaluateCondition(component.condition);
                    console.log('Evaluating condition for', component.name, ':', component.condition, '=', conditionResult);
                    if (!conditionResult) {
                        return;
                    }
                }

                console.log('Adding layer:', component.name);

                // Process template with current config values
                var layerSvg = this._processTemplate(component.svg_content);

                // Add to SVG content - before the closing </svg> tag
                svgContent = svgContent.replace('</svg>', layerSvg + '</svg>');
                addedLayers++;
            }, this);

            console.log('Added', addedLayers, 'layers to SVG');

            // Update the SVG display - use a safe method to insert SVG
            try {
                // Create a temporary div to parse the SVG
                var tempDiv = document.createElement('div');
                tempDiv.innerHTML = svgContent;

                // Clear the container
                this.svgContainer.empty();

                // Append the SVG element
                if (tempDiv.firstChild) {
                    this.svgContainer.append(tempDiv.firstChild);
                    console.log('SVG rendered successfully');
                } else {
                    console.error('Failed to parse SVG content');
                    this._showError('Failed to parse SVG content');
                }
            } catch (e) {
                console.error('Error rendering SVG:', e);
                this._showError('Error rendering SVG');
            }
        },

        _processTemplate: function (template) {
            var self = this;

            if (!template) {
                console.warn('Empty SVG template');
                return '';
            }

            // Clean up any invalid data URLs
            template = template.replace(/data:image\/[^;]+;base64,[^"']+/g, function (match) {
                // Check if the URL is valid
                try {
                    // Try to create a URL object to validate
                    new URL(match);
                    return match;
                } catch (e) {
                    console.error('Invalid data URL detected:', e);
                    // Return a placeholder or empty string
                    return 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2NjY2NjYyIvPjwvc3ZnPg==';
                }
            });

            // Replace ${fieldName} with actual values
            return template.replace(/\${(\w+)}/g, function(match, fieldName) {
                var value = self.configValues[fieldName];
                console.log('Replacing template variable:', fieldName, '=', value);
                return value !== undefined ? value : match;
            });
        },

        _evaluateCondition: function (condition) {
            try {
                // Create a function with the configuration values in scope
                var keys = Object.keys(this.configValues);
                var values = Object.values(this.configValues);

                // Create a function with all field values as parameters
                var evalFunction = new Function(
                    ...keys,
                    'return ' + condition + ';'
                );

                // Call the function with the field values
                return evalFunction(...values);
            } catch (e) {
                console.error("Error evaluating condition:", e, "with values:", this.configValues);
                return false;
            }
        },

        /**
         * Show error message in the SVG container
         */
        _showError: function (message) {
            if (this.svgContainer) {
                this.svgContainer.html('<div class="alert alert-warning">' +
                    '<i class="fas fa-exclamation-triangle me-2"></i>' +
                    (message || 'An error occurred') +
                    '</div>');
            }
        }
    });

    return ProductSvgRenderer;
});
