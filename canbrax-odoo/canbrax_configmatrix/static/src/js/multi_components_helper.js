/**
 * Multi Components Helper
 *
 * This script manages the Multi Components helper panel that shows dynamic mappings
 * and their filtered product matches.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Global variables
    let templateId = null;
    let dynamicMappings = [];
    let currentFieldValues = {};

    // Initialize the multi components helper
    function initialize() {
        // Get template ID
        const templateIdInput = document.querySelector('input[name="template_id"]');
        if (templateIdInput) {
            templateId = parseInt(templateIdInput.value) || 0;
        }

        // Set up event listeners
        setupEventListeners();
    }

    // Set up event listeners
    function setupEventListeners() {
        // Show panel button
        const showButton = document.getElementById('show-multi-components-panel');
        if (showButton) {
            showButton.addEventListener('click', function() {
                showMultiComponentsPanel();
            });
        } else {
            console.error('[MULTI-COMPONENTS] Show button not found!');
        }

        // Hide panel button
        const hideButton = document.getElementById('hide-multi-components-panel');
        if (hideButton) {
            hideButton.addEventListener('click', function() {
                hideMultiComponentsPanel();
            });
        }

        // Refresh button
        const refreshButton = document.getElementById('multi-components-refresh');
        if (refreshButton) {
            refreshButton.addEventListener('click', function() {
                loadDynamicMappings();
            });
        }
    }

    // Show the multi components panel
    function showMultiComponentsPanel() {
        const panel = document.getElementById('multi-components-panel');
        if (panel) {
            panel.style.display = 'block';
            loadDynamicMappings();
        }
    }

    // Hide the multi components panel
    function hideMultiComponentsPanel() {
        const panel = document.getElementById('multi-components-panel');
        if (panel) {
            panel.style.display = 'none';
        }
    }

    // Load dynamic mappings from the template
    function loadDynamicMappings() {
        if (!templateId) {
            console.error('[MULTI-COMPONENTS] No template ID available');
            return;
        }

        // Collect current field values
        collectCurrentFieldValues();

        // Call backend to get dynamic mappings
        fetch('/config_matrix/get_dynamic_mappings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                jsonrpc: "2.0",
                method: "call",
                params: {
                    template_id: templateId,
                    field_values: currentFieldValues
                },
                id: new Date().getTime()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.result && data.result.success) {
                dynamicMappings = data.result.mappings || [];
                renderDynamicMappings();
            } else {
                console.error('[MULTI-COMPONENTS] Error loading dynamic mappings:', data);
                showError('Error loading dynamic mappings');
            }
        })
        .catch(error => {
            console.error('[MULTI-COMPONENTS] Error fetching dynamic mappings:', error);
            showError('Error loading dynamic mappings');
        });
    }

    // Collect current field values
    function collectCurrentFieldValues() {
        currentFieldValues = {};
        const configFields = document.querySelectorAll('.config-field');

        configFields.forEach(field => {
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');

            if (technicalName) {
                let value;
                switch (fieldType) {
                    case 'boolean':
                        value = field.checked;
                        break;
                    case 'number':
                        value = parseFloat(field.value) || 0;
                        break;
                    case 'selection':
                        value = field.value && field.value !== '-- Select an option --' ? field.value : '';
                        break;
                    default:
                        value = field.value;
                }
                currentFieldValues[technicalName] = value;
            }
        });

    }

    // Render dynamic mappings list
    function renderDynamicMappings() {

        const container = document.getElementById('dynamic-questions-list');
        if (!container) {
            console.error('[MULTI-COMPONENTS] Dynamic questions container not found');
            return;
        }

        if (dynamicMappings.length === 0) {
            container.innerHTML = '<div class="text-muted text-center p-3">No dynamic mappings found</div>';
            return;
        }

        let html = '';
        dynamicMappings.forEach((mapping, index) => {
            const isActive = mapping.has_current_value ? 'border-success' : 'border-secondary';
            const statusIcon = mapping.has_current_value ? 'fas fa-check-circle text-success' : 'fas fa-question-circle text-muted';

            html += `
                <div class="card mb-2 ${isActive}" data-mapping-index="${index}">
                    <div class="card-body p-2">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">${mapping.field_name} → ${mapping.option_name}</h6>
                                <small class="text-muted">Reference: ${mapping.reference_field}</small>
                                <br>
                                <small class="text-muted">Match Field: ${mapping.match_field}</small>
                            </div>
                            <i class="${statusIcon}"></i>
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-outline-primary show-products-btn"
                                    data-mapping-index="${index}">
                                Show Products (${mapping.product_count})
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;

        // Add click handlers for show products buttons
        container.querySelectorAll('.show-products-btn').forEach(button => {
            button.addEventListener('click', function() {
                const mappingIndex = parseInt(this.getAttribute('data-mapping-index'));
                showFilteredProducts(mappingIndex);
            });
        });
    }

    // Show filtered products for a specific mapping
    function showFilteredProducts(mappingIndex) {
        const mapping = dynamicMappings[mappingIndex];
        if (!mapping) {
            console.error('[MULTI-COMPONENTS] Mapping not found:', mappingIndex);
            return;
        }

        const container = document.getElementById('filtered-products-list');
        if (!container) {
            console.error('[MULTI-COMPONENTS] Filtered products container not found');
            return;
        }

        // Show loading
        container.innerHTML = '<div class="text-center p-3"><i class="fas fa-spinner fa-spin"></i> Loading products...</div>';

        // Get the current value for the reference field
        const referenceValue = currentFieldValues[mapping.reference_field_technical] || '';

        // Call backend to get filtered products
        fetch('/config_matrix/get_filtered_products', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                jsonrpc: "2.0",
                method: "call",
                params: {
                    mapping_id: mapping.id,
                    reference_value: referenceValue,
                    field_values: currentFieldValues
                },
                id: new Date().getTime()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.result && data.result.success) {
                renderFilteredProducts(data.result.products || [], mapping, referenceValue);
            } else {
                console.error('[MULTI-COMPONENTS] Error loading filtered products:', data);
                container.innerHTML = '<div class="alert alert-danger p-2">Error loading products</div>';
            }
        })
        .catch(error => {
            console.error('[MULTI-COMPONENTS] Error fetching filtered products:', error);
            container.innerHTML = '<div class="alert alert-danger p-2">Error loading products</div>';
        });
    }

    // Render filtered products
    function renderFilteredProducts(products, mapping, referenceValue) {
        const container = document.getElementById('filtered-products-list');
        if (!container) return;

        let html = `
            <div class="mb-3">
                <h6>${mapping.field_name} → ${mapping.option_name}</h6>
                <small class="text-muted">
                    Reference: ${mapping.reference_field} = "${referenceValue || 'Not set'}"
                </small>
            </div>
        `;

        if (products.length === 0) {
            html += '<div class="text-muted text-center p-3">No matching products found</div>';
        } else {
            html += '<div class="list-group list-group-flush">';
            products.forEach(product => {
                const isSelected = product.is_selected ? 'list-group-item-success' : '';
                const selectedIcon = product.is_selected ? '<i class="fas fa-check-circle text-success me-2"></i>' : '';

                html += `
                    <div class="list-group-item ${isSelected}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                ${selectedIcon}<strong>${product.name}</strong>
                                <br>
                                <small class="text-muted">Code: ${product.code || 'N/A'}</small>
                                <br>
                                <small class="text-muted">Match Value: ${product.match_value || 'N/A'}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-secondary">${product.price}</span>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
        }

        container.innerHTML = html;
    }

    // Show error message
    function showError(message) {
        const container = document.getElementById('dynamic-questions-list');
        if (container) {
            container.innerHTML = `<div class="alert alert-danger p-2">${message}</div>`;
        }
    }

    // Initialize after a longer delay to ensure all other scripts have loaded
    setTimeout(initialize, 1500);

    // Make functions available globally
    window.multiComponentsHelper = {
        initialize,
        loadDynamicMappings,
        showMultiComponentsPanel,
        hideMultiComponentsPanel
    };
});
