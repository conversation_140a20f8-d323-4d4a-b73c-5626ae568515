<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="canbrax_configmatrix.Configurator" owl="1">
        <div class="o_config_matrix_configurator">
            <div class="o_config_matrix_header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 t-if="state.template">
                            <t t-esc="state.template.name"/>
                        </h1>
                        <div class="o_config_matrix_product" t-if="state.template">
                            <span>Product: </span>
                            <strong t-esc="state.template.product_name"/>
                        </div>
                    </div>
                    <div t-if="state.template" class="o_config_matrix_use_case">
                        <div class="btn-group" role="group">
                            <button t-if="state.enabledUseCases.includes('check_measure')"
                                    type="button"
                                    t-attf-class="btn {{ state.activeUseCase === 'check_measure' ? 'btn-primary' : 'btn-outline-primary' }}"
                                    t-on-click="() => switchUseCase('check_measure')">
                                Check Measure
                            </button>
                            <button t-if="state.enabledUseCases.includes('sales')"
                                    type="button"
                                    t-attf-class="btn {{ state.activeUseCase === 'sales' ? 'btn-primary' : 'btn-outline-primary' }}"
                                    t-on-click="() => switchUseCase('sales')">
                                Sales/Quoting
                            </button>
                            <button t-if="state.enabledUseCases.includes('online')"
                                    type="button"
                                    t-attf-class="btn {{ state.activeUseCase === 'online' ? 'btn-primary' : 'btn-outline-primary' }}"
                                    t-on-click="() => switchUseCase('online')">
                                Online Sales
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="o_config_matrix_content">
                <div t-if="state.loading" class="o_config_matrix_loading">
                    <div class="d-flex flex-column align-items-center justify-content-center p-5">
                        <i class="fa fa-spinner fa-spin fa-3x mb-3"></i>
                        <div class="h4 text-muted">Loading configuration...</div>
                        <div class="text-muted small">Please wait while we prepare your configuration</div>
                    </div>
                </div>

                <div t-if="state.error" class="o_config_matrix_error alert alert-danger">
                    <div class="d-flex align-items-center">
                        <i class="fa fa-exclamation-triangle fa-2x me-3"></i>
                        <div>
                            <div class="h5 mb-1">Configuration Error</div>
                            <div><t t-esc="state.error"/></div>
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-danger" t-on-click="loadTemplate">
                                    <i class="fa fa-refresh me-1"></i> Try Again
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div t-if="!state.loading and !state.error" class="o_config_matrix_sections">
                    <!-- Section Navigation -->
                    <div class="o_config_matrix_section_nav">
                        <ul class="nav nav-tabs">
                            <t t-foreach="state.sections" t-as="section" t-key="section.id">
                                <li class="nav-item">
                                    <a t-attf-class="nav-link {{ state.currentSection === section_index ? 'active' : '' }}"
                                       href="#"
                                       t-on-click.prevent="() => state.currentSection = section_index">
                                        <t t-esc="section.name"/>
                                    </a>
                                </li>
                            </t>
                        </ul>
                    </div>

                    <!-- Current Section -->
                    <div class="o_config_matrix_section_content">
                        <t t-if="state.sections.length &gt; 0">
                            <div class="o_config_matrix_section">
                                <h3 t-esc="state.sections[state.currentSection].name"/>
                                <p t-if="state.sections[state.currentSection].description"
                                   t-esc="state.sections[state.currentSection].description"/>

                                <div class="o_config_matrix_fields">
                                    <t t-foreach="state.sections[state.currentSection].fields" t-as="field" t-key="field.id">
                                        <div t-if="field.visible" class="o_config_matrix_field mb-3" t-att-data-field-id="field.id">
                                            <!-- Question number in the blue sidebar -->
                                            <span class="question-number" t-esc="field.question_number || ''"></span>
                                            <label t-attf-for="field_{{ field.technical_name }}" class="form-label">
                                                <t t-esc="field.name"/>
                                                <span t-if="field.required" class="text-danger">*</span>
                                            </label>

                                            <div t-if="field.help_text" class="form-text text-muted mb-1">
                                                <t t-esc="field.help_text"/>
                                            </div>

                                            <!-- Text Field -->
                                            <input t-if="field.field_type === 'text'"
                                                   t-attf-id="field_{{ field.technical_name }}"
                                                   type="text"
                                                   class="form-control"
                                                   t-att-required="field.required"
                                                   t-att-value="state.values[field.technical_name] || ''"
                                                   t-on-input="(ev) => onFieldChange(field, ev.target.value)"
                                                   t-att-maxlength="field.max_length"
                                                   t-att-pattern="field.pattern"/>

                                            <!-- Number Field -->
                                            <div t-if="field.field_type === 'number'" class="input-group">
                                                <input t-attf-id="field_{{ field.technical_name }}"
                                                       type="number"
                                                       class="form-control"
                                                       t-att-required="field.required"
                                                       t-att-value="state.values[field.technical_name] || ''"
                                                       t-on-input="(ev) => onFieldChange(field, parseFloat(ev.target.value))"
                                                       t-att-min="field.min_value"
                                                       t-att-max="field.max_value"
                                                       t-att-step="field.decimal_precision ? Math.pow(10, -field.decimal_precision) : 1"/>
                                                <span t-if="field.uom_name" class="input-group-text">
                                                    <t t-esc="field.uom_name"/>
                                                </span>
                                            </div>

                                            <!-- Boolean Field -->
                                            <div t-if="field.field_type === 'boolean'" class="form-check">
                                                <input t-attf-id="field_{{ field.technical_name }}"
                                                       type="checkbox"
                                                       class="form-check-input"
                                                       t-att-checked="state.values[field.technical_name] || false"
                                                       t-on-change="(ev) => onFieldChange(field, ev.target.checked)"/>
                                                <label t-attf-for="field_{{ field.technical_name }}" class="form-check-label">
                                                    <t t-esc="field.boolean_true_label || 'Yes'"/>
                                                </label>
                                            </div>

                                            <!-- Selection Field -->
                                            <select t-if="field.field_type === 'selection'"
                                                    t-attf-id="field_{{ field.technical_name }}"
                                                    class="form-select"
                                                    t-att-required="field.required"
                                                    t-on-change="(ev) => onFieldChange(field, ev.target.value)">
                                                <option value="">-- Select --</option>
                                                <t t-foreach="field.options" t-as="option" t-key="option.id">
                                                    <option t-att-value="option.value"
                                                            t-att-selected="state.values[field.technical_name] === option.value">
                                                        <t t-esc="option.name"/>
                                                    </option>
                                                </t>
                                            </select>

                                            <!-- Date Field -->
                                            <div t-if="field.field_type === 'date'" class="input-group date">
                                                <input t-attf-id="field_{{ field.technical_name }}"
                                                       type="date"
                                                       class="form-control"
                                                       t-att-required="field.required"
                                                       t-att-value="state.values[field.technical_name] || ''"
                                                       t-on-change="(ev) => onFieldChange(field, ev.target.value)"
                                                       autocomplete="off"/>
                                            </div>
                                        </div>
                                    </t>
                                </div>
                            </div>
                        </t>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="o_config_matrix_nav_buttons">
                        <button t-if="state.currentSection &gt; 0"
                                class="btn btn-secondary"
                                t-on-click="prevSection">
                            <i class="fa fa-arrow-left"/> Previous
                        </button>
                        <button t-if="state.currentSection &lt; state.sections.length - 1"
                                class="btn btn-primary"
                                t-on-click="nextSection">
                            Next <i class="fa fa-arrow-right"/>
                        </button>
                    </div>
                </div>
            </div>

            <div class="o_config_matrix_footer">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <div>
                        <button class="btn btn-secondary me-2" t-on-click="cancelConfiguration"
                                t-att-disabled="state.saving">
                            Cancel
                        </button>
                    </div>
                    <div t-if="state.saving" class="text-muted">
                        <i class="fa fa-spinner fa-spin me-1"></i> Saving configuration...
                    </div>
                    <div>
                        <button class="btn btn-primary" t-on-click="saveConfiguration"
                                t-att-disabled="state.saving">
                            <t t-if="state.saving">
                                <i class="fa fa-spinner fa-spin me-1"></i> Saving...
                            </t>
                            <t t-else="1">
                                Save Configuration
                            </t>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </t>
</templates>
