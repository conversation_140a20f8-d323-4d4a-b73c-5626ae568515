<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">

    <!-- Main Domain Builder Interface Template -->
    <div t-name="canbrax_configmatrix.DomainBuilderInterface" class="o_domain_builder_interface">
        <div class="container-fluid h-100">
            <div class="row h-100">
                <!-- Sidebar with fields -->
                <div class="col-md-3 bg-light border-right h-100 overflow-auto">
                    <div class="p-3">
                        <h5 class="mb-3">
                            <i class="fa fa-list-alt mr-2"></i>
                            Available Fields (<t t-esc="state.fields_data.length"/>)
                        </h5>

                        <div class="field-list">
                            <t t-foreach="Object.entries(fieldsGroupedBySection)" t-as="sectionEntry" t-key="sectionEntry[0]">
                                <div class="field-section mb-3">
                                    <h6 class="section-title text-primary">
                                        <i class="fa fa-folder mr-1"></i>
                                        <t t-esc="sectionEntry[0]"/>
                                    </h6>
                                    <t t-foreach="sectionEntry[1]" t-as="field" t-key="field.id">
                                        <div class="field-item card mb-2 cursor-pointer" t-on-click="() => this.onFieldClick(field)">
                                            <div class="card-body p-2">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div class="flex-grow-1">
                                                        <h6 class="card-title mb-1 small">
                                                            <t t-esc="field.name"/>
                                                        </h6>
                                                        <code class="small text-primary">
                                                            <t t-esc="field.technical_name"/>
                                                        </code>
                                                    </div>
                                                    <span t-attf-class="badge badge-{{field.field_type === 'selection' ? 'info' : field.field_type === 'number' ? 'success' : field.field_type === 'boolean' ? 'warning' : 'secondary'}} small">
                                                        <t t-esc="field.field_type"/>
                                                    </span>
                                                </div>
                                                <div t-if="field.options" class="mt-1">
                                                    <small class="text-muted">
                                                        <i class="fa fa-list mr-1"></i>
                                                        <t t-esc="field.options.length"/> options
                                                    </small>
                                                </div>
                                                <div t-if="field.uom" class="mt-1">
                                                    <small class="text-muted">
                                                        <i class="fa fa-ruler mr-1"></i>
                                                        <t t-esc="field.uom.name"/>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </t>
                                </div>
                            </t>
                        </div>

                        <hr/>

                        <h6 class="mb-2">
                            <i class="fa fa-info-circle mr-2"></i>
                            Quick Tips
                        </h6>
                        <div class="alert alert-info p-2 small">
                            <ul class="mb-0 pl-3">
                                <li>Click field names to see details</li>
                                <li>Use AND for all conditions true</li>
                                <li>Use OR for any condition true</li>
                                <li>Selection fields show available options</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Main builder area -->
                <div class="col-md-9 h-100 d-flex flex-column">
                    <div class="p-3 border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="mb-0">
                                <i class="fa fa-cogs mr-2"></i>
                                Domain Builder
                            </h4>
                            <div>
                                <button class="btn btn-primary mr-2" t-on-click="onAddCondition">
                                    <i class="fa fa-plus mr-1"></i>
                                    Add Condition
                                </button>
                                <button class="btn btn-secondary" t-on-click="onClose">
                                    <i class="fa fa-times mr-1"></i>
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Conditions area -->
                    <div class="flex-grow-1 p-3 overflow-auto">
                        <div t-if="state.conditions.length === 0" class="text-center p-5">
                            <div class="mb-3" style="font-size: 4em; color: #dee2e6;">
                                <i class="fa fa-target"></i>
                            </div>
                            <h5 class="text-muted mb-2">No conditions yet</h5>
                            <p class="text-muted mb-3">
                                Click <strong>"Add Condition"</strong> or click any field on the left to start building your domain!
                            </p>
                            <div class="alert alert-info">
                                <h6><i class="fa fa-lightbulb-o mr-2"></i>Quick Start:</h6>
                                <ul class="list-unstyled mb-0 text-left">
                                    <li><i class="fa fa-hand-pointer-o mr-2"></i><strong>Click a field</strong> on the left to add it as a condition</li>
                                    <li><i class="fa fa-plus mr-2"></i><strong>Add Condition</strong> button to create empty condition</li>
                                    <li><i class="fa fa-cog mr-2"></i>Configure operator and value for each condition</li>
                                    <li><i class="fa fa-link mr-2"></i>Use AND/OR logic to combine conditions</li>
                                </ul>
                            </div>
                        </div>

                        <div t-if="state.conditions.length > 0" class="condition-list">
                            <t t-foreach="state.conditions" t-as="condition" t-key="condition.id">
                                <div class="condition-row card mb-3">
                                    <div class="card-body p-3">
                                        <div class="row align-items-center">
                                            <!-- Logic Operator (only for non-first conditions) -->
                                            <div class="col-md-2" t-if="condition_index > 0">
                                                <select class="form-control" t-model="condition.logic" t-on-change="(ev) => this.onConditionChange(condition.id, 'logic', ev.target.value)">
                                                    <option value="and">AND</option>
                                                    <option value="or">OR</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2" t-if="condition_index === 0">
                                                <span class="text-muted">First condition</span>
                                            </div>

                                            <!-- Field Selection -->
                                            <div class="col-md-3">
                                                <select class="form-control" t-model="condition.field" t-on-change="(ev) => this.onConditionChange(condition.id, 'field', ev.target.value)">
                                                    <option value="">Select Field...</option>
                                                    <t t-foreach="state.fields_data" t-as="field" t-key="field.id">
                                                        <option t-att-value="field.technical_name">
                                                            <t t-esc="field.name"/>
                                                        </option>
                                                    </t>
                                                </select>
                                            </div>

                                            <!-- Operator Selection -->
                                            <div class="col-md-2">
                                                <select class="form-control" t-model="condition.operator" t-on-change="(ev) => this.onConditionChange(condition.id, 'operator', ev.target.value)">
                                                    <option value="=">=</option>
                                                    <option value="!=">!=</option>
                                                    <option value="in">in</option>
                                                    <option value="not in">not in</option>
                                                    <option value=">">&gt;</option>
                                                    <option value="&lt;">&lt;</option>
                                                    <option value=">=">&gt;=</option>
                                                    <option value="&lt;=">&lt;=</option>
                                                </select>
                                            </div>

                                            <!-- Value Input -->
                                            <div class="col-md-3">
                                                <input type="text" class="form-control" placeholder="Value..."
                                                       t-model="condition.value"
                                                       t-on-input="(ev) => this.onConditionChange(condition.id, 'value', ev.target.value)"/>
                                            </div>

                                            <!-- Remove Button -->
                                            <div class="col-md-2 text-right">
                                                <button class="btn btn-sm btn-outline-danger" t-on-click="() => this.onRemoveCondition(condition.id)" title="Remove condition">
                                                    <i class="fa fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </div>

                    <!-- Domain output -->
                    <div class="p-3 border-top bg-light">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="mb-2">Generated Domain:</h6>
                                <pre class="bg-dark text-light p-3 rounded small" style="max-height: 150px; overflow-y: auto;"><t t-esc="state.current_domain"/></pre>
                            </div>
                            <div class="col-md-4">
                                <h6 class="mb-2">Status:</h6>
                                <div class="small text-muted">
                                    <t t-esc="state.conditions.length"/> conditions
                                </div>
                                <div class="mt-2">
                                    <button class="btn btn-success btn-sm">
                                        <i class="fa fa-save mr-1"></i>
                                        Save Domain
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</templates>
