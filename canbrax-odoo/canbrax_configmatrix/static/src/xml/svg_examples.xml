<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <!-- Example of a base SVG component -->
    <t t-name="canbrax_configmatrix.base_svg_example">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 600" width="100%" height="100%">
            <!-- Door Frame -->
            <rect x="50" y="50" width="300" height="500" fill="#f5f5f5" stroke="#333" stroke-width="3"/>
            <!-- This is the base door - other components will be added dynamically -->
        </svg>
    </t>
    
    <!-- Example of a layer SVG component for BBQ location -->
    <t t-name="canbrax_configmatrix.bbq_layer_example">
        <rect x="70" y="70" width="260" height="460" fill="#e0e0e0" stroke="#555" stroke-width="1"/>
        <text x="175" y="300" text-anchor="middle" font-size="24" fill="#333">BBQ</text>
    </t>
    
    <!-- Example of a layer SVG component for Kitchen location -->
    <t t-name="canbrax_configmatrix.kitchen_layer_example">
        <rect x="70" y="70" width="260" height="460" fill="#d0f0d0" stroke="#555" stroke-width="1"/>
        <text x="175" y="300" text-anchor="middle" font-size="24" fill="#333">Kitchen</text>
    </t>
</templates>
