<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="canbrax_configmatrix.InteractiveMatrixEditor" owl="1">
        <div class="interactive-matrix-editor">
            <div class="matrix-toolbar mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="toolbar-actions">
                        <button type="button" class="btn btn-sm btn-outline-info me-2" t-on-click="fillSampleData">
                            <i class="fa fa-magic"/> Fill Sample
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-warning me-2" t-on-click="clearAllData">
                            <i class="fa fa-trash"/> Clear All
                        </button>
                    </div>
                    <div class="matrix-stats">
                        <small class="text-muted">
                            <span class="badge badge-primary me-2"><t t-esc="state.stats.total"/> total</span>
                            <span class="badge badge-success me-2"><t t-esc="state.stats.filled"/> filled</span>
                            <span class="badge badge-info"><t t-esc="state.stats.completion"/>% complete</span>
                        </small>
                    </div>
                </div>
            </div>

            <div class="matrix-container" style="max-height: 500px; overflow: auto;">
                <table class="table table-sm table-bordered matrix-table" style="margin: 0; font-size: 12px; border-collapse: separate; border-spacing: 0;">
                    <tbody t-ref="matrixGrid">
                        <!-- Matrix will be rendered here by JavaScript -->
                    </tbody>
                </table>
            </div>

            <div class="matrix-help mt-2">
                <small class="text-muted">
                    <i class="fa fa-info-circle"/>
                    <strong>Excel-like Controls:</strong> Click to select • Double-click to edit • Enter/F2 to edit • Tab to move • Delete to clear • Auto-saves to JSON field above
                </small>
            </div>
        </div>
    </t>

</templates>
