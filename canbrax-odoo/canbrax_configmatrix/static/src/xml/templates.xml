<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="canbrax_configmatrix.ConditionBuilder" owl="1">
        <div class="o_condition_builder">
            <div class="o_condition_builder_current mb-3">
                <label class="form-label">Current Condition:</label>
                <div class="input-group">
                    <input type="text" class="form-control" t-model="state.condition" readonly="readonly"/>
                    <button class="btn btn-secondary" t-on-click="onClearCondition">
                        <i class="fa fa-trash"/> Clear
                    </button>
                </div>
            </div>
            
            <div class="o_condition_builder_form">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label class="form-label">Field:</label>
                        <select class="form-select" t-model="state.selectedField">
                            <option value="">-- Select Field --</option>
                            <t t-foreach="state.fields" t-as="field" t-key="field.technical_name">
                                <option t-att-value="field.technical_name">
                                    <t t-esc="field.name"/> (<t t-esc="field.technical_name"/>)
                                </option>
                            </t>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Operator:</label>
                        <select class="form-select" t-model="state.selectedOperator">
                            <t t-foreach="state.operators" t-as="op" t-key="op.value">
                                <option t-att-value="op.value">
                                    <t t-esc="op.label"/>
                                </option>
                            </t>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label class="form-label">Value:</label>
                        <div class="input-group">
                            <input type="text" class="form-control" t-model="state.fieldValue" 
                                   placeholder="Enter value..."/>
                            <button class="btn btn-primary" t-on-click="onAddCondition">
                                <i class="fa fa-plus"/> Add
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="alert alert-info" role="alert">
                            <p><strong>Tips:</strong></p>
                            <ul>
                                <li>For text fields, enter the value without quotes</li>
                                <li>For boolean fields, enter "true" or "false"</li>
                                <li>For "in" operator, enter comma-separated values</li>
                                <li>For complex conditions, you can edit the condition directly</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>
</templates>
