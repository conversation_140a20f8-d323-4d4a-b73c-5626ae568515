<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="canbrax_configmatrix.DimensionRangesEditor" owl="1">
        <div class="dimension-ranges-editor">
            <!-- Compact Header with Add Button -->
            <div class="d-flex justify-content-between align-items-center mb-2">
                <small class="fw-bold text-muted">
                    <t t-esc="getDimensionType().charAt(0).toUpperCase() + getDimensionType().slice(1)"/> Ranges
                </small>
                <div class="btn-group btn-group-xs">
                    <button type="button" class="btn btn-xs btn-outline-secondary" t-on-click="refreshRanges" title="Refresh">
                        <i class="fa fa-refresh"/>
                    </button>
                    <button type="button" class="btn btn-xs btn-outline-info" t-on-click="loadTestRanges" title="Load Test Data">
                        <i class="fa fa-flask"/>
                    </button>
                    <button type="button" class="btn btn-xs btn-outline-warning" t-on-click="testNormalization" title="Test Normalization">
                        <i class="fa fa-cog"/>
                    </button>
                    <button type="button" class="btn btn-xs btn-outline-primary" t-on-click="addRange">
                        <i class="fa fa-plus"/> Add
                    </button>
                </div>
            </div>

            <!-- Compact Ranges Table -->
            <div class="table-responsive">
                <table class="table table-sm table-bordered" t-ref="rangesTable" style="margin-bottom: 0.5rem; border: 1px solid #dee2e6;">
                    <thead>
                        <tr>
                            <th style="width: 30%; font-size: 11px; padding: 4px;">Value</th>
                            <th style="width: 50%; font-size: 11px; padding: 4px;">Label</th>
                            <th style="width: 20%; font-size: 11px; padding: 4px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr t-if="state.ranges.length === 0">
                            <td colspan="3" class="text-center text-muted py-1" style="font-size: 11px;">
                                No ranges defined. Click "Add" to get started.
                            </td>
                        </tr>
                        <tr t-foreach="state.ranges" t-as="range" t-key="range_index">
                            <!-- Editing Mode -->
                            <t t-if="state.editingIndex === range_index">
                                <td style="padding: 2px;">
                                    <input type="number"
                                           class="form-control form-control-sm"
                                           style="font-size: 11px; padding: 2px 4px;"
                                           t-model="state.editValues.value"
                                           t-on-input="(ev) => this.onInputChange('value', ev)"
                                           t-on-keydown="onKeydown"
                                           placeholder="Value"/>
                                </td>
                                <td style="padding: 2px;">
                                    <input type="text"
                                           class="form-control form-control-sm"
                                           style="font-size: 11px; padding: 2px 4px;"
                                           t-model="state.editValues.label"
                                           t-on-input="(ev) => this.onInputChange('label', ev)"
                                           t-on-keydown="onKeydown"
                                           placeholder="Label"/>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-success" t-on-click="saveEdit" title="Save">
                                            <i class="fa fa-check"/>
                                        </button>
                                        <button type="button" class="btn btn-secondary" t-on-click="cancelEdit" title="Cancel">
                                            <i class="fa fa-times"/>
                                        </button>
                                    </div>
                                </td>
                            </t>

                            <!-- Display Mode -->
                            <t t-else="">
                                <td style="padding: 2px 4px; font-size: 11px;">
                                    <strong><t t-esc="range.value"/></strong>
                                </td>
                                <td style="padding: 2px 4px; font-size: 11px;">
                                    <t t-esc="range.label"/>
                                </td>
                                <td style="padding: 2px;">
                                    <div class="btn-group btn-group-xs">
                                        <button type="button" class="btn btn-xs btn-outline-secondary"
                                                style="font-size: 10px; padding: 1px 4px;"
                                                t-on-click="() => this.startEdit(range_index)" title="Edit">
                                            <i class="fa fa-edit"/>
                                        </button>
                                        <button type="button" class="btn btn-xs btn-outline-danger"
                                                style="font-size: 10px; padding: 1px 4px;"
                                                t-on-click="() => this.removeRange(range_index)" title="Remove">
                                            <i class="fa fa-trash"/>
                                        </button>
                                    </div>
                                </td>
                            </t>
                        </tr>
                    </tbody>
                </table>
            </div>

        </div>
    </t>

</templates>
