.o_config_matrix_configurator {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 1rem;
}

.o_config_matrix_header {
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 1rem;
}

.o_config_matrix_use_case {
    margin-left: 1rem;
}

.o_config_matrix_use_case .btn-group {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border-radius: 0.25rem;
}

.o_config_matrix_use_case .btn {
    font-size: 0.9rem;
    padding: 0.375rem 0.75rem;
}

/* Use case badges */
.o_config_matrix_use_case_badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 0.25rem;
    margin-left: 0.5rem;
}

.o_config_matrix_use_case_badge.check_measure {
    background-color: #e6f2ff;
    color: #0056b3;
    border: 1px solid #b8daff;
}

.o_config_matrix_use_case_badge.sales {
    background-color: #e6fff2;
    color: #00b356;
    border: 1px solid #b8ffda;
}

.o_config_matrix_use_case_badge.online {
    background-color: #fff2e6;
    color: #b35600;
    border: 1px solid #ffdab8;
}

.o_config_matrix_content {
    flex: 1;
    overflow-y: auto;
    padding: 0 1rem;
}

.o_config_matrix_footer {
    margin-top: 1.5rem;
    border-top: 1px solid #dee2e6;
    padding-top: 1rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

.o_config_matrix_loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    font-size: 1.2rem;
}

.o_config_matrix_loading i {
    margin-right: 0.5rem;
}

.o_config_matrix_section_content {
    margin: 1rem 0;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    position: relative;
}

/* Blue sidebar for question numbers */
.o_config_matrix_section_content:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 40px;
    background-color: #e6f2ff; /* Light blue background */
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    z-index: 0;
    border-right: 1px solid #d1e6ff; /* Light blue border */
}

/* Sticky columns for left and right sidebars - FIXED VERSION */
.sticky-column {
    position: sticky !important;
    position: -webkit-sticky !important; /* Safari */
    top: 10px !important;
    height: fit-content !important;
    max-height: calc(100vh - 20px) !important;
    overflow-y: auto !important;
    z-index: 1000 !important;
}

/* Ensure the sidebar cards have proper spacing when sticky */
.sticky-column .card {
    margin-bottom: 1rem;
}

/* Smooth scrolling for the sticky columns */
.sticky-column {
    scroll-behavior: smooth;
}

/* CRITICAL: Ensure the main row container supports sticky positioning */
.container-fluid {
    overflow: visible !important;
}

.container-fluid .row {
    display: flex !important;
    align-items: flex-start !important;
    overflow: visible !important;
}

/* Force the row to use flexbox layout */
.row.d-flex {
    display: flex !important;
    align-items: flex-start !important;
    flex-wrap: nowrap !important;
}

/* Ensure columns have proper flex behavior */
.sticky-column {
    flex: 0 0 auto !important; /* Don't grow or shrink */
}

/* Middle column should grow to fill space */
.col-md-6 {
    flex: 1 1 auto !important;
    min-width: 0 !important; /* Allow content to shrink */
    overflow: visible !important;
}

/* Specific width overrides for sticky columns */
.sticky-column.left-column {
    width: 25% !important;
    min-width: 300px !important;
}

.sticky-column.right-column {
    width: 25% !important;
    min-width: 300px !important;
}

/* Debug styles to verify sticky is working */
.sticky-column::before {
    content: "📌 STICKY";
    position: absolute;
    top: -15px;
    left: 5px;
    font-size: 10px;
    color: #007bff;
    background: white;
    padding: 2px 4px;
    border-radius: 3px;
    z-index: 9999;
    display: none; /* DISABLED - sticky is working */
    border: 1px solid #007bff;
}

.o_config_matrix_nav_buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
}

.o_config_matrix_field {
    margin-bottom: 1rem;
    position: relative;
    transition: background-color 0.3s ease;
}

.o_config_matrix_field label {
    font-weight: bold;
}

/* Question number display */
.o_config_matrix_field {
    position: relative;
    padding-left: 40px; /* Add padding to make room for question numbers */
}

.o_config_matrix_field .question-number {
    position: absolute;
    left: 8px; /* Position in the blue sidebar */
    top: 50%;
    transform: translateY(-50%);
    width: 28px;
    height: 28px;
    color: white; /* White text for maximum contrast */
    text-align: center;
    line-height: 28px;
    font-size: 16px;
    font-weight: 700; /* Bolder text */
    transition: all 0.2s ease;
    background-color: #0056b3; /* Solid blue background */
    border-radius: 50%; /* Circular shape */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* More pronounced shadow */
    z-index: 5; /* Ensure it's above everything */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Hover effect */
.o_config_matrix_field:hover .question-number {
    color: white;
    background-color: #007bff; /* Lighter blue on hover */
    transform: translateY(-50%) scale(1.1); /* Slightly larger on hover */
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3); /* Enhanced shadow on hover */
}

/* Highlight effect for conditional navigation */
.o_config_matrix_field.highlight-field {
    background-color: rgba(0, 123, 255, 0.05);
    animation: pulse 1s ease-in-out;
}

.o_config_matrix_field.highlight-field .question-number {
    color: rgba(0, 123, 255, 1);
    font-weight: 700;
    animation: number-pulse 1s ease-in-out;
}

@keyframes pulse {
    0% { background-color: rgba(0, 123, 255, 0.05); }
    50% { background-color: rgba(0, 123, 255, 0.15); }
    100% { background-color: rgba(0, 123, 255, 0.05); }
}

@keyframes number-pulse {
    0% { transform: translateY(-50%) scale(1); }
    50% { transform: translateY(-50%) scale(1.2); }
    100% { transform: translateY(-50%) scale(1); }
}

.o_config_matrix_field .form-text {
    font-size: 0.875rem;
}

.o_config_matrix_section h3 {
    margin-bottom: 1rem;
}

.o_config_matrix_section p {
    margin-bottom: 1.5rem;
    color: #6c757d;
}

/* Date field styling */
.o_config_matrix_field .input-group.date {
    position: relative;
}

.o_config_matrix_field .input-group.date input[type="date"] {
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    border: 1px solid #ced4da;
    width: 100%;
}

/* Enhanced keyboard navigation feedback */
.field-navigation-highlight {
    background: linear-gradient(135deg, rgba(100, 92, 179, 0.1), rgba(100, 92, 179, 0.05)) !important;
    border: 2px solid #645CB3 !important;
    border-radius: 6px !important;
    transform: scale(1.02) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 4px 12px rgba(100, 92, 179, 0.15) !important;
    position: relative !important;
    z-index: 10 !important;
}

/* Enhanced field focus states */
.config-field:focus {
    border-color: #645CB3 !important;
    box-shadow: 0 0 0 3px rgba(100, 92, 179, 0.15) !important;
    outline: none !important;
    transition: all 0.2s ease !important;
}

/* Keyboard shortcuts help */
.keyboard-shortcuts-hint {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 6px;
    font-size: 12px;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    max-width: 250px;
}

.keyboard-shortcuts-hint.show {
    opacity: 1;
}

.keyboard-shortcuts-hint kbd {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 11px;
    font-family: monospace;
    margin: 0 2px;
}

/* Enhanced dynamic error messages styling for existing system */
.error-message-container {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.error-message-container.error-visible {
    animation: errorPulse 0.5s ease-in-out;
}

@keyframes errorPulse {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.02);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Enhanced styling for dynamic error text */
.dynamic-error-text {
    font-weight: 600;
    color: #721c24;
    font-size: 14px;
    line-height: 1.4;
    display: inline;
}

.static-error-text {
    font-weight: 600;
    color: #721c24;
    font-size: 14px;
    line-height: 1.4;
    display: inline;
}

/* Show/hide logic for dynamic vs static error text */
.dynamic-error-text:empty + .static-error-text {
    display: inline !important;
}

.dynamic-error-text:not(:empty) + .static-error-text {
    display: none !important;
}

/* Enhanced alert styling for error messages */
.error-message-container .alert {
    border-left: 4px solid #dc3545;
    background: linear-gradient(135deg, #f8d7da, #f1c2c7);
    border-radius: 0 6px 6px 0;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.15);
    margin-bottom: 0;
    padding: 8px 12px;
}

/* Error message icons */
.error-message-container .alert i {
    color: #dc3545;
    margin-right: 6px;
    font-size: 14px;
}

/* Field with error styling */
.config-field.has-error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.15) !important;
}

.config-field.has-error:focus {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.25) !important;
}

/* Enhanced dynamic error messages styling */
.error-message-container {
    margin-top: 8px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.error-message-container.error-message-show {
    opacity: 1;
    transform: translateY(0);
}

.error-message-container .alert {
    margin-bottom: 0;
    padding: 8px 12px;
    border-left: 4px solid #dc3545;
    background: linear-gradient(135deg, #f8d7da, #f1c2c7);
    border-radius: 0 6px 6px 0;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.15);
    animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dynamic-error-text {
    font-weight: 600;
    color: #721c24;
    font-size: 14px;
    line-height: 1.4;
}

.dynamic-error-text:empty + .static-error-text {
    display: block;
}

.dynamic-error-text:not(:empty) + .static-error-text {
    display: none;
}

/* Error message icons */
.error-message-container .alert i {
    color: #dc3545;
    margin-right: 6px;
    font-size: 14px;
}

/* Validation message improvements */
.validation-message {
    animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 4px solid #dc3545;
    background: linear-gradient(135deg, #f8d7da, #f1c2c7);
    border-radius: 0 6px 6px 0;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.15);
    margin-top: 8px;
    padding: 8px 12px;
}

/* Field with error styling */
.config-field.has-error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.15) !important;
}

.config-field.has-error:focus {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.25) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .o_config_matrix_section_nav ul {
        flex-wrap: nowrap;
        overflow-x: auto;
        white-space: nowrap;
    }

    .o_config_matrix_footer {
        flex-direction: column;
    }

    .o_config_matrix_footer button {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .keyboard-shortcuts-hint {
        bottom: 10px;
        left: 10px;
        right: 10px;
        max-width: none;
        text-align: center;
    }
}
