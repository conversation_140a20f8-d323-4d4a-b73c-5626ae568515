/* Website Portal CSS - Professional interface for product configuration */

/* Main layout and containers */
.website-portal-container {
    background-color: #f9f9f9;
    border-radius: 6px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 30px;
}

.website-portal-header {
    border-bottom: 2px solid #e5e5e5;
    margin-bottom: 24px;
    padding-bottom: 16px;
}

.website-portal-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.6rem;
}

.website-portal-subtitle {
    color: #5d6d7e;
    font-size: 1rem;
}

/* Configuration cards */
.config-card {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    transition: all 0.2s ease-in-out;
    margin-bottom: 20px;
    overflow: hidden;
}

.config-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.config-card-header {
    background-color: #f5f7fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.config-card-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.config-card-body {
    padding: 20px;
    background-color: #ffffff;
}

/* Tabs and navigation */
.website-portal-tabs .nav-link {
    color: #5d6d7e;
    font-weight: 500;
    padding: 10px 20px;
    border: none;
    border-radius: 4px 4px 0 0;
}

.website-portal-tabs .nav-link.active {
    color: #2c3e50;
    background-color: #fff;
    border-bottom: 3px solid #3498db;
}

.website-portal-tabs .nav-link:hover:not(.active) {
    background-color: #f5f7fa;
}

/* Form elements */
.website-form-group {
    margin-bottom: 20px;
}

.website-form-group label {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 6px;
    display: block;
}

.website-form-control {
    border: 1px solid #dce4ec;
    border-radius: 4px;
    padding: 10px 12px;
    font-size: 0.95rem;
    transition: border-color 0.15s ease-in-out;
}

.website-form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.15rem rgba(52, 152, 219, 0.25);
}

/* Buttons */
.btn-website {
    font-weight: 500;
    padding: 10px 16px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.btn-website-primary {
    background-color: #3498db;
    border-color: #3498db;
    color: white;
}

.btn-website-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.btn-website-secondary {
    background-color: #ecf0f1;
    border-color: #ecf0f1;
    color: #2c3e50;
}

.btn-website-secondary:hover {
    background-color: #dde4e6;
    border-color: #dde4e6;
}

.btn-website-outline {
    background-color: transparent;
    border: 1px solid #bdc3c7;
    color: #5d6d7e;
}

.btn-website-outline:hover {
    background-color: #f5f7fa;
    color: #2c3e50;
}

/* Configuration summary display */
.config-summary {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 15px;
    margin-top: 10px;
}

.config-summary-title {
    font-weight: 600;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e0e0e0;
}

.config-summary-section {
    margin-bottom: 12px;
}

.config-summary-section-title {
    font-weight: 500;
    margin-bottom: 5px;
}

.config-summary-item {
    display: flex;
    padding: 4px 0;
}

.config-summary-item-label {
    font-weight: 500;
    width: 40%;
    color: #5d6d7e;
}

.config-summary-item-value {
    width: 60%;
}

/* Progress indicators */
.config-progress {
    margin-bottom: 20px;
}

.config-progress .progress {
    height: 10px;
    border-radius: 5px;
    background-color: #ecf0f1;
}

.config-progress .progress-bar {
    background-color: #3498db;
    border-radius: 5px;
}

/* Utility classes */
.text-website-primary {
    color: #3498db;
}

.text-website-secondary {
    color: #5d6d7e;
}

.border-website {
    border: 1px solid #e0e0e0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .website-portal-tabs .nav-link {
        padding: 8px 12px;
        font-size: 0.9rem;
    }

    .config-summary-item {
        flex-direction: column;
    }

    .config-summary-item-label,
    .config-summary-item-value {
        width: 100%;
    }
}

/* Split button and actions */
.btn-split {
    padding: 4px 8px;
    font-size: 0.8rem;
    background-color: #f5f7fa;
    border: 1px solid #dce4ec;
    color: #5d6d7e;
}

.btn-split:hover {
    background-color: #ecf0f1;
    color: #2c3e50;
}

/* Multi-item management styles */
.config-item-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.config-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.config-item:hover {
    background-color: #f5f7fa;
}

.config-item:last-child {
    border-bottom: none;
}

.config-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.config-item-title {
    font-weight: 500;
    color: #2c3e50;
}

.config-item-actions {
    display: flex;
    gap: 8px;
}

.config-item-details {
    font-size: 0.9rem;
    color: #5d6d7e;
}

/* Tooltips */
.tooltip-website {
    position: relative;
    display: inline-block;
}

.tooltip-website .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: #34495e;
    color: #fff;
    text-align: center;
    border-radius: 4px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip-website:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Component badges for dynamic/static components */
.badge {
    display: inline-block;
    padding: 0.25em 0.5em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
    text-decoration: none;
}

.badge.bg-info {
    color: #fff;
    background-color: #0dcaf0 !important;
}

.badge.bg-primary {
    color: #fff;
    background-color: #0d6efd !important;
}

.badge.bg-success {
    color: #fff;
    background-color: #198754 !important;
}

.badge.bg-warning {
    color: #000;
    background-color: #ffc107 !important;
}

.badge.bg-dark {
    color: #fff;
    background-color: #212529 !important;
}

.badge.bg-light {
    color: #000;
    background-color: #f8f9fa !important;
}

.badge.bg-secondary {
    color: #fff;
    background-color: #6c757d !important;
}

/* Spacing for badges */
.badge.me-1 {
    margin-right: 0.25rem !important;
}

/* Component list styling */
.list-group-item {
    position: relative;
    display: block;
    padding: 0.5rem 1rem;
    color: #212529;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.list-group-item:first-child {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
}

.list-group-item:last-child {
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

.list-group-item + .list-group-item {
    border-top-width: 0;
}

.list-group-flush .list-group-item {
    border-right-width: 0;
    border-left-width: 0;
    border-radius: 0;
}

.list-group-flush .list-group-item:first-child {
    border-top-width: 0;
}

.list-group-flush:last-child .list-group-item:last-child {
    border-bottom-width: 0;
}

/* Flexbox utilities */
.d-flex {
    display: flex !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.align-items-center {
    align-items: center !important;
}

/* Text utilities */
.fw-bold {
    font-weight: 700 !important;
}

.text-muted {
    color: #6c757d !important;
}

.text-success {
    color: #198754 !important;
}

.small {
    font-size: 0.875em;
}

/* Spacing utilities */
.ms-2 {
    margin-left: 0.5rem !important;
}

.rounded-pill {
    border-radius: 50rem !important;
}

/* Alert styling for component summary */
.alert {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.25rem;
}

.alert-info {
    color: #055160;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.py-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
}

.mb-2 {
    margin-bottom: 0.5rem !important;
}
