/* Visual Matrix Editor Styles - Odoo 18 Compatible */

.visual-matrix-container {
    max-width: 100%;
    overflow-x: auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin: 16px 0;
}

.matrix-table {
    width: 100%;
    border-collapse: collapse;
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-size: 13px;
    background: white;
    table-layout: fixed;
}

.matrix-table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 2;
    min-width: 80px;
    font-size: 12px;
}

.matrix-table th:first-child {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: sticky;
    left: 0;
    z-index: 3;
    font-weight: 700;
    color: #495057;
    border-right: 2px solid #adb5bd;
}

.matrix-table .row-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-right: 2px solid #adb5bd;
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    color: #495057;
    position: sticky;
    left: 0;
    z-index: 1;
    min-width: 80px;
}

.matrix-cell {
    border: 1px solid #d1d5db;
    padding: 0;
    text-align: center;
    position: relative;
    cursor: pointer;
    transition: all 0.15s ease;
    min-width: 80px;
    height: 40px;
    vertical-align: middle;
    background: #ffffff;
}

.matrix-cell:hover {
    background-color: #f8fafc !important;
    border-color: #3b82f6;
    box-shadow: inset 0 0 0 1px #3b82f6;
    z-index: 1;
}

.matrix-cell.selected {
    background-color: #dbeafe !important;
    border: 2px solid #2563eb !important;
    box-shadow: 0 0 0 1px #2563eb;
    z-index: 2;
    outline: none;
}

.matrix-cell.has-value {
    background-color: #f9fafb;
    color: #374151;
    font-weight: 500;
    border-color: #9ca3af;
}

.matrix-cell.has-value:hover {
    background-color: #f3f4f6 !important;
    border-color: #6b7280;
}

.matrix-input {
    width: 100% !important;
    height: 100% !important;
    border: none !important;
    outline: none !important;
    text-align: center !important;
    background: #ffffff !important;
    font-weight: 500 !important;
    color: #1f2937 !important;
    font-size: 13px !important;
    padding: 8px !important;
    box-shadow: inset 0 0 0 2px #059669 !important;
}

.matrix-cell-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    padding: 4px 8px;
    font-size: 13px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 400;
}

/* Excel-like alternating row colors */
.matrix-table tbody tr:nth-child(even) .matrix-cell {
    background-color: #fafbfc;
}

.matrix-table tbody tr:nth-child(even) .matrix-cell.has-value {
    background-color: #f8f9fa;
}

/* Professional number formatting */
.matrix-cell-content.currency::before {
    content: '$';
    color: #6b7280;
    margin-right: 2px;
}

.matrix-cell-content.percentage::after {
    content: '%';
    color: #6b7280;
    margin-left: 2px;
}

/* Better focus and active states */
.matrix-cell:focus-within {
    border-color: #2563eb;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
    z-index: 3;
}

/* Excel-like grid lines */
.matrix-table {
    border: 2px solid #9ca3af;
}

.matrix-table th,
.matrix-table td {
    border-right: 1px solid #d1d5db;
    border-bottom: 1px solid #d1d5db;
}

.matrix-table th:last-child,
.matrix-table td:last-child {
    border-right: 2px solid #9ca3af;
}

.matrix-table tbody tr:last-child td {
    border-bottom: 2px solid #9ca3af;
}

.matrix-toolbar {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    padding: 12px;
    background: transparent;
    border-radius: 6px;
    flex-wrap: wrap;
}

.matrix-toolbar .btn {
    padding: 6px 12px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
    font-weight: 500;
}

.matrix-toolbar .btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.matrix-toolbar .btn-primary {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.matrix-toolbar .btn-primary:hover {
    background: #0056b3;
    border-color: #0056b3;
}

.matrix-toolbar .btn-success {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.matrix-toolbar .btn-success:hover {
    background: #1e7e34;
    border-color: #1e7e34;
}

.matrix-toolbar .btn-warning {
    background: #ffc107;
    color: #212529;
    border-color: #ffc107;
}

.matrix-toolbar .btn-warning:hover {
    background: #e0a800;
    border-color: #e0a800;
}

.matrix-stats {
    display: flex;
    gap: 16px;
    margin: 16px 0;
    padding: 12px;
    background: transparent;
    border-radius: 6px;
    font-size: 13px;
    color: #6c757d;
}

.matrix-stats .stat {
    display: flex;
    align-items: center;
    gap: 4px;
}

.matrix-stats .stat-value {
    font-weight: 600;
    color: #495057;
}

.matrix-instructions {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 12px;
    margin: 16px 0;
    font-size: 13px;
    color: #0c5aa6;
}

.matrix-instructions h4 {
    margin: 0 0 8px 0;
    color: #0c5aa6;
    font-size: 14px;
    font-weight: 600;
}

.matrix-instructions ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
}

.matrix-instructions li {
    margin: 4px 0;
}

/* Responsive design */
@media (max-width: 768px) {
    .matrix-table {
        font-size: 11px;
    }

    .matrix-cell {
        min-width: 60px;
        height: 36px;
    }

    .matrix-toolbar {
        flex-direction: column;
    }

    .matrix-toolbar .btn {
        width: 100%;
    }
}

/* Animation for value changes */
@keyframes cellUpdate {
    0% { background-color: #fff3cd; }
    100% { background-color: inherit; }
}

.matrix-cell.updated {
    animation: cellUpdate 0.5s ease;
}

/* Focus states for accessibility */
.matrix-cell:focus {
    outline: 2px solid #007bff;
    outline-offset: -2px;
}

/* Loading state */
.matrix-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6c757d;
    font-style: italic;
}

/* Empty state */
.matrix-empty {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.matrix-empty h3 {
    color: #495057;
    margin-bottom: 8px;
}
