/* Enhanced Excel-like Matrix Editor Styling */

.excel-matrix-editor {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Toolbar Styling */
.matrix-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 12px 16px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.toolbar-left {
    display: flex;
    gap: 8px;
    align-items: center;
}

.toolbar-right {
    display: flex;
    gap: 12px;
    align-items: center;
    color: #6b7280;
    font-size: 14px;
}

.matrix-stats {
    display: flex;
    align-items: center;
    gap: 16px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 13px;
    color: #4b5563;
}

.stat-value {
    font-weight: 600;
    color: #1f2937;
}

/* Matrix Configuration Panel */
.matrix-config-panel {
    background: white;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.config-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 12px;
}

.config-field {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.config-field label {
    font-size: 12px;
    font-weight: 500;
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.config-field input {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.config-field input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Matrix Grid Container */
.matrix-grid-container {
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    overflow: hidden;
    position: relative;
}

/* Matrix Grid Table */
.matrix-grid {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 13px;
    font-family: 'SF Mono', Monaco, Consolas, monospace;
}

/* Corner Cell */
.corner-cell {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    border: 1px solid #d1d5db;
    border-right-color: #9ca3af;
    border-bottom-color: #9ca3af;
    padding: 8px 12px;
    font-weight: 600;
    font-size: 11px;
    color: #6b7280;
    text-align: center;
    position: sticky;
    top: 0;
    left: 0;
    z-index: 12;
    min-width: 80px;
}

/* Header Cells */
.matrix-grid thead th {
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    border: 1px solid #d1d5db;
    border-right-color: #9ca3af;
    border-bottom-color: #9ca3af;
    padding: 8px 12px;
    font-weight: 600;
    font-size: 12px;
    color: #374151;
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 11;
    min-width: 100px;
    user-select: none;
}

/* Row Headers */
.row-header {
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    border: 1px solid #d1d5db;
    border-right-color: #9ca3af;
    border-bottom-color: #9ca3af;
    padding: 8px 12px;
    font-weight: 600;
    font-size: 12px;
    color: #374151;
    text-align: center;
    position: sticky;
    left: 0;
    z-index: 10;
    min-width: 80px;
    user-select: none;
}

/* Matrix Cells */
.matrix-cell {
    border: 1px solid #e5e7eb;
    padding: 0;
    background: white;
    position: relative;
    min-width: 100px;
    min-height: 32px;
    cursor: cell;
    transition: all 0.15s ease;
}

.matrix-cell:hover {
    background: #f8fafc;
    border-color: #3b82f6;
    box-shadow: inset 0 0 0 1px #3b82f6;
    z-index: 5;
}

.matrix-cell.selected {
    background: #dbeafe;
    border-color: #2563eb;
    box-shadow: inset 0 0 0 2px #2563eb;
    z-index: 6;
}

.matrix-cell.editing {
    background: white;
    border-color: #2563eb;
    box-shadow: inset 0 0 0 2px #2563eb;
    z-index: 7;
}

.matrix-cell.edited {
    background: #f0f9ff;
    border-color: #3b82f6;
}

.matrix-cell.error {
    background: #fef2f2;
    border-color: #dc2626;
    box-shadow: inset 0 0 0 1px #dc2626;
}

/* Cell Content */
.cell-value {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 32px;
    padding: 4px 8px;
    font-size: 13px;
    color: #1f2937;
    font-weight: 500;
}

.cell-value.empty {
    color: #9ca3af;
    font-style: italic;
}

.cell-input {
    width: 100%;
    height: 32px;
    border: none;
    background: transparent;
    padding: 4px 8px;
    font-size: 13px;
    font-family: inherit;
    text-align: center;
    color: #1f2937;
    font-weight: 500;
}

.cell-input:focus {
    outline: none;
}

/* Selection and Range Highlighting */
.matrix-cell.range-selected {
    background: #dbeafe;
    border-color: #3b82f6;
}

.matrix-cell.range-start {
    border-left-color: #1d4ed8;
    border-top-color: #1d4ed8;
}

.matrix-cell.range-end {
    border-right-color: #1d4ed8;
    border-bottom-color: #1d4ed8;
}

/* Context Menu */
.matrix-context-menu {
    position: absolute;
    background: white;
    border-radius: 6px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    min-width: 150px;
    z-index: 1000;
    padding: 4px 0;
}

.context-menu-item {
    padding: 8px 12px;
    font-size: 13px;
    color: #374151;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.15s ease;
}

.context-menu-item:hover {
    background: #f3f4f6;
    color: #1f2937;
}

.context-menu-item.disabled {
    color: #9ca3af;
    cursor: not-allowed;
}

.context-menu-item.disabled:hover {
    background: transparent;
}

.context-menu-separator {
    height: 1px;
    background: #e5e7eb;
    margin: 4px 0;
}

/* Keyboard Shortcuts */
.keyboard-hint {
    position: absolute;
    bottom: 16px;
    right: 16px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 11px;
    z-index: 100;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.excel-matrix-editor:hover .keyboard-hint {
    opacity: 1;
}

/* Loading and States */
.matrix-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e5e7eb;
    border-top-color: #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Progress Indicator */
.matrix-progress {
    width: 100%;
    height: 4px;
    background: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 16px;
}

.matrix-progress-bar {
    height: 100%;
    background: linear-gradient(45deg, #3b82f6, #1d4ed8);
    border-radius: 2px;
    transition: width 0.3s ease;
    position: relative;
}

.matrix-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5));
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Toast Notifications */
.matrix-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #1f2937;
    color: white;
    padding: 12px 16px;
    border-radius: 6px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    z-index: 2000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    font-size: 14px;
    min-width: 250px;
}

.matrix-toast.show {
    transform: translateX(0);
}

.matrix-toast.success {
    background: #10b981;
}

.matrix-toast.error {
    background: #dc2626;
}

.matrix-toast.warning {
    background: #d97706;
}

/* Responsive Design */
@media (max-width: 768px) {
    .excel-matrix-editor {
        padding: 12px;
    }

    .matrix-toolbar {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .toolbar-left,
    .toolbar-right {
        justify-content: center;
    }

    .config-row {
        grid-template-columns: 1fr;
    }

    .matrix-grid {
        font-size: 12px;
    }

    .matrix-cell {
        min-width: 80px;
        min-height: 28px;
    }

    .cell-value,
    .cell-input {
        height: 28px;
        font-size: 12px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .excel-matrix-editor {
        background: #1f2937;
        color: #f9fafb;
    }

    .matrix-toolbar,
    .matrix-config-panel,
    .matrix-grid-container {
        background: #374151;
        border-color: #4b5563;
    }

    .corner-cell,
    .matrix-grid thead th,
    .row-header {
        background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
        color: #d1d5db;
        border-color: #6b7280;
    }

    .matrix-cell {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }

    .matrix-cell:hover {
        background: #4b5563;
        border-color: #60a5fa;
    }

    .matrix-cell.selected {
        background: #1e40af;
        border-color: #3b82f6;
    }

    .config-field input {
        background: #4b5563;
        border-color: #6b7280;
        color: #f9fafb;
    }
}

/* Animation for cell updates */
@keyframes cellUpdate {
    0% { background: #fbbf24; }
    100% { background: inherit; }
}

.matrix-cell.updated {
    animation: cellUpdate 0.5s ease;
}

/* Custom scrollbar for matrix container */
.matrix-grid-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.matrix-grid-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.matrix-grid-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.matrix-grid-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Focus ring for accessibility */
.matrix-cell:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: -2px;
    z-index: 8;
}

/* Print styles */
@media print {
.excel-matrix-editor {
background: white;
box-shadow: none;
padding: 0;
}

.matrix-toolbar {
display: none;
}

.matrix-config-panel {
box-shadow: none;
border: 1px solid #d1d5db;
}

.matrix-grid {
font-size: 10px;
}

.matrix-cell,
.corner-cell,
.matrix-grid thead th,
.row-header {
border-color: #000 !important;
}
}

/* Interactive Matrix Editor Specific Styles */
.interactive-matrix-editor .matrix-toolbar {
    background: #f8f9fa !important; /* Light grey instead of dark */
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px 16px;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.interactive-matrix-editor .toolbar-actions .btn {
    margin-right: 8px;
    border: none;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.interactive-matrix-editor .toolbar-actions .btn-info {
    background-color: #17a2b8 !important; /* Solid teal */
    color: white !important;
}

.interactive-matrix-editor .toolbar-actions .btn-info:hover {
    background-color: #138496 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.interactive-matrix-editor .toolbar-actions .btn-warning {
    background-color: #ffc107 !important; /* Solid yellow */
    color: #212529 !important;
}

.interactive-matrix-editor .toolbar-actions .btn-warning:hover {
    background-color: #e0a800 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.interactive-matrix-editor .matrix-stats {
    color: #495057 !important; /* Darker text for better readability on light background */
}

.interactive-matrix-editor .matrix-stats .badge {
    background-color: #6c757d !important;
    color: white !important;
    font-size: 11px;
    padding: 4px 8px;
    margin-left: 6px;
    border-radius: 12px;
    font-weight: 500;
}

.interactive-matrix-editor .matrix-stats .badge-primary {
    background-color: #007bff !important;
}

.interactive-matrix-editor .matrix-stats .badge-success {
    background-color: #28a745 !important;
}

.interactive-matrix-editor .matrix-stats .badge-info {
    background-color: #17a2b8 !important;
}

/* Price Matrix Form Specific Styles */
.matrix-visual-editor-container .matrix-toolbar {
    background: transparent !important; /* Remove grey background */
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin-bottom: 16px;
}

.matrix-visual-editor-container .toolbar-actions .btn {
    border: 2px solid #6c757d;
    background-color: white;
    color: #6c757d;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.matrix-visual-editor-container .toolbar-actions .btn:hover {
    background-color: #6c757d;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.matrix-visual-editor-container .toolbar-actions .btn i {
    margin-right: 6px;
}
