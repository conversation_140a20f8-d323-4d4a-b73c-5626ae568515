# Dynamic Field Matching for Components - Enhanced User Experience

## Overview

The Dynamic Field Matching feature allows you to automatically select components based on values from other fields in the configuration. This is particularly useful for components like Bug Strip and Jamb Adaptors that should match properties (such as color) of other components like doors or frames.

Instead of creating separate configuration questions for each component's properties, you can set up a dynamic mapping that inherits values from another field in the configuration.

## Enhanced User Interface

The Dynamic Field Match feature now includes several user-friendly enhancements:

### Smart Field Selection
- **Reference Field Dropdown**: Instead of typing technical field names, you can now select from a dropdown of available fields in your template
- **Match Against Options**: Choose which product field to match against (Color, Product Name, Internal Reference, or Product Category)
- **Real-time Preview**: See a preview of which products would match your configuration

### Validation and Testing
- **Test Button**: Test your dynamic field matching with sample data to ensure it works correctly
- **Available Components**: View potential base component names found in your existing products
- **Matching Preview**: See examples of products that would match different field values

## How It Works

1. When creating a component mapping, you can now choose between two mapping types:
   - **Static Product**: The traditional approach where you select a specific product
   - **Dynamic Field Match**: Automatically find a product that matches both a base component name and a value from another field in the configuration

2. For Dynamic Field Match, you specify:
   - **Base Component**: The base name of the component (e.g., "Bug Strip 16mm")
   - **Reference Field**: Select from a dropdown of available fields in your template
   - **Match Against**: Choose which product field to match (Color, Product Name, Internal Reference, or Product Category)

3. During BOM generation, the system will:
   - Get the value from the specified reference field in the configuration
   - Search for a product that matches both the base component name and the reference field value using the selected match criteria
   - Add the matching product to the BOM

## Setup Instructions

### 1. Prepare Your Products

Ensure your products follow a consistent naming convention and have the appropriate fields set:

- Create products for each component and value combination (e.g., "Bug Strip 16mm - Black Custom Matt GN248A")
- Set the matching field on each product to match the exact value used in the configuration (e.g., "Black Custom Matt GN248A")

### 2. Create Dynamic Component Mappings

#### For Field Component Mappings:

1. Go to the field that should include the component
2. Click "Manage Component Mappings"
3. Create a new mapping with:
   - **Mapping Name**: Give it a descriptive name (e.g., "Bug Strip Color Match")
   - **Mapping Type**: Select "Dynamic Field Match"
   - **Base Component**: Enter the base name of your component (e.g., "Bug Strip 16mm")
     - *Tip: The system will show you available base component names from your existing products*
   - **Reference Field**: Select from the dropdown of available fields in your template
   - **Match Against**: Choose which product field to match against (Color, Product Name, Internal Reference, or Product Category)
   - **Quantity Formula**: The quantity calculation (usually "1")
   - **Condition**: Any additional conditions (optional)

4. Use the **Preview & Validation** tab to:
   - See which products would match different field values
   - View available base component names from your products

5. Click **Test Dynamic Match** to verify the mapping works correctly with sample data

#### For Option Component Mappings:

1. Go to the option that should include the component
2. Click "Manage Component Mappings"
3. Follow the same steps as above for field component mappings

### 3. Test Your Configuration

1. Create a test configuration
2. Select a value in the referenced field
3. Generate the BOM
4. Verify that the correct component is included in the BOM

### 4. Advanced Testing

Use the **Test Dynamic Match** button in the component mapping form to:
- Test with sample values from your reference field
- See exactly which products would be selected
- Identify any issues before using in production

## Example: Bug Strip with Frame Color

### Scenario

You want to add a Bug Strip 16mm component that matches the frame color.

### Steps

1. Ensure you have products for each color variant of Bug Strip 16mm
2. In the Bug Strip Size field, create a component mapping:
   - **Mapping Name**: "Bug Strip Color Match"
   - **Mapping Type**: Dynamic Field Match
   - **Base Component**: "Bug Strip 16mm"
   - **Reference Field**: Select "Frame Color" from the dropdown
   - **Match Against**: Color
   - **Quantity Formula**: "1"

3. Use the **Preview & Validation** tab to verify the mapping shows correct products
4. Click **Test Dynamic Match** to test with sample color values

### Result

When a user selects "Black Custom Matt GN248A" as the frame color, the system will automatically find and use the "Bug Strip 16mm - Black Custom Matt GN248A" product in the BOM.

## Example: Matching Other Properties

### Scenario

You want components to match other properties besides color, such as size or material.

### Steps

1. Ensure you have products named consistently with the property value (e.g., "Handle - Aluminum")
2. Create a component mapping:
   - **Mapping Name**: "Handle Material Match"
   - **Mapping Type**: Dynamic Field Match
   - **Base Component**: "Handle"
   - **Reference Field**: Select "Door Material" from the dropdown
   - **Match Against**: Product Name (to match the material in the product name)
   - **Quantity Formula**: "1"

3. Test the mapping using the **Test Dynamic Match** button

### Result

When a user selects "Aluminum" as the door material, the system will automatically find and use the "Handle - Aluminum" product in the BOM.

## Troubleshooting

If the dynamic field matching isn't working as expected:

### Using the Built-in Tools

1. **Use the Test Button**: Click "Test Dynamic Match" in the component mapping form to see exactly what products would be selected
2. **Check the Preview**: Use the "Preview & Validation" tab to see potential matches and available base components
3. **Verify Field Selection**: Ensure you've selected the correct reference field from the dropdown

### Common Issues

1. **Check Product Names**: Ensure your products follow a consistent naming pattern that includes the base component name
2. **Verify Field Values**: Make sure the values in your products exactly match the values in your configuration
3. **Check Match Against Setting**: Verify you've selected the correct product field to match against (Color, Product Name, etc.)
4. **Review Logs**: Check the server logs for warnings about missing product matches

### Advanced Troubleshooting

1. **Available Base Components**: Check the "Available Base Components" section to see what base component names the system found in your products
2. **Matching Preview**: Review the "Matching Preview" to see examples of what products would match different field values
3. **Test with Sample Data**: Use the test functionality to verify your mapping works with actual field values

## Technical Details

The enhanced dynamic field matching uses a flexible domain filter to search for products based on the selected match criteria:

### For Color Matching:
```python
[('name', 'ilike', base_component), ('colour', '=', field_value)]
```

### For Product Name Matching:
```python
[('name', 'ilike', base_component), ('name', 'ilike', field_value)]
```

### For Internal Reference Matching:
```python
[('name', 'ilike', base_component), ('default_code', '=', field_value)]
```

### For Category Matching:
```python
[('name', 'ilike', base_component), ('categ_id', '=', category_id)]
```

This means it will find products where:
- The name contains the base component string (case-insensitive)
- The selected product field matches the value from the reference field in the configuration

If multiple products match, the first one found will be used.

## Other Applications

This feature can be used for many different scenarios beyond color matching:

1. **Material Matching**: Components that should match the material of another part
2. **Size Matching**: Components that should match the size of another part
3. **Style Matching**: Components that should match the style of another part
4. **Finish Matching**: Components that should match the finish of another part

The key is to ensure your products are named consistently and have the appropriate fields set to match the reference field values.
