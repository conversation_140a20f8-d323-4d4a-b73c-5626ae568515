/**
 * Enhanced Configurator Solutions for Odoo 18 Product Configurator
 * 
 * This file addresses the following issues:
 * 1. Enable Enter key for field navigation (like Tab)
 * 2. Dynamic error messages based on pre-conditions
 * 3. Improved calculated field formulas for reusability
 * 4. Required field validation for text fields when conditionally required
 */

// =============================================================================
// 1. SOLUTION: Enhanced Field Navigation with Enter Key Support
// =============================================================================

class FieldNavigationManager {
    constructor() {
        this.currentFieldIndex = 0;
        this.visibleFields = [];
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupNavigation());
        } else {
            this.setupNavigation();
        }
    }

    setupNavigation() {
        console.log('Setting up enhanced field navigation...');
        
        // Setup keyboard event listeners
        document.addEventListener('keydown', (event) => this.handleKeyDown(event));
        
        // Setup field focus tracking
        this.updateVisibleFields();
        
        // Listen for visibility changes
        document.addEventListener('field-visibility-changed', () => {
            this.updateVisibleFields();
        });
        
        // Re-scan when fields are added dynamically
        const observer = new MutationObserver(() => {
            this.updateVisibleFields();
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class']
        });
    }

    updateVisibleFields() {
        // Get all visible config fields in order
        this.visibleFields = Array.from(document.querySelectorAll('.config-field'))
            .filter(field => {
                const container = field.closest('.config-field-container');
                if (!container) return false;
                
                // Check if field is visible (not hidden by conditions)
                const isHidden = container.style.display === 'none' || 
                               container.classList.contains('conditional-field-pending');
                
                return !isHidden && !field.disabled && !field.readOnly;
            })
            .sort((a, b) => {
                // Sort by DOM order and question number
                const aContainer = a.closest('.config-field-container');
                const bContainer = b.closest('.config-field-container');
                
                if (aContainer && bContainer) {
                    const aOrder = parseInt(aContainer.dataset.questionNumber) || 0;
                    const bOrder = parseInt(bContainer.dataset.questionNumber) || 0;
                    
                    if (aOrder !== bOrder) {
                        return aOrder - bOrder;
                    }
                }
                
                // Fallback to DOM position
                const position = a.compareDocumentPosition(b);
                return position & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;
            });
        
        console.log(`Updated visible fields: ${this.visibleFields.length} fields found`);
    }

    handleKeyDown(event) {
        // Only handle Enter key
        if (event.key !== 'Enter') return;
        
        const activeElement = document.activeElement;
        
        // Check if we're in a config field
        if (!activeElement || !activeElement.classList.contains('config-field')) {
            return;
        }
        
        // Don't interfere with textarea Enter behavior
        if (activeElement.tagName.toLowerCase() === 'textarea') {
            return;
        }
        
        // For select fields, let the dropdown behavior work normally
        if (activeElement.tagName.toLowerCase() === 'select' && !event.shiftKey) {
            // Only navigate on Shift+Enter for select fields
            return;
        }
        
        event.preventDefault();
        
        if (event.shiftKey) {
            this.navigateToPrevious();
        } else {
            this.navigateToNext();
        }
    }

    navigateToNext() {
        const currentIndex = this.visibleFields.indexOf(document.activeElement);
        
        if (currentIndex >= 0 && currentIndex < this.visibleFields.length - 1) {
            const nextField = this.visibleFields[currentIndex + 1];
            this.focusField(nextField);
        } else if (this.visibleFields.length > 0) {
            // If at the end or not found, go to first field
            this.focusField(this.visibleFields[0]);
        }
    }

    navigateToPrevious() {
        const currentIndex = this.visibleFields.indexOf(document.activeElement);
        
        if (currentIndex > 0) {
            const prevField = this.visibleFields[currentIndex - 1];
            this.focusField(prevField);
        } else if (this.visibleFields.length > 0) {
            // If at the beginning or not found, go to last field
            this.focusField(this.visibleFields[this.visibleFields.length - 1]);
        }
    }

    focusField(field) {
        if (!field) return;
        
        // Scroll field into view
        field.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center' 
        });
        
        // Focus with a small delay to ensure scrolling completes
        setTimeout(() => {
            field.focus();
            
            // For text/number inputs, select all content
            if (field.type === 'text' || field.type === 'number') {
                field.select();
            }
            
            // Add visual highlight
            this.highlightField(field);
        }, 100);
    }

    highlightField(field) {
        const container = field.closest('.config-field-container');
        if (!container) return;
        
        // Add highlight class
        container.classList.add('field-navigation-highlight');
        
        // Remove highlight after a short time
        setTimeout(() => {
            container.classList.remove('field-navigation-highlight');
        }, 1000);
    }
}

// =============================================================================
// 2. SOLUTION: Dynamic Error Message System
// =============================================================================

class DynamicErrorManager {
    constructor() {
        this.errorTemplates = new Map();
        this.init();
    }

    init() {
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupErrorSystem());
        } else {
            this.setupErrorSystem();
        }
    }

    setupErrorSystem() {
        console.log('Setting up dynamic error message system...');
        
        // Listen for field changes to update error messages
        document.addEventListener('change', (event) => {
            if (event.target.classList.contains('config-field')) {
                setTimeout(() => this.updateErrorMessages(), 100);
            }
        });
        
        // Initial error message setup
        this.updateErrorMessages();
    }

    updateErrorMessages() {
        // Find all fields with dynamic error templates
        document.querySelectorAll('[data-use-dynamic-error="true"]').forEach(container => {
            const errorTemplate = container.dataset.dynamicErrorTemplate;
            const fieldId = container.dataset.fieldId;
            
            if (errorTemplate) {
                this.updateFieldErrorMessage(fieldId, errorTemplate, container);
            }
        });
    }

    updateFieldErrorMessage(fieldId, template, container) {
        try {
            // Get current field values for context
            const context = this.getFieldValuesContext();
            
            // Evaluate the error template
            const errorMessage = this.evaluateErrorTemplate(template, context);
            
            // Update the error message in the DOM
            const errorContainer = container.querySelector('.dynamic-error-text');
            if (errorContainer && errorMessage) {
                errorContainer.textContent = errorMessage;
                
                // Show/hide static error text based on whether we have dynamic content
                const staticErrorText = container.querySelector('.static-error-text');
                if (staticErrorText) {
                    staticErrorText.style.display = errorMessage ? 'none' : 'inline';
                }
            }
            
        } catch (error) {
            console.error(`Error updating dynamic error message for field ${fieldId}:`, error);
        }
    }

    evaluateErrorTemplate(template, context) {
        if (!template) return '';
        
        try {
            // Enhanced template evaluation with better error handling
            let result = template;
            
            // Handle conditional expressions like your example:
            // {_CALCULATED_pre_condition_1 == 'yes' ? "Message 1" : (_CALCULATED_pre_condition_2 == 'yes' ? "Message 2" : "")}
            result = result.replace(/\{([^}]+)\}/g, (match, expression) => {
                try {
                    // Clean and prepare the expression
                    let cleanExpression = this.cleanExpressionForJS(expression);
                    
                    // Create evaluation function with context
                    const func = new Function(...Object.keys(context), `return ${cleanExpression};`);
                    const evalResult = func(...Object.values(context));
                    
                    return String(evalResult || '');
                } catch (e) {
                    console.warn(`Error evaluating error template expression: ${expression}`, e);
                    return match; // Return original if evaluation fails
                }
            });
            
            return result;
        } catch (error) {
            console.error(`Error evaluating error template: ${template}`, error);
            return '';
        }
    }

    getFieldValuesContext() {
        const context = {};
        
        // Get current field values
        if (window.fieldValues) {
            Object.assign(context, window.fieldValues);
        }
        
        // Get calculated fields if available
        if (window.configuratorComponent && typeof window.configuratorComponent.calculateDynamicFields === 'function') {
            try {
                const calculatedFields = window.configuratorComponent.calculateDynamicFields(window.fieldValues || {});
                Object.assign(context, calculatedFields);
            } catch (error) {
                console.warn('Error getting calculated fields for error context:', error);
            }
        }
        
        // Add mathematical functions
        context.Math = Math;
        context.min = Math.min;
        context.max = Math.max;
        context.abs = Math.abs;
        context.round = Math.round;
        
        return context;
    }

    cleanExpressionForJS(expression) {
        // Convert Python-style operators to JavaScript
        return expression
            .replace(/\band\b/g, '&&')
            .replace(/\bor\b/g, '||')
            .replace(/\bnot\b/g, '!')
            .replace(/\bTrue\b/g, 'true')
            .replace(/\bFalse\b/g, 'false')
            .replace(/\bNone\b/g, 'null');
    }
}

// =============================================================================
// 3. SOLUTION: Enhanced Calculated Fields for Reusability
// =============================================================================

class ReusableCalculatedFields {
    constructor() {
        this.fieldMappings = new Map();
        this.templateMappings = new Map();
        this.init();
    }

    init() {
        // Map common field patterns across templates
        this.setupFieldMappings();
        
        // Enhanced calculated field functions
        this.setupCalculatedFieldHelpers();
    }

    setupFieldMappings() {
        // Define common field name patterns that can be mapped across templates
        this.fieldMappings.set('door_type', [
            'door_type', 'door_style', 'type', 'style'
        ]);
        
        this.fieldMappings.set('height', [
            'height', 'door_height', 'opening_height', 'overall_height'
        ]);
        
        this.fieldMappings.set('width', [
            'width', 'door_width', 'opening_width', 'overall_width'
        ]);
        
        this.fieldMappings.set('frame_size', [
            'frame_size', 'frame_width', 'frame_depth'
        ]);
        
        // Add more mappings as needed
    }

    setupCalculatedFieldHelpers() {
        // Enhanced calculated field functions that work across templates
        window.getFieldValue = this.getFieldValue.bind(this);
        window.findFieldByPattern = this.findFieldByPattern.bind(this);
        window.calculateDoorSplitType = this.calculateDoorSplitType.bind(this);
        window.calculateLargestDoorHeight = this.calculateLargestDoorHeight.bind(this);
    }

    getFieldValue(fieldName, context = {}) {
        // First try exact match
        if (context[fieldName] !== undefined) {
            return context[fieldName];
        }
        
        // Try mapped field names
        const mappedFields = this.fieldMappings.get(fieldName) || [];
        for (const mappedName of mappedFields) {
            if (context[mappedName] !== undefined) {
                return context[mappedName];
            }
        }
        
        // Try partial matches (case-insensitive)
        const lowerFieldName = fieldName.toLowerCase();
        for (const [key, value] of Object.entries(context)) {
            if (key.toLowerCase().includes(lowerFieldName) || 
                lowerFieldName.includes(key.toLowerCase())) {
                return value;
            }
        }
        
        return null;
    }

    findFieldByPattern(pattern, context = {}) {
        // Find field by regex pattern
        const regex = new RegExp(pattern, 'i');
        
        for (const [key, value] of Object.entries(context)) {
            if (regex.test(key)) {
                return { key, value };
            }
        }
        
        return null;
    }

    calculateDoorSplitType(context = {}) {
        // Reusable door split type calculation
        const width = this.getFieldValue('width', context) || 
                     this.getFieldValue('door_width', context) || 0;
        
        const height = this.getFieldValue('height', context) || 
                      this.getFieldValue('door_height', context) || 0;
        
        const frameSize = this.getFieldValue('frame_size', context) || 0;
        
        // Generic logic that works across templates
        if (width <= 0 || height <= 0) {
            return 'single'; // Default
        }
        
        // Common business logic for door splitting
        const aspectRatio = width / height;
        const area = width * height;
        
        if (width > 2000 || aspectRatio > 2.5) {
            return 'double';
        } else if (area > 4000000) { // Large area
            return 'double';
        } else {
            return 'single';
        }
    }

    calculateLargestDoorHeight(context = {}) {
        // Reusable largest door height calculation
        const height = this.getFieldValue('height', context) || 0;
        const frameSize = this.getFieldValue('frame_size', context) || 0;
        const splitType = this.calculateDoorSplitType(context);
        
        if (splitType === 'double') {
            // For double doors, each door is roughly half width but full height
            return height - (frameSize * 2); // Account for top and bottom frame
        } else {
            // For single doors, full dimensions minus frame
            return height - (frameSize * 2);
        }
    }

    // Enhanced formula evaluation that handles field mapping
    evaluateReusableFormula(formula, context = {}) {
        try {
            // Replace common field references with mapped values
            let processedFormula = formula;
            
            // Replace field references like {width} with actual values
            processedFormula = processedFormula.replace(/\{([^}]+)\}/g, (match, fieldName) => {
                const value = this.getFieldValue(fieldName, context);
                return value !== null ? value : 0;
            });
            
            // Add helper functions to context
            const enhancedContext = {
                ...context,
                getFieldValue: (name) => this.getFieldValue(name, context),
                calculateDoorSplitType: () => this.calculateDoorSplitType(context),
                calculateLargestDoorHeight: () => this.calculateLargestDoorHeight(context),
                Math: Math,
                min: Math.min,
                max: Math.max,
                abs: Math.abs,
                round: Math.round
            };
            
            // Evaluate the formula
            const func = new Function(...Object.keys(enhancedContext), `return ${processedFormula};`);
            return func(...Object.values(enhancedContext));
            
        } catch (error) {
            console.error(`Error evaluating reusable formula: ${formula}`, error);
            return null;
        }
    }
}

// =============================================================================
// 4. SOLUTION: Enhanced Required Field Validation
// =============================================================================

class RequiredFieldValidator {
    constructor() {
        this.validationRules = new Map();
        this.init();
    }

    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupValidation());
        } else {
            this.setupValidation();
        }
    }

    setupValidation() {
        console.log('Setting up enhanced required field validation...');
        
        // Listen for field changes to update validation
        document.addEventListener('change', (event) => {
            if (event.target.classList.contains('config-field')) {
                setTimeout(() => this.validateAllFields(), 100);
            }
        });
        
        // Listen for form submission
        document.addEventListener('submit', (event) => {
            if (!this.validateAllFields()) {
                event.preventDefault();
                this.showValidationSummary();
            }
        });
        
        // Setup Q7 specific validation (as mentioned in your requirements)
        this.setupConditionalRequiredFields();
    }

    setupConditionalRequiredFields() {
        // Setup validation for Q7 when Q6 is "No"
        const q6Field = this.findFieldByQuestionNumber('Q6');
        
        if (q6Field) {
            q6Field.addEventListener('change', () => {
                setTimeout(() => this.validateQ7BasedOnQ6(), 100);
            });
        }
    }

    validateQ7BasedOnQ6() {
        const q6Field = this.findFieldByQuestionNumber('Q6');
        const q7Field = this.findFieldByQuestionNumber('Q7');
        
        if (!q6Field || !q7Field) return;
        
        const q6Value = this.getFieldValue(q6Field);
        const q7Value = this.getFieldValue(q7Field);
        
        // If Q6 is "No", then Q7 becomes required
        const isQ6No = q6Value === 'no' || q6Value === 'No' || q6Value === false;
        
        if (isQ6No) {
            this.setFieldRequired(q7Field, true);
            
            // Validate Q7 if it's empty
            if (!q7Value || q7Value.trim() === '') {
                this.showFieldValidationError(q7Field, 'This field is required. Please enter a value.');
            } else {
                this.clearFieldValidationError(q7Field);
            }
        } else {
            this.setFieldRequired(q7Field, false);
            this.clearFieldValidationError(q7Field);
        }
    }

    findFieldByQuestionNumber(questionNumber) {
        // Clean up question number (remove Q prefix if present)
        const cleanNumber = questionNumber.replace(/^Q/i, '');
        
        // Try different selectors
        const selectors = [
            `[data-question-number="${questionNumber}"]`,
            `[data-question-number="${cleanNumber}"]`,
            `.config-field-container:has(.question-number:contains("${questionNumber}"))`,
        ];
        
        for (const selector of selectors) {
            try {
                const container = document.querySelector(selector);
                if (container) {
                    return container.querySelector('.config-field');
                }
            } catch (e) {
                // Ignore selector errors
            }
        }
        
        // Fallback: find by text content
        const questionElements = document.querySelectorAll('.question-number');
        for (const element of questionElements) {
            if (element.textContent.trim() === questionNumber || 
                element.textContent.trim() === cleanNumber) {
                const container = element.closest('.config-field-container');
                if (container) {
                    return container.querySelector('.config-field');
                }
            }
        }
        
        return null;
    }

    getFieldValue(field) {
        if (!field) return null;
        
        const fieldType = field.getAttribute('data-field-type') || field.type;
        
        switch (fieldType) {
            case 'boolean':
            case 'checkbox':
                return field.checked;
            case 'selection':
            case 'select':
                return field.value;
            case 'number':
                const numValue = parseFloat(field.value);
                return isNaN(numValue) ? null : numValue;
            default:
                return field.value || null;
        }
    }

    setFieldRequired(field, required) {
        if (!field) return;
        
        const container = field.closest('.config-field-container');
        if (!container) return;
        
        // Add/remove required indicator
        const label = container.querySelector('label');
        if (label) {
            const requiredIndicator = label.querySelector('.required-indicator');
            
            if (required && !requiredIndicator) {
                const indicator = document.createElement('span');
                indicator.className = 'required-indicator text-danger';
                indicator.innerHTML = ' *';
                label.appendChild(indicator);
            } else if (!required && requiredIndicator) {
                requiredIndicator.remove();
            }
        }
        
        // Set HTML5 required attribute for better UX
        if (required) {
            field.setAttribute('required', 'required');
        } else {
            field.removeAttribute('required');
        }
    }

    showFieldValidationError(field, message) {
        if (!field) return;
        
        const container = field.closest('.config-field-container');
        if (!container) return;
        
        // Find or create validation message container
        let validationMsg = container.querySelector('.validation-message');
        if (!validationMsg) {
            validationMsg = document.createElement('div');
            validationMsg.className = 'validation-message mt-2 alert alert-danger py-2 px-3';
            validationMsg.style.cssText = 'font-size: 14px; font-weight: 500; border-left: 4px solid #dc3545;';
            
            // Insert after the field
            const fieldParent = field.parentNode;
            fieldParent.appendChild(validationMsg);
        }
        
        validationMsg.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${message}`;
        validationMsg.style.display = 'block';
        
        // Add error styling to field
        field.classList.add('is-invalid');
    }

    clearFieldValidationError(field) {
        if (!field) return;
        
        const container = field.closest('.config-field-container');
        if (!container) return;
        
        const validationMsg = container.querySelector('.validation-message');
        if (validationMsg) {
            validationMsg.style.display = 'none';
        }
        
        // Remove error styling
        field.classList.remove('is-invalid');
    }

    validateAllFields() {
        let isValid = true;
        const errors = [];
        
        // Find all config fields
        document.querySelectorAll('.config-field').forEach(field => {
            const isRequired = field.hasAttribute('required') || this.isFieldConditionallyRequired(field);
            const isEmpty = this.isFieldEmpty(field);
            
            if (isRequired && isEmpty) {
                isValid = false;
                const fieldName = this.getFieldName(field);
                const errorMsg = `${fieldName} is required. Please enter a value.`;
                
                errors.push(errorMsg);
                this.showFieldValidationError(field, errorMsg);
            } else {
                this.clearFieldValidationError(field);
            }
        });
        
        return isValid;
    }

    isFieldConditionallyRequired(field) {
        // Check if field is conditionally required (like Q7 based on Q6)
        const container = field.closest('.config-field-container');
        if (!container) return false;
        
        const questionNumber = this.getQuestionNumber(container);
        
        // Special case for Q7
        if (questionNumber === 'Q7' || questionNumber === '7') {
            const q6Field = this.findFieldByQuestionNumber('Q6');
            if (q6Field) {
                const q6Value = this.getFieldValue(q6Field);
                return q6Value === 'no' || q6Value === 'No' || q6Value === false;
            }
        }
        
        return false;
    }

    isFieldEmpty(field) {
        const value = this.getFieldValue(field);
        
        if (value === null || value === undefined) return true;
        
        if (typeof value === 'string') {
            return value.trim() === '';
        }
        
        if (typeof value === 'number') {
            return isNaN(value);
        }
        
        return false;
    }

    getFieldName(field) {
        const container = field.closest('.config-field-container');
        if (!container) return 'Field';
        
        const label = container.querySelector('label');
        if (label) {
            return label.textContent.trim().replace(' *', '');
        }
        
        return field.name || field.id || 'Field';
    }

    getQuestionNumber(container) {
        const questionElement = container.querySelector('.question-number');
        if (questionElement) {
            return questionElement.textContent.trim();
        }
        
        const questionAttr = container.getAttribute('data-question-number');
        if (questionAttr) {
            return questionAttr;
        }
        
        return null;
    }

    showValidationSummary() {
        // Show a summary of validation errors
        const summaryContainer = document.getElementById('validation-summary');
        if (summaryContainer) {
            summaryContainer.style.display = 'block';
            summaryContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
        
        // Also show a popup notification
        this.showNotification('Please fix the validation errors before saving.', 'error');
    }

    showNotification(message, type = 'info') {
        // Create a temporary notification
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : 'info'} position-fixed`;
        notification.style.cssText = `
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 9999;
            padding: 15px 25px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            border-radius: 5px;
            max-width: 80%;
        `;
        
        notification.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${message}`;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// =============================================================================
// INITIALIZATION AND CSS STYLES
// =============================================================================

// Initialize all systems when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing enhanced configurator solutions...');
    
    // Initialize all systems
    window.fieldNavigationManager = new FieldNavigationManager();
    window.dynamicErrorManager = new DynamicErrorManager();
    window.reusableCalculatedFields = new ReusableCalculatedFields();
    window.requiredFieldValidator = new RequiredFieldValidator();
    
    // Add CSS styles for enhanced functionality
    addEnhancedStyles();
    
    console.log('Enhanced configurator solutions initialized successfully!');
});

// =============================================================================
// CSS STYLES FOR ENHANCED FUNCTIONALITY
// =============================================================================

function addEnhancedStyles() {
    const style = document.createElement('style');
    style.textContent = `
        /* Field Navigation Highlight */
        .field-navigation-highlight {
            background-color: rgba(100, 92, 179, 0.1) !important;
            border: 2px solid #645CB3 !important;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .field-navigation-highlight .config-field {
            border-color: #645CB3 !important;
            box-shadow: 0 0 0 0.2rem rgba(100, 92, 179, 0.25) !important;
        }
        
        /* Required Field Indicator */
        .required-indicator {
            color: #dc3545 !important;
            font-weight: bold;
        }
        
        /* Enhanced Validation Messages */
        .validation-message {
            animation: slideInDown 0.3s ease-in-out;
            margin-bottom: 10px !important;
            font-size: 14px !important;
            border-left: 4px solid #dc3545 !important;
        }
        
        .validation-message.success {
            border-left-color: #28a745 !important;
            background-color: #d4edda !important;
            color: #155724 !important;
        }
        
        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Invalid Field Styling */
        .config-field.is-invalid {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        }
        
        /* Enhanced Error Messages */
        .dynamic-error-text {
            font-weight: 500;
            color: #721c24;
        }
        
        /* Field Focus Enhancement */
        .config-field:focus {
            border-color: #645CB3 !important;
            box-shadow: 0 0 0 0.2rem rgba(100, 92, 179, 0.25) !important;
            outline: none !important;
        }
        
        /* Loading states for dynamic content */
        .calculating {
            opacity: 0.6;
            pointer-events: none;
        }
        
        .calculating::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid #ccc;
            border-top: 2px solid #645CB3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }
        
        /* Enhanced tooltips for calculated fields */
        .calculated-field-tooltip {
            position: relative;
            cursor: help;
        }
        
        .calculated-field-tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
            z-index: 1000;
        }
        
        .calculated-field-tooltip:hover::after {
            opacity: 1;
        }
    `;
    
    document.head.appendChild(style);
}

// =============================================================================
// ENHANCED HELPER FUNCTIONS FOR TEMPLATE INTEGRATION
// =============================================================================

/**
 * Helper function to integrate with existing template visibility system
 */
function integrateWithTemplateSystem() {
    // Override existing field change handlers to include our enhancements
    if (window.handleFieldChange) {
        const originalHandler = window.handleFieldChange;
        window.handleFieldChange = function(fieldElement) {
            // Call original handler
            originalHandler.call(this, fieldElement);
            
            // Trigger our enhancements
            if (window.dynamicErrorManager) {
                window.dynamicErrorManager.updateErrorMessages();
            }
            
            if (window.requiredFieldValidator) {
                window.requiredFieldValidator.validateAllFields();
            }
            
            if (window.fieldNavigationManager) {
                window.fieldNavigationManager.updateVisibleFields();
            }
        };
    }
    
    // Integrate with configurator component if available
    if (window.configuratorComponent) {
        const component = window.configuratorComponent;
        
        // Override onFieldChange to include our validations
        if (component.onFieldChange) {
            const originalOnFieldChange = component.onFieldChange.bind(component);
            component.onFieldChange = function(field, value) {
                // Call original method
                originalOnFieldChange(field, value);
                
                // Trigger our enhancements
                setTimeout(() => {
                    if (window.dynamicErrorManager) {
                        window.dynamicErrorManager.updateErrorMessages();
                    }
                    
                    if (window.requiredFieldValidator) {
                        window.requiredFieldValidator.validateAllFields();
                    }
                }, 100);
            };
        }
        
        // Enhance calculated fields function
        if (component.calculateDynamicFields) {
            const originalCalculate = component.calculateDynamicFields.bind(component);
            component.calculateDynamicFields = function(fieldValues) {
                // Call original calculation
                const originalResults = originalCalculate(fieldValues);
                
                // Add enhanced calculated fields
                if (window.reusableCalculatedFields) {
                    const enhancedResults = { ...originalResults };
                    
                    // Add reusable calculated fields
                    try {
                        enhancedResults._CALCULATED_door_split_type = 
                            window.reusableCalculatedFields.calculateDoorSplitType(fieldValues);
                        
                        enhancedResults._CALCULATED_largest_door_height = 
                            window.reusableCalculatedFields.calculateLargestDoorHeight(fieldValues);
                        
                        // Add pre-conditions for dynamic error messages
                        enhancedResults._CALCULATED_pre_condition_1_small_pet_door_custom_height_location_mm = 
                            window.reusableCalculatedFields.getFieldValue('frame_size', fieldValues) < 50 ? 'yes' : 'no';
                        
                        enhancedResults._CALCULATED_pre_condition_2_small_pet_door_custom_height_location_mm = 
                            window.reusableCalculatedFields.getFieldValue('frame_size', fieldValues) >= 50 ? 'yes' : 'no';
                        
                    } catch (error) {
                        console.warn('Error calculating enhanced fields:', error);
                    }
                    
                    return enhancedResults;
                }
                
                return originalResults;
            };
        }
    }
}

// =============================================================================
// TEMPLATE-SPECIFIC SOLUTIONS
// =============================================================================

/**
 * Solution for Dynamic Error Messages (Issue #2)
 * 
 * This addresses your specific example:
 * {_CALCULATED_pre_condition_1_small_pet_door_custom_height_location_mm == 'yes' ? 
 *  "*Small Pet Door Height Location must be greater than 200mm and less than Height - 210mm" : 
 *  (_CALCULATED_pre_condition_2_small_pet_door_custom_height_location_mm == 'yes' ? 
 *   "*Small Pet Door Height Location must be greater than 232mm and less than Height - 242mm" : "")}
 */
class SmallPetDoorValidation {
    constructor() {
        this.setupValidation();
    }
    
    setupValidation() {
        // Wait for DOM and then setup the specific validation for Q54
        setTimeout(() => {
            this.setupQ54Validation();
        }, 1000);
    }
    
    setupQ54Validation() {
        // Find Q54 field (Small Pet Door Custom Height Location)
        const q54Field = this.findFieldByName('small_pet_door_custom_height_location_mm') ||
                         this.findFieldByQuestionNumber('Q54');
        
        if (!q54Field) {
            console.warn('Q54 field not found for small pet door validation');
            return;
        }
        
        // Setup dynamic validation
        const container = q54Field.closest('.config-field-container');
        if (container) {
            // Add dynamic error template data
            container.setAttribute('data-use-dynamic-error', 'true');
            container.setAttribute('data-dynamic-error-template', 
                `{_CALCULATED_pre_condition_1_small_pet_door_custom_height_location_mm == 'yes' ? 
                 "*Small Pet Door Height Location must be greater than 200mm and less than Height - 210mm" : 
                 (_CALCULATED_pre_condition_2_small_pet_door_custom_height_location_mm == 'yes' ? 
                  "*Small Pet Door Height Location must be greater than 232mm and less than Height - 242mm" : "")}`
            );
            
            // Add error condition for when to show the error
            container.setAttribute('data-error-condition', 
                `(_CALCULATED_pre_condition_1_small_pet_door_custom_height_location_mm == 'yes' && 
                  (small_pet_door_custom_height_location_mm <= 200 || 
                   small_pet_door_custom_height_location_mm >= (height - 210))) || 
                 (_CALCULATED_pre_condition_2_small_pet_door_custom_height_location_mm == 'yes' && 
                  (small_pet_door_custom_height_location_mm <= 232 || 
                   small_pet_door_custom_height_location_mm >= (height - 242)))`
            );
        }
        
        // Listen for changes to trigger validation
        q54Field.addEventListener('input', () => {
            setTimeout(() => this.validateQ54(), 100);
        });
        
        // Listen for changes to dependent fields
        document.addEventListener('change', (event) => {
            if (event.target.classList.contains('config-field')) {
                setTimeout(() => this.validateQ54(), 100);
            }
        });
    }
    
    validateQ54() {
        const q54Field = this.findFieldByName('small_pet_door_custom_height_location_mm') ||
                         this.findFieldByQuestionNumber('Q54');
        
        if (!q54Field) return;
        
        // Get current values
        const context = this.getValidationContext();
        const q54Value = parseFloat(q54Field.value) || 0;
        
        // Calculate pre-conditions
        const frameSize = context.frame_size || 0;
        const height = context.height || 0;
        
        const preCondition1 = frameSize < 50 ? 'yes' : 'no';
        const preCondition2 = frameSize >= 50 ? 'yes' : 'no';
        
        let errorMessage = '';
        let isValid = true;
        
        if (preCondition1 === 'yes') {
            if (q54Value <= 200 || q54Value >= (height - 210)) {
                errorMessage = 'Small Pet Door Height Location must be greater than 200mm and less than ' + (height - 210) + 'mm';
                isValid = false;
            }
        } else if (preCondition2 === 'yes') {
            if (q54Value <= 232 || q54Value >= (height - 242)) {
                errorMessage = 'Small Pet Door Height Location must be greater than 232mm and less than ' + (height - 242) + 'mm';
                isValid = false;
            }
        }
        
        // Show/hide error message
        if (!isValid && q54Value > 0) {
            this.showFieldError(q54Field, errorMessage);
        } else {
            this.clearFieldError(q54Field);
        }
    }
    
    getValidationContext() {
        const context = {};
        
        // Get field values from window.fieldValues
        if (window.fieldValues) {
            Object.assign(context, window.fieldValues);
        }
        
        // Get calculated fields
        if (window.configuratorComponent && window.configuratorComponent.calculateDynamicFields) {
            try {
                const calculatedFields = window.configuratorComponent.calculateDynamicFields(window.fieldValues || {});
                Object.assign(context, calculatedFields);
            } catch (error) {
                console.warn('Error getting calculated fields for validation:', error);
            }
        }
        
        return context;
    }
    
    findFieldByName(technicalName) {
        return document.querySelector(`[data-technical-name="${technicalName}"]`)?.querySelector('.config-field');
    }
    
    findFieldByQuestionNumber(questionNumber) {
        const cleanNumber = questionNumber.replace(/^Q/i, '');
        const questionElement = Array.from(document.querySelectorAll('.question-number'))
            .find(el => el.textContent.trim() === questionNumber || el.textContent.trim() === cleanNumber);
        
        if (questionElement) {
            const container = questionElement.closest('.config-field-container');
            return container?.querySelector('.config-field');
        }
        
        return null;
    }
    
    showFieldError(field, message) {
        if (window.requiredFieldValidator) {
            window.requiredFieldValidator.showFieldValidationError(field, message);
        }
    }
    
    clearFieldError(field) {
        if (window.requiredFieldValidator) {
            window.requiredFieldValidator.clearFieldValidationError(field);
        }
    }
}

/**
 * Enhanced Calculated Fields for Template Reusability (Issue #3)
 * 
 * This provides a more flexible approach to calculated fields that can work
 * across multiple templates without relying on specific technical names.
 */
class TemplateAgnosticCalculatedFields {
    constructor() {
        this.formulaTemplates = new Map();
        this.setupFormulas();
    }
    
    setupFormulas() {
        // Define reusable formula templates
        this.formulaTemplates.set('door_split_type', {
            formula: `
                const width = getFieldValue('width') || getFieldValue('door_width') || getFieldValue('opening_width') || 0;
                const height = getFieldValue('height') || getFieldValue('door_height') || getFieldValue('opening_height') || 0;
                
                if (width <= 0) return 'single';
                
                const aspectRatio = width / height;
                if (width > 2000 || aspectRatio > 2.5) {
                    return 'double';
                } else {
                    return 'single';
                }
            `,
            description: 'Determines door split type based on dimensions'
        });
        
        this.formulaTemplates.set('largest_door_height', {
            formula: `
                const height = getFieldValue('height') || getFieldValue('door_height') || getFieldValue('opening_height') || 0;
                const frameSize = getFieldValue('frame_size') || getFieldValue('frame_width') || getFieldValue('frame_depth') || 50;
                const splitType = this.calculateFormula('door_split_type', context);
                
                if (splitType === 'double') {
                    return height - (frameSize * 2);
                } else {
                    return height - (frameSize * 2);
                }
            `,
            description: 'Calculates the largest door height considering frame and split type'
        });
        
        this.formulaTemplates.set('effective_opening_area', {
            formula: `
                const width = getFieldValue('width') || 0;
                const height = getFieldValue('height') || 0;
                const frameSize = getFieldValue('frame_size') || 50;
                
                const effectiveWidth = Math.max(0, width - (frameSize * 2));
                const effectiveHeight = Math.max(0, height - (frameSize * 2));
                
                return effectiveWidth * effectiveHeight;
            `,
            description: 'Calculates effective opening area minus frame'
        });
        
        this.formulaTemplates.set('pet_door_clearance', {
            formula: `
                const petDoorHeight = getFieldValue('pet_door_height') || getFieldValue('small_pet_door_height') || 0;
                const overallHeight = getFieldValue('height') || getFieldValue('door_height') || 0;
                const frameSize = getFieldValue('frame_size') || 50;
                
                return Math.max(0, overallHeight - petDoorHeight - (frameSize * 2));
            `,
            description: 'Calculates clearance around pet door'
        });
    }
    
    calculateFormula(formulaName, context = {}) {
        const template = this.formulaTemplates.get(formulaName);
        if (!template) {
            console.warn(`Formula template '${formulaName}' not found`);
            return null;
        }
        
        try {
            // Create enhanced context with helper functions
            const enhancedContext = {
                ...context,
                getFieldValue: (name) => this.getFieldValueByPattern(name, context),
                Math: Math,
                min: Math.min,
                max: Math.max,
                abs: Math.abs,
                round: Math.round,
                calculateFormula: (name, ctx) => this.calculateFormula(name, ctx || context)
            };
            
            // Evaluate the formula
            const func = new Function('context', `
                const { ${Object.keys(enhancedContext).join(', ')} } = context;
                ${template.formula}
            `);
            
            return func(enhancedContext);
            
        } catch (error) {
            console.error(`Error calculating formula '${formulaName}':`, error);
            return null;
        }
    }
    
    getFieldValueByPattern(pattern, context = {}) {
        // Enhanced field value retrieval with pattern matching
        const lowerPattern = pattern.toLowerCase();
        
        // Try exact match first
        if (context[pattern] !== undefined) {
            return context[pattern];
        }
        
        // Try common variations
        const variations = [
            pattern,
            pattern.toLowerCase(),
            pattern.toUpperCase(),
            pattern.replace(/_/g, ''),
            pattern.replace(/-/g, '_'),
            pattern.replace(/\s+/g, '_')
        ];
        
        for (const variation of variations) {
            if (context[variation] !== undefined) {
                return context[variation];
            }
        }
        
        // Try pattern matching for common field types
        const patterns = {
            width: ['width', 'door_width', 'opening_width', 'overall_width', 'w'],
            height: ['height', 'door_height', 'opening_height', 'overall_height', 'h'],
            frame_size: ['frame_size', 'frame_width', 'frame_depth', 'frame'],
            color: ['color', 'colour', 'finish', 'paint'],
            material: ['material', 'substrate', 'core']
        };
        
        for (const [key, patternList] of Object.entries(patterns)) {
            if (patternList.some(p => lowerPattern.includes(p.toLowerCase()))) {
                // Find matching field in context
                for (const [contextKey, value] of Object.entries(context)) {
                    if (patternList.some(p => contextKey.toLowerCase().includes(p.toLowerCase()))) {
                        return value;
                    }
                }
            }
        }
        
        // Fallback: try partial string matching
        for (const [key, value] of Object.entries(context)) {
            if (key.toLowerCase().includes(lowerPattern) || 
                lowerPattern.includes(key.toLowerCase())) {
                return value;
            }
        }
        
        return null;
    }
    
    // Method to get all available formulas (for debugging/UI)
    getAvailableFormulas() {
        return Array.from(this.formulaTemplates.keys()).map(name => ({
            name,
            description: this.formulaTemplates.get(name).description
        }));
    }
}

// Initialize template-specific solutions
document.addEventListener('DOMContentLoaded', function() {
    // Initialize after a short delay to ensure other systems are ready
    setTimeout(() => {
        window.smallPetDoorValidation = new SmallPetDoorValidation();
        window.templateAgnosticCalculatedFields = new TemplateAgnosticCalculatedFields();
        
        // Integrate with existing template system
        integrateWithTemplateSystem();
        
        console.log('Template-specific solutions initialized');
    }, 1500);
});

// =============================================================================
// KEYBOARD SHORTCUTS AND ACCESSIBILITY ENHANCEMENTS
// =============================================================================

/**
 * Additional keyboard shortcuts for better user experience
 */
class KeyboardShortcuts {
    constructor() {
        this.setupShortcuts();
    }
    
    setupShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl/Cmd + S for save
            if ((event.ctrlKey || event.metaKey) && event.key === 's') {
                event.preventDefault();
                this.triggerSave();
            }
            
            // Ctrl/Cmd + Enter for save from any field
            if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
                event.preventDefault();
                this.triggerSave();
            }
            
            // F1 for help/issues panel
            if (event.key === 'F1') {
                event.preventDefault();
                this.toggleIssuesPanel();
            }
            
            // Escape to clear current field
            if (event.key === 'Escape' && document.activeElement.classList.contains('config-field')) {
                this.clearCurrentField();
            }
        });
    }
    
    triggerSave() {
        const saveButton = document.getElementById('save-config-btn') || 
                          document.getElementById('floating-save-btn');
        
        if (saveButton) {
            saveButton.click();
        } else {
            // Try to submit the form
            const form = document.getElementById('config-form');
            if (form) {
                form.submit();
            }
        }
    }
    
    toggleIssuesPanel() {
        const showButton = document.getElementById('show-issues-panel');
        const panel = document.getElementById('issues-panel');
        
        if (panel && panel.style.display === 'block') {
            panel.style.display = 'none';
        } else if (showButton) {
            showButton.click();
        }
    }
    
    clearCurrentField() {
        const activeField = document.activeElement;
        if (activeField && activeField.classList.contains('config-field')) {
            const fieldType = activeField.getAttribute('data-field-type');
            
            if (fieldType === 'boolean') {
                activeField.checked = false;
            } else {
                activeField.value = '';
            }
            
            // Trigger change event
            activeField.dispatchEvent(new Event('change', { bubbles: true }));
        }
    }
}

// Initialize keyboard shortcuts
document.addEventListener('DOMContentLoaded', function() {
    window.keyboardShortcuts = new KeyboardShortcuts();
});

// =============================================================================
// EXPORT FUNCTIONS FOR TEMPLATE INTEGRATION
// =============================================================================

// Export all functionality for use in template files
window.EnhancedConfiguratorSolutions = {
    FieldNavigationManager,
    DynamicErrorManager,
    ReusableCalculatedFields,
    RequiredFieldValidator,
    SmallPetDoorValidation,
    TemplateAgnosticCalculatedFields,
    KeyboardShortcuts
};

// Export helper functions
window.ConfiguratorHelpers = {
    integrateWithTemplateSystem,
    addEnhancedStyles
};