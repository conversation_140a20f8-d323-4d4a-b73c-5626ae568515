# ConfigMatrix Performance Testing Guide

## Quick Start Testing

After implementing the performance optimizations, you can test the improvements using the browser console.

### 1. Open the ConfigMatrix Portal
Navigate to a ConfigMatrix configurator URL like:
```
config_matrix/configurator?product_id=14733&order_line_id=106&matrix_id=24&template_id=24
```

### 2. Open Browser Developer Console
- **Chrome/Edge**: Press F12 or Ctrl+Shift+I
- **Firefox**: Press F12 or Ctrl+Shift+K
- **Safari**: Press Cmd+Option+I

### 3. Run Performance Tests

#### Basic Performance Test
```javascript
// Run comprehensive performance test
window.runPerformanceTest()
```

This will test:
- Single field change performance
- Dependency chain updates
- Cache effectiveness
- Memory usage

#### Stress Test
```javascript
// Run stress test with 100 rapid field changes
window.runStressTest()
```

#### View Performance Dashboard
```javascript
// Show current performance metrics
window.showPerformanceDashboard()
```

#### Validate Optimizations
```javascript
// Check that all optimizations are active
window.validateOptimizations()
```

#### Clear Cache and Reset Metrics
```javascript
// Reset performance metrics and clear cache
window.clearPerformanceCache()
```

## Expected Results

### Performance Metrics to Look For:

1. **Field Change Response Time**: Should be <100ms
2. **Cache Hit Rate**: Should be >80%
3. **Memory Usage**: Should remain stable
4. **Dependency Updates**: Should be minimal (only affected fields)

### Sample Good Results:
```
┌─────────────────────────┬──────────────┐
│ Field Changes           │ 25           │
│ Evaluations            │ 127          │
│ Dependency Updates     │ 8            │
│ Cache Hit Rate         │ 85.2%        │
│ Average Response Time  │ 45.67ms      │
│ Total Time            │ 1141.75ms    │
│ Cache Size            │ 156          │
│ Memory Usage          │ 45MB / 128MB │
└─────────────────────────┴──────────────┘
```

### Warning Signs:
- Average response time >200ms
- Cache hit rate <60%
- Memory usage continuously growing
- High number of evaluations per field change

## Manual Testing

### Test Scenario 1: Single Field Changes
1. Change one field value
2. Observe console logs for `[PERF]` messages
3. Check that only dependent fields are updated
4. Verify response time is <100ms

### Test Scenario 2: Complex Dependencies
1. Change a field that affects many other fields
2. Observe selective updates in console
3. Check that progressive updates are used for large sets
4. Verify UI remains responsive

### Test Scenario 3: Memory Stability
1. Make many field changes over 5+ minutes
2. Run `window.showPerformanceDashboard()` periodically
3. Check that memory usage remains stable
4. Verify cache size doesn't grow indefinitely

## Troubleshooting

### If Performance is Still Slow:

1. **Check Optimization Status**:
   ```javascript
   window.validateOptimizations()
   ```

2. **Clear Cache and Test**:
   ```javascript
   window.clearPerformanceCache()
   window.runPerformanceTest()
   ```

3. **Check Console for Errors**:
   Look for JavaScript errors that might be breaking optimizations

4. **Verify Dependency Graph**:
   Check console logs during form load for dependency graph building

### Common Issues:

1. **Low Cache Hit Rate**: May indicate cache is being cleared too frequently
2. **High Evaluation Count**: May indicate dependency tracking isn't working
3. **Memory Growth**: May indicate memory cleanup isn't running
4. **Slow Response Times**: May indicate backend optimizations aren't active

## Performance Comparison

### Before Optimizations:
- Field change: 200-500ms
- Complex configurations: 1-3 seconds
- Memory usage: Growing continuously
- UI: Freezes during calculations

### After Optimizations:
- Field change: <100ms
- Complex configurations: <200ms
- Memory usage: Stable
- UI: Always responsive

## Advanced Testing

### Custom Performance Test:
```javascript
// Test specific field changes
const field = Array.from(window.configurator.allFields.values())[0];
const startTime = performance.now();
window.configurator.onFieldChange(field, 'test_value');
const duration = performance.now() - startTime;
console.log(`Custom test: ${duration.toFixed(2)}ms`);
```

### Monitor Specific Metrics:
```javascript
// Get specific performance data
const metrics = window.configurator.performanceMetrics;
console.log('Current metrics:', metrics);
```

### Test Cache Effectiveness:
```javascript
// Check cache size and hit rate
const cache = window.configurator.expressionCache;
console.log(`Cache size: ${cache.size}`);
console.log(`Hit rate: ${(metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses) * 100).toFixed(1)}%`);
```

## Reporting Issues

If you encounter performance issues after the optimizations:

1. **Collect Performance Data**:
   - Run `window.runPerformanceTest()`
   - Run `window.validateOptimizations()`
   - Take screenshots of the results

2. **Check Browser Console**:
   - Look for error messages
   - Note any `[PERF]` log messages
   - Check for memory warnings

3. **Provide Context**:
   - Browser type and version
   - Number of fields in the configuration
   - Specific actions that are slow
   - Template ID being used

4. **Test Environment**:
   - Test on different browsers
   - Try with different templates
   - Compare with and without optimizations

## Continuous Monitoring

For ongoing performance monitoring:

1. **Regular Testing**: Run performance tests weekly
2. **Monitor Metrics**: Check dashboard during normal usage
3. **Watch for Regressions**: Compare results over time
4. **Update Baselines**: Adjust expectations as system grows

The performance optimizations include comprehensive monitoring tools to help maintain optimal performance over time.
