# Manufacturing Order Creation - Quick Reference

## Complete Workflow Overview

### 🔧 Configuration Phase
```
User configures product → save_config() → config.generate_bom() ✅
Configuration saved → BOM generated with components → Ready for sale ✅
```

### 📋 Sale Order Phase
```
User creates Sale Order → Adds configured product → Sets quantity ✅
User confirms Sale Order → _validate_configurable_products_boms() ✅
Validation passes → _process_configured_products() ✅
```

### 🚀 Procurement Phase
```
Sale Order confirmation → action_confirm() ✅
Parent method called → _action_launch_stock_rule() ✅
Procurement created → _prepare_procurement_values() adds bom_id ✅
```

### 🏭 Manufacturing Rule Phase
```
Procurement processed → procurement.group.run() ✅
Rule selection → Manufacturing rule selected ✅
Manufacturing triggered → _run_manufacture() ✅
BOM retrieved → _get_matching_bom() uses config BOM ✅
MO created → _prepare_mo_vals() with correct BOM ✅
```

### ✅ Integration Phase
```
Manufacturing Order created → Linked to Sale Order ✅
Stock moves created → Picking generated ✅
Complete traceability → SO → MO → BOM ✅
```

## Key Files and Methods

### `models/sale_order_line.py`
```python
_has_mto_and_manufacture_routes()     # Route detection
_requires_configured_bom()            # BOM requirement check
_prepare_procurement_values()         # BOM injection
```

### `models/stock_move.py`
```python
_prepare_procurement_values()         # BOM preservation
```

### `models/stock_rule.py`
```python
_get_matching_bom()                   # BOM selection (dual strategy)
_prepare_mo_vals()                    # MO creation with sale line link
```

### `models/sale_order.py`
```python
action_confirm()                      # Enhanced confirmation
_validate_configurable_products_boms() # Pre-confirmation validation
```

## Prerequisites Checklist

### Product Setup ✅
- [ ] Product marked as `is_configurable = True`
- [ ] Product type set to `Consumable`
- [ ] Routes assigned: MTO + Manufacturing
- [ ] Configuration template properly set up

### Warehouse Setup ✅
- [ ] Warehouse `manufacture_to_resupply = True`
- [ ] Manufacturing routes active
- [ ] Stock locations configured

### Configuration Setup ✅
- [ ] Template has component mappings
- [ ] BOM generation rules defined
- [ ] Configuration saved and BOM generated

## Validation Rules

### Pre-Confirmation Checks
1. All configurable products must have valid configurations
2. All configurations must have generated BOMs
3. All products must have required routes (MTO + Manufacturing)

### Error Messages
- `"Missing BOM for configurable product"` → Check configuration BOM
- `"Product requires configuration"` → Configure product first
- `"Invalid route configuration"` → Check product routes

## Troubleshooting Quick Fixes

### No Manufacturing Order Created
1. Check product routes: MTO + Manufacturing required
2. Verify warehouse manufacturing settings
3. Check logs for `🏭 CANBRAX: _run_manufacture called`

### Wrong BOM Used
1. Verify configuration has BOM generated
2. Check logs for `✅ CANBRAX: Using configuration BOM`
3. Ensure BOM preservation in procurement values

### MO Not Linked to Sale Line
1. Check `sale_line_id` in MO record
2. Verify logs show `🔗 CANBRAX: Added sale_line_id`
3. Ensure stock move preserves sale line reference

## Debug Log Patterns

### Success Pattern
```
🚀 CANBRAX: Sale Order confirmation started
🔍 CANBRAX: Validating configurable products BOMs
✅ CANBRAX: Validation passed
🚀 CANBRAX: _action_launch_stock_rule called
✅ CANBRAX: Selected rule: (Action: manufacture)
🏭 CANBRAX: _run_manufacture called
✅ CANBRAX: Using configuration BOM
🏭 CANBRAX: Found X Manufacturing Orders
```

### Failure Patterns
```
❌ No _run_manufacture logs → Manufacturing rule not triggered
❌ Wrong BOM ID in logs → BOM not preserved
❌ No MO found → Creation failed
```

## Performance Tips

### For Large Orders
- Process in batches if many configurable products
- Monitor BOM generation time
- Consider async processing for complex configurations

### For Production
- Disable debug logging for performance
- Use INFO level for critical events only
- Monitor procurement processing times

## Integration Points

### Sales Module
- Sale Order validation and confirmation
- Sale Order Line configuration linking
- Procurement value preparation

### Manufacturing Module
- Manufacturing Order creation
- BOM selection and usage
- Work order generation

### Stock Module
- Stock move creation and linking
- Procurement chain processing
- Route and rule management

## Testing Checklist

### Basic Flow Test
1. [ ] Configure product with options
2. [ ] Create Sale Order with configured product
3. [ ] Confirm Sale Order
4. [ ] Verify Manufacturing Order created
5. [ ] Check MO uses correct BOM
6. [ ] Confirm MO linked to Sale Order Line

### Error Handling Test
1. [ ] Try confirming SO without configuration
2. [ ] Try confirming SO with missing BOM
3. [ ] Verify error messages are clear
4. [ ] Confirm validation prevents confirmation

### Integration Test
1. [ ] Test with multiple configurable products
2. [ ] Test mixed orders (configurable + standard)
3. [ ] Verify all MOs created correctly
4. [ ] Check stock moves and pickings

## Common Configurations

### Standard Door Manufacturing
```python
Product: Custom Door
Type: Consumable
Routes: MTO + Manufacturing
Configuration: Wood type, Handle, Size
BOM: Generated from component mappings
```

### Complex Multi-Component Product
```python
Product: Custom Cabinet
Type: Consumable  
Routes: MTO + Manufacturing
Configuration: Multiple sections, hardware, finish
BOM: Multi-level with sub-assemblies
```

## Support Resources

### Documentation
- [04_MANUFACTURING_ORDER_CREATION.md](04_MANUFACTURING_ORDER_CREATION.md) - Complete workflow
- [05_MANUFACTURING_TECHNICAL_IMPLEMENTATION.md](05_MANUFACTURING_TECHNICAL_IMPLEMENTATION.md) - Technical details
- [09_MANUFACTURING_USER_GUIDE.md](09_MANUFACTURING_USER_GUIDE.md) - User instructions

### Log Analysis
- Enable INFO level logging
- Look for `🚀 CANBRAX:` prefixed messages
- Follow the workflow sequence in logs
- Check for error patterns and warnings

### Development
- All overrides are in `canbrax_configmatrix/models/`
- Key files: `sale_order_line.py`, `stock_move.py`, `stock_rule.py`
- Test coverage in `tests/test_manufacturing_order_creation.py`
