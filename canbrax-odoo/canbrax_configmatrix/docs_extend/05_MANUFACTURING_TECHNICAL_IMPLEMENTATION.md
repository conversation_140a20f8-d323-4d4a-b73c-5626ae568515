# Manufacturing Order Creation - Technical Implementation Guide

## Architecture Overview

The Manufacturing Order creation system for configurable products is built on a **multi-layer architecture** that ensures configuration-specific BOMs are preserved through the entire Odoo procurement chain.

## Core Implementation Strategy

### Problem Solved
Standard Odoo procurement system doesn't preserve custom values like `bom_id` through the procurement chain, causing Manufacturing Orders to use default BOMs instead of configuration-specific ones.

### Solution Architecture
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Sale Order    │───▶│   Procurement    │───▶│ Manufacturing   │
│     Line        │    │     Chain        │    │     Order       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
   Add bom_id to          Preserve bom_id           Use config BOM
 procurement values      through chain             for MO creation
```

## File Structure and Responsibilities

### 1. `models/sale_order_line.py`
**Responsibility**: Initial BOM injection and route validation

#### Key Methods:
```python
def _has_mto_and_manufacture_routes(self):
    """Detect products requiring manufacturing"""
    
def _requires_configured_bom(self):
    """Identify lines needing configuration BOMs"""
    
def _prepare_procurement_values(self, group_id=False):
    """Inject bom_id into procurement values"""
```

#### Implementation Details:
- **Route Detection**: Checks for both MTO and Manufacturing routes
- **BOM Injection**: Adds `config_id.bom_id` to procurement values
- **Validation Integration**: Works with sale order validation system

### 2. `models/stock_move.py`
**Responsibility**: BOM preservation through procurement chain

#### Key Method:
```python
def _prepare_procurement_values(self):
    """Preserve bom_id when stock moves create new procurements"""
```

#### Critical Function:
This override is **essential** because:
- Standard Odoo stock moves create new procurement values
- Custom values like `bom_id` are lost in the process
- This method preserves both `bom_id` and `sale_line_id`

### 3. `models/stock_rule.py`
**Responsibility**: BOM selection and MO creation

#### Key Methods:
```python
def _get_matching_bom(self, product_id, company_id, values):
    """Dual-strategy BOM retrieval"""
    
def _prepare_mo_vals(self, ...):
    """Ensure MO is linked to sale order line"""
```

#### Dual Strategy Implementation:
1. **Primary**: Use `bom_id` from procurement values
2. **Fallback**: Lookup BOM from `sale_line_id` directly

### 4. `models/procurement_group.py`
**Responsibility**: Debugging and monitoring

#### Purpose:
- Comprehensive logging of procurement processing
- Rule selection tracking
- Performance monitoring

## Detailed Technical Flow

### Phase 1: Sale Order Confirmation
```python
# sale_order.py
def action_confirm(self):
    self._validate_configurable_products_boms()  # Validate BOMs exist
    self._process_configured_products()          # Apply configurations
    return super().action_confirm()              # Trigger procurement
```

### Phase 2: Procurement Value Preparation
```python
# sale_order_line.py
def _prepare_procurement_values(self, group_id=False):
    values = super()._prepare_procurement_values(group_id)
    
    # Critical: Add configuration BOM to values
    if self.is_configurable and self.config_id and self.config_id.bom_id:
        values['bom_id'] = self.config_id.bom_id
        
    return values
```

### Phase 3: Procurement Chain Processing
```python
# Standard Odoo creates stock moves, which then create new procurements
# Our override preserves the BOM:

# stock_move.py
def _prepare_procurement_values(self):
    values = super()._prepare_procurement_values()
    
    if self.sale_line_id and self.sale_line_id.is_configurable:
        values['sale_line_id'] = self.sale_line_id.id  # For fallback
        if self.sale_line_id.config_id and self.sale_line_id.config_id.bom_id:
            values['bom_id'] = self.sale_line_id.config_id.bom_id  # Preserve BOM
            
    return values
```

### Phase 4: Manufacturing Rule Execution
```python
# stock_rule.py
def _get_matching_bom(self, product_id, company_id, values):
    # Primary strategy: Use procurement values
    if values.get('bom_id', False):
        return values['bom_id']
    
    # Fallback strategy: Direct lookup from sale line
    if values.get('sale_line_id'):
        sale_line = self.env['sale.order.line'].browse(values['sale_line_id'])
        if sale_line.config_id and sale_line.config_id.bom_id:
            return sale_line.config_id.bom_id
    
    # Standard Odoo fallback
    return super()._get_matching_bom(product_id, company_id, values)
```

## Route Handling Strategy

### Understanding Odoo Route Priority
```
Route Sequence (Lower = Higher Priority):
├── MTO Route (sequence: 5)          ← Higher priority
└── Manufacturing Route (sequence: 10) ← Lower priority
```

### How Both Routes Work Together
1. **MTO Rule**: Creates stock move from Stock → Customer
2. **Stock Move**: Triggers procurement for Stock location
3. **Manufacturing Rule**: Creates MO to fulfill Stock location demand

### Implementation Insight
The system **doesn't fight route priority** but **works with it**:
- MTO rule runs first (creates customer demand)
- Manufacturing rule runs second (fulfills stock demand)
- Both rules are necessary for the complete flow

## Error Handling and Validation

### Pre-Confirmation Validation
```python
def _validate_configurable_products_boms(self):
    """Prevent confirmation without proper BOMs"""
    lines_requiring_boms = self.order_line.filtered(
        lambda line: line._requires_configured_bom()
    )
    
    for line in lines_requiring_boms:
        if not line.config_id or not line.config_id.bom_id:
            raise ValidationError(f"Missing BOM for {line.product_id.name}")
```

### Runtime Error Handling
- **Missing BOM**: Graceful fallback to standard BOM selection
- **Invalid Configuration**: Warning logs with detailed context
- **Procurement Failures**: Comprehensive error reporting

## Performance Optimizations

### Efficient BOM Lookup
```python
# Avoid repeated database queries
if values.get('bom_id', False):
    return values['bom_id']  # Direct return, no DB query
```

### Minimal Override Impact
- Only processes configurable products
- Standard products use unchanged Odoo logic
- No performance impact on non-configurable items

### Logging Strategy
```python
# Production: Use INFO level for critical events only
_logger.info("✅ CANBRAX: Using configuration BOM %s", bom.id)

# Debug: Use DEBUG level for detailed tracing
_logger.debug("🔍 CANBRAX: Processing procurement values: %s", values)
```

## Testing Strategy

### Unit Test Coverage
1. **Route Detection**: Test `_has_mto_and_manufacture_routes()`
2. **BOM Injection**: Test `_prepare_procurement_values()`
3. **BOM Preservation**: Test stock move procurement values
4. **BOM Selection**: Test `_get_matching_bom()` with various scenarios

### Integration Test Scenarios
1. **Complete Flow**: Sale Order → MO creation
2. **Mixed Orders**: Configurable + standard products
3. **Error Cases**: Missing BOMs, invalid configurations
4. **Performance**: Large orders with multiple configurable products

### Test Data Requirements
```python
# Required test setup
configurable_product = self.env['product.product'].create({
    'name': 'Test Configurable Product',
    'type': 'consu',  # Important: Must be consumable for procurement
    'is_configurable': True,
    'route_ids': [(6, 0, [mto_route.id, manufacture_route.id])],
})
```

## Debugging and Troubleshooting

### Debug Logging Activation
```python
# In Python code
import logging
_logger = logging.getLogger(__name__)
_logger.setLevel(logging.INFO)

# In Odoo configuration
log_handler = :INFO
log_db = :INFO
```

### Key Log Patterns to Monitor
```
🚀 CANBRAX: Process initiation
🔍 CANBRAX: Detailed analysis  
✅ CANBRAX: Success confirmation
⚠️ CANBRAX: Warning condition
❌ CANBRAX: Error condition
```

### Common Issues and Solutions

#### Issue: No Manufacturing Order Created
**Symptoms**: Sale Order confirms but no MO appears
**Debug**: Check for `🏭 CANBRAX: _run_manufacture called` log
**Solution**: Verify product has both MTO and Manufacturing routes

#### Issue: Wrong BOM Used
**Symptoms**: MO created with default BOM instead of configuration BOM
**Debug**: Check `✅ CANBRAX: Using configuration BOM` log
**Solution**: Verify BOM preservation in procurement values

#### Issue: MO Not Linked to Sale Line
**Symptoms**: MO exists but not visible in sale order line
**Debug**: Check for `🔗 CANBRAX: Added sale_line_id` log
**Solution**: Ensure `sale_line_id` is preserved in MO values

## Maintenance and Updates

### Odoo Version Compatibility
- **Current**: Odoo 18 compatible
- **Future**: Monitor changes to procurement system
- **Backward**: May require adjustments for older versions

### Module Dependencies
```python
'depends': [
    'sale',
    'stock',
    'mrp',
    'sale_stock',
    'sale_mrp',
]
```

### Extension Points
- **Custom BOM Logic**: Override `_get_matching_bom()` further
- **Additional Validation**: Extend `_validate_configured_bom()`
- **Enhanced Logging**: Add custom log handlers
- **Performance Monitoring**: Add timing decorators

## Security Considerations

### Access Control
- Manufacturing Order creation uses `SUPERUSER_ID` (standard Odoo)
- BOM access controlled by standard MRP permissions
- Configuration access controlled by ConfigMatrix permissions

### Data Integrity
- BOM validation prevents invalid Manufacturing Orders
- Procurement chain maintains data consistency
- Error handling prevents partial state corruption
