How to Use the Matrix-Based Visibility System
With these basic views in place, you should now be able to use the dependency system to create visibility conditions:

Go to ConfigMatrix → Dependencies
Click "Create" to add a new dependency
Fill in the required fields:
Select the dependent field (e.g., "Powder Coat Colour")
Select the controlling field (e.g., "Frame Colour")
Choose the operator (e.g., "==")
Enter the value in the appropriate field based on the field type:
For text or selection values: use the text_value or selection_value field
For numeric values: use the number_value field
For boolean values: use the boolean_value field
For lists: use the list_value field with comma-separated values
You can also access the dependencies directly from the field form:

Go to the field's form view
Click on the "Visibility" tab
Click the "Manage Dependencies" button
This will show all dependencies for the current field
The system will automatically generate the correct JavaScript condition and apply it to the field's visibility condition, making fields like "Powder Coat Colour" visible only when "Frame Colour" equals "Powder Coat".

This matrix-based approach should provide a more elegant and user-friendly way to manage visibility conditions compared to writing JavaScript expressions manually.