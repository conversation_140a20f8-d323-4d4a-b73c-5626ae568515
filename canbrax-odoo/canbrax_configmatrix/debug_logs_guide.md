# 🔍 Debug Logs Guide for Manufacturing Order Creation

## 📋 What to Look For in Logs

When you perform **Sale Order confirmation**, look for these log entries in sequence:

### 1. 🚀 Sale Order Confirmation Start
```
🚀 CANBRAX: Sale Order [SO_NAME] confirmation started
```

### 2. 🔍 BOM Validation Phase
```
🔍 CANBRAX: Step 1 - Validating configurable products BOMs
🔍 CANBRAX: Validating X configurable products with MTO+Manufacturing routes for SO [SO_NAME]
📋 CANBRAX: Line requiring BOM - Product: [PRODUCT_NAME], Config: [CONFIG_ID], BOM: [BOM_ID]
✅ CANBRAX: Validation passed for product [PRODUCT_NAME]
✅ CANBRAX: Step 1 completed - BOM validation passed
```

### 3. 🔧 Product Processing Phase
```
🔧 CANBRAX: Step 2 - Processing configured products
✅ CANBRAX: Step 2 completed - Configured products processed
```

### 4. 📞 Parent Method Call
```
📞 CANBRAX: Step 3 - Calling parent action_confirm
```

### 5. 🚀 Stock Rule Launch
```
🚀 CANBRAX: _action_launch_stock_rule called for X lines
🔍 CANBRAX: Processing line - Product: [PRODUCT_NAME], Type: product, Configurable: True, State: sale
🛣️ CANBRAX: Product [PRODUCT_NAME] route check - MTO: True, Manufacture: True, All routes: [ROUTE_NAMES]
🔍 CANBRAX: Checking if [PRODUCT_NAME] requires configured BOM
🛣️ CANBRAX: Product [PRODUCT_NAME] has MTO+Manufacturing routes: True
🎯 CANBRAX: Found X configurable storable lines requiring BOM
🔧 CANBRAX: Processing configurable storable products with type switching
📋 CANBRAX: Configurable line - Product: [PRODUCT_NAME], Routes: [ROUTES], Has Config: True, Config BOM: [BOM_ID]
🔄 CANBRAX: Switching product [PRODUCT_NAME] type: product → consu
📞 CANBRAX: Calling parent _action_launch_stock_rule method
```

### 6. 📦 Procurement Values
```
📦 CANBRAX: Procurement values for [PRODUCT_NAME]: {'bom_id': X, 'route_ids': Y, 'warehouse_id': Z, 'sale_line_id': W}
🎯 CANBRAX: Added BOM X to procurement values for configured product [PRODUCT_NAME]
```

### 7. 🔄 Type Restoration
```
✅ CANBRAX: Parent method completed successfully
🔄 CANBRAX: Restoring product [PRODUCT_NAME] type: consu → product
🎉 CANBRAX: Configurable storable products processing completed
```

### 8. 🏭 Manufacturing Rule (If Manufacturing Order is Created)
```
🏭 CANBRAX: _get_matching_bom called for product [PRODUCT_NAME]
🔍 CANBRAX: Procurement values keys: ['bom_id', 'route_ids', 'warehouse_id', ...]
✅ CANBRAX: Using configuration BOM X for product [PRODUCT_NAME]
```

### 9. 🎉 Completion
```
🎉 CANBRAX: Sale Order [SO_NAME] confirmation completed successfully
```

## ❌ Potential Issues to Look For

### Issue 1: No Configurable Lines Found
```
🎯 CANBRAX: Found 0 configurable storable lines requiring BOM
📞 CANBRAX: No configurable storable lines found, calling parent method directly
```
**Problem**: Product not detected as configurable or doesn't have required routes

### Issue 2: Route Check Failure
```
🛣️ CANBRAX: Product [PRODUCT_NAME] route check - MTO: False, Manufacture: False
```
**Problem**: Product missing MTO or Manufacturing routes

### Issue 3: Missing Configuration/BOM
```
⚠️ CANBRAX: Configurable product [PRODUCT_NAME] missing config or BOM - Config: None, BOM: None
```
**Problem**: Product not configured or BOM not generated

### Issue 4: No BOM in Procurement Values
```
ℹ️ CANBRAX: No bom_id in procurement values for product [PRODUCT_NAME]
```
**Problem**: BOM not passed through procurement values

### Issue 5: Manufacturing Rule Not Called
**Missing**: No `🏭 CANBRAX: _get_matching_bom called` logs
**Problem**: Manufacturing rule not triggered, likely MTO rule selected instead

## 🛠️ Debug Steps Based on Logs

1. **If no CANBRAX logs appear**: Module not loaded or override not working
2. **If stops at Step 1**: BOM validation failing
3. **If stops at Step 3**: Parent method failing
4. **If no type switching**: Product not detected as configurable storable
5. **If no manufacturing rule logs**: Procurement not triggering manufacturing

## 📝 How to Use

1. Confirm a Sale Order with configurable product
2. Check logs for the sequence above
3. Identify where the flow stops or fails
4. Use the issue patterns to diagnose the problem
