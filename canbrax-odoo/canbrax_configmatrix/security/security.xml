<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Module Categories -->
        <record id="module_category_configmatrix" model="ir.module.category">
            <field name="name">ConfigMatrix</field>
            <field name="description">ConfigMatrix Configuration and Management</field>
            <field name="sequence">20</field>
        </record>

        <!-- Security Groups -->
        <record id="group_config_matrix_user" model="res.groups">
            <field name="name">User</field>
            <field name="category_id" ref="module_category_configmatrix"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="comment">Users can configure products but cannot create or modify configuration templates.</field>
        </record>

        <record id="group_config_matrix_admin" model="res.groups">
            <field name="name">Administrator</field>
            <field name="category_id" ref="module_category_configmatrix"/>
            <field name="implied_ids" eval="[(4, ref('group_config_matrix_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_admin'))]"/>
            <field name="comment">Administrators can create and manage configuration templates.</field>
        </record>
        
        <record id="group_config_matrix_builder" model="res.groups">
            <field name="name">Builder</field>
            <field name="category_id" ref="module_category_configmatrix"/>
            <field name="implied_ids" eval="[(4, ref('group_config_matrix_user'))]"/>
            <field name="comment">Professional builders and contractors who get access to specialized builder portal interfaces.</field>
        </record>
    </data>
</odoo>
