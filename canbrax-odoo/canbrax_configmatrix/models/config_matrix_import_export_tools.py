# -*- coding: utf-8 -*-

import base64
import json
import logging
import io
import csv
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)

try:
    import openpyxl
    from openpyxl.utils import get_column_letter
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False


class ConfigMatrixImportExportTools(models.TransientModel):
    _name = 'config.matrix.import.export.tools'
    _description = 'Matrix Import/Export Tools - Consolidated Interface'

    # CSV Import Fields
    csv_import_type = fields.Selection([
        ('price', 'Price Matrix'),
        ('labor', 'Labor Time Matrix')
    ], string="Matrix Type", default='price')
    
    csv_matrix_name = fields.Char("Matrix Name")
    csv_product_template_id = fields.Many2one('product.template', "Product Template")
    csv_file_data = fields.Binary("CSV File")
    csv_file_name = fields.Char("File Name")
    csv_has_preview = fields.Boolean("Has Preview", default=False)
    csv_preview_data = fields.Text("Preview Data")
    
    # CSV Labor Matrix Fields
    csv_operation_type = fields.Selection([
        ('cut_frame', 'Cut Frame'),
        ('cut_mesh', 'Cut Mesh'),
        ('assembly', 'Assembly'),
        ('powder_coating', 'Powder Coating'),
        ('quality_check', 'Quality Check'),
        ('packaging', 'Packaging'),
        ('custom', 'Custom Operation')
    ], string="Operation Type", default='assembly')
    csv_operation_name = fields.Char("Operation Name")
    csv_time_unit = fields.Selection([
        ('minutes', 'Minutes'),
        ('hours', 'Hours'),
        ('seconds', 'Seconds')
    ], string="Time Unit", default='hours')

    # Excel Import Fields
    excel_import_product_template_id = fields.Many2one('product.template', "Product Template")
    excel_import_file_data = fields.Binary("Excel File")
    excel_import_file_name = fields.Char("File Name")
    excel_import_mode = fields.Selection([
        ('create', 'Create New Matrices'),
        ('update', 'Update Existing Matrices'),
        ('replace', 'Replace Existing Matrices')
    ], string="Import Mode", default='create')
    excel_target_category_id = fields.Many2one('config.matrix.category', "Target Category")
    excel_auto_create_categories = fields.Boolean("Auto-create Categories", default=True)
    excel_skip_empty_cells = fields.Boolean("Skip Empty Cells", default=True)
    excel_validate_data = fields.Boolean("Validate Data", default=True)
    excel_create_backup = fields.Boolean("Create Backup", default=True)
    excel_sheet_info = fields.Text("Sheet Information")

    # Excel Export Fields
    excel_export_type = fields.Selection([
        ('all', 'All Matrices'),
        ('template', 'By Template'),
        ('category', 'By Category')
    ], string="Export Type", default='all')
    excel_export_template_id = fields.Many2one('config.matrix.template', "Template")
    excel_export_category_id = fields.Many2one('config.matrix.category', "Category")
    excel_include_metadata = fields.Boolean("Include Metadata", default=True)
    excel_include_empty_cells = fields.Boolean("Include Empty Cells", default=False)
    excel_separate_sheets = fields.Boolean("Separate Sheets", default=True)
    excel_export_file_data = fields.Binary("Excel File")
    excel_export_file_name = fields.Char("File Name")
    excel_export_file_ready = fields.Boolean("File Ready", default=False)

    # Bulk Operations Fields - Simplified
    bulk_export_filter = fields.Selection([
        ('all', 'All Matrices'),
        ('active', 'Active Only'),
        ('recent', 'Recently Modified')
    ], string="Export Filter", default='active')
    bulk_include_inactive = fields.Boolean("Include Inactive", default=False)
    bulk_import_file_data = fields.Binary("Bulk Import File")
    bulk_import_file_name = fields.Char("File Name")
    
    # Recent exports tracking
    recent_exports = fields.Text("Recent Exports", readonly=True)

    def action_csv_preview(self):
        """Preview CSV file content"""
        self.ensure_one()
        if not self.csv_file_data:
            raise UserError(_("Please upload a CSV file first"))
        
        try:
            # Decode and preview CSV content
            csv_content = base64.b64decode(self.csv_file_data).decode('utf-8')
            lines = csv_content.split('\n')[:10]  # First 10 lines
            
            # Parse CSV to show structured preview
            reader = csv.reader(io.StringIO(csv_content))
            preview_lines = []
            for i, row in enumerate(reader):
                if i >= 10:  # Limit to first 10 rows
                    break
                preview_lines.append(' | '.join(row[:8]))  # Limit columns too
                
            preview = '\n'.join(preview_lines)
            if len(lines) > 10:
                preview += f'\n... and {len(lines) - 10} more rows'
            
            self.csv_preview_data = preview
            self.csv_has_preview = True
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('CSV Preview Generated'),
                    'message': _('Preview shows first 10 lines of the CSV file'),
                    'type': 'success',
                }
            }
        except Exception as e:
            raise UserError(_("Error reading CSV file: %s") % str(e))

    def action_csv_import(self):
        """Import CSV matrix"""
        self.ensure_one()
        if not self.csv_file_data or not self.csv_matrix_name:
            raise UserError(_("Please provide CSV file and matrix name"))
        
        if not self.csv_product_template_id:
            raise UserError(_("Please select a product template"))
        
        try:
            csv_content = base64.b64decode(self.csv_file_data).decode('utf-8')
            
            if self.csv_import_type == 'price':
                matrix = self.env['config.matrix.price.matrix'].import_from_csv(
                    name=self.csv_matrix_name,
                    product_template_id=self.csv_product_template_id.id,
                    csv_content=csv_content
                )
                model_name = 'Price Matrix'
                matrix_id = matrix.id
            else:
                matrix = self.env['config.matrix.labor.time.matrix'].import_from_csv(
                    name=self.csv_matrix_name,
                    operation_type=self.csv_operation_type,
                    csv_content=csv_content,
                    product_template_id=self.csv_product_template_id.id
                )
                # Set additional labor fields
                if self.csv_operation_type == 'custom' and self.csv_operation_name:
                    matrix.operation_name = self.csv_operation_name
                matrix.time_unit = self.csv_time_unit
                model_name = 'Labor Time Matrix'
                matrix_id = matrix.id
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Import Successful'),
                    'message': _('%s "%s" imported successfully with ID: %s') % (model_name, matrix.name, matrix_id),
                    'type': 'success',
                }
            }
        except Exception as e:
            _logger.error("CSV Import Error: %s", str(e))
            raise UserError(_("Import failed: %s") % str(e))

    def action_excel_analyze(self):
        """Analyze Excel file structure"""
        self.ensure_one()
        if not self.excel_import_file_data:
            raise UserError(_("Please upload an Excel file first"))
        
        if not OPENPYXL_AVAILABLE:
            raise UserError(_("Excel support requires 'openpyxl' library. Please install it."))
        
        try:
            # Decode Excel file
            excel_data = base64.b64decode(self.excel_import_file_data)
            workbook = openpyxl.load_workbook(io.BytesIO(excel_data), read_only=True)
            
            analysis = []
            analysis.append("Excel File Analysis:")
            analysis.append("=" * 40)
            
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                
                # Get dimensions
                max_row = worksheet.max_row
                max_col = worksheet.max_column
                
                # Try to detect if it's a matrix (has headers)
                first_row = []
                for col in range(1, min(max_col + 1, 10)):  # First 10 columns
                    cell_value = worksheet.cell(1, col).value
                    first_row.append(str(cell_value) if cell_value else "")
                
                analysis.append(f"\\nSheet: '{sheet_name}'")
                analysis.append(f"  Dimensions: {max_row} rows x {max_col} columns")
                analysis.append(f"  First row: {' | '.join(first_row)}")
                
                # Check if looks like a pricing matrix
                if max_row > 2 and max_col > 2:
                    analysis.append(f"  Status: Suitable for matrix import")
                else:
                    analysis.append(f"  Status: Too small for matrix import")
            
            workbook.close()
            self.excel_sheet_info = '\\n'.join(analysis)
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Excel Analysis Complete'),
                    'message': _('Found %d sheet(s) in the Excel file') % len(workbook.sheetnames),
                    'type': 'success',
                }
            }
        except Exception as e:
            _logger.error("Excel Analysis Error: %s", str(e))
            raise UserError(_("Error analyzing Excel file: %s") % str(e))

    def action_excel_import(self):
        """Import Excel matrices"""
        self.ensure_one()
        if not self.excel_import_file_data:
            raise UserError(_("Please upload an Excel file first"))
        
        if not self.excel_import_product_template_id:
            raise UserError(_("Please select a product template"))
        
        if not OPENPYXL_AVAILABLE:
            raise UserError(_("Excel support requires 'openpyxl' library. Please install it."))
        
        try:
            excel_data = base64.b64decode(self.excel_import_file_data)
            workbook = openpyxl.load_workbook(io.BytesIO(excel_data), read_only=True)
            
            imported_matrices = []
            
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                
                # Skip sheets that are too small
                if worksheet.max_row < 3 or worksheet.max_column < 3:
                    continue
                
                # Convert sheet to CSV format
                csv_content = self._excel_sheet_to_csv(worksheet)
                
                # Import as price matrix (default)
                try:
                    matrix_name = f"{sheet_name} - {self.excel_import_product_template_id.name}"
                    matrix = self.env['config.matrix.price.matrix'].import_from_csv(
                        name=matrix_name,
                        product_template_id=self.excel_import_product_template_id.id,
                        csv_content=csv_content
                    )
                    
                    # Set category if specified
                    if self.excel_target_category_id:
                        matrix.category_id = self.excel_target_category_id.id
                    
                    imported_matrices.append(matrix.name)
                    
                except Exception as sheet_error:
                    _logger.warning("Failed to import sheet '%s': %s", sheet_name, str(sheet_error))
                    continue
            
            workbook.close()
            
            if imported_matrices:
                message = _('Successfully imported %d matrices: %s') % (
                    len(imported_matrices), 
                    ', '.join(imported_matrices)
                )
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Excel Import Complete'),
                        'message': message,
                        'type': 'success',
                    }
                }
            else:
                raise UserError(_("No valid matrices found in the Excel file"))
                
        except Exception as e:
            _logger.error("Excel Import Error: %s", str(e))
            raise UserError(_("Excel import failed: %s") % str(e))

    def _excel_sheet_to_csv(self, worksheet):
        """Convert Excel worksheet to CSV string"""
        csv_rows = []
        
        for row in worksheet.iter_rows(values_only=True):
            # Convert row to strings, handling None values
            csv_row = []
            for cell_value in row:
                if cell_value is None:
                    csv_row.append('')
                else:
                    csv_row.append(str(cell_value))
            csv_rows.append(csv_row)
        
        # Convert to CSV string
        output = io.StringIO()
        writer = csv.writer(output)
        writer.writerows(csv_rows)
        return output.getvalue()

    def action_excel_export(self):
        """Export matrices to Excel with enhanced naming and page refresh"""
        self.ensure_one()
        
        if not OPENPYXL_AVAILABLE:
            raise UserError(_("Excel export requires 'openpyxl' library. Please install it."))
        
        try:
            # Get matrices to export based on selection
            matrices = self._get_matrices_for_export()
            
            if not matrices:
                raise UserError(_("No matrices found for export"))
            
            # Create Excel workbook
            workbook = openpyxl.Workbook()
            # Remove default sheet
            workbook.remove(workbook.active)
            
            for matrix in matrices:
                # Create sheet for each matrix
                sheet_name = matrix.name[:31]  # Excel sheet name limit
                worksheet = workbook.create_sheet(title=sheet_name)
                
                # Export matrix data to sheet
                self._export_matrix_to_sheet(matrix, worksheet)
            
            # Save to BytesIO
            excel_buffer = io.BytesIO()
            workbook.save(excel_buffer)
            excel_buffer.seek(0)
            
            # Generate enhanced filename based on export type
            filename = self._generate_export_filename(matrices)
            
            # Encode for storage
            self.excel_export_file_data = base64.b64encode(excel_buffer.getvalue())
            self.excel_export_file_name = filename
            self.excel_export_file_ready = True
            
            # Update recent exports list
            self._update_recent_exports(filename, len(matrices))
            
            # Return refresh action to show updated page
            return {
                'type': 'ir.actions.client',
                'tag': 'reload',
            }
            
        except Exception as e:
            _logger.error("Excel Export Error: %s", str(e))
            raise UserError(_("Excel export failed: %s") % str(e))

    def _get_matrices_for_export(self):
        """Get matrices based on export type selection"""
        if self.excel_export_type == 'template' and self.excel_export_template_id:
            # Get matrices assigned to template
            domain = [('product_template_id', '=', self.excel_export_template_id.product_template_id.id)]
        elif self.excel_export_type == 'category' and self.excel_export_category_id:
            # Get matrices in category
            domain = [('category_id', '=', self.excel_export_category_id.id)]
        else:
            # All matrices
            domain = []
        
        # Add active filter if needed
        if not self.bulk_include_inactive:
            domain.append(('active', '=', True))
        
        return self.env['config.matrix.price.matrix'].search(domain)

    def _export_matrix_to_sheet(self, matrix, worksheet):
        """Export matrix data to Excel worksheet"""
        try:
            # Get matrix data
            height_ranges = json.loads(matrix.height_ranges) if matrix.height_ranges else []
            width_ranges = json.loads(matrix.width_ranges) if matrix.width_ranges else []
            matrix_data = json.loads(matrix.matrix_data) if matrix.matrix_data else {}
            
            # Create header row
            worksheet.cell(1, 1, "Height\\Width")
            for col, width_range in enumerate(width_ranges, 2):
                worksheet.cell(1, col, width_range.get('label', ''))
            
            # Create data rows
            for row, height_range in enumerate(height_ranges, 2):
                worksheet.cell(row, 1, height_range.get('label', ''))
                
                for col, width_range in enumerate(width_ranges, 2):
                    height_key = height_range.get('label', '')
                    width_key = width_range.get('label', '')
                    matrix_key = f"{height_key}_{width_key}"
                    
                    value = matrix_data.get(matrix_key, '')
                    if value:
                        worksheet.cell(row, col, float(value))
            
            # Add metadata if requested
            if self.excel_include_metadata:
                metadata_row = len(height_ranges) + 4
                worksheet.cell(metadata_row, 1, "Matrix Name:")
                worksheet.cell(metadata_row, 2, matrix.name)
                worksheet.cell(metadata_row + 1, 1, "Product:")
                worksheet.cell(metadata_row + 1, 2, matrix.product_template_id.name)
                worksheet.cell(metadata_row + 2, 1, "Currency:")
                worksheet.cell(metadata_row + 2, 2, matrix.currency_id.name if matrix.currency_id else 'N/A')
                
        except Exception as e:
            _logger.error("Error exporting matrix %s: %s", matrix.name, str(e))
            # Add error info to sheet
            worksheet.cell(1, 1, f"Error exporting matrix: {str(e)}")

    def action_bulk_export(self):
        """Bulk export all matrices"""
        self.ensure_one()
        
        try:
            # Get all matrices based on filter
            if self.bulk_export_filter == 'active':
                domain = [('active', '=', True)]
            elif self.bulk_export_filter == 'recent':
                # Last 30 days
                domain = [('write_date', '>=', fields.Date.today() - fields.timedelta(days=30))]
            else:
                domain = []
            
            price_matrices = self.env['config.matrix.price.matrix'].search(domain)
            labor_matrices = self.env['config.matrix.labor.time.matrix'].search(domain)
            
            total_matrices = len(price_matrices) + len(labor_matrices)
            
            # Export as Excel (only format available now)
            return self._bulk_export_excel(price_matrices, labor_matrices)
                
        except Exception as e:
            _logger.error("Bulk Export Error: %s", str(e))
            raise UserError(_("Bulk export failed: %s") % str(e))

    def _bulk_export_excel(self, price_matrices, labor_matrices):
        """Export all matrices to Excel format with enhanced naming"""
        if not OPENPYXL_AVAILABLE:
            raise UserError(_("Excel export requires 'openpyxl' library."))
        
        workbook = openpyxl.Workbook()
        workbook.remove(workbook.active)
        
        # Export price matrices
        for matrix in price_matrices:
            sheet_name = f"Price_{matrix.name}"[:31]
            worksheet = workbook.create_sheet(title=sheet_name)
            self._export_matrix_to_sheet(matrix, worksheet)
        
        # Export labor matrices
        for matrix in labor_matrices:
            sheet_name = f"Labor_{matrix.name}"[:31]
            worksheet = workbook.create_sheet(title=sheet_name)
            # Labor matrices use similar structure
            self._export_labor_matrix_to_sheet(matrix, worksheet)
        
        # Save with enhanced filename
        excel_buffer = io.BytesIO()
        workbook.save(excel_buffer)
        excel_buffer.seek(0)
        
        timestamp = fields.Datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"export_all_{timestamp}.xlsx"
        
        return {
            'type': 'ir.actions.act_url',
            'url': '/web/content/?model=config.matrix.import.export.tools&id=%d&field=excel_export_file_data&download=true&filename=%s' % (
                self.id, filename
            ),
            'target': 'self',
        }

    def _export_labor_matrix_to_sheet(self, matrix, worksheet):
        """Export labor matrix to Excel sheet"""
        try:
            height_ranges = json.loads(matrix.height_ranges) if matrix.height_ranges else []
            width_ranges = json.loads(matrix.width_ranges) if matrix.width_ranges else []
            matrix_data = json.loads(matrix.matrix_data) if matrix.matrix_data else {}
            
            # Similar to price matrix but for labor times
            worksheet.cell(1, 1, "Height\\Width")
            for col, width_range in enumerate(width_ranges, 2):
                worksheet.cell(1, col, width_range.get('label', ''))
            
            for row, height_range in enumerate(height_ranges, 2):
                worksheet.cell(row, 1, height_range.get('label', ''))
                
                for col, width_range in enumerate(width_ranges, 2):
                    height_key = height_range.get('label', '')
                    width_key = width_range.get('label', '')
                    matrix_key = f"{height_key}_{width_key}"
                    
                    value = matrix_data.get(matrix_key, '')
                    if value:
                        worksheet.cell(row, col, float(value))
            
        except Exception as e:
            _logger.error("Error exporting labor matrix %s: %s", matrix.name, str(e))

    def _bulk_export_json(self, price_matrices, labor_matrices):
        """Export matrices as JSON - REMOVED"""
        # This method has been removed as JSON Export was removed from bulk operations
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Export Not Available'),
                'message': _('JSON Export has been removed from bulk operations'),
                'type': 'warning',
            }
        }

    def action_bulk_import(self):
        """Bulk import matrices"""
        self.ensure_one()
        if not self.bulk_import_file_data:
            raise UserError(_("Please upload a file for bulk import"))
        
        # This is a placeholder for now - would need to implement based on file format
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Bulk Import Complete'),
                'message': _('Bulk import functionality will be implemented based on specific requirements'),
                'type': 'info',
            }
        }
    
    def _generate_export_filename(self, matrices):
        """Generate enhanced filename based on export type and content"""
        timestamp = fields.Datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if self.excel_export_type == 'all':
            return f"export_all_{timestamp}.xlsx"
        elif self.excel_export_type == 'template' and self.excel_export_template_id:
            template_name = self.excel_export_template_id.name.replace(' ', '_').lower()
            return f"export_template_{template_name}_{timestamp}.xlsx"
        elif self.excel_export_type == 'category' and self.excel_export_category_id:
            category_name = self.excel_export_category_id.name.replace(' ', '_').lower()
            return f"export_category_{category_name}_{timestamp}.xlsx"
        else:
            return f"export_{timestamp}.xlsx"
    
    def _update_recent_exports(self, filename, matrix_count):
        """Update list of recent exports (keep last 5)"""
        try:
            # Parse existing recent exports
            recent_list = []
            if self.recent_exports:
                recent_list = self.recent_exports.split('\n')
            
            # Add new export entry
            export_time = fields.Datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            new_entry = f"{export_time} - {filename} ({matrix_count} matrices)"
            recent_list.insert(0, new_entry)
            
            # Keep only last 5 entries
            recent_list = recent_list[:5]
            
            # Update field
            self.recent_exports = '\n'.join(recent_list)
            
        except Exception as e:
            _logger.warning("Failed to update recent exports: %s", str(e))
