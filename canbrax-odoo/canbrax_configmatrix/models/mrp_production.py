# -*- coding: utf-8 -*-

from odoo import models, fields, api, _

class MrpBom(models.Model):
    _inherit = 'mrp.bom'

    config_id = fields.Many2one('config.matrix.configuration', "Configuration",
                              ondelete="set null", help="Configuration that generated this BOM")
    is_configured = fields.Boolean("Configured", compute='_compute_is_configured', store=True)
    configuration_summary = fields.Text("Configuration Summary", compute='_compute_configuration_summary')

    @api.depends('config_id')
    def _compute_is_configured(self):
        for bom in self:
            bom.is_configured = bool(bom.config_id)

    @api.depends('config_id')
    def _compute_configuration_summary(self):
        for bom in self:
            if bom.config_id:
                bom.configuration_summary = bom.config_id.get_configuration_summary()
            else:
                bom.configuration_summary = ""

    def action_view_configuration(self):
        """View configuration details"""
        self.ensure_one()

        if not self.config_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Not Configured'),
                    'message': _('This bill of materials is not based on a configured product.'),
                    'type': 'warning',
                }
            }

        return {
            'name': _('Configuration'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.configuration',
            'view_mode': 'form',
            'res_id': self.config_id.id,
        }

class MrpProduction(models.Model):
    _inherit = 'mrp.production'

    config_id = fields.Many2one('config.matrix.configuration', "Configuration",
                              related='bom_id.config_id', store=True)
    is_configured = fields.Boolean("Configured", related='bom_id.is_configured', store=True)
    configuration_summary = fields.Text("Configuration Summary", related='bom_id.configuration_summary')

    def action_view_configuration(self):
        """View configuration details"""
        self.ensure_one()

        if not self.config_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Not Configured'),
                    'message': _('This manufacturing order is not based on a configured product.'),
                    'type': 'warning',
                }
            }

        return {
            'name': _('Configuration'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.configuration',
            'view_mode': 'form',
            'res_id': self.config_id.id,
        }
