# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import json

class ConfigMatrixOption(models.Model):
    _name = 'config.matrix.option'
    _description = 'Configuration Option'
    _order = 'sequence, id'

    name = fields.Char("Option Text", required=True, help="The option text shown to the user")
    value = fields.Char("Option Value", required=True, help="The value stored when this option is selected")
    field_id = fields.Many2one('config.matrix.field', "Field", required=True, ondelete='cascade')
    sequence = fields.Integer("Sequence", default=10, help="Display order within field")

    # Component mapping for BOM generation (Legacy - single component)
    component_product_id = fields.Many2one('product.product', "Component Product",
                                         help="Product to include in BOM when this option is selected")
    quantity_formula = fields.Char("Quantity Formula", default="1",
                                  help="Python expression to calculate component quantity")

    # New field for 1:many component mappings
    component_mapping_ids = fields.One2many(
        'config.matrix.option.component.mapping.extended',
        'option_id',
        "Component Mappings",
        help="Multiple components that can be mapped to this option"
    )

    # Add a computed field to check if using multiple mappings
    has_multiple_components = fields.Boolean(
        "Has Multiple Components",
        compute='_compute_has_multiple_components',
        search='_search_has_multiple_components',
        store=True,
        help="True if this option has multiple component mappings"
    )



    # Visibility condition
    visibility_condition = fields.Char("Visibility Condition",
                                    help="Python expression that determines if option is visible. "
                                         "Empty means always visible.")
    visibility_condition_ids = fields.One2many('config.matrix.option.visibility', 'option_id',
                                             string="Visibility Conditions")

    # Computed field for user-friendly condition text
    condition_display_text = fields.Char("Condition Display Text",
                                        compute='_compute_condition_display_text',
                                        help="User-friendly text describing the visibility conditions")

    # Related fields
    matrix_id = fields.Many2one('config.matrix.template', related='field_id.matrix_id',
                              string="Template", store=True)
    template_state = fields.Selection(related='matrix_id.state', string="Template State")

    _sql_constraints = [
        ('value_field_uniq', 'unique (field_id, value)',
         'Option value must be unique within a field!')
    ]

    @api.depends('component_mapping_ids', 'component_product_id')
    def _compute_has_multiple_components(self):
        """Check if option has multiple component mappings"""
        for option in self:
            option.has_multiple_components = len(option.component_mapping_ids) > 1 or (
                len(option.component_mapping_ids) == 1 and option.component_product_id
            )

    def _search_has_multiple_components(self, operator, value):
        """Search method for has_multiple_components field"""
        # Find options with multiple component mappings
        if operator == '=' and value or operator == '!=' and not value:
            # Looking for options with multiple components
            options_with_multiple = self.search([]).filtered(
                lambda o: len(o.component_mapping_ids) > 1 or
                         (len(o.component_mapping_ids) == 1 and o.component_product_id)
            )
            return [('id', 'in', options_with_multiple.ids)]
        else:
            # Looking for options without multiple components
            options_without_multiple = self.search([]).filtered(
                lambda o: not (len(o.component_mapping_ids) > 1 or
                             (len(o.component_mapping_ids) == 1 and o.component_product_id))
            )
            return [('id', 'in', options_without_multiple.ids)]

    def get_component_mappings(self):
        """Get all component mappings for this option (both legacy and new)"""
        self.ensure_one()
        mappings = []

        # Add simple component mapping if exists
        if self.component_product_id:
            mappings.append({
                'component_product_id': self.component_product_id,
                'quantity_formula': self.quantity_formula or '1',
                'condition': None,
                'sequence': 0,
                'is_legacy': False,
                'mapping_type': 'static',
                'mapping_obj': None
            })

        # Add new mappings
        for mapping in self.component_mapping_ids:
            mappings.append({
                'component_product_id': mapping.component_product_id,
                'quantity_formula': mapping.quantity_formula or '1',
                'condition': mapping.condition,
                'sequence': mapping.sequence,
                'is_legacy': False,
                'mapping_type': mapping.mapping_type,
                'mapping_obj': mapping
            })

        # Sort by sequence
        mappings.sort(key=lambda x: x['sequence'])
        return mappings

    @api.depends('visibility_condition', 'visibility_condition_ids')
    def _compute_condition_display_text(self):
        """Compute user-friendly condition display text"""
        for option in self:
            if not option.visibility_condition or option.visibility_condition == 'true':
                option.condition_display_text = ''
                continue

            # Check if it's a JSON condition (multiple conditions)
            if option.visibility_condition.startswith('__JSON__'):
                try:
                    import json
                    json_str = option.visibility_condition[8:]  # Remove __JSON__ prefix
                    conditions = json.loads(json_str)

                    if len(conditions) == 1:
                        # Single condition
                        option.condition_display_text = conditions[0]['condition']
                    else:
                        # Multiple conditions - create detailed summary
                        condition_details = []
                        for i, c in enumerate(conditions, 1):
                            logic_text = c['logic'].upper() if i > 1 else ""
                            condition_details.append(f"{logic_text} {c['condition']}".strip())

                        option.condition_display_text = f"Multiple conditions: {' '.join(condition_details)}"

                except (json.JSONDecodeError, KeyError):
                    # Fallback for invalid JSON
                    option.condition_display_text = option.visibility_condition
            else:
                # Single condition
                option.condition_display_text = option.visibility_condition



    def action_manage_component_mappings(self):
        """Open wizard to manage multiple component mappings"""
        self.ensure_one()

        return {
            'name': _('Manage Component Mappings'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.option.component.mapping.extended',
            'view_mode': 'list,form',
            'domain': [('option_id', '=', self.id)],
            'context': {
                'default_option_id': self.id,
                'default_sequence': (max(self.component_mapping_ids.mapped('sequence')) + 10) if self.component_mapping_ids else 10
            },
        }

    @api.constrains('quantity_formula')
    def _check_quantity_formula(self):
        for option in self:
            if option.component_product_id and option.quantity_formula:
                try:
                    # Compile the formula to check syntax
                    compile(option.quantity_formula, '<string>', 'eval')
                except Exception as e:
                    raise ValidationError(_("Invalid quantity formula: %s") % str(e))

    @api.constrains('visibility_condition')
    def _check_visibility_condition(self):
        # Bypass validation to allow any condition
        pass

    @api.model_create_multi
    def create(self, vals_list):
        """Create a new option"""
        # Auto-assign sequence if not provided
        for vals in vals_list:
            if not vals.get('sequence') and vals.get('field_id'):
                options = self.search([('field_id', '=', vals['field_id'])])
                if options:
                    vals['sequence'] = max(options.mapped('sequence')) + 10
                else:
                    vals['sequence'] = 10

        return super(ConfigMatrixOption, self).create(vals_list)

    def copy(self, default=None):
        """Copy option"""
        self.ensure_one()
        default = dict(default or {})

        # keep same name for new copy records
        # if not default.get('name'):
        #     default['name'] = _("%s (Copy)") % self.name

        # if not default.get('value'):
        #     # Generate unique value
        #     base_value = self.value
        #     i = 1
        #     while True:
        #         value = f"{base_value}_{i}"
        #         existing = self.search([
        #             ('field_id', '=', self.field_id.id),
        #             ('value', '=', value)
        #         ])
        #         if not existing:
        #             default['value'] = value
        #             break
        #         i += 1

        new_option = super(ConfigMatrixOption, self).copy(default)

        if self.visibility_condition_ids:
            for visibility in self.visibility_condition_ids:
                visibility.copy({'option_id': new_option.id})

        # Copy extended component mappings
        if self.component_mapping_ids:
            for mapping in self.component_mapping_ids:
                mapping.copy({'option_id': new_option.id})

        return new_option

    def action_test_quantity_formula(self):
        """Test quantity formula with sample values"""
        self.ensure_one()

        if not self.quantity_formula:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Formula'),
                    'message': _('This option has no quantity formula to test.'),
                    'type': 'info',
                }
            }

        # Create a new test formula wizard record
        wizard = self.env['config.matrix.test.formula.wizard'].create({
            'formula': self.quantity_formula,
            'option_id': self.id,
            'field_id': self.field_id.id,
            'template_id': self.field_id.section_id.matrix_id.id,
        })

        # Generate test values
        test_values = wizard._generate_test_values(self.quantity_formula, self.field_id.section_id.matrix_id.id)
        wizard.write({'test_values': json.dumps(test_values, indent=2)})

        return {
            'name': _('Test Quantity Formula'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.test.formula.wizard',
            'view_mode': 'form',
            'res_id': wizard.id,
            'target': 'new',
            'context': {'form_view_initial_mode': 'edit'},
        }



    def action_test_visibility_condition(self):
        """View visibility condition"""
        self.ensure_one()

        if not self.visibility_condition:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Condition'),
                    'message': _('This option has no visibility condition, so it will always be visible.'),
                    'type': 'info',
                }
            }

        return {
            'name': _('View Visibility Condition'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.test.condition.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_option_id': self.id,
                'default_condition': self.visibility_condition,
                'default_for_option': True,
            },
        }

    def action_view_option_visibility_conditions(self):
        """View all visibility conditions for this option"""
        self.ensure_one()

        return {
            'name': _('Option Visibility Conditions'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.option.visibility',
            'view_mode': 'list,form',
            'domain': [('option_id', '=', self.id)],
            'context': {'default_option_id': self.id, 'default_matrix_id': self.matrix_id.id},
        }

    def action_clear_visibility_conditions(self):
        """Clear all visibility conditions"""
        self.ensure_one()
        self.visibility_condition_ids.unlink()
        return True

    def action_open_component_builder(self):
        """Open the component builder wizard to create a quantity formula"""
        self.ensure_one()

        return {
            'name': _('Build Quantity Formula'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.component.builder',
            'view_mode': 'form',
            'context': {
                'default_option_id': self.id,
                'default_formula': self.quantity_formula or '1',
                'default_matrix_id': self.matrix_id.id,
            },
        }
