# -*- coding: utf-8 -*-

from odoo import models, fields, api, _

class ConfigMatrixSection(models.Model):
    _name = 'config.matrix.section'
    _description = 'Configuration Section'
    _order = 'sequence, id'

    name = fields.Char("Section Name", required=True)
    matrix_id = fields.Many2one('config.matrix.template', "Template", required=True, ondelete='cascade')
    sequence = fields.Integer("Sequence", default=10, help="Display order")
    description = fields.Text("Description", help="Additional information about this section")
    collapsible = fields.<PERSON><PERSON>an("Collapsible", default=True, help="Whether this section can be collapsed in the UI")
    collapsed_by_default = fields.Boolean("Collapsed by Default", default=False)
    show_header = fields.<PERSON><PERSON>an("Show Header", default=True, help="Whether to display the section name header in the configurator")

    # Fields in this section
    field_ids = fields.One2many('config.matrix.field', 'section_id', "Fields")

    # Use case visibility
    check_measure_visible = fields.Bo<PERSON>an("Check Measure: Visible", default=True,
                                        help="Whether this section is visible in the Check Measure use case")
    sales_visible = fields.<PERSON><PERSON><PERSON>("Sales: Visible", default=True,
                                help="Whether this section is visible in the Sales/Quoting use case")
    online_visible = fields.Boolean("Online: Visible", default=True,
                                 help="Whether this section is visible in the Online Sales use case")

    # Related fields
    template_state = fields.Selection(related='matrix_id.state', string="Template State")

    # Statistics
    field_count = fields.Integer("Fields", compute='_compute_field_count')

    @api.depends('field_ids')
    def _compute_field_count(self):
        for section in self:
            section.field_count = len(section.field_ids)

    def action_view_fields(self):
        """View fields in this section"""
        self.ensure_one()

        return {
            'name': _('Fields in %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.field',
            'view_mode': 'list,form',
            'domain': [('section_id', '=', self.id)],
            'context': {'default_section_id': self.id},
        }

    def get_section_data(self, active_use_case='check_measure'):
        """Get section data for API/UI based on provided use case"""
        self.ensure_one()

        # Check if we're coming from a sales order - if so, always use check_measure
        from_sale_order = self.env.context.get('from_sale_order', False)
        if from_sale_order:
            active_use_case = 'check_measure'

        # Skip this section if not visible in the current use case
        if active_use_case == 'check_measure' and not self.check_measure_visible:
            return None
        elif active_use_case == 'sales' and not self.sales_visible:
            return None
        elif active_use_case == 'online' and not self.online_visible:
            return None

        # Get field data for visible fields only
        fields_data = []
        for field in self.field_ids:
            # Pass the context to ensure we get the right use case
            field_data = field.with_context(from_sale_order=from_sale_order).get_field_data(active_use_case)
            if field_data:  # Only include visible fields
                fields_data.append(field_data)

        # Skip this section if it has no visible fields
        if not fields_data:
            return None

        return {
            'id': self.id,
            'name': self.name,
            'sequence': self.sequence,
            'description': self.description,
            'collapsible': self.collapsible,
            'collapsed_by_default': self.collapsed_by_default,
            'show_header': self.show_header,
            'fields': fields_data,
            'use_case': active_use_case
        }

    @api.model_create_multi
    def create(self, vals_list):
        """Create a new section"""
        # Auto-assign sequence if not provided
        for vals in vals_list:
            if not vals.get('sequence') and vals.get('matrix_id'):
                sections = self.search([('matrix_id', '=', vals['matrix_id'])])
                if sections:
                    vals['sequence'] = max(sections.mapped('sequence')) + 10
                else:
                    vals['sequence'] = 10

        return super(ConfigMatrixSection, self).create(vals_list)

    def copy(self, default=None):
        """Copy section with fields"""
        self.ensure_one()
        default = dict(default or {})

        # dont need to add copy name for new copy records
        # if not default.get('name'):
        #     default['name'] = _("%s (Copy)") % self.name

        # Ensure unique sequence
        if not default.get('sequence') and default.get('matrix_id'):
            sections = self.search([('matrix_id', '=', default['matrix_id'])])
            if sections:
                default['sequence'] = max(sections.mapped('sequence')) + 10

        new_section = super(ConfigMatrixSection, self).copy(default)

        # Copy fields
        for field in self.field_ids:
            field.copy({'section_id': new_section.id})

        return new_section

    def action_add_field(self):
        """Add a new field to this section"""
        self.ensure_one()

        return {
            'name': _('Add Field'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.field',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_section_id': self.id,
                'default_sequence': len(self.field_ids) * 10 + 10,
            },
        }

    def action_resequence_fields(self):
        """Resequence fields in this section"""
        self.ensure_one()

        # Sort fields by current sequence
        fields = self.field_ids.sorted(lambda f: (f.sequence, f.id))

        # Resequence from 10, with steps of 10
        sequence = 10
        for field in fields:
            field.sequence = sequence
            sequence += 10

        # Recompute question numbers for all fields in the template
        if self.matrix_id:
            all_fields = self.env['config.matrix.field'].search([
                ('matrix_id', '=', self.matrix_id.id)
            ])
            all_fields._compute_question_number()

        return True
