# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)

class ConfigMatrixComponentMappingUnified(models.Model):
    """
    Unified component mapping system that handles both field and option mappings.
    Replaces the separate field and option component mapping systems with a single,
    polymorphic model that can map components to either fields or options.
    """
    _name = 'config.matrix.component.mapping'
    _description = 'Unified Component Mapping'
    _order = 'sequence, id'

    # Polymorphic target - can point to field OR option
    target_model = fields.Selection([
        ('config.matrix.field', 'Field'),
        ('config.matrix.option', 'Option')
    ], string="Target Type", required=True, default='config.matrix.option')
    target_id = fields.Integer("Target ID", required=True)
    target_name = fields.Char("Target Name", compute='_compute_target_name', store=True)

    # Legacy compatibility fields (computed from polymorphic fields)
    field_id = fields.Many2one('config.matrix.field', "Field", compute='_compute_legacy_fields', store=True)
    option_id = fields.Many2one('config.matrix.option', "Option", compute='_compute_legacy_fields', store=True)

    # Core mapping fields
    component_product_id = fields.Many2one('product.product', "Component Product", required=False)
    quantity_formula = fields.Char("Quantity Formula", default="1",
                                   help="Python expression to calculate component quantity")
    sequence = fields.Integer("Sequence", default=10, help="Order of components")
    condition = fields.Char("Additional Condition",
                            help="Extra condition for this component (beyond target selection)")

    # Descriptive fields
    name = fields.Char("Mapping Name", help="Optional name to describe this mapping")
    notes = fields.Text("Notes", help="Additional notes about this component mapping")

    # Dynamic component mapping fields
    mapping_type = fields.Selection([
        ('static', 'Static Product'),
        ('dynamic_match', 'Dynamic Field Match')
    ], string="Mapping Type", default='static', required=True,
       help="Static: Use a specific product. Dynamic Field Match: Find product matching a value from another field.")

    base_component = fields.Char("Base Component",
                                help="Base component name to search for (e.g., 'Bug Strip 16mm')")
    reference_field = fields.Char("Reference Field",
                                help="Technical name of the field containing the value to match (e.g., 'sws_dbl_hinge_frame_colour')")

    # Enhanced user-friendly fields for dynamic matching
    reference_field_id = fields.Many2one(
        'config.matrix.field',
        string="Reference Field",
        help="Select the field whose value should be matched",
        domain="[('matrix_id', '=', matrix_id)]"
    )

    match_product_field = fields.Selection([
        ('colour', 'Color'),
        ('name', 'Product Name'),
        ('default_code', 'Internal Reference'),
        ('categ_id', 'Product Category'),
        ('reference_chain', 'Reference Chain Match'),
    ], string="Match Against", default='colour',
       help="Which product field should be matched against the reference field value")

    # Computed fields for better UX
    available_base_components = fields.Text(
        "Available Base Components",
        compute='_compute_available_base_components',
        help="List of potential base component names found in products"
    )

    preview_matching_products = fields.Text(
        "Preview Matching Products",
        compute='_compute_preview_matching_products',
        help="Preview of products that would match this configuration"
    )

    # Related fields for easier access
    matrix_id = fields.Many2one('config.matrix.template', compute='_compute_matrix_id', store=True,
                              string="Template")

    @api.depends('target_model', 'target_id')
    def _compute_target_name(self):
        """Compute the name of the target record"""
        for mapping in self:
            if mapping.target_model and mapping.target_id:
                target_record = self.env[mapping.target_model].browse(mapping.target_id)
                if target_record.exists():
                    mapping.target_name = target_record.name
                else:
                    mapping.target_name = f"Deleted {mapping.target_model.split('.')[-1]} (ID: {mapping.target_id})"
            else:
                mapping.target_name = "No Target"

    @api.depends('target_model', 'target_id')
    def _compute_legacy_fields(self):
        """Compute legacy field_id and option_id for backward compatibility"""
        for mapping in self:
            if mapping.target_model == 'config.matrix.field':
                mapping.field_id = mapping.target_id
                mapping.option_id = False
            elif mapping.target_model == 'config.matrix.option':
                mapping.option_id = mapping.target_id
                mapping.field_id = False
            else:
                mapping.field_id = False
                mapping.option_id = False

    @api.depends('target_model', 'target_id')
    def _compute_matrix_id(self):
        """Compute matrix_id from the target record"""
        for mapping in self:
            if mapping.target_model and mapping.target_id:
                target_record = self.env[mapping.target_model].browse(mapping.target_id)
                if target_record.exists():
                    if hasattr(target_record, 'matrix_id'):
                        mapping.matrix_id = target_record.matrix_id
                    elif hasattr(target_record, 'field_id') and target_record.field_id:
                        mapping.matrix_id = target_record.field_id.matrix_id
                    else:
                        mapping.matrix_id = False
                else:
                    mapping.matrix_id = False
            else:
                mapping.matrix_id = False

    @api.depends('base_component')
    def _compute_available_base_components(self):
        """Compute available base components from product names"""
        for mapping in self:
            if mapping.base_component:
                # Find products that contain the base component name
                products = self.env['product.product'].search([
                    ('name', 'ilike', mapping.base_component)
                ], limit=20)
                
                if products:
                    component_names = set()
                    for product in products:
                        # Extract potential base component names
                        name_parts = product.name.split()
                        for i in range(len(name_parts)):
                            for j in range(i+1, len(name_parts)+1):
                                component_names.add(' '.join(name_parts[i:j]))
                    
                    mapping.available_base_components = '\n'.join(sorted(component_names)[:10])
                else:
                    mapping.available_base_components = "No matching products found"
            else:
                mapping.available_base_components = "Enter a base component name to see suggestions"

    @api.depends('base_component', 'reference_field_id', 'match_product_field')
    def _compute_preview_matching_products(self):
        """Compute preview of products that would match this configuration"""
        for mapping in self:
            if mapping.mapping_type == 'dynamic_match' and mapping.base_component:
                # Find products that match the base component
                products = self.env['product.product'].search([
                    ('name', 'ilike', mapping.base_component)
                ], limit=10)
                
                if products:
                    preview_lines = []
                    for product in products:
                        preview_lines.append(f"• {product.name} (ID: {product.id})")
                    mapping.preview_matching_products = '\n'.join(preview_lines)
                else:
                    mapping.preview_matching_products = "No matching products found"
            else:
                mapping.preview_matching_products = "Configure dynamic matching to see preview"

    @api.constrains('mapping_type', 'component_product_id', 'base_component', 'reference_field', 'reference_field_id')
    def _check_mapping_type_fields(self):
        """Validate required fields based on mapping type"""
        for mapping in self:
            if mapping.mapping_type == 'static' and not mapping.component_product_id:
                raise ValidationError(_("Static mapping requires a component product"))
            elif mapping.mapping_type == 'dynamic_match':
                if not mapping.base_component:
                    raise ValidationError(_("Dynamic field mapping requires a base component"))
                # Accept either the old reference_field or new reference_field_id
                if not mapping.reference_field and not mapping.reference_field_id:
                    raise ValidationError(_("Dynamic field mapping requires a reference field"))

    @api.onchange('reference_field_id')
    def _onchange_reference_field_id(self):
        """Update reference_field when reference_field_id changes"""
        if self.reference_field_id:
            self.reference_field = self.reference_field_id.technical_name

    def action_test_dynamic_match(self):
        """Test the dynamic field matching with sample data"""
        self.ensure_one()
        if self.mapping_type != 'dynamic_match':
            return
        
        # This would implement the dynamic matching test logic
        # For now, just show a notification
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Dynamic Match Test'),
                'message': _('Dynamic matching test functionality would be implemented here.'),
                'type': 'info',
            }
        }

    # Helper methods for creating mappings
    @api.model
    def create_field_mapping(self, field_id, **kwargs):
        """Helper method to create a mapping for a field"""
        vals = {
            'target_model': 'config.matrix.field',
            'target_id': field_id,
            **kwargs
        }
        return self.create(vals)

    @api.model
    def create_option_mapping(self, option_id, **kwargs):
        """Helper method to create a mapping for an option"""
        vals = {
            'target_model': 'config.matrix.option',
            'target_id': option_id,
            **kwargs
        }
        return self.create(vals)
