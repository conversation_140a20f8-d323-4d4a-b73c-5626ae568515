# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class ConfigMatrixDependency(models.Model):
    _name = 'config.matrix.dependency'
    _description = 'Field Dependency'
    _order = 'sequence, id'
    _table = 'config_matrix_dependency'  # Explicitly set the table name

    name = fields.Char("Name", compute='_compute_name', store=True)
    sequence = fields.Integer("Sequence", default=10)

    # Relationship fields
    field_id = fields.Many2one('config.matrix.field', "Dependent Field", required=True, ondelete='cascade',
                             help="This field will be shown or hidden based on the condition")
    controlling_field_id = fields.Many2one('config.matrix.field', "Controlling Field", required=True,
                                         domain="[('matrix_id', '=', matrix_id), ('id', '!=', field_id)]",
                                         help="This field controls the visibility")
    matrix_id = fields.Many2one('config.matrix.template', related='field_id.matrix_id',
                              string="Template", store=True)

    # Condition fields
    operator = fields.Selection([
        ('==', 'Equals'),
        ('!=', 'Not Equals'),
        ('>', 'Greater Than'),
        ('<', 'Less Than'),
        ('>=', 'Greater Than or Equal'),
        ('<=', 'Less Than or Equal'),
        ('in', 'In List'),
        ('not in', 'Not In List'),
        ('contains', 'Contains'),
        ('not_contains', 'Does Not Contain'),
        ('is_set', 'Is Set (Has Value)'),
        ('is_not_set', 'Is Not Set (No Value)')
    ], string="Operator", required=True, default="==")

    value_type = fields.Selection([
        ('text', 'Text Value'),
        ('number', 'Number Value'),
        ('boolean', 'Boolean Value'),
        ('selection', 'Selection Value'),
        ('list', 'List of Values')
    ], string="Value Type", compute='_compute_value_type', store=True)

    # Value fields based on type
    text_value = fields.Char("Text Value")
    number_value = fields.Float("Number Value")
    boolean_value = fields.Boolean("Boolean Value")
    selection_value = fields.Char("Selection Value")
    list_value = fields.Char("List Values", help="Comma-separated list of values")

    # Generated condition
    condition = fields.Char("Generated Condition", compute='_compute_condition', store=True)

    @api.depends('field_id', 'controlling_field_id')
    def _compute_name(self):
        for dependency in self:
            if dependency.field_id and dependency.controlling_field_id:
                dependency.name = f"{dependency.field_id.name} depends on {dependency.controlling_field_id.name}"
            else:
                dependency.name = "New Dependency"

    @api.depends('controlling_field_id')
    def _compute_value_type(self):
        for dependency in self:
            if dependency.controlling_field_id:
                field_type = dependency.controlling_field_id.field_type
                if field_type == 'text':
                    dependency.value_type = 'text'
                elif field_type == 'number':
                    dependency.value_type = 'number'
                elif field_type == 'boolean':
                    dependency.value_type = 'boolean'
                elif field_type == 'selection':
                    dependency.value_type = 'selection'
                else:
                    dependency.value_type = 'text'
            else:
                dependency.value_type = 'text'

    @api.depends('controlling_field_id', 'operator', 'text_value', 'number_value',
                'boolean_value', 'selection_value', 'list_value')
    def _compute_condition(self):
        for dependency in self:
            if not dependency.controlling_field_id:
                dependency.condition = False
                continue

            field_name = dependency.controlling_field_id.technical_name
            operator = dependency.operator

            # Special operators that don't need a value
            if operator == 'is_set':
                dependency.condition = f"{field_name} != null && {field_name} !== ''"
                continue
            elif operator == 'is_not_set':
                dependency.condition = f"{field_name} == null || {field_name} === ''"
                continue

            # Get the appropriate value based on field type
            if dependency.value_type == 'text':
                value = f"'{dependency.text_value or ''}'"
            elif dependency.value_type == 'number':
                value = str(dependency.number_value or 0)
            elif dependency.value_type == 'boolean':
                value = str(dependency.boolean_value).lower()
            elif dependency.value_type == 'selection':
                value = f"'{dependency.selection_value or ''}'"
            elif dependency.value_type == 'list' and dependency.list_value:
                # Convert comma-separated list to array
                items = [item.strip() for item in (dependency.list_value or '').split(',')]
                quoted_items = [f"'{item}'" for item in items if item]
                value = f"[{', '.join(quoted_items)}]"
            else:
                value = "''"

            # Build the condition based on operator
            if operator in ('in', 'not in'):
                dependency.condition = f"{field_name} {operator} {value}"
            elif operator == 'contains':
                dependency.condition = f"{field_name} && {field_name}.includes({value})"
            elif operator == 'not_contains':
                dependency.condition = f"!({field_name} && {field_name}.includes({value}))"
            else:
                dependency.condition = f"{field_name} {operator} {value}"

    @api.constrains('field_id', 'controlling_field_id')
    def _check_fields(self):
        for dependency in self:
            if dependency.field_id.id == dependency.controlling_field_id.id:
                raise ValidationError(_("A field cannot depend on itself."))

            # Check for circular dependencies
            if dependency._check_circular_dependency(dependency.field_id, dependency.controlling_field_id):
                raise ValidationError(_("Circular dependency detected. This would create an infinite loop."))

    def _check_circular_dependency(self, field, controlling_field, visited=None):
        """Check if there's a circular dependency between fields"""
        if visited is None:
            visited = set()

        if field.id in visited:
            return False

        visited.add(field.id)

        # Check if the controlling field depends on the original field
        dependencies = self.search([('field_id', '=', controlling_field.id)])
        for dep in dependencies:
            if dep.controlling_field_id.id == field.id:
                return True
            if self._check_circular_dependency(field, dep.controlling_field_id, visited):
                return True

        return False

    @api.model_create_multi
    def create(self, vals_list):
        """Create a new dependency and update field's visibility condition"""
        dependencies = super(ConfigMatrixDependency, self).create(vals_list)
        for dependency in dependencies:
            dependency._update_field_visibility_condition()
        return dependencies

    def write(self, vals):
        """Update dependency and field's visibility condition"""
        result = super(ConfigMatrixDependency, self).write(vals)
        self._update_field_visibility_condition()
        return result

    def unlink(self):
        """Remove dependency and update field's visibility condition"""
        fields = self.mapped('field_id')
        result = super(ConfigMatrixDependency, self).unlink()
        for field in fields:
            self._update_field_visibility_condition(field)
        return result

    def _update_field_visibility_condition(self, field=None):
        """Update the visibility condition on the field based on all its dependencies"""
        if field is None:
            fields = self.mapped('field_id')
        else:
            fields = field

        for field in fields:
            dependencies = self.search([('field_id', '=', field.id)])

            if not dependencies:
                # If no dependencies, clear the visibility condition
                field.visibility_condition = False
                continue

            # Combine all dependency conditions with AND
            conditions = [dep.condition for dep in dependencies if dep.condition]
            if conditions:
                field.visibility_condition = " && ".join(conditions)
            else:
                field.visibility_condition = False
