# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class ConfigMatrixOperationTemplate(models.Model):
    _name = 'config.matrix.operation.template'
    _description = 'Operation Template for Configuration Matrix'
    _order = 'sequence, name'

    name = fields.Char('Operation Name', required=True, help='Name of the operation')
    sequence = fields.Integer('Sequence', default=10, help='Order of operations')
    active = fields.Bo<PERSON>an('Active', default=True)
    
    # Work Center
    workcenter_id = fields.Many2one('mrp.workcenter', 'Work Center', required=True,
                                   help='Work center where this operation is performed')
    
    # Duration Settings
    default_duration = fields.Float('Default Duration (minutes)', default=60.0,
                                   help='Default duration in minutes if no formula is provided')
    duration_formula = fields.Char('Duration Formula',
                                  help='Python expression to calculate duration dynamically. '
                                       'Leave empty to use default duration.')
    
    # Operation Details
    description = fields.Text('Description', help='Detailed description of the operation')
    
    # Worksheet Settings
    worksheet_type = fields.Selection([
        ('text', 'Text'),
        ('pdf', 'PDF'),
        ('google_slide', 'Google Slide')
    ], string='Worksheet Type', default='text')
    
    worksheet_content = fields.Text('Worksheet Content',
                                   help='Content or instructions for the worksheet')
    
    # Additional Settings
    setup_time = fields.Float('Setup Time (minutes)', default=0.0,
                             help='Time required to setup the operation')
    cleanup_time = fields.Float('Cleanup Time (minutes)', default=0.0,
                               help='Time required to cleanup after the operation')
    
    # Notes and Instructions
    notes = fields.Text('Notes', help='Additional notes or instructions for this operation')

    # Pricing Integration
    operation_price_id = fields.Many2one('config.matrix.operation.price', 'Operation Pricing',
                                        help='Fixed price table entry for this operation')

    # Usage tracking
    usage_count = fields.Integer('Usage Count', compute='_compute_usage_count', store=True,
                                help='Number of times this template has been used')
    
    # Category for organization
    category = fields.Selection([
        ('cutting', 'Cutting'),
        ('assembly', 'Assembly'),
        ('finishing', 'Finishing'),
        ('quality', 'Quality Control'),
        ('packaging', 'Packaging'),
        ('other', 'Other')
    ], string='Category', default='other', help='Category for organizing operations')
    
    @api.depends('name', 'workcenter_id')
    def _compute_display_name(self):
        for template in self:
            if template.workcenter_id:
                template.display_name = f"{template.name} ({template.workcenter_id.name})"
            else:
                template.display_name = template.name
    
    @api.depends()
    def _compute_usage_count(self):
        """Compute how many times this template is used"""
        for template in self:
            # Count field operation mappings
            field_count = self.env['config.matrix.field.operation.mapping'].search_count([
                ('operation_template_id', '=', template.id)
            ])
            # Count option operation mappings
            option_count = self.env['config.matrix.option.operation.mapping'].search_count([
                ('operation_template_id', '=', template.id)
            ])
            template.usage_count = field_count + option_count
    
    @api.constrains('default_duration')
    def _check_default_duration(self):
        for template in self:
            if template.default_duration <= 0:
                raise ValidationError(_("Default duration must be greater than 0"))
    
    @api.constrains('setup_time', 'cleanup_time')
    def _check_times(self):
        for template in self:
            if template.setup_time < 0:
                raise ValidationError(_("Setup time cannot be negative"))
            if template.cleanup_time < 0:
                raise ValidationError(_("Cleanup time cannot be negative"))
    
    def get_calculated_duration(self, config_values=None):
        """
        Calculate the duration for this operation template based on configuration values
        
        Args:
            config_values (dict): Configuration values for formula evaluation
            
        Returns:
            float: Calculated duration in minutes
        """
        self.ensure_one()
        
        if not self.duration_formula:
            return self.default_duration
        
        if not config_values:
            config_values = {}
        
        try:
            # Create safe evaluation context
            from odoo.tools.safe_eval import safe_eval
            import math
            
            ctx = dict(config_values)
            
            # Add math functions
            math_context = {
                'round': round,
                'ceil': math.ceil,
                'floor': math.floor,
                'abs': abs,
                'max': max,
                'min': min,
                'sum': sum
            }
            ctx.update(math_context)
            
            # Evaluate formula
            duration = safe_eval(self.duration_formula, ctx)
            return float(duration)
        except Exception as e:
            _logger.error(f"Error evaluating duration formula for operation template {self.name}: {str(e)}")
            return self.default_duration
    
    def get_total_duration(self, config_values=None):
        """
        Get total duration including setup and cleanup time
        
        Args:
            config_values (dict): Configuration values for formula evaluation
            
        Returns:
            float: Total duration in minutes
        """
        self.ensure_one()
        base_duration = self.get_calculated_duration(config_values)
        return base_duration + self.setup_time + self.cleanup_time
    
    def get_operation_price(self, config_values=None):
        """
        Calculate the sale price for this operation based on duration and pricing table

        Args:
            config_values (dict): Configuration values for duration calculation

        Returns:
            float: Calculated sale price
        """
        self.ensure_one()

        if not self.operation_price_id:
            return 0.0

        # Get the calculated duration
        duration = self.get_total_duration(config_values)

        # Calculate price using the pricing table
        return self.operation_price_id.get_price_for_duration(duration)

    def get_operation_cost(self, config_values=None):
        """
        Calculate the cost for this operation based on duration and pricing table

        Args:
            config_values (dict): Configuration values for duration calculation

        Returns:
            float: Calculated cost
        """
        self.ensure_one()

        if not self.operation_price_id:
            return 0.0

        # Get the calculated duration
        duration = self.get_total_duration(config_values)

        # Calculate cost using the pricing table
        return self.operation_price_id.get_cost_for_duration(duration)

    def copy_data(self, default=None):
        """Override copy to add 'Copy' to the name"""
        default = dict(default or {})
        if 'name' not in default:
            default['name'] = _("%s (Copy)") % self.name
        return super().copy_data(default)
    
    def action_view_usage(self):
        """Action to view where this template is used"""
        self.ensure_one()
        
        # Get all field mappings using this template
        field_mappings = self.env['config.matrix.field.operation.mapping'].search([
            ('operation_template_id', '=', self.id)
        ])
        
        # Get all option mappings using this template
        option_mappings = self.env['config.matrix.option.operation.mapping'].search([
            ('operation_template_id', '=', self.id)
        ])
        
        # Create a combined view or show separate views
        if field_mappings and option_mappings:
            # Show a custom view with both
            return {
                'type': 'ir.actions.act_window',
                'name': f'Usage of {self.name}',
                'view_mode': 'tree,form',
                'res_model': 'config.matrix.operation.mapping',
                'domain': [
                    '|',
                    ('field_id', 'in', [m.field_id.id for m in field_mappings]),
                    ('option_id', 'in', [m.option_id.id for m in option_mappings])
                ],
                'context': {'default_operation_template_id': self.id}
            }
        elif field_mappings:
            return {
                'type': 'ir.actions.act_window',
                'name': f'Field Mappings using {self.name}',
                'view_mode': 'tree,form',
                'res_model': 'config.matrix.field.operation.mapping',
                'domain': [('operation_template_id', '=', self.id)],
            }
        elif option_mappings:
            return {
                'type': 'ir.actions.act_window',
                'name': f'Option Mappings using {self.name}',
                'view_mode': 'tree,form',
                'res_model': 'config.matrix.option.operation.mapping',
                'domain': [('operation_template_id', '=', self.id)],
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'message': _('This operation template is not currently used anywhere.'),
                    'type': 'info',
                }
            }
