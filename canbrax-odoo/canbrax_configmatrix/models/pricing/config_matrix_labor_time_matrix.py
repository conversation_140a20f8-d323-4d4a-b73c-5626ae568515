# -*- coding: utf-8 -*-

import base64
import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class ConfigMatrixLaborTimeMatrix(models.Model):
    _name = 'config.matrix.labor.time.matrix'
    _description = 'Labor Time Matrix for Manufacturing Operations'
    _order = 'name, sequence'

    name = fields.Char("Matrix Name", required=True)
    description = fields.Text("Description")

    # Link to manufacturing operation type
    operation_type = fields.Selection([
        ('cut_frame', 'Cut Frame'),
        ('cut_mesh', 'Cut Mesh'),
        ('assembly', 'Assembly'),
        ('powder_coating', 'Powder Coating'),
        ('quality_check', 'Quality Check'),
        ('packaging', 'Packaging'),
        ('custom', 'Custom Operation')
    ], string="Operation Type", required=True)

    operation_name = fields.Char("Operation Name", help="Custom operation name if type is 'custom'")

    # Link to product category or specific product
    product_template_id = fields.Many2one(
        'product.template',
        "Product Template",
        help="Specific product this matrix applies to (optional)"
    )
    product_category_id = fields.Many2one(
        'product.category',
        "Product Category",
        help="Product category this matrix applies to (optional)"
    )

    sequence = fields.Integer("Sequence", default=10)
    active = fields.Boolean("Active", default=True)

    # Time unit
    time_unit = fields.Selection([
        ('minutes', 'Minutes'),
        ('hours', 'Hours'),
        ('seconds', 'Seconds')
    ], string="Time Unit", default='hours', required=True)

    # NEW: Matrix Category
    category_id = fields.Many2one(
        'config.matrix.category',
        string="Matrix Category",
        help="Categorize this matrix for better organization"
    )

    # Dimension ranges
    height_ranges = fields.Text(
        "Height Ranges",
        help="JSON array of height ranges [{'min': 300, 'max': 450, 'label': '450'}, ...]",
        default="[]"
    )
    width_ranges = fields.Text(
        "Width Ranges",
        help="JSON array of width ranges [{'min': 300, 'max': 450, 'label': '450'}, ...]",
        default="[]"
    )

    # Matrix data
    matrix_data = fields.Text(
        "Matrix Data",
        help="JSON object containing the labor times with keys like 'height_450_width_600': 1.5"
    )

    # Import/Export helpers
    last_imported = fields.Datetime("Last Imported")
    import_source = fields.Char("Import Source")

    @api.model
    def create_from_excel_data(self, name, operation_type, excel_data, product_template_id=None):
        """Create a labor time matrix from Excel data structure

        Args:
            name: Matrix name
            operation_type: Type of operation
            excel_data: Dict with structure:
                {
                    'heights': [450, 600, 750, 900, ...],
                    'widths': [300, 450, 600, 750, ...],
                    'times': {
                        '1200_600': 1,  # height_width: time_value
                        ...
                    }
                }
            product_template_id: Optional product template ID
        """
        # Convert heights to ranges
        heights = excel_data.get('heights', [])
        height_ranges = []
        for i, height in enumerate(heights):
            if i == 0:
                min_height = height
            else:
                min_height = heights[i-1] + 1

            height_ranges.append({
                'min': min_height if i > 0 else height,
                'max': height,
                'label': str(height)
            })

        # Convert widths to ranges
        widths = excel_data.get('widths', [])
        width_ranges = []
        for i, width in enumerate(widths):
            if i == 0:
                min_width = width
            else:
                min_width = widths[i-1] + 1

            width_ranges.append({
                'min': min_width if i > 0 else width,
                'max': width,
                'label': str(width)
            })

        # Create the matrix
        matrix = self.create({
            'name': name,
            'operation_type': operation_type,
            'product_template_id': product_template_id,
            'height_ranges': str(height_ranges),
            'width_ranges': str(width_ranges),
            'matrix_data': str(excel_data.get('times', {})),
            'last_imported': fields.Datetime.now(),
        })

        return matrix

    def action_add_height_range(self):
        """Add a new height range"""
        self.ensure_one()
        import json

        try:
            height_ranges = json.loads(self.height_ranges) if self.height_ranges else []
        except json.JSONDecodeError:
            height_ranges = []

        # Determine next range values
        if height_ranges:
            last_max = max(range_data['max'] for range_data in height_ranges)
            new_min = last_max + 1
            new_max = last_max + 200  # Default 200mm increment
        else:
            new_min = 300
            new_max = 500

        # Add new range
        new_range = {
            'min': new_min,
            'max': new_max,
            'label': str(new_max)
        }
        height_ranges.append(new_range)

        # Sort by max value
        height_ranges.sort(key=lambda x: x['max'])

        self.height_ranges = json.dumps(height_ranges)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Height Range Added',
                'message': f'Added height range {new_min}-{new_max}mm',
                'type': 'success',
            }
        }

    def action_add_width_range(self):
        """Add a new width range"""
        self.ensure_one()
        import json

        try:
            width_ranges = json.loads(self.width_ranges) if self.width_ranges else []
        except json.JSONDecodeError:
            width_ranges = []

        # Determine next range values
        if width_ranges:
            last_max = max(range_data['max'] for range_data in width_ranges)
            new_min = last_max + 1
            new_max = last_max + 200  # Default 200mm increment
        else:
            new_min = 300
            new_max = 500

        # Add new range
        new_range = {
            'min': new_min,
            'max': new_max,
            'label': str(new_max)
        }
        width_ranges.append(new_range)

        # Sort by max value
        width_ranges.sort(key=lambda x: x['max'])

        self.width_ranges = json.dumps(width_ranges)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Width Range Added',
                'message': f'Added width range {new_min}-{new_max}mm',
                'type': 'success',
            }
        }

    def action_generate_standard_ranges(self):
        """Generate standard height and width ranges"""
        self.ensure_one()
        import json

        # Standard height ranges (300mm to 2400mm in 300mm increments)
        height_ranges = []
        for i, max_height in enumerate([600, 900, 1200, 1500, 1800, 2100, 2400]):
            min_height = 300 if i == 0 else height_ranges[i-1]['max'] + 1
            height_ranges.append({
                'min': min_height,
                'max': max_height,
                'label': str(max_height)
            })

        # Standard width ranges (300mm to 2400mm in 300mm increments)
        width_ranges = []
        for i, max_width in enumerate([600, 900, 1200, 1500, 1800, 2100, 2400]):
            min_width = 300 if i == 0 else width_ranges[i-1]['max'] + 1
            width_ranges.append({
                'min': min_width,
                'max': max_width,
                'label': str(max_width)
            })

        self.height_ranges = json.dumps(height_ranges)
        self.width_ranges = json.dumps(width_ranges)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Standard Ranges Generated',
                'message': f'Generated {len(height_ranges)} height ranges and {len(width_ranges)} width ranges',
                'type': 'success',
            }
        }

    def action_open_matrix_editor(self):
        """Open the visual matrix editor for this labor time matrix"""
        self.ensure_one()

        return {
            'name': _('Labor Time Matrix Editor - %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.visual.editor',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_matrix_id': self.id,
                'default_matrix_type': 'labor',
            },
        }

    def get_labor_time_for_dimensions(self, height, width):
        """Get the labor time for given dimensions

        Args:
            height: Height in mm
            width: Width in mm

        Returns:
            float: Labor time in the matrix's time unit, or False if not found
        """
        self.ensure_one()

        import json

        try:
            height_ranges = json.loads(self.height_ranges) if self.height_ranges else []
            width_ranges = json.loads(self.width_ranges) if self.width_ranges else []
            matrix_data = json.loads(self.matrix_data) if self.matrix_data else {}
        except json.JSONDecodeError:
            _logger.error(f"Invalid JSON in labor time matrix {self.id}")
            return False

        # Find matching height range
        height_key = None
        for h_range in height_ranges:
            if h_range['min'] <= height <= h_range['max']:
                height_key = h_range['label']
                break

        # Find matching width range
        width_key = None
        for w_range in width_ranges:
            if w_range['min'] <= width <= w_range['max']:
                width_key = w_range['label']
                break

        if not height_key or not width_key:
            return False

        # Look up time in matrix data
        matrix_key = f"{height_key}_{width_key}"
        time_value = matrix_data.get(matrix_key)

        if time_value is not None:
            return float(time_value)

        return False

    def get_labor_time_in_hours(self, height, width):
        """Get labor time converted to hours

        Args:
            height: Height in mm
            width: Width in mm

        Returns:
            float: Labor time in hours, or False if not found
        """
        time_value = self.get_labor_time_for_dimensions(height, width)

        if time_value is False:
            return False

        # Convert to hours based on time unit
        if self.time_unit == 'hours':
            return time_value
        elif self.time_unit == 'minutes':
            return time_value / 60.0
        elif self.time_unit == 'seconds':
            return time_value / 3600.0

        return time_value

    def action_export_to_csv(self):
        """Export matrix to CSV format and return as download"""
        self.ensure_one()

        csv_content = self.export_to_csv()

        # Create attachment for download
        attachment = self.env['ir.attachment'].create({
            'name': f'{self.name}_labor_times.csv',
            'type': 'binary',
            'datas': base64.b64encode(csv_content.encode('utf-8')),
            'res_model': self._name,
            'res_id': self.id,
            'mimetype': 'text/csv',
        })

        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{attachment.id}?download=true',
            'target': 'self',
        }

    def export_to_csv(self):
        """Export matrix to CSV format"""
        self.ensure_one()

        import json
        import csv
        import io

        try:
            height_ranges = json.loads(self.height_ranges) if self.height_ranges else []
            width_ranges = json.loads(self.width_ranges) if self.width_ranges else []
            matrix_data = json.loads(self.matrix_data) if self.matrix_data else {}
        except json.JSONDecodeError:
            raise UserError(_("Invalid matrix data format"))

        # Create CSV content
        output = io.StringIO()
        writer = csv.writer(output)

        # Header row
        header = ['Height\\Width'] + [w_range['label'] for w_range in width_ranges]
        writer.writerow(header)

        # Data rows
        for h_range in height_ranges:
            row = [h_range['label']]
            for w_range in width_ranges:
                matrix_key = f"{h_range['label']}_{w_range['label']}"
                time_value = matrix_data.get(matrix_key, '')
                row.append(time_value)
            writer.writerow(row)

        csv_content = output.getvalue()
        output.close()

        return csv_content

    @api.model
    def import_from_csv(self, name, operation_type, csv_content, product_template_id=None):
        """Import matrix from CSV content

        Args:
            name: Matrix name
            operation_type: Type of operation
            csv_content: CSV content as string
            product_template_id: Optional product template ID
        """
        import csv
        import io

        reader = csv.reader(io.StringIO(csv_content))
        rows = list(reader)

        if len(rows) < 2:
            raise UserError(_("CSV must have at least header and one data row"))

        # Parse header
        header = rows[0]
        if len(header) < 2:
            raise UserError(_("CSV must have at least height and width columns"))

        width_labels = header[1:]  # Skip first column (Height\Width)

        # Parse data rows
        height_labels = []
        matrix_data = {}

        for row in rows[1:]:
            if len(row) < 2:
                continue

            height_label = row[0]
            height_labels.append(height_label)

            for i, time_str in enumerate(row[1:]):
                if time_str and time_str.strip():
                    try:
                        time_value = float(time_str.strip())
                        width_label = width_labels[i]
                        matrix_key = f"{height_label}_{width_label}"
                        matrix_data[matrix_key] = time_value
                    except ValueError:
                        continue

        # Create ranges (assuming labels are the max values)
        height_ranges = []
        for i, label in enumerate(height_labels):
            try:
                max_val = int(label)
                min_val = int(height_labels[i-1]) + 1 if i > 0 else max_val
                height_ranges.append({
                    'min': min_val,
                    'max': max_val,
                    'label': label
                })
            except ValueError:
                continue

        width_ranges = []
        for i, label in enumerate(width_labels):
            try:
                max_val = int(label)
                min_val = int(width_labels[i-1]) + 1 if i > 0 else max_val
                width_ranges.append({
                    'min': min_val,
                    'max': max_val,
                    'label': label
                })
            except ValueError:
                continue

        # Create matrix
        matrix = self.create({
            'name': name,
            'operation_type': operation_type,
            'product_template_id': product_template_id,
            'height_ranges': str(height_ranges),
            'width_ranges': str(width_ranges),
            'matrix_data': str(matrix_data),
            'last_imported': fields.Datetime.now(),
            'import_source': 'csv'
        })

        return matrix


class ConfigMatrixLaborTimeMatrixCell(models.Model):
    _name = 'config.matrix.labor.time.matrix.cell'
    _description = 'Labor Time Matrix Cell'
    _order = 'matrix_id, height_min, width_min'

    matrix_id = fields.Many2one('config.matrix.labor.time.matrix', "Labor Time Matrix", required=True, ondelete='cascade')

    # Dimension ranges for this cell
    height_min = fields.Integer("Height Min", required=True)
    height_max = fields.Integer("Height Max", required=True)
    width_min = fields.Integer("Width Min", required=True)
    width_max = fields.Integer("Width Max", required=True)

    # Value
    labor_time = fields.Float("Labor Time", digits=(12, 4))

    # Notes
    notes = fields.Text("Notes")

    # Display helpers
    height_range_display = fields.Char("Height Range", compute='_compute_range_display', store=True)
    width_range_display = fields.Char("Width Range", compute='_compute_range_display', store=True)

    @api.depends('height_min', 'height_max', 'width_min', 'width_max')
    def _compute_range_display(self):
        for cell in self:
            if cell.height_min == cell.height_max:
                cell.height_range_display = str(cell.height_max)
            else:
                cell.height_range_display = f"{cell.height_min}-{cell.height_max}"

            if cell.width_min == cell.width_max:
                cell.width_range_display = str(cell.width_max)
            else:
                cell.width_range_display = f"{cell.width_min}-{cell.width_max}"

    def matches_dimensions(self, height, width):
        """Check if this cell matches the given dimensions"""
        return (self.height_min <= height <= self.height_max and
                self.width_min <= width <= self.width_max)
