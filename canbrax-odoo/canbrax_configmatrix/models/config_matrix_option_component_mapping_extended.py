# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)

class ConfigMatrixOptionComponentMappingExtended(models.Model):
    """
    Extended many-to-many relationship between options and components.
    Allows a single option to map to multiple components with different formulas.
    """
    _name = 'config.matrix.option.component.mapping.extended'
    _description = 'Option Component Mapping Extended'
    _order = 'sequence, id'

    option_id = fields.Many2one('config.matrix.option', "Option", required=True, ondelete='cascade')
    component_product_id = fields.Many2one('product.product', "Component Product", required=False)
    quantity_formula = fields.Char("Quantity Formula", default="1",
                                   help="Python expression to calculate component quantity")
    sequence = fields.Integer("Sequence", default=10, help="Order of components")
    condition = fields.Char("Additional Condition",
                            help="Extra condition for this component (beyond option selection)")

    # Optional descriptive fields
    name = fields.Char("Mapping Name", help="Optional name to describe this mapping")
    notes = fields.Text("Notes", help="Additional notes about this component mapping")

    # Dynamic component mapping fields
    mapping_type = fields.Selection([
        ('static', 'Static Product'),
        ('dynamic_match', 'Dynamic Field Match')
    ], string="Mapping Type", default='static', required=True,
       help="Static: Use a specific product. Dynamic Field Match: Find product matching a value from another field.")

    base_component = fields.Char("Base Component",
                                help="Base component name to search for (e.g., 'Bug Strip 16mm')")
    reference_field = fields.Char("Reference Field",
                                help="Technical name of the field containing the value to match (e.g., 'sws_dbl_hinge_frame_colour')")

    # Enhanced user-friendly fields for dynamic matching
    reference_field_id = fields.Many2one(
        'config.matrix.field',
        string="Reference Field",
        help="Select the field whose value should be matched",
        domain="[('matrix_id', '=', matrix_id), ('id', '!=', field_id)]"
    )

    match_product_field = fields.Selection([
        ('colour', 'Color'),
        ('name', 'Product Name'),
        ('default_code', 'Internal Reference'),
        ('categ_id', 'Product Category'),
        ('reference_chain', 'Reference Chain Match'),
    ], string="Match Against", default='colour',
       help="Which product field should be matched against the reference field value")

    # Computed fields for better UX
    available_base_components = fields.Text(
        "Available Base Components",
        compute='_compute_available_base_components',
        help="List of potential base component names found in products"
    )

    preview_matching_products = fields.Text(
        "Preview Matching Products",
        compute='_compute_preview_matching_products',
        help="Preview of products that would match this configuration"
    )

    # Related fields for easier access
    option_name = fields.Char(related='option_id.name', string="Option Name", store=True)
    option_value = fields.Char(related='option_id.value', string="Option Value", store=True)
    field_id = fields.Many2one(related='option_id.field_id', string="Field", store=True)
    field_name = fields.Char(related='option_id.field_id.name', string="Field Name", store=True)
    field_technical_name = fields.Char(related='option_id.field_id.technical_name', string="Technical Name", store=True)
    matrix_id = fields.Many2one(related='option_id.field_id.matrix_id', string="Template", store=True)
    section_id = fields.Many2one(related='option_id.field_id.section_id', string="Section", store=True)

    @api.constrains('quantity_formula')
    def _check_quantity_formula(self):
        """Validate quantity formula syntax"""
        for mapping in self:
            if mapping.quantity_formula:
                try:
                    compile(mapping.quantity_formula, '<string>', 'eval')
                except Exception as e:
                    raise ValidationError(_("Invalid quantity formula in mapping %s: %s") % (mapping.name or 'Unnamed', str(e)))

    @api.constrains('condition')
    def _check_condition(self):
        """Validate additional condition syntax"""
        for mapping in self:
            if mapping.condition:
                try:
                    compile(mapping.condition, '<string>', 'eval')
                except Exception as e:
                    raise ValidationError(_("Invalid condition in mapping %s: %s") % (mapping.name or 'Unnamed', str(e)))

    @api.constrains('mapping_type', 'component_product_id', 'base_component', 'reference_field', 'reference_field_id')
    def _check_mapping_type_fields(self):
        """Validate required fields based on mapping type"""
        for mapping in self:
            if mapping.mapping_type == 'static' and not mapping.component_product_id:
                raise ValidationError(_("Static mapping requires a component product"))
            elif mapping.mapping_type == 'dynamic_match':
                if not mapping.base_component:
                    raise ValidationError(_("Dynamic field mapping requires a base component"))
                # Accept either the old reference_field or new reference_field_id
                if not mapping.reference_field and not mapping.reference_field_id:
                    raise ValidationError(_("Dynamic field mapping requires a reference field"))

    @api.depends('base_component')
    def _compute_available_base_components(self):
        """Compute available base component names from existing products"""
        for mapping in self:
            if not mapping.base_component:
                mapping.available_base_components = ""
                continue

            # Search for products that might match the base component pattern
            products = self.env['product.product'].search([
                ('name', 'ilike', mapping.base_component)
            ], limit=10)

            if products:
                component_names = []
                for product in products:
                    # Extract potential base component names
                    name_parts = product.name.split(' - ')
                    if len(name_parts) > 1:
                        base_name = name_parts[0].strip()
                        if base_name not in component_names:
                            component_names.append(base_name)

                mapping.available_base_components = "\n".join(component_names[:5])
            else:
                mapping.available_base_components = "No matching products found"

    @api.depends('base_component', 'reference_field_id', 'match_product_field')
    def _compute_preview_matching_products(self):
        """Compute preview of products that would match this configuration"""
        for mapping in self:
            if mapping.mapping_type != 'dynamic_match' or not mapping.base_component:
                mapping.preview_matching_products = ""
                continue

            # Get sample values from the reference field if it's a selection field
            sample_values = []
            if mapping.reference_field_id and mapping.reference_field_id.field_type == 'selection':
                options = mapping.reference_field_id.option_ids[:3]  # Get first 3 options
                sample_values = [opt.value for opt in options if opt.value]

            if not sample_values:
                sample_values = ["Sample Value"]

            preview_lines = []
            for sample_value in sample_values:
                # Build search domain based on match_product_field
                domain = [('name', 'ilike', mapping.base_component)]

                if mapping.match_product_field == 'colour':
                    domain.append(('colour', '=', sample_value))
                elif mapping.match_product_field == 'name':
                    domain.append(('name', 'ilike', sample_value))
                elif mapping.match_product_field == 'default_code':
                    domain.append(('default_code', '=', sample_value))
                elif mapping.match_product_field == 'reference_chain':
                    # Reference Chain Match: sample_value -> product internal reference -> component with same internal reference
                    reference_product = self.env['product.product'].search([('colour', '=', sample_value)], limit=1)
                    if reference_product and reference_product.default_code:
                        domain.append(('default_code', '=', reference_product.default_code))
                    else:
                        # No reference product found, skip this sample
                        continue

                products = self.env['product.product'].search(domain, limit=2)

                if products:
                    product_names = [p.name for p in products]
                    preview_lines.append(f"'{sample_value}' → {', '.join(product_names)}")
                else:
                    preview_lines.append(f"'{sample_value}' → No matches found")

            mapping.preview_matching_products = "\n".join(preview_lines)

    @api.onchange('reference_field_id')
    def _onchange_reference_field_id(self):
        """Sync the new reference_field_id with the old reference_field"""
        if self.reference_field_id:
            self.reference_field = self.reference_field_id.technical_name

    @api.onchange('reference_field')
    def _onchange_reference_field(self):
        """Sync the old reference_field with the new reference_field_id"""
        if self.reference_field and self.matrix_id:
            field = self.env['config.matrix.field'].search([
                ('technical_name', '=', self.reference_field),
                ('matrix_id', '=', self.matrix_id.id)
            ], limit=1)
            if field:
                self.reference_field_id = field

    def name_get(self):
        """Display name for the mapping"""
        result = []
        for mapping in self:
            if mapping.name:
                name = mapping.name
            elif mapping.mapping_type == 'dynamic_match':
                ref_field_name = mapping.reference_field_id.name if mapping.reference_field_id else mapping.reference_field
                name = f"{mapping.field_name} ({mapping.option_name}) → {mapping.base_component} (matches {ref_field_name})"
            else:
                name = f"{mapping.field_name} ({mapping.option_name}) → {mapping.component_product_id.name}"
            result.append((mapping.id, name))
        return result

    def action_test_dynamic_match(self):
        """Test the dynamic field matching with sample data"""
        self.ensure_one()

        if self.mapping_type != 'dynamic_match':
            raise ValidationError(_("This action is only available for dynamic field mappings"))

        # Get sample values from the reference field
        sample_values = []
        if self.reference_field_id and self.reference_field_id.field_type == 'selection':
            options = self.reference_field_id.option_ids[:5]
            sample_values = [(opt.value, opt.name) for opt in options if opt.value]

        if not sample_values:
            sample_values = [("Sample Value", "Sample Display Name")]

        # Test matching for each sample value
        test_results = []
        for value, display_name in sample_values:
            config_values = {
                self.reference_field or self.reference_field_id.technical_name: value
            }

            matching_product = self.find_matching_product(config_values)
            if matching_product:
                test_results.append(f"✓ '{display_name}' → {matching_product.name}")
            else:
                test_results.append(f"✗ '{display_name}' → No matching product found")

        message = "Dynamic Field Match Test Results:\n\n" + "\n".join(test_results)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Test Results'),
                'message': message,
                'type': 'info',
                'sticky': True,
            }
        }

    def find_matching_product(self, config_values):
        """
        Find a product matching the base component and reference field value from configuration

        Args:
            config_values: Dictionary of configuration values

        Returns:
            product.product record or False
        """
        self.ensure_one()

        if self.mapping_type != 'dynamic_match':
            return self.component_product_id

        # Get reference field value from configuration
        # Try both new and old field reference methods
        field_key = self.reference_field_id.technical_name if self.reference_field_id else self.reference_field
        field_value = config_values.get(field_key)

        if not field_value:
            _logger.warning(f"No value found for reference field {field_key}")
            return False

        # Build search domain based on match_product_field
        domain = [('name', 'ilike', self.base_component)]

        match_field = self.match_product_field or 'colour'  # Default to colour for backward compatibility

        if match_field == 'colour':
            # Try multiple matching strategies for colour field
            # First try exact match
            exact_match = self.env['product.product'].search([
                ('name', 'ilike', self.base_component),
                ('colour', '=', field_value)
            ], limit=1)

            if exact_match:
                _logger.info(f"Found exact colour match: {exact_match.name}")
                return exact_match

            # Try case-insensitive match
            case_insensitive_match = self.env['product.product'].search([
                ('name', 'ilike', self.base_component),
                ('colour', 'ilike', field_value)
            ], limit=1)

            if case_insensitive_match:
                _logger.info(f"Found case-insensitive colour match: {case_insensitive_match.name}")
                return case_insensitive_match

            # Try normalized matching (replace underscores with spaces, case insensitive)
            normalized_value = field_value.replace('_', ' ')
            normalized_match = self.env['product.product'].search([
                ('name', 'ilike', self.base_component),
                ('colour', 'ilike', normalized_value)
            ], limit=1)

            if normalized_match:
                _logger.info(f"Found normalized colour match: {normalized_match.name} (searched for: {normalized_value})")
                return normalized_match

            # If no match found, log available colours for debugging
            available_products = self.env['product.product'].search([('name', 'ilike', self.base_component)])
            available_colours = [p.colour for p in available_products if p.colour]
            _logger.warning(f"No colour match found for '{field_value}'. Available colours: {available_colours}")
            return False
        elif match_field == 'name':
            domain.append(('name', 'ilike', field_value))
        elif match_field == 'default_code':
            domain.append(('default_code', '=', field_value))
        elif match_field == 'categ_id':
            # For category matching, we need to handle it differently
            category = self.env['product.category'].search([('name', '=', field_value)], limit=1)
            if category:
                domain.append(('categ_id', '=', category.id))
            else:
                _logger.warning(f"No product category found with name {field_value}")
                return False
        elif match_field == 'reference_chain':
            # Reference Chain Match: field_value -> product internal reference -> component with same internal reference
            # First, find the product that has the field_value as its colour
            reference_product = self.env['product.product'].search([('colour', '=', field_value)], limit=1)
            if not reference_product or not reference_product.default_code:
                _logger.warning(f"No product found with colour '{field_value}' or product has no internal reference")
                return False

            # Now search for components with the same internal reference
            domain.append(('default_code', '=', reference_product.default_code))
            _logger.info(f"Reference chain match: {field_value} -> {reference_product.name} ({reference_product.default_code}) -> searching components")

        # For non-colour fields, use the original domain search
        products = self.env['product.product'].search(domain, limit=1)
        if not products:
            _logger.warning(f"No product found matching {self.base_component} with {match_field}={field_value}")
            return False

        _logger.info(f"Found matching product: {products[0].name} for {self.base_component} with {match_field}={field_value}")
        return products[0]

    def action_open_component_builder(self):
        """Open the component builder wizard for this mapping"""
        self.ensure_one()

        return {
            'name': _('Build Quantity Formula'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.component.builder',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_option_id': self.option_id.id,
                'default_matrix_id': self.option_id.matrix_id.id,
                'default_component_product_id': self.component_product_id.id,
                'default_quantity_formula': self.quantity_formula or '1',
                'multiple_component_mapping_id': self.id,  # Pass the mapping ID for updating
                'mapping_model': 'config.matrix.option.component.mapping.extended',  # Specify the model
            },
        }

    def copy(self, default=None):
        default = dict(default or {})
        if 'option_id' not in default and self.option_id:
            default['option_id'] = self.option_id.id
        return super(ConfigMatrixOptionComponentMappingExtended, self).copy(default)


