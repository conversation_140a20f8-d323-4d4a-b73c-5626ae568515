# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class BuilderProject(models.Model):
    _name = 'builder.project'
    _description = 'Builder Project'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    name = fields.Char("Project Name", required=True, tracking=True)
    partner_id = fields.Many2one('res.partner', "Builder", required=True, tracking=True)
    street = fields.Char("Street", tracking=True)
    street2 = fields.Char("Street 2", tracking=True)
    city = fields.Char("City", tracking=True)
    state_id = fields.Many2one('res.country.state', "State", tracking=True)
    zip = fields.Char("ZIP", tracking=True)
    country_id = fields.Many2one('res.country', "Country", tracking=True)

    # Related configurations
    configuration_ids = fields.One2many('config.matrix.configuration', 'project_id', "Configurations")
    sale_order_ids = fields.Many2many('sale.order', string="Sale Orders", compute='_compute_sale_orders', store=True)

    # Counters
    door_count = fields.Integer("Number of Doors", compute='_compute_counts', store=True)
    screen_count = fields.Integer("Number of Screens", compute='_compute_counts', store=True)
    total_count = fields.Integer("Total Items", compute='_compute_counts', store=True)

    # Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled')
    ], default='draft', string="Status", tracking=True)

    # Dates
    create_date = fields.Datetime("Created On", readonly=True)
    completion_date = fields.Date("Completion Date", tracking=True)

    # Notes
    notes = fields.Text("Notes", tracking=True)

    @api.depends('configuration_ids', 'configuration_ids.product_category')
    def _compute_counts(self):
        for project in self:
            project.door_count = len(project.configuration_ids.filtered(lambda c: c.product_category == 'door'))
            project.screen_count = len(project.configuration_ids.filtered(lambda c: c.product_category == 'screen'))
            project.total_count = len(project.configuration_ids)

    @api.depends('configuration_ids', 'configuration_ids.sale_order_line_id', 'configuration_ids.sale_order_line_id.order_id')
    def _compute_sale_orders(self):
        for project in self:
            orders = project.configuration_ids.mapped('sale_order_line_id.order_id')
            project.sale_order_ids = [(6, 0, orders.ids)]

    def action_view_configurations(self):
        """Open the configurations related to this project"""
        self.ensure_one()
        return {
            'name': _('Configurations'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.configuration',
            'view_mode': 'list,form',
            'domain': [('project_id', '=', self.id)],
            'context': {'default_project_id': self.id}
        }

    def action_view_sale_orders(self):
        """Open the sale orders related to this project"""
        self.ensure_one()
        return {
            'name': _('Sale Orders'),
            'type': 'ir.actions.act_window',
            'res_model': 'sale.order',
            'view_mode': 'list,form',
            'domain': [('id', 'in', self.sale_order_ids.ids)],
        }

    def action_set_in_progress(self):
        """Set project to In Progress"""
        self.write({'state': 'in_progress'})

    def action_set_completed(self):
        """Set project to Completed"""
        self.write({'state': 'completed'})

    def action_set_cancelled(self):
        """Set project to Cancelled"""
        self.write({'state': 'cancelled'})

    def action_reset_to_draft(self):
        """Reset project to Draft"""
        self.write({'state': 'draft'})
