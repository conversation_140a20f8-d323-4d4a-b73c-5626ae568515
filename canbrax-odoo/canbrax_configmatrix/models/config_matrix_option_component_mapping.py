# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo import tools

class ConfigMatrixOptionComponentMapping(models.Model):
    """
    Virtual model to display options with component products in the template form.
    This is a computed model that doesn't store data but provides a view
    of all options with component products across all fields in the template.
    """
    _name = 'config.matrix.option.component.mapping'
    _description = 'Option Component Mapping'
    _auto = False  # This is not a real table in the database

    id = fields.Integer("ID")
    template_id = fields.Many2one('config.matrix.template', "Template")
    section_id = fields.Many2one('config.matrix.section', "Section")
    section_name = fields.Char("Section")
    field_id = fields.Many2one('config.matrix.field', "Field")
    field_name = fields.Char("Field")
    option_id = fields.Many2one('config.matrix.option', "Option")
    option_name = fields.Char("Option")
    component_product_id = fields.Many2one('product.product', "Component Product")
    quantity_formula = fields.Char("Quantity Formula")

    def init(self):
        """Initialize the SQL view"""
        # Create or replace the view - no need to check if it exists
        self._cr.execute("""
                CREATE OR REPLACE VIEW config_matrix_option_component_mapping AS (
                    SELECT
                        o.id AS id,
                        f.matrix_id AS template_id,
                        s.id AS section_id,
                        s.name AS section_name,
                        f.id AS field_id,
                        f.name AS field_name,
                        o.id AS option_id,
                        o.name AS option_name,
                        o.component_product_id AS component_product_id,
                        o.quantity_formula AS quantity_formula
                    FROM
                        config_matrix_option o
                    JOIN
                        config_matrix_field f ON o.field_id = f.id
                    JOIN
                        config_matrix_section s ON f.section_id = s.id
                    WHERE
                        o.component_product_id IS NOT NULL
                )
            """)

    def action_view_option(self):
        """Open the option form view"""
        self.ensure_one()
        return {
            'name': _('Field Option'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.option',
            'res_id': self.option_id.id,
            'view_mode': 'form',
            'target': 'current',
        }
