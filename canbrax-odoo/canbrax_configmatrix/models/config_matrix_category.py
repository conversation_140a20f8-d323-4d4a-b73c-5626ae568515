# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import logging

_logger = logging.getLogger(__name__)

class ConfigMatrixCategory(models.Model):
    """Matrix categories to organize different types of pricing matrices"""
    _name = 'config.matrix.category'
    _description = 'Matrix Category'
    _order = 'sequence, name'

    name = fields.Char("Category Name", required=True)
    code = fields.Char("Category Code", required=True)
    description = fields.Text("Description")
    category_type = fields.Selection([
        ('door_frame', 'Door Frame'),
        ('hardware', 'Hardware'),
        ('glass_panel', 'Glass/Panel'),
        ('labor', 'Labor Operations'),
        ('custom', 'Custom Category')
    ], string="Category Type", required=True, default='custom')
    
    sequence = fields.Integer("Sequence", default=10)
    active = fields.Boolean("Active", default=True)
    
    # Matrix relationships
    price_matrix_ids = fields.One2many(
        'config.matrix.price.matrix',
        'category_id',
        string="Price Matrices"
    )
    labor_matrix_ids = fields.One2many(
        'config.matrix.labor.time.matrix', 
        'category_id',
        string="Labor Matrices"
    )
    
    # Template assignments
    template_assignment_ids = fields.One2many(
        'config.matrix.template.category.assignment',
        'category_id',
        string="Template Assignments"
    )
    
    # Statistics
    matrix_count = fields.Integer(
        "Total Matrices",
        compute='_compute_matrix_count',
        store=True
    )
    template_count = fields.Integer(
        "Templates Using",
        compute='_compute_template_count',
        store=True
    )
    
    # Configuration
    is_required = fields.Boolean(
        "Required for Templates", 
        default=False,
        help="If checked, templates must have at least one matrix from this category"
    )
    allow_multiple = fields.Boolean(
        "Allow Multiple Matrices",
        default=True,
        help="Allow multiple matrices of this category per template"
    )
    
    _sql_constraints = [
        ('code_uniq', 'unique (code)', 'Category code must be unique!')
    ]
    
    @api.depends('price_matrix_ids', 'labor_matrix_ids')
    def _compute_matrix_count(self):
        for category in self:
            category.matrix_count = len(category.price_matrix_ids) + len(category.labor_matrix_ids)
    
    @api.depends('template_assignment_ids')
    def _compute_template_count(self):
        for category in self:
            category.template_count = len(category.template_assignment_ids.mapped('template_id'))
    
    def action_view_matrices(self):
        """View all matrices in this category"""
        self.ensure_one()
        
        # Combine price and labor matrices
        price_matrix_ids = self.price_matrix_ids.ids
        labor_matrix_ids = self.labor_matrix_ids.ids
        
        if not price_matrix_ids and not labor_matrix_ids:
            raise UserError(_("No matrices found in this category."))
        
        # Return action based on what matrices we have
        if price_matrix_ids and not labor_matrix_ids:
            return {
                'name': _('Price Matrices - %s') % self.name,
                'type': 'ir.actions.act_window',
                'res_model': 'config.matrix.price.matrix',
                'view_mode': 'list,form',
                'domain': [('id', 'in', price_matrix_ids)],
                'context': {'default_category_id': self.id},
            }
        elif labor_matrix_ids and not price_matrix_ids:
            return {
                'name': _('Labor Matrices - %s') % self.name, 
                'type': 'ir.actions.act_window',
                'res_model': 'config.matrix.labor.time.matrix',
                'view_mode': 'list,form',
                'domain': [('id', 'in', labor_matrix_ids)],
                'context': {'default_category_id': self.id},
            }
        else:
            # Mixed - show a selection wizard
            return {
                'name': _('Matrices - %s') % self.name,
                'type': 'ir.actions.act_window',
                'res_model': 'config.matrix.category.matrix.wizard',
                'view_mode': 'form',
                'target': 'new',
                'context': {
                    'default_category_id': self.id,
                    'price_matrix_ids': price_matrix_ids,
                    'labor_matrix_ids': labor_matrix_ids,
                },
            }


class ConfigMatrixTemplateCategoryAssignment(models.Model):
    """Assignment of matrix categories to templates"""
    _name = 'config.matrix.template.category.assignment'
    _description = 'Template Category Assignment'
    _order = 'sequence, category_id'
    
    template_id = fields.Many2one(
        'config.matrix.template',
        string="Template",
        required=True,
        ondelete='cascade'
    )
    category_id = fields.Many2one(
        'config.matrix.category',
        string="Matrix Category", 
        required=True,
        ondelete='cascade'
    )
    
    sequence = fields.Integer("Sequence", default=10)
    active = fields.Boolean("Active", default=True)
    
    # Configuration for this assignment
    is_required = fields.Boolean(
        "Required",
        related='category_id.is_required',
        readonly=True
    )
    max_matrices = fields.Integer(
        "Maximum Matrices",
        default=0,
        help="0 = unlimited, otherwise maximum number of matrices allowed from this category"
    )
    
    # Quick access to matrices - computed fields instead of related
    available_price_matrix_count = fields.Integer(
        "Available Price Matrices",
        compute='_compute_available_matrices'
    )
    available_labor_matrix_count = fields.Integer(
        "Available Labor Matrices",
        compute='_compute_available_matrices'
    )
    
    # Selected matrices for this template
    selected_price_matrix_ids = fields.Many2many(
        'config.matrix.price.matrix',
        'tpl_cat_price_rel',
        'assignment_id', 'matrix_id',
        string="Selected Price Matrices"
    )
    selected_labor_matrix_ids = fields.Many2many(
        'config.matrix.labor.time.matrix',
        'tpl_cat_labor_rel', 
        'assignment_id', 'matrix_id',
        string="Selected Labor Matrices"
    )
    
    # Statistics
    selected_matrix_count = fields.Integer(
        "Selected Matrices",
        compute='_compute_selected_count',
        store=True
    )
    
    _sql_constraints = [
        ('template_category_uniq', 'unique (template_id, category_id)', 
         'Each category can only be assigned once per template!')
    ]
    
    @api.onchange('category_id')
    def _onchange_category_id(self):
        """Filter available matrices when category changes"""
        if self.category_id:
            # Clear current selections when category changes
            self.selected_price_matrix_ids = [(5, 0, 0)]
            self.selected_labor_matrix_ids = [(5, 0, 0)]
            
            # Set domain for matrix selection
            price_domain = [('category_id', '=', self.category_id.id)]
            labor_domain = [('category_id', '=', self.category_id.id)]
            
            return {
                'domain': {
                    'selected_price_matrix_ids': price_domain,
                    'selected_labor_matrix_ids': labor_domain
                }
            }
        else:
            return {
                'domain': {
                    'selected_price_matrix_ids': [],
                    'selected_labor_matrix_ids': []
                }
            }
    
    @api.depends('selected_price_matrix_ids', 'selected_labor_matrix_ids')
    def _compute_selected_count(self):
        for assignment in self:
            assignment.selected_matrix_count = (
                len(assignment.selected_price_matrix_ids) + 
                len(assignment.selected_labor_matrix_ids)
            )
    
    @api.depends('category_id', 'category_id.price_matrix_ids', 'category_id.labor_matrix_ids')
    def _compute_available_matrices(self):
        """Compute count of available matrices in the category"""
        for assignment in self:
            if assignment.category_id:
                assignment.available_price_matrix_count = len(assignment.category_id.price_matrix_ids)
                assignment.available_labor_matrix_count = len(assignment.category_id.labor_matrix_ids)
            else:
                assignment.available_price_matrix_count = 0
                assignment.available_labor_matrix_count = 0
    
    @api.constrains('selected_price_matrix_ids', 'selected_labor_matrix_ids', 'max_matrices')
    def _check_max_matrices(self):
        for assignment in self:
            if assignment.max_matrices > 0:
                total_selected = len(assignment.selected_price_matrix_ids) + len(assignment.selected_labor_matrix_ids)
                if total_selected > assignment.max_matrices:
                    raise ValidationError(
                        _("Too many matrices selected for category '%s'. Maximum allowed: %d") % 
                        (assignment.category_id.name, assignment.max_matrices)
                    )
    
    def action_configure_matrices(self):
        """Open wizard to configure matrices for this assignment"""
        self.ensure_one()
        
        return {
            'name': _('Configure Matrices - %s') % self.category_id.name,
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.template.category.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_assignment_id': self.id,
                'default_template_id': self.template_id.id,
                'default_category_id': self.category_id.id,
            },
        }


class ConfigMatrixCategoryMatrixWizard(models.TransientModel):
    """Wizard to view matrices in a category"""
    _name = 'config.matrix.category.matrix.wizard'
    _description = 'Category Matrix Viewer'
    
    category_id = fields.Many2one('config.matrix.category', string="Category", readonly=True)
    matrix_type = fields.Selection([
        ('price', 'Price Matrices'),
        ('labor', 'Labor Matrices'),
        ('both', 'All Matrices')
    ], string="View", default='both')
    
    def action_view_price_matrices(self):
        """View price matrices"""
        self.ensure_one()
        price_matrix_ids = self.env.context.get('price_matrix_ids', [])
        
        return {
            'name': _('Price Matrices - %s') % self.category_id.name,
            'type': 'ir.actions.act_window', 
            'res_model': 'config.matrix.price.matrix',
            'view_mode': 'list,form',
            'domain': [('id', 'in', price_matrix_ids)],
            'target': 'current',
        }
    
    def action_view_labor_matrices(self):
        """View labor matrices"""
        self.ensure_one()
        labor_matrix_ids = self.env.context.get('labor_matrix_ids', [])
        
        return {
            'name': _('Labor Matrices - %s') % self.category_id.name,
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.labor.time.matrix', 
            'view_mode': 'list,form',
            'domain': [('id', 'in', labor_matrix_ids)],
            'target': 'current',
        }


class ConfigMatrixTemplateCategoryWizard(models.TransientModel):
    """Wizard to configure matrices for a template category assignment"""
    _name = 'config.matrix.template.category.wizard'
    _description = 'Template Category Configuration Wizard'
    
    assignment_id = fields.Many2one(
        'config.matrix.template.category.assignment',
        string="Assignment",
        required=True
    )
    template_id = fields.Many2one(
        'config.matrix.template',
        string="Template",
        related='assignment_id.template_id',
        readonly=True
    )
    category_id = fields.Many2one(
        'config.matrix.category',
        string="Category",
        related='assignment_id.category_id', 
        readonly=True
    )
    
    # Matrix selection
    price_matrix_ids = fields.Many2many(
        'config.matrix.price.matrix',
        'wiz_price_rel',
        'wizard_id', 'matrix_id',
        string="Price Matrices"
    )
    labor_matrix_ids = fields.Many2many(
        'config.matrix.labor.time.matrix',
        'wiz_labor_rel',
        'wizard_id', 'matrix_id',
        string="Labor Matrices"
    )
    
    @api.model
    def default_get(self, fields_list):
        """Load current matrix selections"""
        res = super().default_get(fields_list)
        
        if 'assignment_id' in res:
            assignment = self.env['config.matrix.template.category.assignment'].browse(res['assignment_id'])
            res['price_matrix_ids'] = [(6, 0, assignment.selected_price_matrix_ids.ids)]
            res['labor_matrix_ids'] = [(6, 0, assignment.selected_labor_matrix_ids.ids)]
        
        return res
    
    @api.onchange('category_id')
    def _onchange_category_id(self):
        """Filter available matrices based on selected category"""
        if self.category_id:
            # Filter price matrices
            price_domain = [('category_id', '=', self.category_id.id)]
            # Filter labor matrices  
            labor_domain = [('category_id', '=', self.category_id.id)]
            
            return {
                'domain': {
                    'price_matrix_ids': price_domain,
                    'labor_matrix_ids': labor_domain
                }
            }
        else:
            return {
                'domain': {
                    'price_matrix_ids': [],
                    'labor_matrix_ids': []
                }
            }
    
    def action_save_configuration(self):
        """Save the matrix configuration"""
        self.ensure_one()
        
        # Update the assignment
        self.assignment_id.write({
            'selected_price_matrix_ids': [(6, 0, self.price_matrix_ids.ids)],
            'selected_labor_matrix_ids': [(6, 0, self.labor_matrix_ids.ids)],
        })
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Configuration Saved'),
                'message': _('Matrix configuration updated for category "%s"') % self.category_id.name,
                'type': 'success',
            }
        }
