# -*- coding: utf-8 -*-

def uninstall_hook(env):
    """Remove programmatically created menu items"""

    # Find and remove the Dependencies menu under Matrix
    dependencies_menu = env['ir.ui.menu'].search([
        ('name', '=', 'Dependencies'),
        ('parent_id', '=', env.ref('canbrax_configmatrix.menu_config_matrix_root').id)
    ])

    if dependencies_menu:
        dependencies_menu.unlink()
        print("Dependencies menu under Matrix removed successfully")
    else:
        print("Dependencies menu under Matrix not found")

    # Find and remove the Dependencies root menu if it exists
    root_dependencies_menu = env['ir.ui.menu'].search([
        ('name', '=', 'Dependencies'),
        ('parent_id', '=', False)
    ])

    if root_dependencies_menu:
        root_dependencies_menu.unlink()
        print("Dependencies root menu removed successfully")
    else:
        print("Dependencies root menu not found")

def post_init_hook(env):
    """Remove the Dependencies menu after module installation"""

    # Find and remove the Dependencies menu under Matrix
    dependencies_menu = env['ir.ui.menu'].search([
        ('name', '=', 'Dependencies'),
        ('parent_id', '=', env.ref('canbrax_configmatrix.menu_config_matrix_root').id)
    ])

    if dependencies_menu:
        dependencies_menu.unlink()
        print("Dependencies menu under Matrix removed successfully")
    else:
        print("Dependencies menu under Matrix not found")

    # Find and remove the Dependencies root menu if it exists
    root_dependencies_menu = env['ir.ui.menu'].search([
        ('name', '=', 'Dependencies'),
        ('parent_id', '=', False)
    ])

    if root_dependencies_menu:
        root_dependencies_menu.unlink()
        print("Dependencies root menu removed successfully")
    else:
        print("Dependencies root menu not found")
