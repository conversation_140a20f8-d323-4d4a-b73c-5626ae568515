# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import timedelta
import logging

_logger = logging.getLogger(__name__)

class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    is_configurable = fields.Boolean(related='product_id.product_tmpl_id.is_configurable',
                                    readonly=True, string="Configurable")
    config_id = fields.Many2one('config.matrix.configuration', "Configuration",
                              copy=False, ondelete="set null")
    is_configured = fields.Boolean(compute='_compute_is_configured', store=True,
                                 string="Configured")
    configuration_summary = fields.Text("Configuration Summary", compute='_compute_configuration_summary', store=True)

    # Fields for tracking related configurations
    original_config_line_id = fields.Many2one('sale.order.line', "Original Configuration Line",
                                           copy=False, ondelete="set null",
                                           help="Reference to the original line this was split from")
    related_config_line_ids = fields.One2many('sale.order.line', 'original_config_line_id',
                                           string="Related Configuration Lines",
                                           help="Lines that were split from this configuration")

    @api.depends('config_id')
    def _compute_is_configured(self):
        for line in self:
            line.is_configured = bool(line.config_id)

    @api.depends('config_id')
    def _compute_configuration_summary(self):
        for line in self:
            if line.config_id:
                line.configuration_summary = line.config_id.get_configuration_summary()
            else:
                line.configuration_summary = ""

    def action_configure_product(self):
        """Open the product configurator for this order line"""
        self.ensure_one()

        # Check if product is configurable
        if not self.is_configurable:
            raise UserError(_("This product is not configurable."))

        # Get configuration matrix
        matrix = self.product_id.product_tmpl_id.matrix_id
        if not matrix:
            raise UserError(_("No configuration matrix found for this product."))

        # Add debug logging
        _logger = logging.getLogger(__name__)
        _logger.info(f"Configuring product: {self.product_id.name}, Matrix ID: {matrix.id}")

        # Create URL parameters
        params = {
            'product_id': self.product_id.id,
            'order_line_id': self.id,
            'matrix_id': matrix.id,
            'template_id': matrix.id,
            'config_id': self.config_id.id if self.config_id else False
        }

        # Log parameters for debugging
        _logger.info(f"Configuration parameters: {params}")

        # Build URL with parameters
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        url_params = '&'.join([f"{key}={value}" for key, value in params.items() if value])
        configurator_url = f"{base_url}/config_matrix/configurator?{url_params}"

        _logger.info(f"Opening configurator URL: {configurator_url}")

        return {
            'type': 'ir.actions.act_url',
            'url': configurator_url,
            'target': 'self',
        }

    def action_view_configuration(self):
        """View configuration details"""
        self.ensure_one()

        if not self.config_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Not Configured'),
                    'message': _('This product has not been configured yet.'),
                    'type': 'warning',
                }
            }

        return {
            'name': _('Configuration'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.configuration',
            'view_mode': 'form',
            'res_id': self.config_id.id,
        }

    @api.onchange('product_id')
    def _onchange_product_id_warning(self):
        """Override to reset configuration when product changes"""
        result = super(SaleOrderLine, self)._onchange_product_id_warning()

        # Reset configuration
        self.config_id = False

        return result

    def _get_product_attributes_values_dict(self):
        """Override to add configuration ID to product attributes"""
        result = super(SaleOrderLine, self)._get_product_attributes_values_dict()

        # For configurable products, add config_id to ensure each configuration is treated as unique
        if self.is_configurable:
            # Add a unique identifier to prevent merging lines
            # Use config_id if available, otherwise use line id to ensure uniqueness
            unique_id = self.config_id.id if self.config_id else f"new_{self.id}"
            result['unique_config_id'] = unique_id

        return result

    def _is_line_mergeable(self):
        """Override to prevent merging configurable products"""
        if self.is_configurable:
            return False
        return super(SaleOrderLine, self)._is_line_mergeable()

    def _prepare_invoice_line(self, **optional_values):
        """Add configuration information to invoice line"""
        res = super(SaleOrderLine, self)._prepare_invoice_line(**optional_values)

        if self.config_id:
            # Add configuration summary to description
            if self.configuration_summary:
                if res.get('name'):
                    res['name'] = f"{res['name']}\n\n{self.configuration_summary}"
                else:
                    res['name'] = self.configuration_summary

            # Add configuration reference
            res['config_id'] = self.config_id.id

        return res

    def _has_mto_and_manufacture_routes(self):
        """
        Check if the product on this sale order line has both MTO and Manufacturing routes.

        Returns:
            bool: True if product has both stock.route_warehouse0_mto and mrp.route_warehouse0_manufacture routes
        """
        self.ensure_one()

        if not self.product_id:
            return False

        # Get the route references
        try:
            mto_route = self.env.ref('stock.route_warehouse0_mto', raise_if_not_found=False)
            manufacture_route = self.env.ref('mrp.route_warehouse0_manufacture', raise_if_not_found=False)

            if not mto_route or not manufacture_route:
                _logger.warning("Could not find MTO or Manufacturing routes")
                return False

            # Check if product has both routes
            product_routes = self.product_id.route_ids | self.product_id.categ_id.total_route_ids

            has_mto = mto_route in product_routes
            has_manufacture = manufacture_route in product_routes

            _logger.debug(f"Product {self.product_id.name} - MTO: {has_mto}, Manufacture: {has_manufacture}")

            return has_mto and has_manufacture

        except Exception as e:
            _logger.error(f"Error checking routes for product {self.product_id.name}: {str(e)}")
            return False

    def _requires_configured_bom(self):
        """
        Check if this sale order line requires a configured BOM for manufacturing.

        Returns:
            bool: True if this is a configurable product with MTO+Manufacturing routes
        """
        self.ensure_one()

        return (
            self.is_configurable and
            self._has_mto_and_manufacture_routes()
        )

    def _validate_configured_bom(self):
        """
        Validate that configurable products with MTO+Manufacturing routes have a valid BOM.

        Raises:
            ValidationError: If configurable product lacks required BOM
        """
        self.ensure_one()

        if not self._requires_configured_bom():
            return True

        if not self.config_id:
            raise ValidationError(self.env._(
                "Product '%s' on line %s is configurable and requires both MTO and Manufacturing routes, "
                "but no configuration has been created. Please configure the product before confirming the order."
            ) % (self.product_id.name, self.id))

        if not self.config_id.bom_id:
            raise ValidationError(self.env._(
                "Product '%s' on line %s is configured but no Bill of Materials (BOM) has been generated. "
                "The BOM is required for manufacturing. Please ensure the configuration is complete and try again."
            ) % (self.product_id.name, self.id))

        _logger.info(f"Validated BOM for configured product {self.product_id.name} - BOM ID: {self.config_id.bom_id.id}")
        return True

    def _prepare_procurement_values(self, group_id=False):
        """
        Override to add configuration BOM to procurement values.

        This method extends the standard procurement values to include the BOM ID
        from the configuration when available. This ensures that Manufacturing Orders
        created from configured products use the correct BOM.

        Args:
            group_id: Procurement group ID

        Returns:
            dict: Procurement values including bom_id when available
        """
        # Call the sale_stock version of _prepare_procurement_values
        values = super(SaleOrderLine, self)._prepare_procurement_values(group_id)

        # If we don't have the sale_stock fields, add them manually
        if 'sale_line_id' not in values:
            self.ensure_one()
            # Use the delivery date if there is else use date_order and lead time
            date_deadline = self.order_id.commitment_date or self._expected_date()
            date_planned = date_deadline - timedelta(days=self.order_id.company_id.security_lead)
            values.update({
                'group_id': group_id,
                'sale_line_id': self.id,
                'date_planned': date_planned,
                'date_deadline': date_deadline,
                'route_ids': self.route_id,
                'warehouse_id': self.warehouse_id,
                'partner_id': self.order_id.partner_shipping_id.id,
                'location_final_id': self._get_location_final(),
                'product_description_variants': self.with_context(lang=self.order_id.partner_id.lang)._get_sale_order_line_multiline_description_variants(),
                'company_id': self.order_id.company_id,
                'product_packaging_id': self.product_packaging_id,
                'sequence': self.sequence,
                'never_product_template_attribute_value_ids': self.product_no_variant_attribute_value_ids,
            })

        # Add configuration BOM if this is a configurable product with a BOM
        if self.is_configurable and self.config_id and self.config_id.bom_id:
            values['bom_id'] = self.config_id.bom_id
            _logger.info(f"Added BOM {self.config_id.bom_id.id} to procurement values for configured product {self.product_id.name}")

        return values

    def _action_launch_stock_rule(self, previous_product_uom_qty=False):
        """
        Override to handle configurable products with storable type.

        The standard Odoo _action_launch_stock_rule only processes products with type 'consu',
        but configurable products that need manufacturing are typically 'product' (storable) type.
        This override ensures that configurable products with MTO+Manufacturing routes
        trigger procurement even if they are storable products.
        """
        # First, handle configurable storable products
        configurable_storable_lines = self.filtered(
            lambda line: line.product_id.type == 'product' and
                        line.is_configurable and
                        line._requires_configured_bom()
        )

        if configurable_storable_lines:
            # Temporarily change product type to 'consu' so standard method processes them
            original_types = {}
            for line in configurable_storable_lines:
                original_types[line.product_id.id] = line.product_id.type
                line.product_id.type = 'consu'

            try:
                # Call the standard method
                result = super(SaleOrderLine, self)._action_launch_stock_rule(previous_product_uom_qty)
            finally:
                # Restore original product types
                for line in configurable_storable_lines:
                    line.product_id.type = original_types[line.product_id.id]

            return result
        else:
            # For non-configurable products, use standard behavior
            return super(SaleOrderLine, self)._action_launch_stock_rule(previous_product_uom_qty)



