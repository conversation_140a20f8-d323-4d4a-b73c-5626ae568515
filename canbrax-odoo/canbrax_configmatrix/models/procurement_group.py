# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
import logging

_logger = logging.getLogger(__name__)


class ProcurementGroup(models.Model):
    _inherit = 'procurement.group'

    @api.model
    def run(self, procurements, raise_user_error=True):
        """
        Override to add comprehensive logging for procurement processing.
        
        This method adds detailed logging to track how procurements are processed,
        which rules are selected, and why Manufacturing Orders might not be created.
        """
        _logger.info("🚀 CANBRAX: procurement.group.run called with %d procurements", len(procurements))
        
        # Log each procurement
        for i, procurement in enumerate(procurements):
            _logger.info("🔍 CANBRAX: Procurement %d - Product: %s, Qty: %s, Location: %s", 
                        i+1, procurement.product_id.name, procurement.product_qty, procurement.location_id.name)
            _logger.info("📋 CANBRAX: Procurement %d values: %s", i+1,
                        {k: v for k, v in procurement.values.items() if k in ['bom_id', 'sale_line_id', 'route_ids', 'warehouse_id']})
            
            # Log product routes
            product_routes = procurement.product_id.route_ids | procurement.product_id.categ_id.total_route_ids
            _logger.info("🛣️ CANBRAX: Procurement %d product routes: %s", i+1, [r.name for r in product_routes])
            
            # Log procurement routes
            proc_routes = procurement.values.get('route_ids', self.env['stock.route'])
            _logger.info("🛣️ CANBRAX: Procurement %d procurement routes: %s", i+1, [r.name for r in proc_routes])
        
        # Call parent method
        _logger.info("📞 CANBRAX: Calling parent procurement.group.run")
        result = super(ProcurementGroup, self).run(procurements, raise_user_error)
        _logger.info("✅ CANBRAX: procurement.group.run completed")
        
        return result

    def _get_rule(self, product_id, location_id, values):
        """
        Override to add logging for rule selection.
        """
        _logger.info("🔍 CANBRAX: _get_rule called for product %s at location %s", 
                    product_id.name, location_id.name)
        
        # Log the routes being considered
        route_ids = values.get('route_ids', self.env['stock.route'])
        if not route_ids:
            route_ids = product_id.route_ids | product_id.categ_id.total_route_ids
        
        _logger.info("🛣️ CANBRAX: Routes being considered: %s", [r.name for r in route_ids])
        
        # Call parent method
        rule = super(ProcurementGroup, self)._get_rule(product_id, location_id, values)
        
        if rule:
            _logger.info("✅ CANBRAX: Selected rule: %s (Action: %s, Route: %s, Sequence: %s)", 
                        rule.name, rule.action, rule.route_id.name, rule.sequence)
        else:
            _logger.warning("❌ CANBRAX: No rule found for product %s at location %s", 
                           product_id.name, location_id.name)
        
        return rule

    def _run_scheduler_tasks(self, use_new_cursor=False, company_id=False):
        """
        Override to add logging for scheduler tasks.
        """
        _logger.info("🔄 CANBRAX: _run_scheduler_tasks called")
        result = super(ProcurementGroup, self)._run_scheduler_tasks(use_new_cursor, company_id)
        _logger.info("✅ CANBRAX: _run_scheduler_tasks completed")
        return result
