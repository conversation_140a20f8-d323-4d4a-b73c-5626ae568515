# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
import logging

_logger = logging.getLogger(__name__)

# the process create mo is complicated so i need these to add log incase of errors, this doesn't affect the process

class ProcurementGroup(models.Model):
    _inherit = 'procurement.group'

    @api.model
    def run(self, procurements, raise_user_error=True):
        """
        Override to process procurements.
        """
        result = super(ProcurementGroup, self).run(procurements, raise_user_error)
        return result

    def _get_rule(self, product_id, location_id, values):
        """
        Override to select rule.
        """
        route_ids = values.get('route_ids', self.env['stock.route'])
        if not route_ids:
            route_ids = product_id.route_ids | product_id.categ_id.total_route_ids

        rule = super(ProcurementGroup, self)._get_rule(product_id, location_id, values)
        return rule

    def _run_scheduler_tasks(self, use_new_cursor=False, company_id=False):
        """
        Override to run scheduler tasks.
        """
        result = super(ProcurementGroup, self)._run_scheduler_tasks(use_new_cursor, company_id)
        return result
