# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
import re
from markupsafe import Markup

class ConfigMatrixSvgComponent(models.Model):
    _name = 'config.matrix.svg.component'
    _description = 'SVG Component for Product Visualization'
    _order = 'z_index'

    name = fields.Char("Component Name", required=True)
    template_id = fields.Many2one('config.matrix.template', "Template", required=True, ondelete='cascade')

    # Component type
    component_type = fields.Selection([
        ('base', 'Base SVG (Background)'),
        ('layer', 'SVG Layer (Conditional)'),
    ], required=True, default='layer')

    # SVG content
    svg_content = fields.Text("SVG Content", required=True,
        help="SVG markup for this component. Use ${field_name} for values from configuration.")

    # Visibility condition
    condition = fields.Char("Visibility Condition",
        help="JavaScript condition that determines if this component is included. Leave empty for always visible.")

    # Positioning and styling
    z_index = fields.Integer("Z-Index", default=10,
        help="Display order - higher numbers are displayed on top")

    # SVG Previews
    svg_preview = fields.Html("SVG Preview", compute='_compute_svg_preview', sanitize=False)
    combined_preview = fields.Html("Combined Preview", compute='_compute_combined_preview', sanitize=False)

    @api.model
    def create(self, vals):
        """Clean SVG content before saving"""
        if 'svg_content' in vals:
            vals['svg_content'] = self._clean_svg_content(vals['svg_content'])
        return super(ConfigMatrixSvgComponent, self).create(vals)

    def write(self, vals):
        """Clean SVG content before saving"""
        if 'svg_content' in vals:
            vals['svg_content'] = self._clean_svg_content(vals['svg_content'])
        return super(ConfigMatrixSvgComponent, self).write(vals)

    def _clean_svg_content(self, content):
        """Clean SVG content to ensure it's valid and safe"""
        if not content:
            return content

        # Remove XML declaration if present
        content = re.sub(r'<\?xml[^>]*\?>', '', content)

        # Remove DOCTYPE if present
        content = re.sub(r'<!DOCTYPE[^>]*>', '', content)

        # Ensure SVG tag has proper namespace
        if '<svg' in content and 'xmlns=' not in content:
            content = content.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"')

        # Remove comments
        content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)

        return content

    @api.depends('svg_content', 'component_type')
    def _compute_svg_preview(self):
        """Generate a preview of the SVG component"""
        for component in self:
            if not component.svg_content:
                component.svg_preview = '<div class="alert alert-warning">No SVG content</div>'
                continue

            try:
                content = component.svg_content

                # For layer components, wrap in an SVG tag for preview
                if component.component_type == 'layer':
                    content = f'''
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 600" width="100%" height="100%">
                        {content}
                    </svg>
                    '''

                # Set the preview - use Markup to prevent HTML escaping
                component.svg_preview = Markup(f'''
                <div style="display: flex; justify-content: center; align-items: center; height: 100%;">
                    {content}
                </div>
                ''')
            except Exception as e:
                component.svg_preview = f'<div class="alert alert-danger">Error rendering SVG: {str(e)}</div>'

    @api.depends('svg_content', 'component_type', 'template_id', 'z_index')
    def _compute_combined_preview(self):
        """Generate a preview of the SVG component combined with other components"""
        for component in self:
            if not component.svg_content or not component.template_id:
                component.combined_preview = '<div class="alert alert-warning">No SVG content or template</div>'
                continue

            try:
                # Find the base SVG component
                base_component = self.search([
                    ('template_id', '=', component.template_id.id),
                    ('component_type', '=', 'base')
                ], limit=1)

                if not base_component:
                    component.combined_preview = '<div class="alert alert-warning">No base SVG component found</div>'
                    continue

                # Start with the base SVG
                base_content = base_component.svg_content

                # Find all layer components sorted by z-index (smaller to larger)
                layer_components = self.search([
                    ('template_id', '=', component.template_id.id),
                    ('component_type', '=', 'layer'),
                    ('id', '!=', component.id)  # Exclude current component
                ], order='z_index')

                # Build the combined SVG
                combined_content = base_content

                # Add layers in order of z-index (smaller to larger)
                current_added = False

                for layer in layer_components:
                    # If we reach a layer with higher z-index than current component, add current component first
                    if component.component_type == 'layer' and layer.z_index > component.z_index and not current_added:
                        combined_content = combined_content.replace('</svg>', component.svg_content + '</svg>')
                        current_added = True

                    # Only include layers with z-index less than or equal to current component's z-index
                    # or all layers if viewing a base component
                    if component.component_type == 'base' or layer.z_index <= component.z_index:
                        combined_content = combined_content.replace('</svg>', layer.svg_content + '</svg>')

                # Add current component at the end if it hasn't been added yet and it's a layer
                if component.component_type == 'layer' and not current_added:
                    combined_content = combined_content.replace('</svg>', component.svg_content + '</svg>')

                    # Add a highlight to show this component's position
                    highlight = f'''
                    <rect x="0" y="0" width="100%" height="100%"
                          fill="none" stroke="#FF5722" stroke-width="3" stroke-dasharray="5,5"/>
                    <text x="50%" y="20" text-anchor="middle" font-size="14" fill="#FF5722" font-weight="bold">
                        This Layer (Z-Index: {component.z_index})
                    </text>
                    '''
                    combined_content = combined_content.replace('</svg>', highlight + '</svg>')

                # Set the preview with a container
                component.combined_preview = Markup(f'''
                <div style="display: flex; justify-content: center; align-items: center; height: 100%;">
                    {combined_content}
                </div>
                ''')
            except Exception as e:
                component.combined_preview = f'<div class="alert alert-danger">Error rendering combined SVG: {str(e)}</div>'

    def action_preview_svg(self):
        """Open a dialog with the SVG preview"""
        self.ensure_one()
        return {
            'name': _('SVG Preview: %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.svg.component',
            'view_mode': 'form',
            'view_id': self.env.ref('canbrax_configmatrix.view_config_matrix_svg_component_preview_form').id,
            'res_id': self.id,
            'target': 'new',
            'flags': {'mode': 'readonly'},
            'context': {'form_view_initial_mode': 'view'},
        }
