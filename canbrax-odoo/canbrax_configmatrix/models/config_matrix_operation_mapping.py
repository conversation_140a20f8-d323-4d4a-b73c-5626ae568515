# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)

class ConfigMatrixOperationMapping(models.Model):
    """
    Unified operation mapping system that handles both field and option mappings.
    Maps configuration choices to manufacturing operations and work centers.
    """
    _name = 'config.matrix.operation.mapping'
    _description = 'Unified Operation Mapping'
    _order = 'sequence, id'

    # Polymorphic target - can point to field OR option
    target_model = fields.Selection([
        ('config.matrix.field', 'Field'),
        ('config.matrix.option', 'Option')
    ], string="Target Type", required=True, default='config.matrix.option')
    target_id = fields.Integer("Target ID", required=True)
    target_name = fields.Char("Target Name", compute='_compute_target_name', store=True)

    # Legacy compatibility fields (computed from polymorphic fields)
    field_id = fields.Many2one('config.matrix.field', "Field", compute='_compute_legacy_fields', store=True)
    option_id = fields.Many2one('config.matrix.option', "Option", compute='_compute_legacy_fields', store=True)

    # Core operation mapping fields
    workcenter_id = fields.Many2one('mrp.workcenter', "Work Center", required=True)
    operation_name = fields.Char("Operation Name", required=True, help="Name for this operation")
    duration_formula = fields.Char("Duration Formula", default="60.0",
                                   help="Python expression to calculate operation duration in minutes")
    sequence = fields.Integer("Sequence", default=10, help="Order of operations")
    condition = fields.Char("Additional Condition",
                            help="Extra condition for this operation (beyond target selection)")

    # Descriptive fields
    name = fields.Char("Mapping Name", help="Optional name to describe this mapping")
    notes = fields.Text("Notes", help="Additional notes about this operation mapping")

    # Operation details
    setup_time = fields.Float("Setup Time (minutes)", default=0.0, help="Setup time in minutes")
    cleanup_time = fields.Float("Cleanup Time (minutes)", default=0.0, help="Cleanup time in minutes")
    
    # Worksheet information
    worksheet_type = fields.Selection([
        ('pdf', 'PDF'),
        ('google_slide', 'Google Slide'),
        ('text', 'Text')
    ], string="Worksheet Type", default='text')
    worksheet_content = fields.Text("Worksheet Content", help="Instructions or content for the operation")

    @api.depends('target_model', 'target_id')
    def _compute_target_name(self):
        """Compute the display name of the target"""
        for mapping in self:
            if mapping.target_model and mapping.target_id:
                target_record = self.env[mapping.target_model].browse(mapping.target_id)
                if target_record.exists():
                    mapping.target_name = target_record.name
                else:
                    mapping.target_name = f"Deleted {mapping.target_model} ({mapping.target_id})"
            else:
                mapping.target_name = ""

    @api.depends('target_model', 'target_id')
    def _compute_legacy_fields(self):
        """Compute legacy field_id and option_id for backward compatibility"""
        for mapping in self:
            mapping.field_id = False
            mapping.option_id = False
            
            if mapping.target_model == 'config.matrix.field' and mapping.target_id:
                mapping.field_id = mapping.target_id
            elif mapping.target_model == 'config.matrix.option' and mapping.target_id:
                mapping.option_id = mapping.target_id

    @api.model
    def create_for_field(self, field_id, workcenter_id, operation_name, duration_formula="60.0"):
        """Helper method to create operation mapping for a field"""
        return self.create({
            'target_model': 'config.matrix.field',
            'target_id': field_id,
            'workcenter_id': workcenter_id,
            'operation_name': operation_name,
            'duration_formula': duration_formula,
        })

    @api.model
    def create_for_option(self, option_id, workcenter_id, operation_name, duration_formula="60.0"):
        """Helper method to create operation mapping for an option"""
        return self.create({
            'target_model': 'config.matrix.option',
            'target_id': option_id,
            'workcenter_id': workcenter_id,
            'operation_name': operation_name,
            'duration_formula': duration_formula,
        })

    def get_target_record(self):
        """Get the actual target record (field or option)"""
        self.ensure_one()
        if self.target_model and self.target_id:
            return self.env[self.target_model].browse(self.target_id)
        return self.env[self.target_model]

    def action_open_operation_builder(self):
        """Open the operation builder wizard for this mapping"""
        self.ensure_one()

        return {
            'name': _('Build Operation Duration Formula'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.operation.builder',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_field_id': self.field_id.id if self.field_id else False,
                'default_option_id': self.option_id.id if self.option_id else False,
                'default_matrix_id': self.field_id.matrix_id.id if self.field_id else (self.option_id.matrix_id.id if self.option_id else False),
                'default_duration_formula': self.duration_formula or '60.0',
                'default_workcenter_id': self.workcenter_id.id,
                'default_operation_name': self.operation_name,
                'operation_mapping_id': self.id,  # Pass the mapping ID for updating
            },
        }

    @api.constrains('target_model', 'target_id')
    def _check_target_exists(self):
        """Ensure the target record exists"""
        for mapping in self:
            if mapping.target_model and mapping.target_id:
                target_record = self.env[mapping.target_model].browse(mapping.target_id)
                if not target_record.exists():
                    raise ValidationError(_("The target %s with ID %s does not exist.") % (mapping.target_model, mapping.target_id))

    def name_get(self):
        """Custom name_get to show meaningful names"""
        result = []
        for mapping in self:
            name = f"{mapping.operation_name}"
            if mapping.target_name:
                name += f" ({mapping.target_name})"
            if mapping.workcenter_id:
                name += f" - {mapping.workcenter_id.name}"
            result.append((mapping.id, name))
        return result
