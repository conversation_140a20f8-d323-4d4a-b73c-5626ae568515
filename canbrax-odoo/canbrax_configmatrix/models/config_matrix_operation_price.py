# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class ConfigMatrixOperationPrice(models.Model):
    _name = 'config.matrix.operation.price'
    _description = 'Fixed Price Table for Operations'
    _order = 'sequence, description'

    # Basic Information
    sequence = fields.Integer('Sequence', default=10, help='Order of display')
    active = fields.Boolean('Active', default=True)
    
    # Description and Classification
    description = fields.Char('Description', required=True, 
                             help='Main description of the operation (e.g., "Cut Extrusions on Double Mitre Saw")')
    sub_description = fields.Char('Sub-Description', 
                                 help='Additional details about the operation type, units, etc.')
    
    # Pricing Information
    unit_type = fields.Selection([
        ('hr', 'Hours'),
        ('min', 'Minutes'),
        ('each', 'Each'),
        ('length', 'Per Length'),
        ('area', 'Per Area'),
        ('volume', 'Per Volume'),
        ('kg', 'Per Kilogram'),
        ('item', 'Per Item'),
        ('pair', 'Per Pair'),
        ('hinge', 'Per Hinge'),
        ('fixed', 'Fixed Price')
    ], string='Unit Type', required=True, default='hr',
       help='The unit of measurement for pricing')
    
    cost_price = fields.Float('Cost Price', digits='Product Price', 
                             help='Internal cost for this operation')
    sale_price = fields.Float('Sale Price', digits='Product Price', required=True,
                             help='Sale price for this operation')
    
    # Markup and Profit
    markup_percentage = fields.Float('Markup %', compute='_compute_markup', store=True,
                                   help='Markup percentage from cost to sale price')
    profit_margin = fields.Float('Profit Margin', compute='_compute_profit_margin', store=True,
                                help='Profit amount (Sale Price - Cost Price)')
    
    # Categories and Tags
    category = fields.Selection([
        ('labour', 'Labour'),
        ('fixed_time', 'Fixed Time'),
        ('material', 'Material Handling'),
        ('setup', 'Setup/Preparation'),
        ('finishing', 'Finishing'),
        ('quality', 'Quality Control'),
        ('packaging', 'Packaging'),
        ('other', 'Other')
    ], string='Category', default='labour', help='Category of operation')
    
    # Work Center Integration
    workcenter_ids = fields.Many2many('mrp.workcenter', 
                                     'operation_price_workcenter_rel',
                                     'price_id', 'workcenter_id',
                                     string='Applicable Work Centers',
                                     help='Work centers where this pricing applies')
    
    # Notes and Additional Info
    notes = fields.Text('Notes', help='Additional notes about this pricing entry')
    
    # Usage Tracking
    usage_count = fields.Integer('Usage Count', compute='_compute_usage_count', store=True,
                                help='Number of operation templates using this pricing')
    
    # Company and Currency
    company_id = fields.Many2one('res.company', 'Company',
                                default=lambda self: self.env.company,
                                help='Company this pricing applies to')
    currency_id = fields.Many2one('res.currency', 'Currency',
                                 related='company_id.currency_id', readonly=True,
                                 help='Currency for pricing')

    @api.depends('cost_price', 'sale_price')
    def _compute_markup(self):
        """Calculate markup percentage"""
        for record in self:
            if record.cost_price and record.cost_price > 0:
                record.markup_percentage = ((record.sale_price - record.cost_price) / record.cost_price) * 100
            else:
                record.markup_percentage = 0.0

    @api.depends('cost_price', 'sale_price')
    def _compute_profit_margin(self):
        """Calculate profit margin"""
        for record in self:
            record.profit_margin = record.sale_price - record.cost_price

    @api.depends()
    def _compute_usage_count(self):
        """Compute how many operation templates use this pricing"""
        for record in self:
            # Count operation templates that reference this pricing
            count = self.env['config.matrix.operation.template'].search_count([
                ('operation_price_id', '=', record.id)
            ])
            record.usage_count = count

    @api.constrains('sale_price', 'cost_price')
    def _check_prices(self):
        """Validate price values"""
        for record in self:
            if record.sale_price < 0:
                raise ValidationError(_("Sale price cannot be negative"))
            if record.cost_price < 0:
                raise ValidationError(_("Cost price cannot be negative"))

    def name_get(self):
        """Custom name display"""
        result = []
        for record in self:
            name = record.description
            if record.sub_description:
                name += f" ({record.sub_description})"
            if record.sale_price:
                name += f" - ${record.sale_price:.2f}/{record.unit_type}"
            result.append((record.id, name))
        return result

    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        """Enhanced search functionality"""
        if args is None:
            args = []
        
        domain = args[:]
        if name:
            domain += ['|', '|',
                      ('description', operator, name),
                      ('sub_description', operator, name),
                      ('category', operator, name)]
        
        return self.search(domain, limit=limit).name_get()

    def copy_data(self, default=None):
        """Override copy to add 'Copy' to the description"""
        default = dict(default or {})
        if 'description' not in default:
            default['description'] = _("%s (Copy)") % self.description
        return super().copy_data(default)

    def get_price_for_duration(self, duration_minutes):
        """
        Calculate the price for a given duration
        
        Args:
            duration_minutes (float): Duration in minutes
            
        Returns:
            float: Calculated price
        """
        self.ensure_one()
        
        if self.unit_type == 'fixed':
            return self.sale_price
        elif self.unit_type == 'min':
            return self.sale_price * duration_minutes
        elif self.unit_type == 'hr':
            return self.sale_price * (duration_minutes / 60.0)
        else:
            # For other unit types, return the base price
            # This can be extended based on specific requirements
            return self.sale_price

    def get_cost_for_duration(self, duration_minutes):
        """
        Calculate the cost for a given duration
        
        Args:
            duration_minutes (float): Duration in minutes
            
        Returns:
            float: Calculated cost
        """
        self.ensure_one()
        
        if self.unit_type == 'fixed':
            return self.cost_price
        elif self.unit_type == 'min':
            return self.cost_price * duration_minutes
        elif self.unit_type == 'hr':
            return self.cost_price * (duration_minutes / 60.0)
        else:
            # For other unit types, return the base cost
            return self.cost_price
