# -*- coding: utf-8 -*-

import logging
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

_logger = logging.getLogger(__name__)

class ConfigMatrixVirtualDomain(models.Model):
    """
    Virtual model that enables the standard Odoo domain widget to work with 
    dynamic template fields. This model doesn't create database tables but 
    provides field definitions dynamically based on template context.
    """
    _name = 'config.matrix.virtual.domain'
    _description = 'Virtual Model for Template Domain Building'
    _auto = False  # Don't create database table
    _table_query = None  # No SQL table needed

    # We need at least one field for the model to be valid
    dummy_field = fields.Char('Dummy Field')

    @api.model
    def fields_get(self, allfields=None, attributes=None):
        """
        Override fields_get to return dynamic template fields based on context.
        This is the core method that makes the standard domain widget work.
        """
        # Get base fields first (minimal set)
        res = {}
        
        # Get template from context
        template_id = self._get_template_id_from_context()
        
        if not template_id:
            _logger.warning("No template_id found in context for virtual domain model")
            return res
        
        try:
            template = self.env['config.matrix.template'].browse(template_id)
            if not template.exists():
                _logger.warning(f"Template {template_id} not found for virtual domain model")
                return res
            
            # Build dynamic field definitions from template
            for section in template.section_ids:
                for field in section.field_ids:
                    field_def = self._build_field_definition(field, section)
                    res[field.technical_name] = field_def
                    
            _logger.info(f"Generated {len(res)} virtual fields for template {template.name}")
            
        except Exception as e:
            _logger.error(f"Error generating virtual fields: {str(e)}")
            
        return res
    
    def _get_template_id_from_context(self):
        """
        Get template ID from various context sources.
        We try multiple approaches to ensure we can get the template ID.
        """
        # Method 1: Direct template_id in context
        template_id = self.env.context.get('template_id')
        if template_id:
            return template_id
            
        # Method 2: From domain_template_id (alternative context key)
        template_id = self.env.context.get('domain_template_id')
        if template_id:
            return template_id
            
        # Method 3: From active field's matrix_id
        field_id = self.env.context.get('field_id') or self.env.context.get('active_id')
        if field_id and self.env.context.get('active_model') == 'config.matrix.field':
            try:
                field = self.env['config.matrix.field'].browse(field_id)
                if field.exists() and field.matrix_id:
                    return field.matrix_id.id
            except Exception:
                pass
                
        return None
    
    def _build_field_definition(self, field, section):
        """
        Convert a config.matrix.field to Odoo field definition format
        that the domain widget can understand.
        """
        field_def = {
            'string': field.name,
            'type': self._convert_field_type(field.field_type),
            'searchable': True,
            'sortable': True,
            'help': field.check_measure_help_text or field.sales_help_text or field.online_help_text or '',
            'section': section.name,  # Custom attribute for grouping
        }
        
        # Add type-specific properties
        if field.field_type == 'selection' and field.option_ids:
            field_def['selection'] = [
                (opt.value, opt.name) for opt in field.option_ids.sorted('sequence')
            ]
            
        elif field.field_type == 'number':
            field_def['type'] = 'float'  # Use float for better domain widget support
            if field.uom_id:
                field_def['help'] += f" (Unit: {field.uom_id.name})"
                
        elif field.field_type == 'boolean':
            field_def['type'] = 'boolean'
            
        elif field.field_type == 'text':
            field_def['type'] = 'char'  # Domain widget works better with char than text
            
        elif field.field_type == 'date':
            field_def['type'] = 'date'
            
        return field_def
    
    def _convert_field_type(self, matrix_field_type):
        """
        Convert matrix field types to Odoo field types that work well
        with the domain widget.
        """
        type_mapping = {
            'text': 'char',
            'number': 'float',
            'selection': 'selection',
            'boolean': 'boolean',
            'date': 'date',
        }
        return type_mapping.get(matrix_field_type, 'char')
    
    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        """
        Override name_search to prevent issues when the domain widget
        tries to search for records (which don't exist in this virtual model).
        """
        return []
    
    @api.model
    def search(self, args, offset=0, limit=None, order=None, count=False):
        """
        Override search to return empty recordset since this is a virtual model.
        """
        if count:
            return 0
        return self.browse([])
    
    @api.model
    def read(self, ids, fields_list=None, load='_classic_read'):
        """
        Override read to prevent issues when domain widget tries to read records.
        """
        return []
    
    def write(self, vals):
        """
        Override write to prevent any write operations on virtual model.
        """
        return True
    
    def unlink(self):
        """
        Override unlink to prevent deletion attempts on virtual records.
        """
        return True
