# -*- coding: utf-8 -*-

import logging
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class ConfigMatrixOptionVisibility(models.Model):
    _name = 'config.matrix.option.visibility'
    _description = 'Option Visibility Condition'
    _order = 'sequence, id'

    name = fields.Char("Name", compute='_compute_name', store=True)
    sequence = fields.Integer("Sequence", default=10)

    # Relationship fields
    option_id = fields.Many2one('config.matrix.option', "Option", required=True, ondelete='cascade',
                              help="The option this condition applies to")
    matrix_id = fields.Many2one('config.matrix.template', related='option_id.matrix_id',
                              string="Template", store=True)

    # Condition type
    condition_type = fields.Selection([
        ('field', 'Field Condition'),
        ('expression', 'Custom Expression')
    ], string="Type", required=True, default='field')

    # Field condition
    dependent_field_id = fields.Many2one('config.matrix.field', "Dependent Field",
                                       domain="[('matrix_id', '=', matrix_id)]",
                                       help="This field controls the visibility")

    @api.onchange('option_id')
    def _onchange_option_id(self):
        """Update domain for dependent fields when option changes"""
        if self.option_id:
            return {'domain': {
                'dependent_field_id': [
                    ('matrix_id', '=', self.option_id.matrix_id.id)
                ]
            }}

    operator = fields.Selection([
        ('==', 'Equals'),
        ('!=', 'Not Equals'),
        ('>', 'Greater Than'),
        ('<', 'Less Than'),
        ('>=', 'Greater Than or Equal'),
        ('<=', 'Less Than or Equal'),
        ('in', 'In List'),
        ('not in', 'Not In List'),
        ('contains', 'Contains'),
        ('not_contains', 'Does Not Contain'),
        ('is_set', 'Is Set (Has Value)'),
        ('is_not_set', 'Is Not Set (No Value)')
    ], string="Operator")

    # Value fields based on type
    value_type = fields.Selection([
        ('text', 'Text Value'),
        ('number', 'Number Value'),
        ('boolean', 'Boolean Value'),
        ('selection', 'Selection Value'),
        ('list', 'List of Values')
    ], string="Value Type", compute='_compute_value_type', store=True)

    text_value = fields.Char("Text Value")
    number_value = fields.Float("Number Value")
    boolean_value = fields.Boolean("Boolean Value")
    selection_value = fields.Char("Selection Value")
    list_value = fields.Char("List Values", help="Comma-separated list of values")

    # For multi-selection with 'in' and 'not in' operators
    selected_option_ids = fields.Many2many('config.matrix.option', 'option_visibility_selected_option_rel',
                                          'visibility_id', 'option_id', string="Selected Options")

    # Custom expression
    expression = fields.Char("Expression",
                           help="Python expression that determines visibility")

    # Logic operator for combining with other conditions
    logic_operator = fields.Selection([
        ('and', 'AND'),
        ('or', 'OR')
    ], string="Logic", default='and',
       help="How this condition combines with others (AND: all must be true, OR: any can be true)")

    # Generated condition
    generated_condition = fields.Char("Generated Condition", compute='_compute_generated_condition', store=True)

    @api.depends('option_id', 'dependent_field_id', 'condition_type')
    def _compute_name(self):
        for condition in self:
            if condition.condition_type == 'field' and condition.option_id and condition.dependent_field_id:
                condition.name = f"{condition.option_id.name} depends on {condition.dependent_field_id.name}"
            elif condition.condition_type == 'expression' and condition.option_id and condition.expression:
                condition.name = f"{condition.option_id.name}: {condition.expression[:30]}..."
            else:
                condition.name = "New Condition"

    @api.depends('dependent_field_id')
    def _compute_value_type(self):
        for condition in self:
            if condition.dependent_field_id:
                field_type = condition.dependent_field_id.field_type
                if field_type == 'text':
                    condition.value_type = 'text'
                elif field_type == 'number':
                    condition.value_type = 'number'
                elif field_type == 'boolean':
                    condition.value_type = 'boolean'
                elif field_type == 'selection':
                    condition.value_type = 'selection'
                else:
                    condition.value_type = 'text'
            else:
                condition.value_type = 'text'

    @api.onchange('condition_type')
    def _onchange_condition_type(self):
        """Clear fields when condition type changes"""
        if self.condition_type == 'expression':
            # Clear field-related values when switching to expression
            self.dependent_field_id = False
            self.operator = False
            self.text_value = False
            self.number_value = 0
            self.boolean_value = False
            self.selection_value = False
            self.list_value = False
            self.selected_option_ids = False
        elif self.condition_type == 'field':
            # Clear expression when switching to field condition
            self.expression = False

    @api.onchange('dependent_field_id')
    def _onchange_dependent_field(self):
        """Update selection values when dependent field changes"""
        if self.dependent_field_id and self.dependent_field_id.field_type == 'selection':
            # If the field is a selection field, get its options
            options = self.dependent_field_id.option_ids
            if options:
                # Clear any existing selected options
                self.selected_option_ids = False

                # Return domain for both selection_value and selected_option_ids
                return {
                    'domain': {
                        'selection_value': [('id', 'in', options.ids)],
                        'selected_option_ids': [('field_id', '=', self.dependent_field_id.id)]
                    }
                }

        # For other field types, clear any domain
        self.selected_option_ids = False
        return {
            'domain': {
                'selection_value': [],
                'selected_option_ids': []
            }
        }

    @api.depends('condition_type', 'dependent_field_id', 'operator', 'text_value',
                'number_value', 'boolean_value', 'selection_value', 'list_value', 'expression')
    def _compute_generated_condition(self):
        for condition in self:
            try:
                if condition.condition_type == 'expression':
                    condition.generated_condition = condition.expression or "True"
                    continue

                if not condition.dependent_field_id or not condition.dependent_field_id.technical_name:
                    condition.generated_condition = "True"  # Default to always visible
                    continue

                field_name = condition.dependent_field_id.technical_name
                operator = condition.operator

                # Special operators that don't need a value
                if operator == 'is_set':
                    condition.generated_condition = f"{field_name} != False"
                    continue
                elif operator == 'is_not_set':
                    condition.generated_condition = f"{field_name} == False"
                    continue
                elif not operator:
                    condition.generated_condition = "True"  # Default to always visible if no operator
                    continue

                # Get the appropriate value based on field type
                if condition.value_type == 'text':
                    value = f"'{condition.text_value or ''}'"
                elif condition.value_type == 'number':
                    value = str(condition.number_value or 0)
                elif condition.value_type == 'boolean':
                    value = str(condition.boolean_value).lower()
                elif condition.value_type == 'selection':
                    if condition.operator in ('in', 'not in') and condition.selected_option_ids:
                        # For 'in' and 'not in' operators, use the selected options
                        option_values = [f"'{opt.value}'" for opt in condition.selected_option_ids]
                        value = f"[{', '.join(option_values)}]"

                        # Also update the list_value field for backward compatibility
                        condition.list_value = ', '.join([opt.value for opt in condition.selected_option_ids])
                    else:
                        # For other operators, use the single selection value
                        value = f"'{condition.selection_value or ''}'"
                elif condition.value_type == 'list' and condition.list_value:
                    # Convert comma-separated list to array
                    items = [item.strip() for item in (condition.list_value or '').split(',')]
                    quoted_items = [f"'{item}'" for item in items if item]
                    value = f"[{', '.join(quoted_items)}]"
                else:
                    value = "''"

                # Build the condition based on operator
                if operator in ('in', 'not in'):
                    condition.generated_condition = f"{field_name} {operator} {value}"
                elif operator == 'contains':
                    condition.generated_condition = f"{field_name} && {field_name}.includes({value})"
                elif operator == 'not_contains':
                    condition.generated_condition = f"!({field_name} && {field_name}.includes({value}))"
                else:
                    condition.generated_condition = f"{field_name} {operator} {value}"
            except Exception:
                # If anything goes wrong, use a safe default
                condition.generated_condition = "True"  # Default to always visible

    @api.model_create_multi
    def create(self, vals_list):
        """Create a new condition and update option's visibility condition"""
        # Set a default generated condition to avoid validation errors
        for vals in vals_list:
            vals['generated_condition'] = "True"

            # If this is a custom expression, make sure text_value is not required
            if vals.get('condition_type') == 'expression':
                # Make sure text_value is not required for custom expressions
                vals['text_value'] = vals.get('text_value', '')

        # If we're creating a condition with is_set or is_not_set operator, ensure we have a valid field
            if vals.get('operator') in ('is_set', 'is_not_set') and vals.get('dependent_field_id'):
                # Pre-compute the generated condition
                field = self.env['config.matrix.field'].browse(vals.get('dependent_field_id'))
                if field and field.technical_name:
                    if vals.get('operator') == 'is_set':
                        vals['generated_condition'] = f"{field.technical_name} != False"
                    else:
                        vals['generated_condition'] = f"{field.technical_name} == False"

        conditions = super(ConfigMatrixOptionVisibility, self).create(vals_list)
        for condition in conditions:
            self._update_option_visibility_condition(condition.option_id)
        return conditions

    def write(self, vals):
        """Update condition and option's visibility condition"""
        # Set a default generated condition to avoid validation errors if operator is changing
        if 'operator' in vals:
            vals['generated_condition'] = "True"

        # If this is a custom expression, make sure text_value is not required
        if vals.get('condition_type') == 'expression' or (any(rec.condition_type == 'expression' for rec in self)):
            # Make sure text_value is not required for custom expressions
            vals['text_value'] = vals.get('text_value', '')

        # If we're updating to is_set or is_not_set operator, ensure we have a valid field
        if vals.get('operator') in ('is_set', 'is_not_set'):
            for condition in self:
                field = condition.dependent_field_id
                if field and field.technical_name:
                    if vals.get('operator') == 'is_set':
                        vals['generated_condition'] = f"{field.technical_name} != False"
                    else:
                        vals['generated_condition'] = f"{field.technical_name} == False"

        options = self.mapped('option_id')
        result = super(ConfigMatrixOptionVisibility, self).write(vals)
        for option in options:
            self._update_option_visibility_condition(option)
        return result

    def unlink(self):
        """Remove condition and update option's visibility condition"""
        options = self.mapped('option_id')
        result = super(ConfigMatrixOptionVisibility, self).unlink()
        for option in options:
            self._update_option_visibility_condition(option)
        return result

    def _update_option_visibility_condition(self, option):
        """Update the visibility condition on the option based on all its conditions"""
        _logger = logging.getLogger(__name__)
        _logger.info(f"Updating visibility condition for option: {option.name} (ID: {option.id})")

        conditions = self.search([('option_id', '=', option.id)], order='sequence, id')
        _logger.info(f"Found {len(conditions)} conditions")

        if not conditions:
            # If no conditions, clear the visibility condition
            _logger.info("No conditions found, clearing visibility condition")
            option.visibility_condition = False
            return

        # Create a JSON structure with all conditions
        # This will be parsed by the JavaScript to apply the correct logic
        condition_data = []

        for condition in conditions:
            if not condition.generated_condition or not condition.generated_condition.strip():
                continue

            condition_data.append({
                'condition': condition.generated_condition,
                'logic': condition.logic_operator
            })

        if not condition_data:
            # If no valid conditions, use a safe default
            _logger.info("No valid conditions found, setting to always visible")
            option.visibility_condition = "true"  # Default to always visible
            return

        # Store the conditions as a JSON string
        import json
        condition_json = json.dumps(condition_data)
        _logger.info(f"Setting visibility condition to JSON: {condition_json}")

        # Use a special marker to indicate this is a JSON condition
        option.visibility_condition = f"__JSON__{condition_json}"
