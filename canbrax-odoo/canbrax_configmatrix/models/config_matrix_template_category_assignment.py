# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class ConfigMatrixTemplateCategoryAssignment(models.Model):
    _name = 'config.matrix.template.category.assignment'
    _description = 'Template Matrix Category Assignment'
    _order = 'sequence, id'
    
    template_id = fields.Many2one(
        'config.matrix.template',
        string="Template",
        required=True,
        ondelete='cascade'
    )
    category_id = fields.Many2one(
        'config.matrix.category',
        string="Matrix Category",
        required=True
    )
    sequence = fields.Integer("Sequence", default=10)
    active = fields.<PERSON><PERSON>an("Active", default=True)
    
    # Configuration options
    is_required = fields.<PERSON><PERSON>an(
        "Required",
        help="Whether this category is required for the template"
    )
    max_matrices = fields.Integer(
        "Maximum Matrices",
        default=0,
        help="Maximum number of matrices allowed for this category (0 = unlimited)"
    )
    
    # Computed fields
    selected_matrix_count = fields.Integer(
        "Selected Matrices",
        compute='_compute_selected_matrix_count',
        store=True
    )
    category_type = fields.Selection(
        related='category_id.category_type',
        store=True,
        string="Category Type"
    )
    
    @api.depends('template_id', 'category_id')
    def _compute_selected_matrix_count(self):
        """Count how many matrices are assigned for this category"""
        for assignment in self:
            if not assignment.template_id or not assignment.category_id:
                assignment.selected_matrix_count = 0
                continue
                
            # Count matrices of this category assigned to the template
            price_matrices = self.env['config.matrix.price.matrix'].search_count([
                ('category_id', '=', assignment.category_id.id),
                ('product_template_id', '=', assignment.template_id.product_template_id.id)
            ])
            
            labor_matrices = self.env['config.matrix.labor.time.matrix'].search_count([
                ('category_id', '=', assignment.category_id.id),
                ('product_template_id', '=', assignment.template_id.product_template_id.id)
            ])
            
            assignment.selected_matrix_count = price_matrices + labor_matrices
    
    @api.constrains('max_matrices', 'selected_matrix_count')
    def _check_max_matrices(self):
        """Validate that selected matrices don't exceed maximum"""
        for assignment in self:
            if assignment.max_matrices > 0 and assignment.selected_matrix_count > assignment.max_matrices:
                raise ValidationError(
                    _("Category '%s' has %d matrices assigned but maximum allowed is %d") % (
                        assignment.category_id.name,
                        assignment.selected_matrix_count,
                        assignment.max_matrices
                    )
                )
    
    @api.constrains('template_id', 'category_id')
    def _check_unique_assignment(self):
        """Ensure each category is assigned only once per template"""
        for assignment in self:
            existing = self.search([
                ('template_id', '=', assignment.template_id.id),
                ('category_id', '=', assignment.category_id.id),
                ('id', '!=', assignment.id)
            ])
            if existing:
                raise ValidationError(
                    _("Category '%s' is already assigned to template '%s'") % (
                        assignment.category_id.name,
                        assignment.template_id.name
                    )
                )
    
    def action_configure_matrices(self):
        """Open configuration wizard for matrices in this category"""
        self.ensure_one()
        
        return {
            'name': _('Configure %s Matrices') % self.category_id.name,
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.category.configuration.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_template_id': self.template_id.id,
                'default_category_id': self.category_id.id,
                'default_assignment_id': self.id,
            },
        }
    
    def get_available_matrices(self):
        """Get matrices available for this category and template"""
        self.ensure_one()
        
        # Get price matrices
        price_matrices = self.env['config.matrix.price.matrix'].search([
            ('category_id', '=', self.category_id.id),
            ('product_template_id', '=', self.template_id.product_template_id.id)
        ])
        
        # Get labor matrices
        labor_matrices = self.env['config.matrix.labor.time.matrix'].search([
            ('category_id', '=', self.category_id.id),
            ('product_template_id', '=', self.template_id.product_template_id.id)
        ])
        
        return {
            'price_matrices': price_matrices,
            'labor_matrices': labor_matrices,
            'total_count': len(price_matrices) + len(labor_matrices)
        }
    
    def assign_matrices(self, price_matrix_ids=None, labor_matrix_ids=None):
        """assign specific matrices to this template category"""
        self.ensure_one()
        
        # Validate matrix limits
        total_new_matrices = len(price_matrix_ids or []) + len(labor_matrix_ids or [])
        if self.max_matrices > 0 and total_new_matrices > self.max_matrices:
            raise ValidationError(
                _("Cannot assign %d matrices. Maximum allowed for this category is %d.") % (
                    total_new_matrices, self.max_matrices
                )
            )
        
        # For now, we'll use the categorized matrix fields on the template
        # In a more complex implementation, you might have a separate mapping table
        
        if price_matrix_ids:
            if self.category_type == 'door_frame':
                self.template_id.door_frame_matrix_ids = [(6, 0, price_matrix_ids)]
            elif self.category_type == 'hardware':
                self.template_id.hardware_matrix_ids = [(6, 0, price_matrix_ids)]
            elif self.category_type == 'glass_panel':
                self.template_id.glass_panel_matrix_ids = [(6, 0, price_matrix_ids)]
        
        if labor_matrix_ids:
            if self.category_type == 'labor':
                self.template_id.labor_operation_matrix_ids = [(6, 0, labor_matrix_ids)]
        
        # Recompute the count
        self._compute_selected_matrix_count()
        
        return True
