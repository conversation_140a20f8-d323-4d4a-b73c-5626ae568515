# -*- coding: utf-8 -*-

import logging
import json
import base64
import io
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)

class ConfigMatrixExcelImporter(models.TransientModel):
    """Enhanced Excel importer with category support"""
    _name = 'config.matrix.excel.importer'
    _description = 'Enhanced Excel Matrix Importer'

    name = fields.Char("Import Name", required=True, default="Excel Matrix Import")
    excel_file = fields.Binary("Excel File", required=True)
    filename = fields.Char("Filename")
    
    # Category selection
    target_category_id = fields.Many2one(
        'config.matrix.category',
        string="Target Category",
        help="Category to assign imported matrices to"
    )
    auto_create_categories = fields.Boolean(
        "Auto-create Categories",
        default=True,
        help="Automatically create categories based on sheet names"
    )
    
    # Import options
    product_template_id = fields.Many2one(
        'product.template',
        string="Product Template",
        help="Product template for imported matrices"
    )
    import_mode = fields.Selection([
        ('create', 'Create New Matrices'),
        ('update', 'Update Existing Matrices'),
        ('create_update', 'Create or Update')
    ], default='create_update', required=True)
    
    # Processing options
    skip_empty_cells = fields.Boolean("Skip Empty Cells", default=True)
    validate_data = fields.Boolean("Validate Data", default=True)
    create_backup = fields.Boolean("Create Backup", default=True)
    
    # Results
    import_log = fields.Text("Import Log", readonly=True)
    matrices_created = fields.Integer("Matrices Created", readonly=True)
    matrices_updated = fields.Integer("Matrices Updated", readonly=True)
    errors_count = fields.Integer("Errors", readonly=True)
    
    def action_import_excel(self):
        """Import matrices from Excel file with category support"""
        self.ensure_one()
        
        if not self.excel_file:
            raise UserError(_("Please select an Excel file to import"))
        
        try:
            # Decode the file
            file_data = base64.b64decode(self.excel_file)
            
            # Import using openpyxl or xlrd based on file type
            if self.filename and self.filename.lower().endswith('.xlsx'):
                results = self._import_xlsx(file_data)
            else:
                results = self._import_xls(file_data)
            
            # Update results
            self.write({
                'matrices_created': results.get('created', 0),
                'matrices_updated': results.get('updated', 0),  
                'errors_count': results.get('errors', 0),
                'import_log': results.get('log', '')
            })
            
            return {
                'name': _('Import Results'),
                'type': 'ir.actions.act_window',
                'res_model': 'config.matrix.excel.importer',
                'res_id': self.id,
                'view_mode': 'form',
                'target': 'new',
                'context': {'show_results': True}
            }
            
        except Exception as e:
            _logger.error(f"Excel import error: {str(e)}")
            raise UserError(_("Import failed: %s") % str(e))
    
    def _import_xlsx(self, file_data):
        """Import XLSX file using openpyxl"""
        try:
            import openpyxl
        except ImportError:
            raise UserError(_("openpyxl library is required for .xlsx files"))
        
        workbook = openpyxl.load_workbook(io.BytesIO(file_data), data_only=True)
        results = {'created': 0, 'updated': 0, 'errors': 0, 'log': ''}
        
        for sheet_name in workbook.sheetnames:
            try:
                sheet_results = self._process_sheet(workbook[sheet_name], sheet_name)
                results['created'] += sheet_results['created']
                results['updated'] += sheet_results['updated'] 
                results['errors'] += sheet_results['errors']
                results['log'] += sheet_results['log']
            except Exception as e:
                results['errors'] += 1
                results['log'] += f"Error processing sheet {sheet_name}: {str(e)}\n"
                _logger.error(f"Sheet processing error: {str(e)}")
        
        return results
    
    def _detect_matrix_type(self, sheet_name):
        """Detect if sheet contains price or labor data"""
        sheet_name_lower = sheet_name.lower()
        
        labor_keywords = ['labor', 'labour', 'time', 'hours', 'manufacturing', 'assembly', 'cutting']
        price_keywords = ['price', 'cost', 'pricing', 'rate', 'tariff']
        
        if any(keyword in sheet_name_lower for keyword in labor_keywords):
            return 'labor'
        elif any(keyword in sheet_name_lower for keyword in price_keywords):
            return 'price'
        else:
            # Default to price if unclear
            return 'price'
    
    def _get_or_create_category(self, sheet_name, matrix_type):
        """Get existing category or create new one"""
        if self.target_category_id:
            return self.target_category_id
        
        if not self.auto_create_categories:
            # Use default category based on type
            category_type = 'labor' if matrix_type == 'labor' else 'door_frame'
            category = self.env['config.matrix.category'].search([
                ('category_type', '=', category_type)
            ], limit=1)
            if category:
                return category
        
        # Create new category based on sheet name
        category_name = sheet_name.replace('_', ' ').title()
        category_code = sheet_name.upper().replace(' ', '_')
        
        # Determine category type
        if matrix_type == 'labor':
            category_type = 'labor'
        elif 'hardware' in sheet_name.lower():
            category_type = 'hardware'
        elif 'glass' in sheet_name.lower() or 'panel' in sheet_name.lower():
            category_type = 'glass_panel'
        else:
            category_type = 'door_frame'
        
        category = self.env['config.matrix.category'].create({
            'name': category_name,
            'code': category_code,
            'category_type': category_type,
            'description': f"Auto-created from Excel import: {sheet_name}",
        })
        
        return category
    
    def _extract_matrix_data_from_sheet(self, sheet):
        """Extract matrix data from Excel sheet"""
        matrix_data = {
            'heights': [],
            'widths': [],
            'data': {}
        }
        
        # Find the data range
        max_row = sheet.max_row
        max_col = sheet.max_column
        
        if max_row < 2 or max_col < 2:
            return matrix_data
        
        # Extract width headers (first row, skip first cell)
        for col in range(2, max_col + 1):
            cell_value = sheet.cell(row=1, column=col).value
            if cell_value is not None:
                try:
                    width = int(float(str(cell_value).replace('mm', '').replace(' ', '')))
                    matrix_data['widths'].append(width)
                except (ValueError, TypeError):
                    continue
        
        # Extract height headers and data
        for row in range(2, max_row + 1):
            # Get height from first column
            height_cell = sheet.cell(row=row, column=1).value
            if height_cell is not None:
                try:
                    height = int(float(str(height_cell).replace('mm', '').replace(' ', '')))
                    matrix_data['heights'].append(height)
                    
                    # Extract data for this row
                    for col in range(2, min(len(matrix_data['widths']) + 2, max_col + 1)):
                        width_index = col - 2
                        if width_index < len(matrix_data['widths']):
                            width = matrix_data['widths'][width_index]
                            cell_value = sheet.cell(row=row, column=col).value
                            
                            if cell_value is not None and str(cell_value).strip():
                                try:
                                    value = float(cell_value)
                                    key = f"{height}_{width}"
                                    matrix_data['data'][key] = value
                                except (ValueError, TypeError):
                                    continue
                except (ValueError, TypeError):
                    continue
        
        return matrix_data
    
    def _create_price_matrix(self, matrix_name, matrix_data, category):
        """Create a price matrix from extracted data"""
        try:
            matrix = self.env['config.matrix.price.matrix'].create({
                'name': matrix_name,
                'product_template_id': self.product_template_id.id if self.product_template_id else None,
                'category_id': category.id,
                'matrix_type': 'price',
                'height_ranges': json.dumps(self._create_ranges(matrix_data['heights'])),
                'width_ranges': json.dumps(self._create_ranges(matrix_data['widths'])),
                'matrix_data': json.dumps(matrix_data['data']),
                'last_imported': fields.Datetime.now(),
                'import_source': f"Excel: {self.filename or 'unknown'}"
            })
            return matrix
        except Exception as e:
            _logger.error(f"Price matrix creation failed: {str(e)}")
            return None
    
    def _create_labor_matrix(self, matrix_name, matrix_data, category):
        """Create a labor time matrix from extracted data"""
        try:
            matrix = self.env['config.matrix.labor.time.matrix'].create({
                'name': matrix_name,
                'product_template_id': self.product_template_id.id if self.product_template_id else None,
                'category_id': category.id,
                'operation_type': 'custom',
                'time_unit': 'hours',
                'height_ranges': json.dumps(self._create_ranges(matrix_data['heights'])),
                'width_ranges': json.dumps(self._create_ranges(matrix_data['widths'])),
                'matrix_data': json.dumps(matrix_data['data']),
                'last_imported': fields.Datetime.now(),
                'import_source': f"Excel: {self.filename or 'unknown'}"
            })
            return matrix
        except Exception as e:
            _logger.error(f"Labor matrix creation failed: {str(e)}")
            return None
    
    def _create_ranges(self, values):
        """Create range data from sorted values"""
        if not values:
            return []

        sorted_values = sorted(set(values))
        ranges = []

        for i, value in enumerate(sorted_values):
            if i == 0:
                # For the first range, use the value itself as minimum
                # This creates ranges like: 600-600, 601-800, 801-1000, etc.
                min_val = value
            else:
                min_val = sorted_values[i-1] + 1

            ranges.append({
                'min': min_val,
                'max': value,
                'label': str(value)
            })

        return ranges

    def test_create_ranges(self):
        """Test method to verify range creation"""
        test_values = [600, 800, 1000, 1200, 1500]
        ranges = self._create_ranges(test_values)
        _logger.info(f"Test ranges created: {ranges}")
        return ranges
    
    def _process_sheet(self, sheet, sheet_name):
        """Process an openpyxl worksheet"""
        results = {'created': 0, 'updated': 0, 'errors': 0, 'log': f"Processing sheet: {sheet_name}\n"}
        
        # Determine if this is a price or labor matrix
        matrix_type = self._detect_matrix_type(sheet_name)
        
        # Get or create category
        category = self._get_or_create_category(sheet_name, matrix_type)
        
        # Extract matrix data
        try:
            matrix_data = self._extract_matrix_data_from_sheet(sheet)
            
            if not matrix_data['heights'] or not matrix_data['widths']:
                results['errors'] += 1
                results['log'] += f"  No valid matrix data found in {sheet_name}\n"
                return results
            
            # Create or update matrix
            matrix_name = f"{sheet_name} - {self.product_template_id.name if self.product_template_id else 'Imported'}"
            
            if matrix_type == 'labor':
                matrix = self._create_labor_matrix(matrix_name, matrix_data, category)
            else:
                matrix = self._create_price_matrix(matrix_name, matrix_data, category)
            
            if matrix:
                results['created'] += 1
                results['log'] += f"  ✓ Created matrix: {matrix.name}\n"
                results['log'] += f"    Category: {category.name}\n"
                results['log'] += f"    Size: {len(matrix_data['heights'])} x {len(matrix_data['widths'])}\n"
                results['log'] += f"    Data points: {len(matrix_data['data'])}\n"
            else:
                results['errors'] += 1
                results['log'] += f"  ✗ Failed to create matrix from {sheet_name}\n"
                
        except Exception as e:
            results['errors'] += 1
            results['log'] += f"  ✗ Error: {str(e)}\n"
            _logger.error(f"Matrix creation error: {str(e)}")
        
        return results


class ConfigMatrixExcelExporter(models.TransientModel):
    """Enhanced Excel exporter with category support"""
    _name = 'config.matrix.excel.exporter'
    _description = 'Enhanced Excel Matrix Exporter'

    name = fields.Char("Export Name", required=True, default="Matrix Export")
    
    # Export options
    export_type = fields.Selection([
        ('template', 'Single Template'),
        ('category', 'Matrix Category'),
        ('all', 'All Matrices')
    ], default='template', required=True)
    
    template_id = fields.Many2one(
        'config.matrix.template',
        string="Template",
        help="Template to export matrices from"
    )
    category_id = fields.Many2one(
        'config.matrix.category', 
        string="Category",
        help="Category to export matrices from"
    )
    
    # Format options
    include_metadata = fields.Boolean("Include Metadata", default=True)
    include_empty_cells = fields.Boolean("Include Empty Cells", default=False)
    separate_sheets = fields.Boolean("Separate Sheets per Matrix", default=True)
    
    # Output
    excel_file = fields.Binary("Excel File", readonly=True)
    filename = fields.Char("Filename", readonly=True)
    
    def action_export_excel(self):
        """Export matrices to Excel file"""
        self.ensure_one()
        
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Alignment
            from openpyxl.utils import get_column_letter
        except ImportError:
            raise UserError(_("openpyxl library is required for Excel export"))
        
        # Create workbook
        workbook = openpyxl.Workbook()
        workbook.remove(workbook.active)  # Remove default sheet
        
        # Get matrices to export
        matrices = self._get_matrices_to_export()
        
        if not matrices:
            raise UserError(_("No matrices found to export"))
        
        # Export each matrix
        for matrix in matrices:
            self._add_matrix_to_workbook(workbook, matrix)
        
        # Save workbook to binary
        output = io.BytesIO()
        workbook.save(output)
        output.seek(0)
        
        # Encode to base64
        excel_data = base64.b64encode(output.getvalue())
        
        # Update record
        filename = f"{self.name}_{fields.Date.today().strftime('%Y%m%d')}.xlsx"
        self.write({
            'excel_file': excel_data,
            'filename': filename
        })
        
        return {
            'name': _('Export Complete'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.excel.exporter',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': {'show_download': True}
        }
    
    def _get_matrices_to_export(self):
        """Get matrices based on export type"""
        if self.export_type == 'template' and self.template_id:
            # Get all matrices assigned to template
            price_matrices = []
            labor_matrices = []
            
            # Get from category assignments
            for assignment in self.template_id.category_assignment_ids:
                price_matrices.extend(assignment.selected_price_matrix_ids)
                labor_matrices.extend(assignment.selected_labor_matrix_ids)
            
            # Get from direct assignments
            price_matrices.extend(self.template_id.door_frame_matrix_ids)
            price_matrices.extend(self.template_id.hardware_matrix_ids)
            price_matrices.extend(self.template_id.glass_panel_matrix_ids)
            labor_matrices.extend(self.template_id.labor_operation_matrix_ids)
            
            return list(set(price_matrices + labor_matrices))
            
        elif self.export_type == 'category' and self.category_id:
            return list(self.category_id.price_matrix_ids + self.category_id.labor_matrix_ids)
            
        elif self.export_type == 'all':
            price_matrices = self.env['config.matrix.price.matrix'].search([])
            labor_matrices = self.env['config.matrix.labor.time.matrix'].search([])
            return list(price_matrices + labor_matrices)
        
        return []
    
    def _add_matrix_to_workbook(self, workbook, matrix):
        """Add a matrix as a worksheet"""
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Alignment
        except ImportError:
            return
        
        # Create worksheet
        sheet_name = matrix.name[:31]  # Excel sheet name limit
        worksheet = workbook.create_sheet(title=sheet_name)
        
        # Parse matrix data
        try:
            if hasattr(matrix, 'height_ranges'):
                # Price matrix
                height_ranges = json.loads(matrix.height_ranges or '[]')
                width_ranges = json.loads(matrix.width_ranges or '[]')
                matrix_data = json.loads(matrix.matrix_data or '{}')
            else:
                # Labor matrix - similar structure
                height_ranges = json.loads(matrix.height_ranges or '[]')
                width_ranges = json.loads(matrix.width_ranges or '[]')
                matrix_data = json.loads(matrix.matrix_data or '{}')
        except json.JSONDecodeError:
            height_ranges = []
            width_ranges = []
            matrix_data = {}
        
        # Add headers
        worksheet.cell(row=1, column=1, value="Height\\Width")
        
        # Width headers
        for i, width_range in enumerate(width_ranges):
            worksheet.cell(row=1, column=i + 2, value=width_range['label'])
        
        # Height headers and data
        for i, height_range in enumerate(height_ranges):
            row_num = i + 2
            worksheet.cell(row=row_num, column=1, value=height_range['label'])
            
            # Data cells
            for j, width_range in enumerate(width_ranges):
                col_num = j + 2
                key = f"{height_range['label']}_{width_range['label']}"
                value = matrix_data.get(key, '' if not self.include_empty_cells else 0)
                worksheet.cell(row=row_num, column=col_num, value=value)
        
        # Apply styling
        self._apply_excel_styling(worksheet, len(height_ranges), len(width_ranges))
        
        # Add metadata if requested
        if self.include_metadata:
            self._add_metadata_to_sheet(worksheet, matrix, len(height_ranges) + 5)
    
    def _apply_excel_styling(self, worksheet, height_count, width_count):
        """Apply Excel-like styling to worksheet"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        except ImportError:
            return
        
        # Header styling
        header_font = Font(bold=True, color="000000")
        header_fill = PatternFill(start_color="E8E8E8", end_color="E8E8E8", fill_type="solid")
        
        # Apply to first row and first column
        for col in range(1, width_count + 2):
            cell = worksheet.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal="center", vertical="center")
        
        for row in range(2, height_count + 2):
            cell = worksheet.cell(row=row, column=1)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal="center", vertical="center")
        
        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 20)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def _add_metadata_to_sheet(self, worksheet, matrix, start_row):
        """Add matrix metadata to worksheet"""
        metadata = [
            ("Matrix Name:", matrix.name),
            ("Category:", matrix.category_id.name if hasattr(matrix, 'category_id') and matrix.category_id else "N/A"),
            ("Type:", getattr(matrix, 'matrix_type', 'labor') if hasattr(matrix, 'matrix_type') else 'Labor Time'),
            ("Last Imported:", str(matrix.last_imported) if matrix.last_imported else "N/A"),
            ("Import Source:", matrix.import_source or "N/A"),
        ]
        
        for i, (label, value) in enumerate(metadata):
            worksheet.cell(row=start_row + i, column=1, value=label)
            worksheet.cell(row=start_row + i, column=2, value=value)
