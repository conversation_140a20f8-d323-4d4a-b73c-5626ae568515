# -*- coding: utf-8 -*-

import re
import json
import logging
import hashlib
from odoo import models, fields, api, tools, _
from odoo.exceptions import ValidationError
from odoo.tools.safe_eval import safe_eval

_logger = logging.getLogger(__name__)

class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    _description = 'Configuration Field'
    _order = 'sequence, id'

    name = fields.Char('Field Label', required=True, help='The label shown to users')
    technical_name = fields.Char('Technical Name', required=True, help='Identifier used in conditions and formulas')

    # Dynamic Help Text per Use Case
    check_measure_use_dynamic_help = fields.Boolean('Use Dynamic Help (Check Measure)', default=False,
                                                   help='Enable dynamic help text for Check Measure use case')
    check_measure_dynamic_help_template = fields.Text('Dynamic Help Template (Check Measure)',
                                                     help='Template for dynamic help text using {field_name} placeholders')

    sales_use_dynamic_help = fields.Boolean('Use Dynamic Help (Sales)', default=False,
                                           help='Enable dynamic help text for Sales use case')
    sales_dynamic_help_template = fields.Text('Dynamic Help Template (Sales)',
                                             help='Template for dynamic help text using {field_name} placeholders')

    online_use_dynamic_help = fields.Boolean('Use Dynamic Help (Online)', default=False,
                                            help='Enable dynamic help text for Online use case')
    online_dynamic_help_template = fields.Text('Dynamic Help Template (Online)',
                                              help='Template for dynamic help text using {field_name} placeholders')

    # Dynamic Default Value per Use Case
    check_measure_use_dynamic_default = fields.Boolean('Use Dynamic Default (Check Measure)', default=False,
                                                      help='Enable dynamic default value for Check Measure use case')
    check_measure_dynamic_default_template = fields.Text('Dynamic Default Template (Check Measure)',
                                                        help='Template for dynamic default value using {field_name} placeholders')

    sales_use_dynamic_default = fields.Boolean('Use Dynamic Default (Sales)', default=False,
                                              help='Enable dynamic default value for Sales use case')
    sales_dynamic_default_template = fields.Text('Dynamic Default Template (Sales)',
                                                help='Template for dynamic default value using {field_name} placeholders')

    online_use_dynamic_default = fields.Boolean('Use Dynamic Default (Online)', default=False,
                                               help='Enable dynamic default value for Online use case')
    online_dynamic_default_template = fields.Text('Dynamic Default Template (Online)',
                                                 help='Template for dynamic default value using {field_name} placeholders')
    section_id = fields.Many2one('config.matrix.section', 'Section', required=True, ondelete='cascade')
    sequence = fields.Integer('Sequence', default=10, help='Display order within section')
    question_number = fields.Integer('Question', compute='_compute_question_number', store=True,
                                  help='Automatically assigned question number based on sequence')

    # Field type and options
    field_type = fields.Selection([
        ('text', 'Text'),
        ('number', 'Number'),
        ('selection', 'Selection'),
        ('boolean', 'Yes/No'),
        ('date', 'Date'),
    ], required=True, default='text')
    visibility_condition = fields.Char('Visibility Condition',
                                   help='Python expression that determines if field is visible. '
                                        'Empty means always visible.')

    # Text field options
    min_length = fields.Integer('Minimum Length')
    max_length = fields.Integer('Maximum Length')
    pattern = fields.Char('Validation Pattern', help='Regular expression for validation')

    # Number field options
    decimal_precision = fields.Integer('Decimal Precision', default=3)
    uom_id = fields.Many2one('uom.uom', string='Unit of Measure',
                          help='Select the unit of measure for this field')

    # Selection field options
    option_ids = fields.One2many('config.matrix.option', 'field_id', 'Options')
    is_searchable_dropdown = fields.Boolean('Searchable Dropdown', default=False,
                                           help='If checked, this selection field will be rendered as a searchable/typable dropdown in the configurator')

    # Boolean field options
    boolean_true_label = fields.Char('True Label', default='Yes')
    boolean_false_label = fields.Char('False Label', default='No')

    # Component mapping (Legacy - single component)
    component_product_id = fields.Many2one('product.product', 'Component Product',
                                      help='Product to include in BOM when this field is used')
    quantity_formula = fields.Char('Quantity Formula', default='1',
                               help='Python expression to calculate quantity')

    # New field for 1:many component mappings
    component_mapping_ids = fields.One2many(
        'config.matrix.field.component.mapping',
        'field_id',
        'Component Mappings',
        help='Multiple components that can be mapped to this field'
    )

    # Add a computed field to check if using multiple mappings
    has_multiple_components = fields.Boolean(
        'Has Multiple Components',
        compute='_compute_has_multiple_components',
        search='_search_has_multiple_components',
        store=True,
        help='True if this field has multiple component mappings'
    )



    # Related fields
    matrix_id = fields.Many2one('config.matrix.template', related='section_id.matrix_id',
                          string='Template', store=True)
    template_state = fields.Selection(related='matrix_id.state', string='Template State')

    # Use case properties - Check Measure
    check_measure_visible = fields.Boolean('Check Measure: Visible', default=True,
                                        help='Whether this field is visible in the Check Measure use case')
    check_measure_min_value = fields.Char('Check Measure: Min Value',
                                        help='Minimum value for this field in the Check Measure use case. Can be a number (e.g., "300") or expression (e.g., "field_name > 100 ? 200 : 150")')
    check_measure_max_value = fields.Char('Check Measure: Max Value',
                                        help='Maximum value for this field in the Check Measure use case. Can be a number (e.g., "2000") or expression (e.g., "door_height > 2510 ? 1310 : 2000")')
    check_measure_default_value = fields.Char('Check Measure: Default Value',
                                           help='Default value for this field in the Check Measure use case')
    check_measure_default_option_id = fields.Many2one('config.matrix.option', string='Check Measure: Default Option',
                                                   domain="[('field_id', '=', id)]",
                                                   help='Default option for selection fields in the Check Measure use case')
    check_measure_help_text = fields.Text('Check Measure: Help Text',
                                       help='Help text for this field in the Check Measure use case')

    # Error Message fields for Check Measure
    check_measure_use_dynamic_error = fields.Boolean('Use Dynamic Error (Check Measure)', default=False,
                                                    help='Enable dynamic error message for Check Measure use case')
    check_measure_dynamic_error_template = fields.Text('Dynamic Error Template (Check Measure)',
                                                      help='Template for dynamic error message using {field_name} placeholders')
    check_measure_error_condition = fields.Char('Error Condition (Check Measure)',
                                               help='Python expression that determines when to show error message. Empty means never show.')
    check_measure_error_text = fields.Text('Check Measure: Error Message',
                                          help='Error message for this field in the Check Measure use case')

    # Use case properties - Sales/Quoting
    sales_visible = fields.Boolean('Sales: Visible', default=True,
                                help='Whether this field is visible in the Sales/Quoting use case')
    sales_min_value = fields.Char('Sales: Min Value',
                                help='Minimum value for this field in the Sales/Quoting use case. Can be a number (e.g., "300") or expression (e.g., "field_name > 100 ? 200 : 150")')
    sales_max_value = fields.Char('Sales: Max Value',
                                help='Maximum value for this field in the Sales/Quoting use case. Can be a number (e.g., "2000") or expression (e.g., "door_height > 2510 ? 1310 : 2000")')
    sales_default_value = fields.Char('Sales: Default Value',
                                   help='Default value for this field in the Sales/Quoting use case')
    sales_default_option_id = fields.Many2one('config.matrix.option', string='Sales: Default Option',
                                           domain="[('field_id', '=', id)]",
                                           help='Default option for selection fields in the Sales/Quoting use case')
    sales_help_text = fields.Text('Sales: Help Text',
                               help='Help text for this field in the Sales/Quoting use case')

    # Error Message fields for Sales
    sales_use_dynamic_error = fields.Boolean('Use Dynamic Error (Sales)', default=False,
                                            help='Enable dynamic error message for Sales use case')
    sales_dynamic_error_template = fields.Text('Dynamic Error Template (Sales)',
                                              help='Template for dynamic error message using {field_name} placeholders')
    sales_error_condition = fields.Char('Error Condition (Sales)',
                                       help='Python expression that determines when to show error message. Empty means never show.')
    sales_error_text = fields.Text('Sales: Error Message',
                                  help='Error message for this field in the Sales use case')

    # Use case properties - Online Sales
    online_visible = fields.Boolean('Online: Visible', default=True,
                                 help='Whether this field is visible in the Online Sales use case')
    online_min_value = fields.Char('Online: Min Value',
                                 help='Minimum value for this field in the Online Sales use case. Can be a number (e.g., "300") or expression (e.g., "field_name > 100 ? 200 : 150")')
    online_max_value = fields.Char('Online: Max Value',
                                 help='Maximum value for this field in the Online Sales use case. Can be a number (e.g., "2000") or expression (e.g., "door_height > 2510 ? 1310 : 2000")')
    online_default_value = fields.Char('Online: Default Value',
                                    help='Default value for this field in the Online Sales use case')
    online_default_option_id = fields.Many2one('config.matrix.option', string='Online: Default Option',
                                            domain="[('field_id', '=', id)]",
                                            help='Default option for selection fields in the Online Sales use case')
    online_help_text = fields.Text('Online: Help Text',
                                help='Help text for this field in the Online Sales use case')

    # Error Message fields for Online
    online_use_dynamic_error = fields.Boolean('Use Dynamic Error (Online)', default=False,
                                             help='Enable dynamic error message for Online use case')
    online_dynamic_error_template = fields.Text('Dynamic Error Template (Online)',
                                               help='Template for dynamic error message using {field_name} placeholders')
    online_error_condition = fields.Char('Error Condition (Online)',
                                        help='Python expression that determines when to show error message. Empty means never show.')
    online_error_text = fields.Text('Online: Error Message',
                                   help='Error message for this field in the Online use case')

    # Instructional image
    instructional_image = fields.Binary('Instructional Image',
                                    help='Image to guide users on how to measure or select the correct value')
    instructional_image_filename = fields.Char('Image Filename')
    show_in_preview = fields.Boolean('Show in Preview Panel', default=False,
                                  help='If checked, the instructional image will also appear below the Product Preview')



    # Visibility conditions
    visibility_condition_ids = fields.One2many('config.matrix.visibility.condition', 'field_id',
                                            string='Visibility Conditions')

    # Dependencies
    dependency_ids = fields.One2many('config.matrix.dependency', 'field_id',
                                   string='Dependencies')

    _sql_constraints = [
        ('technical_name_uniq', 'unique (matrix_id, technical_name)',
         'Technical name must be unique within template!')
    ]

    @api.depends('component_mapping_ids', 'component_product_id')
    def _compute_has_multiple_components(self):
        """Check if field has multiple component mappings"""
        for field in self:
            field.has_multiple_components = len(field.component_mapping_ids) > 1 or (
                len(field.component_mapping_ids) == 1 and field.component_product_id
            )

    def _search_has_multiple_components(self, operator, value):
        """Search method for has_multiple_components field"""
        # Find fields with multiple component mappings
        if operator == '=' and value or operator == '!=' and not value:
            # Looking for fields with multiple components
            fields_with_multiple = self.search([]).filtered(
                lambda f: len(f.component_mapping_ids) > 1 or
                         (len(f.component_mapping_ids) == 1 and f.component_product_id)
            )
            return [('id', 'in', fields_with_multiple.ids)]
        else:
            # Looking for fields without multiple components
            fields_without_multiple = self.search([]).filtered(
                lambda f: not (len(f.component_mapping_ids) > 1 or
                             (len(f.component_mapping_ids) == 1 and f.component_product_id))
            )
            return [('id', 'in', fields_without_multiple.ids)]

    def get_component_mappings(self):
        """Get all component mappings for this field (both legacy and new)"""
        self.ensure_one()
        mappings = []

        # Add simple component mapping if exists
        if self.component_product_id:
            mappings.append({
                'component_product_id': self.component_product_id,
                'quantity_formula': self.quantity_formula or '1',
                'condition': None,
                'sequence': 0,
                'is_legacy': False,
                'mapping_type': 'static',
                'mapping_obj': None
            })

        # Add new mappings
        for mapping in self.component_mapping_ids:
            mappings.append({
                'component_product_id': mapping.component_product_id,
                'quantity_formula': mapping.quantity_formula or '1',
                'condition': mapping.condition,
                'sequence': mapping.sequence,
                'is_legacy': False,
                'mapping_type': mapping.mapping_type,
                'mapping_obj': mapping
            })

        # Sort by sequence
        mappings.sort(key=lambda x: x['sequence'])
        return mappings



    def action_manage_component_mappings(self):
        """Open wizard to manage multiple component mappings"""
        self.ensure_one()

        return {
            'name': _('Manage Component Mappings'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.field.component.mapping',
            'view_mode': 'list,form',
            'domain': [('field_id', '=', self.id)],
            'context': {
                'default_field_id': self.id,
                'default_sequence': (max(self.component_mapping_ids.mapped('sequence')) + 10) if self.component_mapping_ids else 10
            },
        }

    @api.constrains('technical_name')
    def _check_technical_name(self):
        for field in self:
            if not re.match(r'^[a-z][a-z0-9_]*$', field.technical_name):
                raise ValidationError(_('Technical name must start with a lowercase letter and '
                                      'contain only lowercase letters, numbers, and underscores.'))

    @api.onchange('name')
    def _onchange_name(self):
        """Generate technical name from field label"""
        if self.name and not self.technical_name:
            # Convert to lowercase, replace spaces with underscores, remove special chars
            technical_name = re.sub(r'[^a-z0-9_]', '', self.name.lower().replace(' ', '_'))
            # Ensure it starts with a letter
            if technical_name and not technical_name[0].isalpha():
                technical_name = 'f_' + technical_name
            # Truncate if too long
            if len(technical_name) > 32:
                technical_name = technical_name[:32]

            # Check if technical name already exists in template
            if self.section_id and self.section_id.matrix_id:
                existing = self.search([
                    ('matrix_id', '=', self.section_id.matrix_id.id),
                    ('technical_name', '=', technical_name),
                    ('id', '!=', self._origin.id or 0)
                ])

                if existing:
                    # Add suffix to make unique
                    i = 1
                    while True:
                        new_name = f'{technical_name}_{i}'
                        existing = self.search([
                            ('matrix_id', '=', self.section_id.matrix_id.id),
                            ('technical_name', '=', new_name),
                            ('id', '!=', self._origin.id or 0)
                        ])
                        if not existing:
                            technical_name = new_name
                            break
                        i += 1

            self.technical_name = technical_name

    @api.constrains('visibility_condition')
    def _check_visibility_condition(self):
        # Bypass validation to allow any condition
        pass

    @api.constrains('quantity_formula')
    def _check_quantity_formula(self):
        for field in self:
            if field.component_product_id and field.quantity_formula:
                try:
                    # Compile the formula to check syntax
                    compile(field.quantity_formula, '<string>', 'eval')
                except Exception as e:
                    raise ValidationError(_('Invalid quantity formula: %s') % str(e))

    @api.constrains('check_measure_min_value', 'check_measure_max_value',
                    'sales_min_value', 'sales_max_value',
                    'online_min_value', 'online_max_value')
    def _check_dynamic_min_max_values(self):
        """Validate dynamic min/max value expressions - very permissive validation"""
        for field in self:
            if field.field_type != 'number':
                continue

            # List of fields to validate
            value_fields = [
                (field.check_measure_min_value, 'Check Measure Min Value'),
                (field.check_measure_max_value, 'Check Measure Max Value'),
                (field.sales_min_value, 'Sales Min Value'),
                (field.sales_max_value, 'Sales Max Value'),
                (field.online_min_value, 'Online Min Value'),
                (field.online_max_value, 'Online Max Value')
            ]

            for value, field_name in value_fields:
                if value:
                    # Very basic validation - just check it's not empty and has reasonable content
                    value = value.strip()
                    if not value:
                        continue

                    # Check if it's a valid number
                    try:
                        float(value)
                        continue  # Valid number, skip further validation
                    except (ValueError, TypeError):
                        pass

                    # For expressions, just check basic format - be very permissive
                    if len(value) > 200:  # Prevent extremely long expressions
                        raise ValidationError(_(
                            '%s is too long. Please use a shorter expression.'
                        ) % field_name)

                    # Allow any expression that contains reasonable characters including curly braces for calculated fields
                    import re
                    if not re.match(r'^[a-zA-Z0-9_\s\>\<\=\?\:\.\+\-\*\/\(\)\{\}]+$', value):
                        raise ValidationError(_(
                            '%s contains invalid characters. Use only letters, numbers, '
                            'spaces, and operators (>, <, ==, ?, :, +, -, *, /, (, ), {, }).'
                        ) % field_name)

    @api.depends('sequence', 'section_id', 'section_id.sequence', 'matrix_id')
    def _compute_question_number(self):
        """
        Compute the question number based on the field's sequence within the template.
        This assigns sequential numbers to all fields across all sections.
        """
        for template in self.mapped('matrix_id'):
            # Get all sections for this template, sorted by sequence
            sections = self.env['config.matrix.section'].search([
                ('matrix_id', '=', template.id)
            ], order='sequence, id')

            # Get all fields for each section in order
            counter = 1
            for section in sections:
                fields = self.search([
                    ('section_id', '=', section.id)
                ], order='sequence, id')

                # Assign sequential numbers
                for field in fields:
                    field.question_number = counter
                    counter += 1

    @api.model_create_multi
    def create(self, vals_list):
        """Create a new field"""
        # Auto-assign sequence if not provided
        for vals in vals_list:
            if not vals.get('sequence') and vals.get('section_id'):
                fields = self.search([('section_id', '=', vals['section_id'])])
                if fields:
                    vals['sequence'] = max(fields.mapped('sequence')) + 10
                else:
                    vals['sequence'] = 10

            # Set default UOM to millimeter for number fields if not specified
            if vals.get('field_type') == 'number' and not vals.get('uom_id'):
                mm_uom = self.env.ref('uom.product_uom_millimeter', raise_if_not_found=False)
                if mm_uom:
                    vals['uom_id'] = mm_uom.id

            # Set default decimal precision for number fields if not specified
            if vals.get('field_type') == 'number' and 'decimal_precision' not in vals:
                vals['decimal_precision'] = 3

        results = super(ConfigMatrixField, self).create(vals_list)

        # Trigger recomputation of question numbers for all fields in the same template
        for result in results:
            if result.matrix_id:
                fields = self.search([('matrix_id', '=', result.matrix_id.id)])
                fields._compute_question_number()

        return results

    def write(self, vals):
        """Update field and recompute question numbers if sequence changes"""
        # Check if sequence is being changed
        sequence_changed = 'sequence' in vals
        section_changed = 'section_id' in vals

        result = super(ConfigMatrixField, self).write(vals)

        # If sequence or section changed, recompute question numbers
        if sequence_changed or section_changed:
            templates = self.mapped('matrix_id')
            for template in templates:
                fields = self.search([('matrix_id', '=', template.id)])
                fields._compute_question_number()

        return result

    def generate_dynamic_help(self, values=None, use_case='check_measure'):
        """Generate dynamic help text based on current field values and use case"""
        if not values:
            values = {}

        # Determine which dynamic help template to use based on use case
        use_dynamic_help = False
        template = None

        if use_case == 'check_measure':
            use_dynamic_help = self.check_measure_use_dynamic_help
            template = self.check_measure_dynamic_help_template
        elif use_case == 'sales':
            use_dynamic_help = self.sales_use_dynamic_help
            template = self.sales_dynamic_help_template
        elif use_case == 'online':
            use_dynamic_help = self.online_use_dynamic_help
            template = self.online_dynamic_help_template

        if not use_dynamic_help or not template:
            # Return static help text based on use case
            if use_case == 'check_measure':
                return self.check_measure_help_text or ""
            elif use_case == 'sales':
                return self.sales_help_text or ""
            elif use_case == 'online':
                return self.online_help_text or ""
            return ""

        try:
            # Create a safe context for template evaluation
            context = {
                'field_name': self.technical_name,
                'field_label': self.name,
                **values
            }

            # Add mathematical functions for calculations
            context.update({
                'min': min,
                'max': max,
                'abs': abs,
                'round': round,
            })

            # Handle complex expressions in {expression} format
            def replace_expression(match):
                expression = match.group(1)
                try:
                    # Evaluate the expression safely
                    result = eval(expression, {"__builtins__": {}}, context)
                    return str(result)
                except Exception:
                    return f"[Error in expression: {expression}]"

            # Replace {expression} patterns
            dynamic_help = re.sub(r'\{([^}]+)\}', replace_expression, template)

            return dynamic_help

        except Exception:
            # Fallback to static help if dynamic generation fails
            if use_case == 'check_measure':
                return self.check_measure_help_text or ""
            elif use_case == 'sales':
                return self.sales_help_text or ""
            elif use_case == 'online':
                return self.online_help_text or ""
            return ""

    # PERFORMANCE: Cached visibility evaluation methods
    @tools.ormcache('self.id', 'values_hash')
    def evaluate_visibility_cached(self, values_hash, field_values):
        """Cached visibility evaluation"""
        return self._evaluate_visibility_uncached(field_values)

    def _evaluate_visibility_uncached(self, field_values):
        """Original visibility evaluation logic without caching"""
        if not self.visibility_condition:
            return True

        try:
            # Create safe evaluation context with configuration values
            ctx = dict(field_values)

            # Add math functions
            import math
            math_context = {k: getattr(math, k) for k in dir(math) if not k.startswith('_')}
            ctx.update(math_context)

            # Evaluate the condition
            result = safe_eval(self.visibility_condition, ctx)
            return result
        except Exception as e:
            _logger.error(f"Error evaluating visibility for {self.name}: {e}")
            return True  # Default to visible on error

    def evaluate_visibility(self, field_values):
        """Main visibility evaluation with caching"""
        if not self.visibility_condition:
            return True

        # Create hash of field values for cache key
        values_str = json.dumps(field_values, sort_keys=True, default=str)
        values_hash = hashlib.md5(values_str.encode()).hexdigest()

        return self.evaluate_visibility_cached(values_hash, field_values)

    @api.model
    def resequence_fields(self, template_id):
        """
        Recompute question numbers for all fields in a template.
        This method can be called after drag and drop reordering.
        """
        if not template_id:
            return False

        fields = self.search([('matrix_id', '=', template_id)])
        if fields:
            fields._compute_question_number()

        return True

    def copy(self, default=None):
        self.ensure_one()
        default = dict(default or {})

        # dont need to add copy name for new copy records
        # if not default.get('name'):
        #     default['name'] = _('%s (Copy)') % self.name

        if not default.get('technical_name'):
            base_name = self.technical_name
            i = 1
            while True:
                technical_name = f'{base_name}_{i}'
                existing = self.search([
                    ('matrix_id', '=', self.matrix_id.id),
                    ('technical_name', '=', technical_name)
                ])
                if not existing:
                    default['technical_name'] = technical_name
                    break
                i += 1

        new_field = super(ConfigMatrixField, self).copy(default)

        # Copy options for selection fields
        if self.field_type == 'selection':
            for option in self.option_ids:
                option.copy({'field_id': new_field.id})

        # Copy component mappings for fields with multiple mappings
        if self.component_mapping_ids:
            for mapping in self.component_mapping_ids:
                mapping.copy({'field_id': new_field.id})

        if self.visibility_condition_ids:
            for condition in self.visibility_condition_ids:
                condition.copy({'field_id': new_field.id})

        # Recompute question numbers
        if new_field.matrix_id:
            fields = self.search([('matrix_id', '=', new_field.matrix_id.id)])
            fields._compute_question_number()

        if new_field.check_measure_default_option_id and new_field.option_ids:
            option = new_field.option_ids.filtered(lambda o: o.name == new_field.check_measure_default_option_id.name and o.value == new_field.check_measure_default_option_id.value)
            if option:
                new_field.check_measure_default_option_id = option.id

        return new_field

    @api.onchange('field_type')
    def _onchange_field_type(self):
        """Reset type-specific fields when type changes"""
        if self.field_type == 'text':
            # Clear number-specific fields
            self.decimal_precision = 0
            self.uom_id = False
            # Clear min/max values for all use cases
            self.check_measure_min_value = False
            self.check_measure_max_value = False
            self.sales_min_value = False
            self.sales_max_value = False
            self.online_min_value = False
            self.online_max_value = False
        elif self.field_type == 'number':
            self.min_length = False
            self.max_length = False
            self.pattern = False
            self.decimal_precision = 3
            # Set default UOM to millimeter if not already set
            if not self.uom_id:
                mm_uom = self.env.ref('uom.product_uom_millimeter', raise_if_not_found=False)
                if mm_uom:
                    self.uom_id = mm_uom.id
        elif self.field_type == 'selection':
            self.min_length = False
            self.max_length = False
            self.pattern = False
            # Clear min/max values for all use cases
            self.check_measure_min_value = False
            self.check_measure_max_value = False
            self.sales_min_value = False
            self.sales_max_value = False
            self.online_min_value = False
            self.online_max_value = False
            self.decimal_precision = 0
            self.uom_id = False
        elif self.field_type == 'boolean':
            self.min_length = False
            self.max_length = False
            self.pattern = False
            # Clear min/max values for all use cases
            self.check_measure_min_value = False
            self.check_measure_max_value = False
            self.sales_min_value = False
            self.sales_max_value = False
            self.online_min_value = False
            self.online_max_value = False
            self.decimal_precision = 0
            self.uom_id = False
            if not self.boolean_true_label:
                self.boolean_true_label = 'Yes'
            if not self.boolean_false_label:
                self.boolean_false_label = 'No'
        elif self.field_type == 'date':
            self.min_length = False
            self.max_length = False
            self.pattern = False
            # Clear min/max values for all use cases
            self.check_measure_min_value = False
            self.check_measure_max_value = False
            self.sales_min_value = False
            self.sales_max_value = False
            self.online_min_value = False
            self.online_max_value = False
            self.decimal_precision = 0
            self.uom_id = False

    def action_add_option(self):
        """Add an option to selection field"""
        self.ensure_one()

        if self.field_type != 'selection':
            raise ValidationError(_('Options can only be added to selection fields.'))

        return {
            'name': _('Add Option'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.option',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_field_id': self.id,
                'default_sequence': len(self.option_ids) * 10 + 10,
            },
        }

    def evaluate_dynamic_value(self, expression, field_values=None):
        """Evaluate a dynamic expression that can be a number or formula"""
        if not expression:
            return None

        # If it's just a number, return it as float
        try:
            return float(expression)
        except (ValueError, TypeError):
            pass

        # If we have field values, try to evaluate as expression
        if field_values:
            try:
                # Simple ternary operator support: condition ? value1 : value2
                if '?' in expression and ':' in expression:
                    parts = expression.split('?', 1)
                    if len(parts) == 2:
                        condition_part = parts[0].strip()
                        value_parts = parts[1].split(':', 1)
                        if len(value_parts) == 2:
                            true_value = value_parts[0].strip()
                            false_value = value_parts[1].strip()

                            # Evaluate condition
                            condition_result = self._evaluate_simple_condition(condition_part, field_values)
                            if condition_result:
                                return float(true_value)
                            else:
                                return float(false_value)

            except Exception:
                pass

        return None

    def _evaluate_simple_condition(self, condition, field_values):
        """Evaluate a simple condition like 'field > 2510'"""
        try:
            # Replace field names with their values
            for field_name, value in field_values.items():
                if field_name in condition:
                    condition = condition.replace(field_name, str(value))

            # Simple operator evaluation
            if '>' in condition:
                parts = condition.split('>')
                if len(parts) == 2:
                    left = float(parts[0].strip())
                    right = float(parts[1].strip())
                    return left > right
            elif '<' in condition:
                parts = condition.split('<')
                if len(parts) == 2:
                    left = float(parts[0].strip())
                    right = float(parts[1].strip())
                    return left < right
            elif '==' in condition:
                parts = condition.split('==')
                if len(parts) == 2:
                    left = parts[0].strip()
                    right = parts[1].strip()
                    return left == right

        except Exception:
            pass

        return False

    def get_field_data(self, active_use_case='check_measure', field_values=None):
        """Get field data for API/UI based on provided use case"""
        self.ensure_one()

        # Check if we're coming from a sales order - if so, always use check_measure
        from_sale_order = self.env.context.get('from_sale_order', False)
        if from_sale_order:
            active_use_case = 'check_measure'

        # Determine visibility based on use case
        if active_use_case == 'check_measure' and not self.check_measure_visible:
            return None
        elif active_use_case == 'sales' and not self.sales_visible:
            return None
        elif active_use_case == 'online' and not self.online_visible:
            return None

        # Get properties based on use case
        if active_use_case == 'check_measure':
            min_value_expr = self.check_measure_min_value if self.check_measure_min_value else None
            max_value_expr = self.check_measure_max_value if self.check_measure_max_value else None
            default_value = self.check_measure_default_value
            default_option_id = self.check_measure_default_option_id
            help_text = self.check_measure_help_text
            use_dynamic_default = self.check_measure_use_dynamic_default
            dynamic_default_template = self.check_measure_dynamic_default_template
        elif active_use_case == 'sales':
            min_value_expr = self.sales_min_value if self.sales_min_value else None
            max_value_expr = self.sales_max_value if self.sales_max_value else None
            default_value = self.sales_default_value
            default_option_id = self.sales_default_option_id
            help_text = self.sales_help_text
            use_dynamic_default = self.sales_use_dynamic_default
            dynamic_default_template = self.sales_dynamic_default_template
        elif active_use_case == 'online':
            min_value_expr = self.online_min_value if self.online_min_value else None
            max_value_expr = self.online_max_value if self.online_max_value else None
            default_value = self.online_default_value
            default_option_id = self.online_default_option_id
            help_text = self.online_help_text
            use_dynamic_default = self.online_use_dynamic_default
            dynamic_default_template = self.online_dynamic_default_template
        else:
            min_value_expr = None
            max_value_expr = None
            default_value = None
            default_option_id = None
            help_text = None
            use_dynamic_default = False
            dynamic_default_template = None

        # Evaluate dynamic min/max values
        min_value = self.evaluate_dynamic_value(min_value_expr, field_values)
        max_value = self.evaluate_dynamic_value(max_value_expr, field_values)

        # Handle dynamic default values
        if use_dynamic_default and dynamic_default_template:
            try:
                # Generate dynamic default value
                dynamic_default = self.generate_dynamic_default(field_values or {}, active_use_case)
                if dynamic_default is not None and dynamic_default != "":
                    default_value = dynamic_default
                    _logger.debug(f"Applied dynamic default '{dynamic_default}' to field {self.technical_name}")
            except Exception as e:
                # Log the error and fall back to static default
                _logger.warning(f"Failed to generate dynamic default for field {self.technical_name}: {e}")
                # Keep the original default_value

        # For selection fields, use the default option's value if set
        if self.field_type == 'selection' and default_option_id:
            default_value = default_option_id.value

        # All fields are required
        # Build field data with use case-specific settings
        data = {
            'id': self.id,
            'name': self.name,
            'technical_name': self.technical_name,
            'field_type': self.field_type,
            'required': True,  # All fields are required
            'help_text': help_text,
            'default_value': default_value,
            'visibility_condition': self.visibility_condition,
            'sequence': self.sequence,
            'question_number': self.question_number,
            'use_case': active_use_case,
            # Add dynamic default information
            'use_dynamic_default': use_dynamic_default,
            'dynamic_default_template': dynamic_default_template,
        }

        # Add type-specific properties
        if self.field_type == 'number':
            data.update({
                'min_value': min_value,
                'max_value': max_value,
                'decimal_precision': self.decimal_precision,
                'uom_id': self.uom_id.id if self.uom_id else False,
                'uom_name': self.uom_id.name if self.uom_id else '',
            })
        elif self.field_type == 'text':
            data.update({
                'min_length': self.min_length,
                'max_length': self.max_length,
                'pattern': self.pattern,
            })
        elif self.field_type == 'selection':
            data['options'] = [
                {'id': opt.id, 'value': opt.value, 'name': opt.name}
                for opt in self.option_ids
            ]
        elif self.field_type == 'boolean':
            data.update({
                'true_label': self.boolean_true_label or 'Yes',
                'false_label': self.boolean_false_label or 'No',
            })
        elif self.field_type == 'date':
            # No special properties for date fields yet
            pass

        return data

    @api.model
    def get_field_widget(self, field_type):
        """Get appropriate widget for field type"""
        widgets = {
            'text': 'text',
            'number': 'number',
            'selection': 'selection',
            'boolean': 'boolean',
            'date': 'date',
        }
        return widgets.get(field_type, 'text')

    def action_clear_visibility_conditions(self):
        """Clear all visibility conditions"""
        self.ensure_one()
        self.visibility_condition_ids.unlink()
        return True



    def action_manage_dependencies(self):
        """Open wizard to manage dependencies for this field"""
        self.ensure_one()

        # Create a wizard to manage dependencies
        wizard = self.env['config.matrix.manage.dependencies.wizard'].create({
            'field_id': self.id,
        })

        return {
            'name': _('Manage Dependencies'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.manage.dependencies.wizard',
            'res_id': wizard.id,
            'view_mode': 'form',
            'target': 'new',
            'context': self.env.context,
        }

    def action_test_visibility_condition(self):
        """Test visibility condition with sample values"""
        self.ensure_one()

        if not self.visibility_condition:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Condition'),
                    'message': _('This field has no visibility condition, so it will always be visible.'),
                    'type': 'info',
                }
            }

        return {
            'name': _('Test Visibility Condition'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.test.condition.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_field_id': self.id,
                'default_condition': self.visibility_condition,
            },
        }

    def action_convert_visibility_condition(self):
        """Convert string visibility condition to visibility_condition_ids records"""
        self.ensure_one()

        if not self.visibility_condition:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Condition'),
                    'message': _('This field has no visibility condition to convert.'),
                    'type': 'info',
                }
            }

        # Check if it's a JSON condition (from import)
        if self.visibility_condition.startswith('__JSON__'):
            try:
                # Extract the JSON part
                json_str = self.visibility_condition[8:]  # Remove __JSON__ prefix
                # Parse JSON but we don't need to use the conditions directly
                json.loads(json_str)  # Just validate the JSON

                # Clear existing conditions
                self.visibility_condition_ids.unlink()

                # Create a single expression condition
                self.env['config.matrix.visibility.condition'].create({
                    'field_id': self.id,
                    'condition_type': 'expression',
                    'expression': self.visibility_condition,
                    'logic_operator': 'and',
                    'sequence': 10,
                })

                # Success message
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Conversion Successful'),
                        'message': _('Visibility condition has been converted to a condition record.'),
                        'type': 'success',
                    }
                }
            except Exception as e:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Conversion Failed'),
                        'message': _('Failed to convert condition: %s') % str(e),
                        'type': 'danger',
                    }
                }

    def action_add_visibility_condition(self):
        """Add a new visibility condition for this field"""
        self.ensure_one()

        return {
            'name': _('Add Visibility Condition'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.visibility.condition',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_field_id': self.id, 'default_matrix_id': self.matrix_id.id},
        }

    def action_view_field_visibility_conditions(self):
        """View all visibility conditions for this field"""
        self.ensure_one()

        return {
            'name': _('Field Visibility Conditions'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.visibility.condition',
            'view_mode': 'list,form',
            'domain': [('field_id', '=', self.id)],
            'context': {'default_field_id': self.id, 'default_matrix_id': self.matrix_id.id},
        }

    def action_open_component_builder(self):
        """Open the component builder wizard"""
        self.ensure_one()

        return {
            'name': _('Build Component Mapping'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.component.builder',
            'view_mode': 'form',
            'context': {
                'default_field_id': self.id,
                'default_matrix_id': self.matrix_id.id,
                'default_quantity_formula': self.quantity_formula or '1',
            },
        }

    def action_clear_component_mapping(self):
        """Clear the component mapping"""
        self.ensure_one()
        self.component_product_id = False
        self.quantity_formula = "1"
        return True

    def action_test_quantity_formula(self):
        """Test quantity formula with sample values"""
        self.ensure_one()

        if not self.component_product_id or not self.quantity_formula:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Formula'),
                    'message': _('This field has no component or quantity formula.'),
                    'type': 'info',
                }
            }

        return {
            'name': _('Test Quantity Formula'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.test.formula.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_field_id': self.id,
                'default_formula': self.quantity_formula,
            },
        }

    def find_field_dependencies(self):
        """Find fields referenced in visibility condition and quantity formula"""
        self.ensure_one()

        dependencies = set()
        expressions = []

        if self.visibility_condition:
            expressions.append(self.visibility_condition)

        if self.component_product_id and self.quantity_formula:
            expressions.append(self.quantity_formula)

        # Extract field references from expressions
        for expr in expressions:
            # Find all potential field references
            # This is a simple approach that looks for valid technical names
            matches = re.findall(r'([a-z][a-z0-9_]*)', expr)

            for match in matches:
                # Check if it's a field in the same template
                field = self.search([
                    ('matrix_id', '=', self.matrix_id.id),
                    ('technical_name', '=', match),
                    ('id', '!=', self.id)
                ], limit=1)

                if field:
                    dependencies.add(field.id)

        return list(dependencies)

    def check_circular_dependencies(self):
        """Check for circular dependencies in visibility conditions"""
        self.ensure_one()

        visited = set()
        path = []

        def dfs(field):
            if field.id in visited:
                return False

            visited.add(field.id)
            path.append(field)

            dependencies = field.find_field_dependencies()
            for dep_id in dependencies:
                # Check if this dependency is in the current path (circular)
                if dep_id in [f.id for f in path]:
                    # Found circular dependency
                    return True

                dep_field = self.browse(dep_id)
                if dfs(dep_field):
                    return True

            path.pop()
            return False

        return dfs(self)

    # Additional methods for extension

    def get_extended_field_types(self):
        """Get extended field types from installed modules"""
        field_types = [
            ('text', 'Text'),
            ('number', 'Number'),
            ('selection', 'Selection'),
            ('boolean', 'Yes/No'),
        ]

        # Allow other modules to extend field types
        if hasattr(self, '_get_field_type_extensions'):
            field_types.extend(self._get_field_type_extensions())

        return field_types

    def generate_validation_code(self):
        """Generate validation code for this field"""
        self.ensure_one()

        code = []

        # All fields are required
        code.append(f"if not value:\n    errors.append('{self.name} is required')")

        if self.field_type == 'text':
            if self.min_length:
                code.append(f"if value and len(value) < {self.min_length}:\n    errors.append('{self.name} must be at least {self.min_length} characters')")

            if self.max_length:
                code.append(f"if value and len(value) > {self.max_length}:\n    errors.append('{self.name} cannot exceed {self.max_length} characters')")

            if self.pattern:
                code.append(f"if value and not re.match(r'{self.pattern}', value):\n    errors.append('{self.name} has an invalid format')")

        elif self.field_type == 'number':
            code.append("try:\n    num_value = float(value)\nexcept (ValueError, TypeError):\n    errors.append('Invalid number format')\n    return errors")

            # Note: Min/max validation is now handled dynamically in the frontend
            # based on use-case specific min/max values

        return "\n".join(code)

    def generate_dynamic_error(self, values=None, use_case='check_measure'):
        """Generate dynamic error message based on current field values and use case"""
        if not values:
            values = {}

        # Determine which dynamic error template to use based on use case
        use_dynamic_error = False
        template = None
        error_condition = None

        if use_case == 'check_measure':
            use_dynamic_error = self.check_measure_use_dynamic_error
            template = self.check_measure_dynamic_error_template
            error_condition = self.check_measure_error_condition
        elif use_case == 'sales':
            use_dynamic_error = self.sales_use_dynamic_error
            template = self.sales_dynamic_error_template
            error_condition = self.sales_error_condition
        elif use_case == 'online':
            use_dynamic_error = self.online_use_dynamic_error
            template = self.online_dynamic_error_template
            error_condition = self.online_error_condition

        # Check if error condition is met
        if error_condition:
            try:
                # Create a safe evaluation context
                eval_context = {
                    'True': True,
                    'False': False,
                    'None': None,
                    'min': min,
                    'max': max,
                    'abs': abs,
                    'round': round,
                }
                eval_context.update(values)

                # Evaluate the error condition
                condition_result = eval(error_condition, {"__builtins__": {}}, eval_context)
                if not condition_result:
                    return ""  # Don't show error if condition is not met
            except Exception:
                return ""  # Don't show error if condition evaluation fails

        if not use_dynamic_error or not template:
            # Return static error text based on use case if condition is met
            if error_condition:  # Only show static error if condition exists and was met
                if use_case == 'check_measure':
                    return self.check_measure_error_text or ""
                elif use_case == 'sales':
                    return self.sales_error_text or ""
                elif use_case == 'online':
                    return self.online_error_text or ""
            return ""

        # Process the template with current field values
        try:
            # Create a safe context for template evaluation
            context = {
                'field_name': self.technical_name,
                'field_label': self.name,
                **values
            }

            # Add mathematical functions for calculations
            context.update({
                'min': min,
                'max': max,
                'abs': abs,
                'round': round,
            })

            # Handle complex expressions in {expression} format
            def replace_expression(match):
                expression = match.group(1)
                try:
                    # Evaluate the expression safely
                    result = eval(expression, {"__builtins__": {}}, context)
                    return str(result)
                except Exception:
                    return f"[Error in expression: {expression}]"

            # Replace {expression} patterns
            dynamic_error = re.sub(r'\{([^}]+)\}', replace_expression, template)

            return dynamic_error

        except Exception:
            # Fallback to static error if dynamic generation fails
            if use_case == 'check_measure':
                return self.check_measure_error_text or ""
            elif use_case == 'sales':
                return self.sales_error_text or ""
            elif use_case == 'online':
                return self.online_error_text or ""

    def generate_dynamic_default(self, values=None, use_case='check_measure'):
        """Generate dynamic default value based on current field values and use case"""
        if not values:
            values = {}

        # Determine which dynamic default template to use based on use case
        use_dynamic_default = False
        template = None

        if use_case == 'check_measure':
            use_dynamic_default = self.check_measure_use_dynamic_default
            template = self.check_measure_dynamic_default_template
        elif use_case == 'sales':
            use_dynamic_default = self.sales_use_dynamic_default
            template = self.sales_dynamic_default_template
        elif use_case == 'online':
            use_dynamic_default = self.online_use_dynamic_default
            template = self.online_dynamic_default_template

        if not use_dynamic_default or not template:
            return None

        # Validate template before processing
        if not self._validate_dynamic_expression(template):
            _logger.warning(f"Invalid dynamic default template for field {self.technical_name}: {template}")
            return None

        # Process the template with current field values
        try:
            # Create a safe context for template evaluation
            context = {
                'field_name': self.technical_name,
                'field_label': self.name,
                **values
            }

            # Add mathematical functions for calculations
            context.update({
                'min': min,
                'max': max,
                'abs': abs,
                'round': round,
                'len': len,
                'str': str,
                'int': int,
                'float': float,
                'bool': bool,
            })

            # Handle complex expressions in {expression} format
            def replace_expression(match):
                expression = match.group(1).strip()
                if not expression:
                    return ""

                try:
                    # Additional validation for the expression
                    if not self._validate_single_expression(expression):
                        _logger.warning(f"Invalid expression in dynamic default: {expression}")
                        return ""

                    # Convert JavaScript-style ternary to Python conditional expression
                    python_expression = self._convert_js_to_python(expression)

                    # Evaluate the expression safely with restricted builtins
                    safe_builtins = {
                        '__builtins__': {
                            'len': len, 'str': str, 'int': int, 'float': float, 'bool': bool,
                            'min': min, 'max': max, 'abs': abs, 'round': round
                        }
                    }
                    result = eval(python_expression, safe_builtins, context)
                    return str(result) if result is not None else ""
                except (NameError, KeyError) as e:
                    # Missing variable - this is expected during initial load
                    _logger.debug(f"Missing variable in dynamic default expression for {self.technical_name}: {e}")
                    return ""
                except Exception as e:
                    # Log other errors for debugging
                    _logger.error(f"Error evaluating dynamic default expression '{expression}' for field {self.technical_name}: {e}")
                    return ""

            # Replace {expression} patterns
            dynamic_default = re.sub(r'\{([^}]+)\}', replace_expression, template)

            return dynamic_default.strip()

        except Exception as e:
            _logger.error(f"Error generating dynamic default for field {self.technical_name}: {e}")
            return None

    def _validate_dynamic_expression(self, template):
        """Validate a dynamic default template for basic safety"""
        if not template or not isinstance(template, str):
            return False

        # Check for dangerous patterns
        dangerous_patterns = [
            'import ', '__import__', 'exec', 'eval', 'compile',
            'open(', 'file(', 'input(', 'raw_input(',
            'globals(', 'locals(', 'vars(', 'dir(',
            'getattr', 'setattr', 'delattr', 'hasattr',
            '__', 'subprocess', 'os.', 'sys.',
        ]

        template_lower = template.lower()
        for pattern in dangerous_patterns:
            if pattern in template_lower:
                return False

        # Check for balanced braces
        brace_count = 0
        for char in template:
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count < 0:
                    return False

        return brace_count == 0

    def _validate_single_expression(self, expression):
        """Validate a single expression within braces"""
        if not expression or not isinstance(expression, str):
            return False

        # Check for dangerous patterns in the expression
        dangerous_patterns = [
            'import ', '__import__', 'exec', 'eval', 'compile',
            'open(', 'file(', 'input(', 'raw_input(',
            'globals(', 'locals(', 'vars(', 'dir(',
            'getattr', 'setattr', 'delattr',
            'subprocess', 'os.', 'sys.',
        ]

        expression_lower = expression.lower()
        for pattern in dangerous_patterns:
            if pattern in expression_lower:
                return False

        # Check for balanced parentheses
        paren_count = 0
        for char in expression:
            if char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
                if paren_count < 0:
                    return False

        return paren_count == 0

    def _convert_js_to_python(self, expression):
        """Convert JavaScript-style expressions to Python syntax"""
        # Handle ternary operator: condition ? value1 : value2 -> value1 if condition else value2
        ternary_pattern = r'(.+?)\s*\?\s*(.+?)\s*:\s*(.+)'
        match = re.match(ternary_pattern, expression)
        if match:
            condition, true_value, false_value = match.groups()
            return f"({true_value.strip()}) if ({condition.strip()}) else ({false_value.strip()})"

        # Convert JavaScript operators to Python
        python_expression = expression
        python_expression = re.sub(r'\b&&\b', ' and ', python_expression)
        python_expression = re.sub(r'\b\|\|\b', ' or ', python_expression)
        python_expression = re.sub(r'\b!\b', ' not ', python_expression)
        python_expression = re.sub(r'\btrue\b', 'True', python_expression)
        python_expression = re.sub(r'\bfalse\b', 'False', python_expression)
        python_expression = re.sub(r'\bnull\b', 'None', python_expression)

        return python_expression