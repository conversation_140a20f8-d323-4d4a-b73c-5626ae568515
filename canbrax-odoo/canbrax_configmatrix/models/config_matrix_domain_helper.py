# -*- coding: utf-8 -*-

import json
import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)

class ConfigMatrixDomainHelper(models.TransientModel):
    """
    UTILITY MODEL - NOT IMPORTED
    
    Helper model for domain building with dynamic fields.
    This is a standalone utility class that provides domain validation 
    and helper functions for the ConfigMatrix system.
    
    This model is not imported in __init__.py as it's meant to be used
    as a utility only when needed.
    """
    _name = 'config.matrix.domain.helper'
    _description = 'Domain Builder Helper for Dynamic Fields'

    template_id = fields.Many2one('config.matrix.template', 'Template', required=True)
    field_id = fields.Many2one('config.matrix.field', 'Field')
    current_domain = fields.Text('Current Domain', help='Current domain condition')
    built_domain = fields.Text('Built Domain', help='Domain built using the helper')

    @api.model
    def get_template_fields_data(self, template_id):
        """Get all fields for a template formatted for the domain builder"""
        try:
            template = self.env['config.matrix.template'].browse(template_id)
            if not template.exists():
                _logger.warning(f"Template {template_id} does not exist")
                return []

            fields_data = []
            for section in template.section_ids:
                if not section.field_ids:
                    continue

                for field in section.field_ids:
                    try:
                        field_data = {
                            'id': field.id,
                            'name': field.name or '',
                            'technical_name': field.technical_name or '',
                            'field_type': field.field_type or 'text',
                            'section_name': section.name or '',
                        }

                        # Add options for selection fields
                        if field.field_type == 'selection' and field.option_ids:
                            field_data['options'] = []
                            for opt in field.option_ids:
                                if opt.value:  # Only add options with values
                                    field_data['options'].append({
                                        'value': opt.value,
                                        'name': opt.name or opt.value,
                                        'id': opt.id
                                    })

                        # Add UOM info for number fields
                        if field.field_type == 'number' and field.uom_id:
                            field_data['uom'] = {
                                'name': field.uom_id.name or '',
                                'symbol': field.uom_id.name or ''
                            }

                        fields_data.append(field_data)
                    except Exception as e:
                        _logger.error(f"Error processing field {field.id}: {str(e)}")
                        continue

            return fields_data
        except Exception as e:
            _logger.error(f"Error getting template fields data for template {template_id}: {str(e)}")
            return []

    @api.model
    def validate_domain(self, domain_str, template_id):
        """Validate a domain string against template fields"""
        try:
            # Parse the domain
            domain = json.loads(domain_str) if isinstance(domain_str, str) else domain_str

            if not isinstance(domain, list):
                return {'valid': False, 'error': 'Domain must be a list'}

            # Get template fields for validation
            template = self.env['config.matrix.template'].browse(template_id)
            valid_fields = {f.technical_name: f for section in template.section_ids for f in section.field_ids}

            # Validate each condition in the domain
            errors = []
            for item in domain:
                try:
                    if isinstance(item, list) and len(item) >= 3:
                        field_name, operator, value = item[0], item[1], item[2]

                        # Check if field exists
                        if field_name not in valid_fields:
                            errors.append(f"Field '{field_name}' not found in template")
                            continue

                        field = valid_fields[field_name]

                        # Validate operator for field type
                        valid_operators = self._get_valid_operators(field.field_type)
                        if operator not in valid_operators:
                            errors.append(f"Operator '{operator}' not valid for {field.field_type} field '{field_name}'")

                        # Validate value type
                        value_error = self._validate_value_for_field(field, value)
                        if value_error:
                            errors.append(f"Field '{field_name}': {value_error}")
                    elif isinstance(item, str) and item in ['&', '|']:
                        # Logic operators are valid
                        continue
                    else:
                        errors.append(f"Invalid domain item format: {item}")
                except Exception as e:
                    errors.append(f"Error processing domain item {item}: {str(e)}")
                    continue

            if errors:
                return {'valid': False, 'error': '; '.join(errors)}

            return {'valid': True, 'message': 'Domain is valid'}

        except json.JSONDecodeError as e:
            return {'valid': False, 'error': f'Invalid JSON format: {str(e)}'}
        except Exception as e:
            return {'valid': False, 'error': f'Validation error: {str(e)}'}

    def _get_valid_operators(self, field_type):
        """Get valid operators for a field type"""
        base_operators = ['=', '!=']

        if field_type == 'number':
            return base_operators + ['>', '<', '>=', '<=']
        elif field_type == 'selection':
            return base_operators + ['in', 'not in']
        elif field_type == 'text':
            return base_operators + ['ilike', 'not ilike', 'like', 'not like']
        elif field_type == 'boolean':
            return base_operators
        else:
            return base_operators

    def _validate_value_for_field(self, field, value):
        """Validate a value against a field definition"""
        if field.field_type == 'number':
            if not isinstance(value, (int, float)):
                try:
                    float(value)
                except (ValueError, TypeError):
                    return f"Value must be a number, got {type(value).__name__}"

        elif field.field_type == 'boolean':
            if not isinstance(value, bool):
                return f"Value must be boolean, got {type(value).__name__}"

        elif field.field_type == 'selection':
            if isinstance(value, list):
                # For 'in' and 'not in' operators
                valid_values = {opt.value for opt in field.option_ids}
                invalid_values = set(value) - valid_values
                if invalid_values:
                    return f"Invalid option values: {', '.join(invalid_values)}"
            else:
                # For '=' and '!=' operators
                valid_values = {opt.value for opt in field.option_ids}
                if value not in valid_values:
                    return f"Invalid option value '{value}'. Valid options: {', '.join(valid_values)}"

        return None  # No error

    @api.model
    def generate_domain_examples(self, template_id):
        """Generate example domains for a template"""
        template = self.env['config.matrix.template'].browse(template_id)
        if not template.exists():
            return []

        examples = []

        # Get some fields for examples
        selection_fields = []
        number_fields = []
        boolean_fields = []

        for section in template.section_ids:
            for field in section.field_ids:
                if field.field_type == 'selection' and field.option_ids:
                    selection_fields.append(field)
                elif field.field_type == 'number':
                    number_fields.append(field)
                elif field.field_type == 'boolean':
                    boolean_fields.append(field)

        # Generate examples based on available fields
        if selection_fields:
            field = selection_fields[0]
            if field.option_ids:
                examples.append({
                    'title': 'Simple Selection Condition',
                    'description': f'Show field when {field.name} equals specific value',
                    'domain': json.dumps([[field.technical_name, '=', field.option_ids[0].value]])
                })

                if len(field.option_ids) > 1:
                    values = [opt.value for opt in field.option_ids[:2]]
                    examples.append({
                        'title': 'Multiple Selection Values (OR)',
                        'description': f'Show field when {field.name} is one of several values',
                        'domain': json.dumps([[field.technical_name, 'in', values]])
                    })

        if number_fields:
            field = number_fields[0]
            examples.append({
                'title': 'Number Range Condition',
                'description': f'Show field when {field.name} is within range',
                'domain': json.dumps([[field.technical_name, '>=', 1000], [field.technical_name, '<=', 2000]])
            })

        if len(selection_fields) > 1:
            field1, field2 = selection_fields[:2]
            examples.append({
                'title': 'Multiple Field Conditions (AND)',
                'description': f'Show when both {field1.name} and {field2.name} meet conditions',
                'domain': json.dumps([
                    [field1.technical_name, '=', field1.option_ids[0].value],
                    [field2.technical_name, '=', field2.option_ids[0].value]
                ])
            })

        if selection_fields and number_fields:
            sel_field = selection_fields[0]
            num_field = number_fields[0]
            examples.append({
                'title': 'Mixed Field Types',
                'description': f'Combine selection and number conditions',
                'domain': json.dumps([
                    [sel_field.technical_name, '=', sel_field.option_ids[0].value],
                    [num_field.technical_name, '>', 500]
                ])
            })

        return examples

    @api.model
    def validate_domain_for_template(self, domain_str, template_id):
        """
        Validate a domain string against template fields.
        Returns validation result with details.
        """
        try:
            import json

            if isinstance(domain_str, str):
                domain = json.loads(domain_str)
            else:
                domain = domain_str

            if not isinstance(domain, list):
                return {
                    'valid': False,
                    'error': 'Domain must be a list',
                    'details': []
                }

            template = self.env['config.matrix.template'].browse(template_id)
            if not template.exists():
                return {
                    'valid': False,
                    'error': f'Template {template_id} not found',
                    'details': []
                }

            # Build map of valid fields
            valid_fields = {}
            for section in template.section_ids:
                for field in section.field_ids:
                    valid_fields[field.technical_name] = field

            # Validate domain conditions
            errors = []
            warnings = []

            for item in domain:
                if isinstance(item, list) and len(item) == 3:
                    field_name, operator, value = item

                    # Check if field exists
                    if field_name not in valid_fields:
                        errors.append(f"Field '{field_name}' not found in template")
                        continue

                    field = valid_fields[field_name]

                    # Validate operator for field type
                    valid_operators = self._get_valid_operators_for_field_type(field.field_type)
                    if operator not in valid_operators:
                        errors.append(f"Operator '{operator}' not valid for {field.field_type} field '{field_name}'")

                    # Validate value
                    value_validation = self._validate_value_for_field_enhanced(field, operator, value)
                    if value_validation.get('error'):
                        errors.append(f"Field '{field_name}': {value_validation['error']}")
                    elif value_validation.get('warning'):
                        warnings.append(f"Field '{field_name}': {value_validation['warning']}")

            return {
                'valid': len(errors) == 0,
                'error': '; '.join(errors) if errors else None,
                'warnings': warnings,
                'field_count': len([item for item in domain if isinstance(item, list) and len(item) == 3])
            }

        except json.JSONDecodeError as e:
            return {
                'valid': False,
                'error': f'Invalid JSON format: {str(e)}',
                'details': []
            }
        except Exception as e:
            return {
                'valid': False,
                'error': f'Validation error: {str(e)}',
                'details': []
            }

    def _get_valid_operators_for_field_type(self, field_type):
        """Get valid operators for each field type."""
        base_operators = ['=', '!=']

        if field_type == 'number':
            return base_operators + ['>', '<', '>=', '<=']
        elif field_type == 'selection':
            return base_operators + ['in', 'not in']
        elif field_type == 'text':
            return base_operators + ['ilike', 'not ilike', 'like', 'not like']
        elif field_type == 'boolean':
            return base_operators
        elif field_type == 'date':
            return base_operators + ['>', '<', '>=', '<=']
        else:
            return base_operators

    def _validate_value_for_field_enhanced(self, field, operator, value):
        """Validate a value against field type and constraints."""
        result = {'error': None, 'warning': None}

        if field.field_type == 'number':
            if operator in ['>', '<', '>=', '<=', '=', '!=']:
                try:
                    float(value)
                except (ValueError, TypeError):
                    result['error'] = f"Value must be a number, got '{value}'"

        elif field.field_type == 'boolean':
            if not isinstance(value, bool) and value not in [True, False, 'true', 'false', 1, 0]:
                result['error'] = f"Value must be boolean, got '{value}'"

        elif field.field_type == 'selection':
            valid_values = {opt.value for opt in field.option_ids}
            if operator in ['in', 'not in']:
                if isinstance(value, list):
                    invalid_values = set(value) - valid_values
                    if invalid_values:
                        result['error'] = f"Invalid option values: {', '.join(invalid_values)}"
                else:
                    result['error'] = f"Value must be a list for 'in/not in' operators, got '{value}'"
            else:
                if value not in valid_values:
                    result['error'] = f"Invalid option value '{value}'. Valid options: {', '.join(valid_values)}"

        return result

    @api.model
    def get_template_field_info(self, template_id):
        """
        Get comprehensive field information for a template.
        Useful for displaying field references and help.
        """
        template = self.env['config.matrix.template'].browse(template_id)
        if not template.exists():
            return {}

        field_info = {
            'template_name': template.name,
            'sections': []
        }

        for section in template.section_ids.sorted('sequence'):
            section_info = {
                'name': section.name,
                'fields': []
            }

            for field in section.field_ids.sorted('sequence'):
                field_data = {
                    'name': field.name,
                    'technical_name': field.technical_name,
                    'field_type': field.field_type,
                    'help_text': field.check_measure_help_text or field.sales_help_text or field.online_help_text or '',
                    'required': True,  # All matrix fields are typically required
                }

                if field.field_type == 'selection' and field.option_ids:
                    field_data['options'] = [
                        {'value': opt.value, 'name': opt.name}
                        for opt in field.option_ids.sorted('sequence')
                    ]
                elif field.field_type == 'number' and field.uom_id:
                    field_data['unit'] = field.uom_id.name

                section_info['fields'].append(field_data)

            field_info['sections'].append(section_info)

        return field_info
