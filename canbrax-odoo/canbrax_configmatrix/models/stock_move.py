# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
import logging

_logger = logging.getLogger(__name__)


class StockMove(models.Model):
    _inherit = 'stock.move'

    def _prepare_procurement_values(self):
        """
        Override to preserve BOM ID for configurable products.
        
        The standard _prepare_procurement_values method in stock.move doesn't preserve
        custom values like bom_id that were added in sale.order.line._prepare_procurement_values.
        This override ensures that when stock moves create new procurements (which happens
        in the MTO flow), the configuration BOM is preserved.
        
        Returns:
            dict: Procurement values including bom_id for configurable products
        """
        values = super(StockMove, self)._prepare_procurement_values()
        
        _logger.info("🔄 CANBRAX: stock.move._prepare_procurement_values called for product %s", self.product_id.name)
        
        # Check if this move is related to a configurable product sale order line
        if self.sale_line_id and self.sale_line_id.is_configurable:
            _logger.info("🔍 CANBRAX: Move linked to configurable sale line %s", self.sale_line_id.id)
            
            # Check if the sale line has a configuration with BOM
            if self.sale_line_id.config_id and self.sale_line_id.config_id.bom_id:
                config_bom = self.sale_line_id.config_id.bom_id
                values['bom_id'] = config_bom
                _logger.info("✅ CANBRAX: Preserved configuration BOM %s in stock move procurement values for product %s", 
                            config_bom.id, self.product_id.name)
            else:
                _logger.warning("⚠️ CANBRAX: Configurable sale line %s missing config or BOM - Config: %s, BOM: %s", 
                               self.sale_line_id.id,
                               self.sale_line_id.config_id.id if self.sale_line_id.config_id else 'None',
                               self.sale_line_id.config_id.bom_id.id if self.sale_line_id.config_id and self.sale_line_id.config_id.bom_id else 'None')
        
        _logger.info("📦 CANBRAX: Stock move procurement values for %s: %s", self.product_id.name,
                    {k: v for k, v in values.items() if k in ['bom_id', 'sale_line_id', 'group_id']})
        
        return values
