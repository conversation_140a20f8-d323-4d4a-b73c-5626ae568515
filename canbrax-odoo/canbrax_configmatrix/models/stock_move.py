# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
import logging

_logger = logging.getLogger(__name__)


class StockMove(models.Model):
    _inherit = 'stock.move'

    def _prepare_procurement_values(self):
        """
            Procurement values including bom_id for configurable products
        """
        values = super(StockMove, self)._prepare_procurement_values()

        # Check if this move is related to a configurable product sale order line
        if self.sale_line_id and self.sale_line_id.is_configurable:
            # Preserve sale_line_id for MO linking
            values['sale_line_id'] = self.sale_line_id.id

            # Check if the sale line has a configuration with BOM
            if self.sale_line_id.config_id and self.sale_line_id.config_id.bom_id:
                config_bom = self.sale_line_id.config_id.bom_id
                values['bom_id'] = config_bom
            else:
                pass

        return values
