# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)

class ConfigMatrixOptionOperationMapping(models.Model):
    """
    Extended many-to-many relationship between options and operations.
    Allows a single option to map to multiple operations with different work centers and durations.
    """
    _name = 'config.matrix.option.operation.mapping'
    _description = 'Option Operation Mapping'
    _order = 'sequence, id'

    # Core relationships
    option_id = fields.Many2one('config.matrix.option', "Option", required=True, ondelete='cascade')

    # Operation Template Reference
    operation_template_id = fields.Many2one('config.matrix.operation.template', 'Operation Template',
                                           required=True, help='Template operation to use')

    # Override settings (optional)
    custom_duration_formula = fields.Char('Custom Duration Formula',
                                         help='Override the template duration formula. Leave empty to use template default.')
    sequence = fields.Integer("Sequence", default=10, help="Order of operations")
    condition = fields.Char("Additional Condition",
                            help="Extra condition for this operation (beyond option selection)")

    # Computed fields from template
    operation_name = fields.Char('Operation Name', related='operation_template_id.name', readonly=True)
    workcenter_id = fields.Many2one('mrp.workcenter', 'Work Center',
                                   related='operation_template_id.workcenter_id', readonly=True)
    duration_formula = fields.Char('Duration Formula', compute='_compute_duration_formula', readonly=True)

    # Descriptive fields
    name = fields.Char("Mapping Name", help="Optional name to describe this mapping")
    notes = fields.Text("Notes", help="Additional notes about this operation mapping")

    @api.depends('operation_template_id', 'custom_duration_formula')
    def _compute_duration_formula(self):
        """Compute the effective duration formula"""
        for mapping in self:
            if mapping.custom_duration_formula:
                mapping.duration_formula = mapping.custom_duration_formula
            elif mapping.operation_template_id:
                mapping.duration_formula = mapping.operation_template_id.duration_formula or str(mapping.operation_template_id.default_duration)
            else:
                mapping.duration_formula = '60.0'

    # Operation timing details
    setup_time = fields.Float("Setup Time (minutes)", default=0.0, help="Setup time in minutes")
    cleanup_time = fields.Float("Cleanup Time (minutes)", default=0.0, help="Cleanup time in minutes")
    
    # Worksheet information
    worksheet_type = fields.Selection([
        ('pdf', 'PDF'),
        ('google_slide', 'Google Slide'),
        ('text', 'Text')
    ], string="Worksheet Type", default='text')
    worksheet_content = fields.Text("Worksheet Content", help="Instructions or content for the operation")

    # Related fields for convenience
    matrix_id = fields.Many2one('config.matrix.template', related='option_id.matrix_id', string='Template', store=True)
    field_id = fields.Many2one('config.matrix.field', related='option_id.field_id', string='Field', store=True)
    option_name = fields.Char(related='option_id.name', string='Option Name', store=True)
    workcenter_name = fields.Char(related='workcenter_id.name', string='Work Center Name', store=True)

    def action_open_operation_builder(self):
        """Open the operation builder wizard for this mapping"""
        self.ensure_one()

        return {
            'name': _('Build Operation Duration Formula'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.operation.builder',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_option_id': self.option_id.id,
                'default_matrix_id': self.option_id.matrix_id.id,
                'default_duration_formula': self.duration_formula or '60.0',
                'default_workcenter_id': self.workcenter_id.id,
                'default_operation_name': self.operation_name,
                'option_operation_mapping_id': self.id,  # Pass the mapping ID for updating
            },
        }

    def get_operation_data(self, config_values=None):
        """
        Get operation data for BOM generation
        
        Args:
            config_values: Dictionary of configuration values for formula evaluation
            
        Returns:
            dict: Operation data with calculated duration
        """
        self.ensure_one()
        
        # Calculate duration using formula
        duration = self._calculate_duration(config_values or {})
        
        return {
            'name': self.operation_name,
            'workcenter_id': self.workcenter_id.id,
            'time_cycle': duration,  # Duration in minutes
            'time_cycle_manual': duration,
            'sequence': self.sequence,
            'worksheet_type': self.worksheet_type,
            'worksheet': self.worksheet_content or '',
            'note': self.notes or '',
        }

    def _calculate_duration(self, config_values):
        """
        Calculate operation duration using the formula
        
        Args:
            config_values: Dictionary of configuration values
            
        Returns:
            float: Duration in minutes
        """
        self.ensure_one()
        
        if not self.duration_formula:
            return 60.0  # Default 1 hour
            
        try:
            # Create safe evaluation context
            safe_dict = {
                '__builtins__': {},
                'min': min,
                'max': max,
                'abs': abs,
                'round': round,
                'float': float,
                'int': int,
            }
            
            # Add configuration values to context
            safe_dict.update(config_values)
            
            # Evaluate the formula
            result = eval(self.duration_formula, safe_dict)
            return float(result) if result is not None else 60.0
            
        except Exception as e:
            _logger.warning(f"Error evaluating duration formula '{self.duration_formula}': {e}")
            return 60.0  # Default fallback

    @api.constrains('duration_formula')
    def _check_duration_formula(self):
        """Validate the duration formula"""
        for mapping in self:
            if mapping.duration_formula:
                try:
                    # Test with empty config values
                    mapping._calculate_duration({})
                except Exception as e:
                    raise ValidationError(_("Invalid duration formula: %s") % str(e))

    def name_get(self):
        """Custom name_get to show meaningful names"""
        result = []
        for mapping in self:
            name = f"{mapping.operation_name}"
            if mapping.option_name:
                name += f" ({mapping.option_name})"
            if mapping.workcenter_name:
                name += f" - {mapping.workcenter_name}"
            result.append((mapping.id, name))
        return result

    @api.model
    def create(self, vals):
        """Override create to set default operation name if not provided"""
        if not vals.get('operation_name') and vals.get('workcenter_id'):
            workcenter = self.env['mrp.workcenter'].browse(vals['workcenter_id'])
            if workcenter.exists():
                vals['operation_name'] = f"Operation - {workcenter.name}"
        return super().create(vals)
