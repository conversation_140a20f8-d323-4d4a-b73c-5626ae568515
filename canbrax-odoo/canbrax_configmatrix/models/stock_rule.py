# -*- coding: utf-8 -*-

from odoo import models, api, _
import logging

_logger = logging.getLogger(__name__)

class StockRule(models.Model):
    _inherit = 'stock.rule'

    def _get_matching_bom(self, product_id, company_id, values):
        """
        Override to use BOM from configuration when available.
        
        This method extends the standard BOM selection logic to prioritize
        BOMs from product configurations. When a configured product has a
        specific BOM generated from its configuration, that BOM should be
        used for manufacturing instead of the default product BOM.
        
        The standard method already checks for values.get('bom_id', False) first,
        so we just need to ensure the configuration BOM is passed through
        procurement values (which is handled in sale_order_line._prepare_procurement_values).
        
        Args:
            product_id: Product to find BOM for
            company_id: Company context
            values: Procurement values (may contain bom_id from configuration)
            
        Returns:
            mrp.bom: The matching BOM record
        """
        # Check if a specific BOM is provided in values (from configuration)
        if values.get('bom_id', False):
            config_bom = values['bom_id']
            _logger.info(f"Using configuration BOM {config_bom.id} for product {product_id.name}")
            return config_bom
        
        # Fall back to standard BOM selection
        return super(StockRule, self)._get_matching_bom(product_id, company_id, values)
