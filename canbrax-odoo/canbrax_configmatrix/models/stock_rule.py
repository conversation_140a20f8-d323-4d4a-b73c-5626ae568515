# -*- coding: utf-8 -*-

from odoo import models, api, _
import logging

_logger = logging.getLogger(__name__)

class StockRule(models.Model):
    _inherit = 'stock.rule'

    def _get_matching_bom(self, product_id, company_id, values):
        """
        Override to use BOM from configuration when available.
        
        This method extends the standard BOM selection logic to prioritize
        BOMs from product configurations. When a configured product has a
        specific BOM generated from its configuration, that BOM should be
        used for manufacturing instead of the default product BOM.
        
        The standard method already checks for values.get('bom_id', False) first,
        so we just need to ensure the configuration BOM is passed through
        procurement values (which is handled in sale_order_line._prepare_procurement_values).
        
        Args:
            product_id: Product to find BOM for
            company_id: Company context
            values: Procurement values (may contain bom_id from configuration)
            
        Returns:
            mrp.bom: The matching BOM record
        """
        _logger.info("🏭 CANBRAX: _get_matching_bom called for product %s", product_id.name)
        _logger.info("🔍 CANBRAX: Procurement values keys: %s", list(values.keys()))

        # Check if a specific BOM is provided in values (from configuration)
        if values.get('bom_id', False):
            config_bom = values['bom_id']
            _logger.info("✅ CANBRAX: Using configuration BOM %s for product %s", config_bom.id, product_id.name)
            return config_bom
        else:
            _logger.info("ℹ️ CANBRAX: No bom_id in procurement values for product %s", product_id.name)

        # Alternative approach: Look up BOM from sale order line if available
        sale_line_id = values.get('sale_line_id')
        if sale_line_id:
            _logger.info("🔍 CANBRAX: Found sale_line_id %s, checking for configuration BOM", sale_line_id)
            sale_line = self.env['sale.order.line'].browse(sale_line_id)
            if sale_line.exists() and sale_line.is_configurable and sale_line.config_id and sale_line.config_id.bom_id:
                config_bom = sale_line.config_id.bom_id
                _logger.info("✅ CANBRAX: Found configuration BOM %s from sale line %s for product %s",
                            config_bom.id, sale_line_id, product_id.name)
                return config_bom
            else:
                _logger.info("ℹ️ CANBRAX: Sale line %s has no configuration BOM for product %s",
                            sale_line_id, product_id.name)

        # Fall back to standard BOM selection
        _logger.info("📞 CANBRAX: Calling parent _get_matching_bom for product %s", product_id.name)
        bom = super(StockRule, self)._get_matching_bom(product_id, company_id, values)
        _logger.info("🎯 CANBRAX: Parent method returned BOM %s for product %s", bom.id if bom else 'None', product_id.name)
        return bom

    def _run_manufacture(self, procurements):
        """
        Override to add logging for manufacturing order creation.
        """
        _logger.info("🏭 CANBRAX: _run_manufacture called with %d procurements", len(procurements))

        for procurement, rule in procurements:
            _logger.info("🔍 CANBRAX: Processing procurement - Product: %s, Qty: %s, Rule: %s",
                        procurement.product_id.name, procurement.product_qty, rule.name)
            _logger.info("📋 CANBRAX: Procurement values: %s",
                        {k: v for k, v in procurement.values.items() if k in ['bom_id', 'sale_line_id', 'group_id', 'route_ids']})

        # Call parent method
        result = super(StockRule, self)._run_manufacture(procurements)
        _logger.info("✅ CANBRAX: _run_manufacture completed")

        return result

    def _prepare_mo_vals(self, product_id, product_qty, product_uom, location_dest_id, name, origin, company_id, values, bom):
        """
        Override to add logging for MO preparation.
        """
        _logger.info("🏭 CANBRAX: _prepare_mo_vals called for product %s with BOM %s",
                    product_id.name, bom.id if bom else 'None')

        # Call parent method
        mo_vals = super(StockRule, self)._prepare_mo_vals(product_id, product_qty, product_uom, location_dest_id, name, origin, company_id, values, bom)

        # Ensure sale_line_id is preserved in MO values
        if values.get('sale_line_id'):
            mo_vals['sale_line_id'] = values['sale_line_id']
            _logger.info("🔗 CANBRAX: Added sale_line_id %s to MO values", values['sale_line_id'])

        _logger.info("📋 CANBRAX: MO values prepared: %s",
                    {k: v for k, v in mo_vals.items() if k in ['product_id', 'product_qty', 'bom_id', 'origin', 'sale_line_id']})

        return mo_vals
