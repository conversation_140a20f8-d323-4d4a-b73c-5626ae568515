# -*- coding: utf-8 -*-

from odoo import models, api, _
import logging

_logger = logging.getLogger(__name__)

class StockRule(models.Model):
    _inherit = 'stock.rule'

    def _get_matching_bom(self, product_id, company_id, values):
        """
        Override to use BOM from configuration when available.
        
        This method extends the standard BOM selection logic to prioritize
        BOMs from product configurations. When a configured product has a
        specific BOM generated from its configuration, that BOM should be
        used for manufacturing instead of the default product BOM.
        
        The standard method already checks for values.get('bom_id', False) first,
        so we just need to ensure the configuration BOM is passed through
        procurement values (which is handled in sale_order_line._prepare_procurement_values).
        
        Args:
            product_id: Product to find BOM for
            company_id: Company context
            values: Procurement values (may contain bom_id from configuration)
            
        Returns:
            mrp.bom: The matching BOM record
        """
        _logger.info("🏭 CANBRAX: _get_matching_bom called for product %s", product_id.name)
        _logger.info("🔍 CANBRAX: Procurement values keys: %s", list(values.keys()))

        # Check if a specific BOM is provided in values (from configuration)
        if values.get('bom_id', False):
            config_bom = values['bom_id']
            _logger.info("✅ CANBRAX: Using configuration BOM %s for product %s", config_bom.id, product_id.name)
            return config_bom
        else:
            _logger.info("ℹ️ CANBRAX: No bom_id in procurement values for product %s", product_id.name)

        # Alternative approach: Look up BOM from sale order line if available
        sale_line_id = values.get('sale_line_id')
        if sale_line_id:
            _logger.info("🔍 CANBRAX: Found sale_line_id %s, checking for configuration BOM", sale_line_id)
            sale_line = self.env['sale.order.line'].browse(sale_line_id)
            if sale_line.exists() and sale_line.is_configurable and sale_line.config_id and sale_line.config_id.bom_id:
                config_bom = sale_line.config_id.bom_id
                _logger.info("✅ CANBRAX: Found configuration BOM %s from sale line %s for product %s",
                            config_bom.id, sale_line_id, product_id.name)
                return config_bom
            else:
                _logger.info("ℹ️ CANBRAX: Sale line %s has no configuration BOM for product %s",
                            sale_line_id, product_id.name)

        # Fall back to standard BOM selection
        _logger.info("📞 CANBRAX: Calling parent _get_matching_bom for product %s", product_id.name)
        bom = super(StockRule, self)._get_matching_bom(product_id, company_id, values)
        _logger.info("🎯 CANBRAX: Parent method returned BOM %s for product %s", bom.id if bom else 'None', product_id.name)
        return bom
