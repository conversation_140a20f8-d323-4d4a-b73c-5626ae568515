# -*- coding: utf-8 -*-

from odoo import models, api, _
import logging

_logger = logging.getLogger(__name__)

class StockRule(models.Model):
    _inherit = 'stock.rule'

    def _get_matching_bom(self, product_id, company_id, values):
        """
        Override to use BOM from configuration when available.
        """

        # Check if a specific BOM is provided in values (from configuration)
        if values.get('bom_id', False):
            config_bom = values['bom_id']
            return config_bom

        # Alternative approach: Look up BOM from sale order line if available
        sale_line_id = values.get('sale_line_id')
        if sale_line_id:
            sale_line = self.env['sale.order.line'].browse(sale_line_id)
            if sale_line.exists() and sale_line.is_configurable and sale_line.config_id and sale_line.config_id.bom_id:
                config_bom = sale_line.config_id.bom_id
                return config_bom

        # Fall back to standard BOM selection
        bom = super(StockRule, self)._get_matching_bom(product_id, company_id, values)
        return bom

    def _run_manufacture(self, procurements):
        """
        Override to add logging for manufacturing order creation.
        """

        # Call parent method
        result = super(StockRule, self)._run_manufacture(procurements)
        return result

    def _prepare_mo_vals(self, product_id, product_qty, product_uom, location_dest_id, name, origin, company_id, values, bom):
        """
        Override to add logging for MO preparation.
        """

        # Call parent method
        mo_vals = super(StockRule, self)._prepare_mo_vals(product_id, product_qty, product_uom, location_dest_id, name, origin, company_id, values, bom)

        # Ensure sale_line_id is preserved in MO values
        if values.get('sale_line_id'):
            mo_vals['sale_line_id'] = values['sale_line_id']

        return mo_vals
