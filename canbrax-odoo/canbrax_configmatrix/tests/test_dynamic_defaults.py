# -*- coding: utf-8 -*-

import unittest
from unittest.mock import patch, MagicMock
from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError


class TestDynamicDefaults(TransactionCase):
    
    def setUp(self):
        super().setUp()
        self.ConfigMatrixField = self.env['config.matrix.field']
        self.ConfigMatrixTemplate = self.env['config.matrix.template']
        self.ConfigMatrixSection = self.env['config.matrix.section']
        
        # Create a test template
        self.template = self.ConfigMatrixTemplate.create({
            'name': 'Test Template',
            'description': 'Test template for dynamic defaults'
        })
        
        # Create a test section
        self.section = self.ConfigMatrixSection.create({
            'name': 'Test Section',
            'template_id': self.template.id,
            'sequence': 1
        })
    
    def test_validate_dynamic_expression_safe(self):
        """Test that safe expressions pass validation"""
        field = self.ConfigMatrixField.create({
            'name': 'Test Field',
            'technical_name': 'test_field',
            'field_type': 'text',
            'section_id': self.section.id,
            'check_measure_use_dynamic_default': True,
            'check_measure_dynamic_default_template': '{field1 == "value1" ? "default1" : "default2"}'
        })
        
        # Test safe expression
        safe_template = '{field1 == "value1" ? "default1" : "default2"}'
        self.assertTrue(field._validate_dynamic_expression(safe_template))
        
        # Test simple variable substitution
        simple_template = 'Value: {field1}'
        self.assertTrue(field._validate_dynamic_expression(simple_template))
    
    def test_validate_dynamic_expression_dangerous(self):
        """Test that dangerous expressions fail validation"""
        field = self.ConfigMatrixField.create({
            'name': 'Test Field',
            'technical_name': 'test_field',
            'field_type': 'text',
            'section_id': self.section.id,
        })
        
        # Test dangerous expressions
        dangerous_templates = [
            '{import os}',
            '{exec("malicious code")}',
            '{eval("dangerous")}',
            '{open("/etc/passwd")}',
            '{__import__("os")}',
            '{globals()}',
            '{getattr(obj, "attr")}',
        ]
        
        for template in dangerous_templates:
            with self.subTest(template=template):
                self.assertFalse(field._validate_dynamic_expression(template))
    
    def test_validate_single_expression_safe(self):
        """Test that safe single expressions pass validation"""
        field = self.ConfigMatrixField.create({
            'name': 'Test Field',
            'technical_name': 'test_field',
            'field_type': 'text',
            'section_id': self.section.id,
        })
        
        safe_expressions = [
            'field1 == "value1"',
            'field1 + field2',
            'min(field1, field2)',
            'field1 > 0 ? "yes" : "no"',
            'str(field1)',
        ]
        
        for expr in safe_expressions:
            with self.subTest(expression=expr):
                self.assertTrue(field._validate_single_expression(expr))
    
    def test_validate_single_expression_dangerous(self):
        """Test that dangerous single expressions fail validation"""
        field = self.ConfigMatrixField.create({
            'name': 'Test Field',
            'technical_name': 'test_field',
            'field_type': 'text',
            'section_id': self.section.id,
        })
        
        dangerous_expressions = [
            'import os',
            'exec("code")',
            'eval("expr")',
            'open("file")',
            '__import__("module")',
            'getattr(obj, "attr")',
        ]
        
        for expr in dangerous_expressions:
            with self.subTest(expression=expr):
                self.assertFalse(field._validate_single_expression(expr))
    
    def test_generate_dynamic_default_success(self):
        """Test successful dynamic default generation"""
        field = self.ConfigMatrixField.create({
            'name': 'Test Field',
            'technical_name': 'test_field',
            'field_type': 'text',
            'section_id': self.section.id,
            'check_measure_use_dynamic_default': True,
            'check_measure_dynamic_default_template': '{field1 == "red" ? "Color is red" : "Color is not red"}'
        })
        
        # Test with matching condition
        values = {'field1': 'red'}
        result = field.generate_dynamic_default(values, 'check_measure')
        self.assertEqual(result, 'Color is red')
        
        # Test with non-matching condition
        values = {'field1': 'blue'}
        result = field.generate_dynamic_default(values, 'check_measure')
        self.assertEqual(result, 'Color is not red')
    
    def test_generate_dynamic_default_missing_variable(self):
        """Test dynamic default generation with missing variables"""
        field = self.ConfigMatrixField.create({
            'name': 'Test Field',
            'technical_name': 'test_field',
            'field_type': 'text',
            'section_id': self.section.id,
            'check_measure_use_dynamic_default': True,
            'check_measure_dynamic_default_template': '{missing_field == "value" ? "yes" : "no"}'
        })
        
        # Test with missing variable - should handle gracefully
        values = {'other_field': 'value'}
        result = field.generate_dynamic_default(values, 'check_measure')
        # Should return empty string for missing variables
        self.assertEqual(result, '')
    
    def test_generate_dynamic_default_invalid_template(self):
        """Test dynamic default generation with invalid template"""
        field = self.ConfigMatrixField.create({
            'name': 'Test Field',
            'technical_name': 'test_field',
            'field_type': 'text',
            'section_id': self.section.id,
            'check_measure_use_dynamic_default': True,
            'check_measure_dynamic_default_template': '{import os}'
        })
        
        # Test with dangerous template - should return None
        values = {'field1': 'value'}
        result = field.generate_dynamic_default(values, 'check_measure')
        self.assertIsNone(result)
    
    def test_generate_dynamic_default_disabled(self):
        """Test dynamic default generation when disabled"""
        field = self.ConfigMatrixField.create({
            'name': 'Test Field',
            'technical_name': 'test_field',
            'field_type': 'text',
            'section_id': self.section.id,
            'check_measure_use_dynamic_default': False,
            'check_measure_dynamic_default_template': '{field1}'
        })
        
        # Test with disabled dynamic default
        values = {'field1': 'value'}
        result = field.generate_dynamic_default(values, 'check_measure')
        self.assertIsNone(result)
    
    def test_get_field_data_with_dynamic_default(self):
        """Test get_field_data method with dynamic defaults"""
        field = self.ConfigMatrixField.create({
            'name': 'Test Field',
            'technical_name': 'test_field',
            'field_type': 'text',
            'section_id': self.section.id,
            'check_measure_use_dynamic_default': True,
            'check_measure_dynamic_default_template': '{color == "red" ? "Red selected" : "Other color"}',
            'check_measure_default_value': 'Static default',
            'check_measure_visible': True,
        })
        
        # Test with values that trigger dynamic default
        field_values = {'color': 'red'}
        data = field.get_field_data('check_measure', field_values)
        
        self.assertIsNotNone(data)
        self.assertEqual(data['default_value'], 'Red selected')
        self.assertTrue(data['use_dynamic_default'])
    
    def test_get_field_data_fallback_to_static(self):
        """Test get_field_data falls back to static default when dynamic fails"""
        field = self.ConfigMatrixField.create({
            'name': 'Test Field',
            'technical_name': 'test_field',
            'field_type': 'text',
            'section_id': self.section.id,
            'check_measure_use_dynamic_default': True,
            'check_measure_dynamic_default_template': '{import os}',  # Invalid template
            'check_measure_default_value': 'Static default',
            'check_measure_visible': True,
        })
        
        # Test with invalid dynamic template - should fall back to static
        field_values = {'color': 'red'}
        data = field.get_field_data('check_measure', field_values)
        
        self.assertIsNotNone(data)
        self.assertEqual(data['default_value'], 'Static default')
