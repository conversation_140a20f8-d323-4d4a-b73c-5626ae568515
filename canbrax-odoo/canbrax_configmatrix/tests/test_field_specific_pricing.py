# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
import json


class TestFieldSpecificPricing(TransactionCase):
    """Test field-specific pricing matrix functionality"""

    def setUp(self):
        super().setUp()
        
        # Create a product template
        self.product_template = self.env['product.template'].create({
            'name': 'Test Configurable Door',
            'type': 'product',
            'list_price': 100.0,
        })
        
        # Create a configuration template
        self.template = self.env['config.matrix.template'].create({
            'name': 'Test Door Template',
            'product_template_id': self.product_template.id,
            'code': 'TEST_DOOR',
            'active': True,
        })
        
        # Create a section
        self.section = self.env['config.matrix.section'].create({
            'name': 'Dimensions',
            'matrix_id': self.template.id,
            'sequence': 1,
        })
        
        # Create height and width fields
        self.height_field = self.env['config.matrix.field'].create({
            'name': 'Door Height',
            'technical_name': 'door_height',
            'section_id': self.section.id,
            'field_type': 'number',
            'sequence': 1,
        })
        
        self.width_field = self.env['config.matrix.field'].create({
            'name': 'Door Width',
            'technical_name': 'door_width',
            'section_id': self.section.id,
            'field_type': 'number',
            'sequence': 2,
        })
        
        # Create pricing matrices
        self.price_matrix_1 = self.env['config.matrix.price.matrix'].create({
            'name': 'Height Matrix',
            'product_template_id': self.product_template.id,
            'matrix_type': 'price',
            'height_ranges': json.dumps([
                {'min': 1000, 'max': 1500, 'label': '1500'},
                {'min': 1501, 'max': 2000, 'label': '2000'},
            ]),
            'width_ranges': json.dumps([
                {'min': 600, 'max': 800, 'label': '800'},
                {'min': 801, 'max': 1000, 'label': '1000'},
            ]),
            'matrix_data': json.dumps({
                '1500_800': 150.0,
                '1500_1000': 180.0,
                '2000_800': 200.0,
                '2000_1000': 250.0,
            }),
        })
        
        self.price_matrix_2 = self.env['config.matrix.price.matrix'].create({
            'name': 'Width Matrix',
            'product_template_id': self.product_template.id,
            'matrix_type': 'price',
            'height_ranges': json.dumps([
                {'min': 1000, 'max': 1500, 'label': '1500'},
                {'min': 1501, 'max': 2000, 'label': '2000'},
            ]),
            'width_ranges': json.dumps([
                {'min': 600, 'max': 800, 'label': '800'},
                {'min': 801, 'max': 1000, 'label': '1000'},
            ]),
            'matrix_data': json.dumps({
                '1500_800': 50.0,
                '1500_1000': 75.0,
                '2000_800': 100.0,
                '2000_1000': 125.0,
            }),
        })

    def test_field_specific_pricing_single_matrix(self):
        """Test pricing with a single field-specific matrix"""
        # Assign matrix to height field
        self.height_field.pricing_matrix_id = self.price_matrix_1
        
        # Test configuration values
        config_values = {
            'door_height': 1200,
            'door_width': 700,
        }
        
        # Get price from template
        price_result = self.template.get_configuration_price(config_values)
        
        # Should get price from height field's matrix
        self.assertEqual(price_result['price'], 150.0)
        self.assertEqual(len(price_result['breakdown']), 1)
        self.assertEqual(price_result['breakdown'][0]['matrix_name'], 'Height Matrix')
        self.assertEqual(price_result['breakdown'][0]['field_name'], 'Door Height')

    def test_field_specific_pricing_multiple_matrices(self):
        """Test pricing with multiple field-specific matrices"""
        # Assign matrices to both fields
        self.height_field.pricing_matrix_id = self.price_matrix_1
        self.width_field.pricing_matrix_id = self.price_matrix_2
        
        # Test configuration values
        config_values = {
            'door_height': 1200,
            'door_width': 700,
        }
        
        # Get price from template
        price_result = self.template.get_configuration_price(config_values)
        
        # Should get combined price from both matrices
        self.assertEqual(price_result['price'], 200.0)  # 150.0 + 50.0
        self.assertEqual(len(price_result['breakdown']), 2)
        
        # Check breakdown details
        matrix_names = [item['matrix_name'] for item in price_result['breakdown']]
        self.assertIn('Height Matrix', matrix_names)
        self.assertIn('Width Matrix', matrix_names)

    def test_no_field_specific_pricing_fallback(self):
        """Test fallback to template-level pricing when no field-specific matrices"""
        # Don't assign any field-specific matrices
        
        # Test configuration values
        config_values = {
            'door_height': 1200,
            'door_width': 700,
        }
        
        # Get price from template (should return 0 since no template-level matrices either)
        price_result = self.template.get_configuration_price(config_values)
        
        # Should return 0 price with no breakdown
        self.assertEqual(price_result['price'], 0.0)
        self.assertEqual(len(price_result['breakdown']), 0)
