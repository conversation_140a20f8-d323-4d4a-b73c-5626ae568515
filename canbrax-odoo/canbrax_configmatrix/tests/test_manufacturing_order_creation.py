# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError, UserError
from odoo import fields
import json
import logging

_logger = logging.getLogger(__name__)

class TestManufacturingOrderCreation(TransactionCase):
    """Test Manufacturing Order creation for configurable products with MTO+Manufacturing routes"""

    def setUp(self):
        super().setUp()

        # Get route references
        self.mto_route = self.env.ref('stock.route_warehouse0_mto')
        self.manufacture_route = self.env.ref('mrp.route_warehouse0_manufacture')

        # Ensure warehouse is configured for manufacturing
        self.warehouse = self.env.ref('stock.warehouse0')
        self.warehouse.manufacture_to_resupply = True

        # Activate MTO route
        self.mto_route.active = True
        
        # Create a configurable product template
        self.configurable_product_template = self.env['product.template'].create({
            'name': 'Test Configurable Door',
            'type': 'product',  # Storable product that requires manufacturing
            'is_configurable': True,
            'route_ids': [(6, 0, [self.mto_route.id, self.manufacture_route.id])],
            'list_price': 100.0,
        })
        
        self.configurable_product = self.configurable_product_template.product_variant_ids[0]
        
        # Create a non-configurable product for comparison
        self.regular_product_template = self.env['product.template'].create({
            'name': 'Test Regular Product',
            'type': 'product',
            'is_configurable': False,
            'route_ids': [(6, 0, [self.mto_route.id, self.manufacture_route.id])],
            'list_price': 50.0,
        })
        
        self.regular_product = self.regular_product_template.product_variant_ids[0]
        
        # Create a configurable product without manufacturing routes
        self.configurable_no_routes_template = self.env['product.template'].create({
            'name': 'Test Configurable No Routes',
            'type': 'product',
            'is_configurable': True,
            'list_price': 75.0,
        })
        
        self.configurable_no_routes = self.configurable_no_routes_template.product_variant_ids[0]
        
        # Create configuration template
        self.config_template = self.env['config.matrix.template'].create({
            'name': 'Test Door Template',
            'code': 'TEST_DOOR',
            'product_template_id': self.configurable_product_template.id,
            'state': 'active',
        })
        
        # Create a section
        self.section = self.env['config.matrix.section'].create({
            'name': 'Door Specifications',
            'template_id': self.config_template.id,
            'sequence': 1,
        })
        
        # Create fields
        self.height_field = self.env['config.matrix.field'].create({
            'name': 'Door Height',
            'technical_name': 'door_height',
            'field_type': 'number',
            'section_id': self.section.id,
            'sequence': 1,
        })
        
        self.width_field = self.env['config.matrix.field'].create({
            'name': 'Door Width',
            'technical_name': 'door_width',
            'field_type': 'number',
            'section_id': self.section.id,
            'sequence': 2,
        })
        
        # Create component products
        self.frame_product = self.env['product.product'].create({
            'name': 'Door Frame',
            'type': 'product',
        })
        
        self.panel_product = self.env['product.product'].create({
            'name': 'Door Panel',
            'type': 'product',
        })

        # Create BOM for the configurable product
        self.bom = self.env['mrp.bom'].create({
            'product_tmpl_id': self.configurable_product_template.id,
            'product_qty': 1.0,
            'type': 'normal',
            'bom_line_ids': [(0, 0, {
                'product_id': self.frame_product.id,
                'product_qty': 1.0,
            }), (0, 0, {
                'product_id': self.panel_product.id,
                'product_qty': 1.0,
            })],
        })

        # Create partner
        self.partner = self.env['res.partner'].create({
            'name': 'Test Customer',
        })

    def test_route_detection(self):
        """Test route detection utilities"""
        # Create sale order
        sale_order = self.env['sale.order'].create({
            'partner_id': self.partner.id,
        })
        
        # Test configurable product with both routes
        line_with_routes = self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': self.configurable_product.id,
            'product_uom_qty': 1,
        })
        
        self.assertTrue(line_with_routes._has_mto_and_manufacture_routes())
        self.assertTrue(line_with_routes._requires_configured_bom())
        
        # Test configurable product without routes
        line_no_routes = self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': self.configurable_no_routes.id,
            'product_uom_qty': 1,
        })
        
        self.assertFalse(line_no_routes._has_mto_and_manufacture_routes())
        self.assertFalse(line_no_routes._requires_configured_bom())
        
        # Test regular product with routes
        line_regular = self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': self.regular_product.id,
            'product_uom_qty': 1,
        })
        
        self.assertTrue(line_regular._has_mto_and_manufacture_routes())
        self.assertFalse(line_regular._requires_configured_bom())  # Not configurable

    def test_validation_missing_configuration(self):
        """Test validation when configurable product lacks configuration"""
        # Create sale order with configurable product but no configuration
        sale_order = self.env['sale.order'].create({
            'partner_id': self.partner.id,
        })
        
        line = self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': self.configurable_product.id,
            'product_uom_qty': 1,
        })
        
        # Should raise ValidationError when trying to confirm
        with self.assertRaises(ValidationError) as cm:
            sale_order.action_confirm()
        
        self.assertIn('no configuration has been created', str(cm.exception))

    def test_validation_missing_bom(self):
        """Test validation when configuration exists but BOM is missing"""
        # Create sale order
        sale_order = self.env['sale.order'].create({
            'partner_id': self.partner.id,
        })
        
        line = self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': self.configurable_product.id,
            'product_uom_qty': 1,
        })
        
        # Create configuration without BOM
        config = self.env['config.matrix.configuration'].create({
            'template_id': self.config_template.id,
            'product_id': self.configurable_product.id,
            'sale_order_line_id': line.id,
            'config_data': json.dumps({
                'door_height': 2100,
                'door_width': 900,
            }),
        })
        
        line.config_id = config.id
        
        # Should raise ValidationError when trying to confirm
        with self.assertRaises(ValidationError) as cm:
            sale_order.action_confirm()
        
        self.assertIn('no Bill of Materials (BOM) has been generated', str(cm.exception))

    def test_successful_validation_with_bom(self):
        """Test successful validation when configuration has BOM"""
        # Create sale order
        sale_order = self.env['sale.order'].create({
            'partner_id': self.partner.id,
        })
        
        line = self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': self.configurable_product.id,
            'product_uom_qty': 1,
        })
        
        # Create configuration with BOM
        config = self.env['config.matrix.configuration'].create({
            'template_id': self.config_template.id,
            'product_id': self.configurable_product.id,
            'sale_order_line_id': line.id,
            'config_data': json.dumps({
                'door_height': 2100,
                'door_width': 900,
            }),
        })
        
        # Generate BOM
        config.generate_bom()
        line.config_id = config.id
        
        # Should not raise any exception
        try:
            sale_order.action_confirm()
            self.assertEqual(sale_order.state, 'sale')
        except Exception as e:
            self.fail(f"Sale order confirmation failed unexpectedly: {str(e)}")

    def test_procurement_values_with_bom(self):
        """Test that procurement values include BOM ID from configuration"""
        # Create sale order
        sale_order = self.env['sale.order'].create({
            'partner_id': self.partner.id,
        })
        
        line = self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': self.configurable_product.id,
            'product_uom_qty': 1,
        })
        
        # Create configuration with BOM
        config = self.env['config.matrix.configuration'].create({
            'template_id': self.config_template.id,
            'product_id': self.configurable_product.id,
            'sale_order_line_id': line.id,
            'config_data': json.dumps({
                'door_height': 2100,
                'door_width': 900,
            }),
        })
        
        # Generate BOM
        config.generate_bom()
        line.config_id = config.id
        
        # Test procurement values
        procurement_values = line._prepare_procurement_values()
        
        self.assertIn('bom_id', procurement_values)
        self.assertEqual(procurement_values['bom_id'], config.bom_id)

    def test_non_configurable_product_unaffected(self):
        """Test that non-configurable products are not affected by the changes"""
        # Create sale order with regular product
        sale_order = self.env['sale.order'].create({
            'partner_id': self.partner.id,
        })
        
        line = self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': self.regular_product.id,
            'product_uom_qty': 1,
        })
        
        # Should confirm without issues
        try:
            sale_order.action_confirm()
            self.assertEqual(sale_order.state, 'sale')
        except Exception as e:
            self.fail(f"Regular product sale order failed unexpectedly: {str(e)}")
        
        # Procurement values should not include bom_id
        procurement_values = line._prepare_procurement_values()
        self.assertNotIn('bom_id', procurement_values)

    def test_end_to_end_manufacturing_order_creation(self):
        """Test complete flow: configuration → BOM generation → sale order confirmation → MO creation"""
        # Create sale order
        sale_order = self.env['sale.order'].create({
            'partner_id': self.partner.id,
        })

        line = self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': self.configurable_product.id,
            'product_uom_qty': 1,
        })

        # Create configuration
        config = self.env['config.matrix.configuration'].create({
            'template_id': self.config_template.id,
            'product_id': self.configurable_product.id,
            'sale_order_line_id': line.id,
            'config_data': json.dumps({
                'door_height': 2100,
                'door_width': 900,
            }),
        })

        # Generate BOM (simulating save_config behavior)
        config.generate_bom()
        line.config_id = config.id

        # Verify BOM was created
        self.assertTrue(config.bom_id, "BOM should be generated")
        self.assertEqual(config.bom_id.product_id, self.configurable_product)

        # Confirm sale order
        sale_order.action_confirm()
        self.assertEqual(sale_order.state, 'sale')

        # Check that procurement values include the BOM
        procurement_values = line._prepare_procurement_values()
        self.assertIn('bom_id', procurement_values)
        self.assertEqual(procurement_values['bom_id'], config.bom_id)

        # Note: In a real test environment, we would also verify that the Manufacturing Order
        # is created with the correct BOM, but this requires triggering the procurement engine
        # which is complex in unit tests. The key validation is that the BOM is passed through
        # procurement values, which we've verified above.

        _logger.info(f"End-to-end test completed successfully. BOM {config.bom_id.id} passed to procurement.")

    def test_mixed_order_validation(self):
        """Test sale order with mix of configurable and regular products"""
        # Create sale order
        sale_order = self.env['sale.order'].create({
            'partner_id': self.partner.id,
        })

        # Add regular product (should not require validation)
        regular_line = self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': self.regular_product.id,
            'product_uom_qty': 1,
        })

        # Add configurable product without routes (should not require validation)
        no_routes_line = self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': self.configurable_no_routes.id,
            'product_uom_qty': 1,
        })

        # Add configurable product with routes but no configuration (should fail)
        config_line = self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': self.configurable_product.id,
            'product_uom_qty': 1,
        })

        # Should fail validation due to missing configuration
        with self.assertRaises(ValidationError) as cm:
            sale_order.action_confirm()

        self.assertIn('no configuration has been created', str(cm.exception))

        # Now add proper configuration
        config = self.env['config.matrix.configuration'].create({
            'template_id': self.config_template.id,
            'product_id': self.configurable_product.id,
            'sale_order_line_id': config_line.id,
            'config_data': json.dumps({
                'door_height': 2100,
                'door_width': 900,
            }),
        })

        config.generate_bom()
        config_line.config_id = config.id

        # Now should confirm successfully
        sale_order.action_confirm()
        self.assertEqual(sale_order.state, 'sale')

    def test_manufacturing_order_creation_basic(self):
        """Test basic Manufacturing Order creation for products with BOM"""
        # Create sale order with regular product that has BOM and routes
        sale_order = self.env['sale.order'].create({
            'partner_id': self.partner.id,
        })

        line = self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': self.configurable_product.id,
            'product_uom_qty': 1,
        })

        # Confirm the sale order
        sale_order.action_confirm()

        # Check that Manufacturing Order was created
        mo = self.env['mrp.production'].search([
            ('product_id', '=', self.configurable_product.id),
            ('origin', 'like', sale_order.name)
        ])

        self.assertTrue(mo, 'Manufacturing Order should be created for product with BOM and manufacturing route')
        self.assertEqual(mo.product_qty, 1.0, 'Manufacturing Order quantity should match sale order line quantity')
        self.assertEqual(mo.bom_id, self.bom, 'Manufacturing Order should use the correct BOM')
