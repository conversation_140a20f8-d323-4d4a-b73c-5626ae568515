#!/usr/bin/env python3
"""
Simple validation script to test dynamic default improvements.
This can be run independently to verify the validation logic works.
"""

import re
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG)
_logger = logging.getLogger(__name__)


class MockField:
    """Mock field class to test validation methods"""
    
    def __init__(self, technical_name="test_field"):
        self.technical_name = technical_name
    
    def _validate_dynamic_expression(self, template):
        """Validate a dynamic default template for basic safety"""
        if not template or not isinstance(template, str):
            return False
        
        # Check for dangerous patterns
        dangerous_patterns = [
            'import ', '__import__', 'exec', 'eval', 'compile',
            'open(', 'file(', 'input(', 'raw_input(',
            'globals(', 'locals(', 'vars(', 'dir(',
            'getattr', 'setattr', 'delattr', 'hasattr',
            '__', 'subprocess', 'os.', 'sys.',
        ]
        
        template_lower = template.lower()
        for pattern in dangerous_patterns:
            if pattern in template_lower:
                return False
        
        # Check for balanced braces
        brace_count = 0
        for char in template:
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count < 0:
                    return False
        
        return brace_count == 0

    def _validate_single_expression(self, expression):
        """Validate a single expression within braces"""
        if not expression or not isinstance(expression, str):
            return False
        
        # Check for dangerous patterns in the expression
        dangerous_patterns = [
            'import ', '__import__', 'exec', 'eval', 'compile',
            'open(', 'file(', 'input(', 'raw_input(',
            'globals(', 'locals(', 'vars(', 'dir(',
            'getattr', 'setattr', 'delattr',
            'subprocess', 'os.', 'sys.',
        ]
        
        expression_lower = expression.lower()
        for pattern in dangerous_patterns:
            if pattern in expression_lower:
                return False
        
        # Check for balanced parentheses
        paren_count = 0
        for char in expression:
            if char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
                if paren_count < 0:
                    return False
        
        return paren_count == 0

    def _convert_js_to_python(self, expression):
        """Convert JavaScript-style expressions to Python syntax"""
        # Handle ternary operator: condition ? value1 : value2 -> value1 if condition else value2
        ternary_pattern = r'(.+?)\s*\?\s*(.+?)\s*:\s*(.+)'
        match = re.match(ternary_pattern, expression)
        if match:
            condition, true_value, false_value = match.groups()
            return f"({true_value.strip()}) if ({condition.strip()}) else ({false_value.strip()})"

        # Convert JavaScript operators to Python
        python_expression = expression
        python_expression = re.sub(r'\b&&\b', ' and ', python_expression)
        python_expression = re.sub(r'\b\|\|\b', ' or ', python_expression)
        python_expression = re.sub(r'\b!\b', ' not ', python_expression)
        python_expression = re.sub(r'\btrue\b', 'True', python_expression)
        python_expression = re.sub(r'\bfalse\b', 'False', python_expression)
        python_expression = re.sub(r'\bnull\b', 'None', python_expression)

        return python_expression

    def generate_dynamic_default_safe(self, values=None, template=None):
        """Safe version of generate_dynamic_default for testing"""
        if not values:
            values = {}
        
        if not template:
            return None

        # Validate template before processing
        if not self._validate_dynamic_expression(template):
            _logger.warning(f"Invalid dynamic default template for field {self.technical_name}: {template}")
            return None

        try:
            # Create a safe context for template evaluation
            context = {
                'field_name': self.technical_name,
                'field_label': 'Test Field',
                **values
            }

            # Add mathematical functions for calculations
            context.update({
                'min': min,
                'max': max,
                'abs': abs,
                'round': round,
                'len': len,
                'str': str,
                'int': int,
                'float': float,
                'bool': bool,
            })

            # Handle complex expressions in {expression} format
            def replace_expression(match):
                expression = match.group(1).strip()
                if not expression:
                    return ""
                
                try:
                    # Additional validation for the expression
                    if not self._validate_single_expression(expression):
                        _logger.warning(f"Invalid expression in dynamic default: {expression}")
                        return ""

                    # Convert JavaScript-style ternary to Python conditional expression
                    python_expression = self._convert_js_to_python(expression)

                    # Evaluate the expression safely with restricted builtins
                    safe_builtins = {
                        '__builtins__': {
                            'len': len, 'str': str, 'int': int, 'float': float, 'bool': bool,
                            'min': min, 'max': max, 'abs': abs, 'round': round
                        }
                    }
                    result = eval(python_expression, safe_builtins, context)
                    return str(result) if result is not None else ""
                except (NameError, KeyError) as e:
                    # Missing variable - this is expected during initial load
                    _logger.debug(f"Missing variable in dynamic default expression for {self.technical_name}: {e}")
                    return ""
                except Exception as e:
                    # Log other errors for debugging
                    _logger.error(f"Error evaluating dynamic default expression '{expression}' for field {self.technical_name}: {e}")
                    return ""

            # Replace {expression} patterns
            dynamic_default = re.sub(r'\{([^}]+)\}', replace_expression, template)

            return dynamic_default.strip()

        except Exception as e:
            _logger.error(f"Error generating dynamic default for field {self.technical_name}: {e}")
            return None


def test_validation():
    """Test the validation improvements"""
    print("Testing Dynamic Default Validation Improvements")
    print("=" * 50)
    
    field = MockField("test_field")
    
    # Test safe templates
    safe_templates = [
        '{color == "red" ? "Red selected" : "Other color"}',
        'Value: {field1}',
        '{min(width, height)}',
        '{str(field1) + " suffix"}',
        'Simple text without expressions',
    ]
    
    print("\n✅ Testing SAFE templates:")
    for template in safe_templates:
        result = field._validate_dynamic_expression(template)
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status}: {template}")
    
    # Test dangerous templates
    dangerous_templates = [
        '{import os}',
        '{exec("malicious code")}',
        '{eval("dangerous")}',
        '{open("/etc/passwd")}',
        '{__import__("os")}',
        '{globals()}',
        '{getattr(obj, "attr")}',
    ]
    
    print("\n❌ Testing DANGEROUS templates (should fail):")
    for template in dangerous_templates:
        result = field._validate_dynamic_expression(template)
        status = "✅ BLOCKED" if not result else "❌ ALLOWED"
        print(f"  {status}: {template}")
    
    # Test dynamic default generation
    print("\n🔧 Testing Dynamic Default Generation:")
    
    test_cases = [
        {
            'template': '{color == "red" ? "Red selected" : "Other color"}',
            'values': {'color': 'red'},
            'expected': 'Red selected'
        },
        {
            'template': '{color == "red" ? "Red selected" : "Other color"}',
            'values': {'color': 'blue'},
            'expected': 'Other color'
        },
        {
            'template': 'Size: {width} x {height}',
            'values': {'width': 100, 'height': 200},
            'expected': 'Size: 100 x 200'
        },
        {
            'template': '{missing_field}',  # Missing variable
            'values': {'other_field': 'value'},
            'expected': ''
        },
        {
            'template': '{import os}',  # Dangerous template
            'values': {'field': 'value'},
            'expected': None
        },
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        result = field.generate_dynamic_default_safe(
            test_case['values'], 
            test_case['template']
        )
        expected = test_case['expected']
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"  Test {i} {status}: Template: {test_case['template']}")
        print(f"    Values: {test_case['values']}")
        print(f"    Expected: {expected}")
        print(f"    Got: {result}")
        print()


if __name__ == "__main__":
    test_validation()
