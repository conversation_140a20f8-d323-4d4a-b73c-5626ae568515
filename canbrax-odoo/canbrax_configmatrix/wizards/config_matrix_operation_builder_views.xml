<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Operation Builder Form View -->
    <record id="view_config_matrix_operation_builder_form" model="ir.ui.view">
        <field name="name">config.matrix.operation.builder.form</field>
        <field name="model">config.matrix.operation.builder</field>
        <field name="arch" type="xml">
            <form string="Build Operation Mapping">
                <sheet>
                    <div class="alert alert-info" role="alert">
                        <p><strong>Add an operation mapping for this field/option</strong></p>
                        <p>Select an existing operation template or create a new one. This operation will be added to the manufacturing routing when this field/option is used.</p>
                    </div>

                    <group>
                        <field name="matrix_id" invisible="1"/>
                        <field name="field_id" invisible="1"/>
                        <field name="option_id" invisible="1"/>
                        <field name="create_new_template"/>
                    </group>

                    <!-- Existing Template Selection -->
                    <group string="Select Operation Template" invisible="create_new_template">
                        <field name="operation_template_id"
                               required="not create_new_template"
                               options="{'no_create': True, 'no_create_edit': True}"
                               domain="[('active', '=', True)]"/>
                        <field name="operation_name" readonly="1" invisible="not operation_template_id"/>
                        <field name="workcenter_id" readonly="1" invisible="not operation_template_id"/>
                    </group>

                    <!-- New Template Creation -->
                    <group string="Create New Template" invisible="not create_new_template">
                        <field name="new_template_name"
                               required="create_new_template"/>
                        <field name="new_template_workcenter_id"
                               required="create_new_template"/>
                        <field name="new_template_category"/>
                    </group>

                    <!-- Duration Configuration (only for new templates) -->
                    <group invisible="not create_new_template">
                        <field name="formula_type" widget="radio"/>
                    </group>

                    <!-- Fixed Duration -->
                    <group string="Fixed Duration" invisible="not create_new_template or formula_type != 'fixed'">
                        <field name="fixed_duration" string="Duration (minutes)"/>
                    </group>

                    <!-- Field-based Duration -->
                    <group string="Field-based Duration" invisible="not create_new_template or formula_type != 'field'">
                        <field name="reference_field_id" domain="[('matrix_id', '=', matrix_id)]"/>
                        <field name="multiplier"/>
                        <field name="base_duration"/>
                    </group>

                    <!-- Calculation -->
                    <group string="Calculation" invisible="not create_new_template or formula_type != 'calculation'">
                        <field name="calculation_formula" placeholder="e.g., height * width / 1000"/>
                    </group>

                    <!-- Conditional -->
                    <group string="Conditional Duration" invisible="not create_new_template or formula_type != 'conditional'">
                        <field name="condition_field_id" domain="[('matrix_id', '=', matrix_id)]"/>
                        <field name="condition_value"/>
                        <field name="true_duration"/>
                        <field name="false_duration"/>
                    </group>

                    <!-- Advanced Formula -->
                    <group string="Advanced Formula" invisible="not create_new_template or formula_type != 'advanced'">
                        <field name="advanced_formula" widget="text" placeholder="Python expression for complex duration calculations"/>
                    </group>

                    <!-- Operation Details (only for new templates) -->
                    <group string="Operation Details" invisible="not create_new_template">
                        <group>
                            <field name="setup_time"/>
                            <field name="cleanup_time"/>
                        </group>
                        <group>
                            <field name="worksheet_type"/>
                        </group>
                    </group>

                    <group string="Worksheet Content" invisible="not create_new_template or worksheet_type == 'pdf'">
                        <field name="worksheet_content" widget="text" nolabel="1"/>
                    </group>

                    <group string="Notes" invisible="not create_new_template">
                        <field name="notes" widget="text" nolabel="1"/>
                    </group>

                    <!-- Result -->
                    <group string="Generated Formula">
                        <field name="result_formula" readonly="1"/>
                        <field name="test_result" readonly="1"/>
                    </group>
                </sheet>
                <footer>
                    <button name="action_test_formula" string="Test Formula" type="object" class="btn-secondary"
                            invisible="create_new_template"/>
                    <button name="action_apply_operation" string="Apply Operation" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Operation Builder Action -->
    <record id="action_config_matrix_operation_builder" model="ir.actions.act_window">
        <field name="name">Build Operation Mapping</field>
        <field name="res_model">config.matrix.operation.builder</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

</odoo>
