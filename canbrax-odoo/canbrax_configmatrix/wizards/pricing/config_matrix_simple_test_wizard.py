# -*- coding: utf-8 -*-

import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class ConfigMatrixSimpleTestWizard(models.TransientModel):
    _name = 'config.matrix.simple.test.wizard'
    _description = 'Simple Matrix Test (No Product Creation)'

    def action_create_test_matrices(self):
        """Create test matrices using any existing product"""
        
        # Find any existing product template
        existing_product = self.env['product.template'].search([], limit=1)
        
        if not existing_product:
            raise UserError(_('No product templates found in the system. Please create at least one product first.'))
        
        # Sample price data
        price_data = {
            'heights': [1000, 1200, 1500, 2000],
            'widths': [600, 800, 1000, 1200], 
            'prices': {
                '1200_800': 211.48,
                '1000_600': 185.25,
                '1500_1000': 275.60,
                '2000_1200': 385.90,
            }
        }
        
        # Sample labor data
        labor_data = {
            'heights': [1000, 1200, 1500, 2000],
            'widths': [600, 800, 1000, 1200],
            'times': {
                '1200_800': 2.5,
                '1000_600': 2.0,
                '1500_1000': 3.5,
                '2000_1200': 4.5,
            }
        }
        
        created_items = []
        
        try:
            # Create Price Matrix directly
            price_matrix = self.env['config.matrix.price.matrix'].create({
                'name': f'Test Price Matrix - {existing_product.name}',
                'product_template_id': existing_product.id,
                'matrix_type': 'price',
                'height_ranges': str([
                    {'min': 1000, 'max': 1000, 'label': '1000'},
                    {'min': 1001, 'max': 1200, 'label': '1200'},
                    {'min': 1201, 'max': 1500, 'label': '1500'},
                    {'min': 1501, 'max': 2000, 'label': '2000'},
                ]),
                'width_ranges': str([
                    {'min': 600, 'max': 600, 'label': '600'},
                    {'min': 601, 'max': 800, 'label': '800'},
                    {'min': 801, 'max': 1000, 'label': '1000'},
                    {'min': 1001, 'max': 1200, 'label': '1200'},
                ]),
                'matrix_data': str(price_data['prices']),
            })
            created_items.append(f'Price Matrix (ID: {price_matrix.id})')
            
            # Create Labor Time Matrix directly
            labor_matrix = self.env['config.matrix.labor.time.matrix'].create({
                'name': f'Test Labor Matrix - {existing_product.name}',
                'operation_type': 'assembly',
                'product_template_id': existing_product.id,
                'time_unit': 'hours',
                'height_ranges': str([
                    {'min': 1000, 'max': 1000, 'label': '1000'},
                    {'min': 1001, 'max': 1200, 'label': '1200'},
                    {'min': 1201, 'max': 1500, 'label': '1500'},
                    {'min': 1501, 'max': 2000, 'label': '2000'},
                ]),
                'width_ranges': str([
                    {'min': 600, 'max': 600, 'label': '600'},
                    {'min': 601, 'max': 800, 'label': '800'},
                    {'min': 801, 'max': 1000, 'label': '1000'},
                    {'min': 1001, 'max': 1200, 'label': '1200'},
                ]),
                'matrix_data': str(labor_data['times']),
            })
            created_items.append(f'Labor Matrix (ID: {labor_matrix.id})')
            
            # Test the price lookup
            test_price = price_matrix.get_price_for_dimensions(1200, 800)
            test_labor = labor_matrix.get_labor_time_for_dimensions(1200, 800)
            
            success_message = f"""✅ Test matrices created successfully!

Created items:
{chr(10).join(f'• {item}' for item in created_items)}

✅ Test Results:
• Price lookup for 1200x800: ${test_price or 'Not found'}
• Labor time for 1200x800: {test_labor or 'Not found'} hours

✅ Next steps:
1. Go to Matrix → Pricing Matrices → Price Matrices to view
2. Go to Matrix → Pricing Matrices → Labor Time Matrices to view
3. Test matrix assignments in configuration templates"""
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Test Matrices Created!'),
                    'message': success_message,
                    'type': 'success',
                    'sticky': True,
                }
            }
            
        except Exception as e:
            raise UserError(_(f"Error creating test matrices: {str(e)}"))
