# -*- coding: utf-8 -*-

import json
import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class ConfigMatrixVisualEditor(models.TransientModel):
    _name = 'config.matrix.visual.editor'
    _description = 'Visual Matrix Editor - Excel-like Interface'

    # Matrix being edited
    matrix_id = fields.Many2one('config.matrix.price.matrix', "Price Matrix")
    labor_matrix_id = fields.Many2one('config.matrix.labor.time.matrix', "Labor Matrix")

    matrix_type = fields.Selection([
        ('price', 'Price Matrix'),
        ('labor', 'Labor Time Matrix')
    ], string="Matrix Type", required=True, default='price')

    # Basic matrix info
    name = fields.Char("Matrix Name", required=False, default="New Matrix")
    product_template_id = fields.Many2one('product.template', "Product Template", required=False)

    # For labor matrices
    operation_type = fields.Selection([
        ('cut_frame', 'Cut Frame'),
        ('cut_mesh', 'Cut Mesh'),
        ('assembly', 'Assembly'),
        ('powder_coating', 'Powder Coating'),
        ('quality_check', 'Quality Check'),
        ('packaging', 'Packaging'),
        ('custom', 'Custom Operation')
    ], string="Operation Type")

    time_unit = fields.Selection([
        ('minutes', 'Minutes'),
        ('hours', 'Hours'),
        ('seconds', 'Seconds')
    ], string="Time Unit", default='hours')

    # Dimension setup
    height_values = fields.Text("Height Values", default="1000,1200,1500,1800,2000",
                               help="Comma-separated height values (e.g., 1000,1200,1500)")
    width_values = fields.Text("Width Values", default="600,800,1000,1200,1500",
                              help="Comma-separated width values (e.g., 600,800,1000)")

    # Matrix data as JSON for easy manipulation
    matrix_data_json = fields.Text("Matrix Data JSON", default="{}")

    # UI state
    matrix_html = fields.Html("Matrix HTML", compute='_compute_matrix_html')
    matrix_rows = fields.Integer("Matrix Rows", compute='_compute_matrix_dimensions')
    matrix_cols = fields.Integer("Matrix Columns", compute='_compute_matrix_dimensions')

    # Additional UI fields for the view
    matrix_name = fields.Char("Matrix Name", related='name', readonly=False)
    currency_name = fields.Char("Currency/Unit", default="AUD")
    heights_text = fields.Text("Heights Text", related='height_values', readonly=False)
    widths_text = fields.Text("Widths Text", related='width_values', readonly=False)

    # Statistics fields
    total_cells = fields.Integer("Total Cells", compute='_compute_matrix_stats')
    filled_cells = fields.Integer("Filled Cells", compute='_compute_matrix_stats')
    completion_rate = fields.Float("Completion Rate", compute='_compute_matrix_stats')

    @api.depends('height_values', 'width_values')
    def _compute_matrix_dimensions(self):
        for record in self:
            heights = record._parse_dimension_values(record.height_values)
            widths = record._parse_dimension_values(record.width_values)
            record.matrix_rows = len(heights)
            record.matrix_cols = len(widths)

    @api.depends('height_values', 'width_values', 'matrix_data_json')
    def _compute_matrix_stats(self):
        for record in self:
            heights = record._parse_dimension_values(record.height_values)
            widths = record._parse_dimension_values(record.width_values)
            total = len(heights) * len(widths)

            try:
                matrix_data = json.loads(record.matrix_data_json) if record.matrix_data_json else {}
                filled = len([v for v in matrix_data.values() if v])
            except json.JSONDecodeError:
                filled = 0

            record.total_cells = total
            record.filled_cells = filled
            record.completion_rate = round((filled / total * 100) if total > 0 else 0, 1)

    @api.depends('height_values', 'width_values', 'matrix_data_json', 'matrix_type')
    def _compute_matrix_html(self):
        for record in self:
            record.matrix_html = record._generate_matrix_html()

    def _parse_dimension_values(self, values_text):
        """Parse comma-separated dimension values"""
        if not values_text:
            return []
        try:
            return [int(v.strip()) for v in values_text.split(',') if v.strip()]
        except ValueError:
            return []

    def _generate_matrix_html(self):
        """Generate HTML table for matrix editing"""
        heights = self._parse_dimension_values(self.height_values)
        widths = self._parse_dimension_values(self.width_values)

        if not heights or not widths:
            return "<p>Please enter height and width values to see the matrix.</p>"

        try:
            matrix_data = json.loads(self.matrix_data_json) if self.matrix_data_json else {}
        except json.JSONDecodeError:
            matrix_data = {}

        # Build HTML table
        html = """
        <style>
            .matrix-table {
                border-collapse: collapse;
                width: 100%;
                font-family: monospace;
                font-size: 12px;
            }
            .matrix-table th, .matrix-table td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: center;
                min-width: 80px;
            }
            .matrix-table th {
                background-color: #f5f5f5;
                font-weight: bold;
            }
            .matrix-table .row-header {
                background-color: #f0f0f0;
                font-weight: bold;
            }
            .matrix-table input {
                width: 70px;
                text-align: center;
                border: none;
                background: transparent;
            }
            .matrix-table input:focus {
                background-color: #e6f3ff;
                border: 1px solid #0078d4;
            }
            .has-value {
                background-color: #f0f8ff;
            }
        </style>
        <table class="matrix-table">
        """

        # Header row
        html += "<tr><th>Height \\ Width</th>"
        for width in widths:
            html += f"<th>{width}</th>"
        html += "</tr>"

        # Data rows
        for height in heights:
            html += f"<tr><td class='row-header'>{height}</td>"
            for width in widths:
                key = f"{height}_{width}"
                value = matrix_data.get(key, "")
                css_class = "has-value" if value else ""
                html += f"""<td class='{css_class}'>
                    <input type='text'
                           value='{value}'
                           data-height='{height}'
                           data-width='{width}'
                           onchange='updateMatrixValue(this)'
                           placeholder='0.00'>
                </td>"""
            html += "</tr>"

        html += "</table>"

        # Add JavaScript for real-time updates
        html += """
        <script>
            function updateMatrixValue(input) {
                const height = input.dataset.height;
                const width = input.dataset.width;
                const value = input.value;
                const key = height + '_' + width;

                // Get current matrix data
                let matrixData = {};
                try {
                    const jsonField = document.querySelector('textarea[name="matrix_data_json"]');
                    if (jsonField && jsonField.value) {
                        matrixData = JSON.parse(jsonField.value);
                    }
                } catch (e) {
                    matrixData = {};
                }

                // Update value
                if (value && value.trim()) {
                    matrixData[key] = parseFloat(value) || value;
                    input.parentElement.classList.add('has-value');
                } else {
                    delete matrixData[key];
                    input.parentElement.classList.remove('has-value');
                }

                // Update JSON field
                if (jsonField) {
                    jsonField.value = JSON.stringify(matrixData, null, 2);
                    jsonField.dispatchEvent(new Event('change'));
                }
            }

            // Auto-save functionality
            let saveTimeout;
            function autoSave() {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => {
                    console.log('Auto-saving matrix data...');
                    // Trigger form update
                    const form = document.querySelector('form');
                    if (form) {
                        // Could trigger an auto-save here
                    }
                }, 2000);
            }
        </script>
        """

        return html

    @api.model
    def create_new_matrix(self, matrix_type='price'):
        """Create a new matrix editor instance"""
        # Get a default product if available
        default_product = self.env['product.template'].search([], limit=1)

        values = {
            'matrix_type': matrix_type,
            'name': f'New {matrix_type.title()} Matrix',
            'product_template_id': default_product.id if default_product else False,
        }

        if matrix_type == 'labor':
            values.update({
                'operation_type': 'assembly',
                'time_unit': 'hours',
            })

        return self.create(values)

    def action_load_existing_matrix(self):
        """Load an existing matrix for editing"""
        self.ensure_one()

        if self.matrix_type == 'price' and self.matrix_id:
            matrix = self.matrix_id

            # Parse existing data
            try:
                height_ranges = json.loads(matrix.height_ranges) if matrix.height_ranges else []
                width_ranges = json.loads(matrix.width_ranges) if matrix.width_ranges else []
                matrix_data = json.loads(matrix.matrix_data) if matrix.matrix_data else {}

                # Extract dimension values
                heights = [r['max'] for r in height_ranges]
                widths = [r['max'] for r in width_ranges]

                self.write({
                    'name': matrix.name,
                    'product_template_id': matrix.product_template_id.id,
                    'height_values': ','.join(map(str, heights)),
                    'width_values': ','.join(map(str, widths)),
                    'matrix_data_json': json.dumps(matrix_data, indent=2),
                })

            except (json.JSONDecodeError, KeyError) as e:
                raise UserError(_("Error loading matrix data: %s") % str(e))

        elif self.matrix_type == 'labor' and self.labor_matrix_id:
            matrix = self.labor_matrix_id

            # Similar loading for labor matrices
            try:
                height_ranges = json.loads(matrix.height_ranges) if matrix.height_ranges else []
                width_ranges = json.loads(matrix.width_ranges) if matrix.width_ranges else []
                matrix_data = json.loads(matrix.matrix_data) if matrix.matrix_data else {}

                heights = [r['max'] for r in height_ranges]
                widths = [r['max'] for r in width_ranges]

                self.write({
                    'name': matrix.name,
                    'product_template_id': matrix.product_template_id.id,
                    'operation_type': matrix.operation_type,
                    'time_unit': matrix.time_unit,
                    'height_values': ','.join(map(str, heights)),
                    'width_values': ','.join(map(str, widths)),
                    'matrix_data_json': json.dumps(matrix_data, indent=2),
                })

            except (json.JSONDecodeError, KeyError) as e:
                raise UserError(_("Error loading matrix data: %s") % str(e))

    def action_save_matrix(self):
        """Save the matrix data"""
        self.ensure_one()

        heights = self._parse_dimension_values(self.height_values)
        widths = self._parse_dimension_values(self.width_values)

        if not heights or not widths:
            raise UserError(_("Please enter valid height and width values"))

        # Parse matrix data
        try:
            matrix_data = json.loads(self.matrix_data_json) if self.matrix_data_json else {}
        except json.JSONDecodeError:
            raise UserError(_("Invalid matrix data format"))

        # Generate dimension ranges
        height_ranges = []
        for i, height in enumerate(heights):
            min_height = heights[i-1] + 1 if i > 0 else height
            height_ranges.append({
                'min': min_height,
                'max': height,
                'label': str(height)
            })

        width_ranges = []
        for i, width in enumerate(widths):
            min_width = widths[i-1] + 1 if i > 0 else width
            width_ranges.append({
                'min': min_width,
                'max': width,
                'label': str(width)
            })

        # Save matrix
        if self.matrix_type == 'price':
            if self.matrix_id:
                # Update existing
                self.matrix_id.write({
                    'name': self.name,
                    'product_template_id': self.product_template_id.id,
                    'height_ranges': json.dumps(height_ranges),
                    'width_ranges': json.dumps(width_ranges),
                    'matrix_data': json.dumps(matrix_data),
                })
                matrix = self.matrix_id
            else:
                # Create new
                matrix = self.env['config.matrix.price.matrix'].create({
                    'name': self.name,
                    'product_template_id': self.product_template_id.id,
                    'matrix_type': 'price',
                    'height_ranges': json.dumps(height_ranges),
                    'width_ranges': json.dumps(width_ranges),
                    'matrix_data': json.dumps(matrix_data),
                })
                self.matrix_id = matrix

        elif self.matrix_type == 'labor':
            if self.labor_matrix_id:
                # Update existing
                self.labor_matrix_id.write({
                    'name': self.name,
                    'product_template_id': self.product_template_id.id,
                    'operation_type': self.operation_type,
                    'time_unit': self.time_unit,
                    'height_ranges': json.dumps(height_ranges),
                    'width_ranges': json.dumps(width_ranges),
                    'matrix_data': json.dumps(matrix_data),
                })
                matrix = self.labor_matrix_id
            else:
                # Create new
                matrix = self.env['config.matrix.labor.time.matrix'].create({
                    'name': self.name,
                    'product_template_id': self.product_template_id.id,
                    'operation_type': self.operation_type,
                    'time_unit': self.time_unit,
                    'height_ranges': json.dumps(height_ranges),
                    'width_ranges': json.dumps(width_ranges),
                    'matrix_data': json.dumps(matrix_data),
                })
                self.labor_matrix_id = matrix

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Matrix Saved Successfully'),
                'message': f'Matrix "{self.name}" has been saved with {len(matrix_data)} data points.',
                'type': 'success',
            }
        }

    def action_test_lookup(self):
        """Test matrix lookup with sample values"""
        self.ensure_one()

        # Ensure name is set
        if not self.name:
            self.name = f"Test {self.matrix_type.title()} Matrix"

        heights = self._parse_dimension_values(self.height_values)
        widths = self._parse_dimension_values(self.width_values)

        if not heights or not widths:
            raise UserError(_("Please enter height and width values first"))

        # Use middle values for testing
        test_height = heights[len(heights)//2] if heights else 1200
        test_width = widths[len(widths)//2] if widths else 800

        try:
            matrix_data = json.loads(self.matrix_data_json) if self.matrix_data_json else {}
            test_key = f"{test_height}_{test_width}"
            test_value = matrix_data.get(test_key, "No value set")

            message = f"""Test Lookup Results:

Dimensions: {test_height} x {test_width}
Matrix Key: {test_key}
Value: {test_value}

Matrix contains {len(matrix_data)} data points:
{', '.join(list(matrix_data.keys())[:10])}{'...' if len(matrix_data) > 10 else ''}"""

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Matrix Test Results'),
                    'message': message,
                    'type': 'info',
                    'sticky': True,
                }
            }

        except json.JSONDecodeError:
            raise UserError(_("Invalid matrix data format"))

    def action_fill_sample_data(self):
        """Fill matrix with sample data for testing"""
        self.ensure_one()

        # Ensure name is set
        if not self.name:
            self.name = f"Sample {self.matrix_type.title()} Matrix"

        heights = self._parse_dimension_values(self.height_values)
        widths = self._parse_dimension_values(self.width_values)

        if not heights or not widths:
            raise UserError(_("Please set height and width values first"))

        sample_data = {}

        if self.matrix_type == 'price':
            # Generate sample prices
            base_price = 150.0
            for i, height in enumerate(heights):
                for j, width in enumerate(widths):
                    # Price increases with size
                    price = base_price + (i * 25) + (j * 20) + (height * width * 0.0001)
                    sample_data[f"{height}_{width}"] = round(price, 2)

        elif self.matrix_type == 'labor':
            # Generate sample labor times
            base_time = 1.0
            for i, height in enumerate(heights):
                for j, width in enumerate(widths):
                    # Time increases with size
                    time = base_time + (i * 0.5) + (j * 0.3) + (height * width * 0.000001)
                    sample_data[f"{height}_{width}"] = round(time, 2)

        self.matrix_data_json = json.dumps(sample_data, indent=2)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Sample Data Generated'),
                'message': f'Generated {len(sample_data)} sample data points. Review and save when ready.',
                'type': 'success',
            }
        }

    def action_clear_matrix(self):
        """Clear all matrix data"""
        self.ensure_one()

        # Ensure name is set
        if not self.name:
            self.name = f"New {self.matrix_type.title()} Matrix"

        self.matrix_data_json = "{}"

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Matrix Cleared'),
                'message': 'All matrix data has been cleared. You can now enter new values.',
                'type': 'warning',
            }
        }
