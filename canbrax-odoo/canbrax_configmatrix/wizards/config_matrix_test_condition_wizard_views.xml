<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- View Visibility Condition Wizard Form View -->
    <record id="view_config_matrix_view_condition_wizard_form" model="ir.ui.view">
        <field name="name">config.matrix.view.condition.wizard.form</field>
        <field name="model">config.matrix.test.condition.wizard</field>
        <field name="arch" type="xml">
            <form string="View Visibility Condition">
                <sheet>
                    <group>
                        <field name="field_id" invisible="for_option" readonly="1"/>
                        <field name="option_id" invisible="not for_option" readonly="1"/>
                        <field name="for_option" invisible="1"/>
                        <field name="template_id" invisible="1"/>
                    </group>
                    <group>
                        <field name="condition" required="1"/>
                    </group>
                    <group>
                        <field name="result" readonly="1"/>
                        <field name="error" readonly="1" invisible="error == False"/>
                    </group>
                    <div class="alert alert-info" role="alert">
                        <p><strong>Condition Examples:</strong></p>
                        <ul>
                            <li><code>door_type == 'sliding'</code> - Visible when door_type is 'sliding'</li>
                            <li><code>width > 1000</code> - Visible when width is greater than 1000</li>
                            <li><code>has_lock == True</code> - Visible when has_lock is True</li>
                            <li><code>door_type in ['sliding', 'folding']</code> - Visible when door_type is 'sliding' or 'folding'</li>
                            <li><code>door_type == 'sliding' and width > 1000</code> - Visible when both conditions are true</li>
                        </ul>
                    </div>
                </sheet>
                <footer>
                    <button name="action_test_condition" string="Test" type="object" class="btn-primary"/>
                    <button name="action_delete_condition" string="Delete" type="object" class="oe_highlight text-bg-danger"/>
                    <button string="Close" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- View Condition Action -->
    <record id="action_config_matrix_test_condition" model="ir.actions.act_window">
        <field name="name">View Visibility Condition</field>
        <field name="res_model">config.matrix.test.condition.wizard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_config_matrix_view_condition_wizard_form"/>
        <field name="target">new</field>
    </record>
</odoo>
