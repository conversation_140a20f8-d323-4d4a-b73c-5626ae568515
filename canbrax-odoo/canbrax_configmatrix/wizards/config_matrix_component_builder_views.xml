<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Component Builder Form View -->
    <record id="view_config_matrix_component_builder_form" model="ir.ui.view">
        <field name="name">config.matrix.component.builder.form</field>
        <field name="model">config.matrix.component.builder</field>
        <field name="arch" type="xml">
            <form string="Build Component Mapping">
                <sheet>
                    <div class="alert alert-info" role="alert">
                        <p><strong>Build a component mapping for this field</strong></p>
                        <p>This will add the selected component to the Bill of Materials when this field is used.</p>
                    </div>

                    <group>
                        <field name="matrix_id" invisible="1"/>
                        <field name="field_id" invisible="1"/>
                        <field name="component_product_id"/>
                    </group>

                    <group>
                        <field name="formula_type" widget="radio"/>
                    </group>

                    <!-- Fixed quantity -->
                    <group invisible="formula_type != 'fixed'">
                        <field name="fixed_quantity"/>
                    </group>

                    <!-- Field-based quantity -->
                    <group invisible="formula_type != 'field'">
                        <field name="quantity_field_id"
                               domain="[('matrix_id', '=', matrix_id), ('field_type', '=', 'number')]"
                               options="{'no_create': True}"/>
                    </group>

                    <!-- Calculation -->
                    <div invisible="formula_type != 'calculation'">
                        <group>
                            <field name="calculation_type"/>
                        </group>

                        <group invisible="calculation_type != 'area'">
                            <field name="width_field_id"
                                   domain="[('matrix_id', '=', matrix_id), ('field_type', '=', 'number')]"
                                   options="{'no_create': True}"/>
                            <field name="height_field_id"
                                   domain="[('matrix_id', '=', matrix_id), ('field_type', '=', 'number')]"
                                   options="{'no_create': True}"/>
                            <field name="unit_conversion"/>
                        </group>

                        <group invisible="calculation_type != 'perimeter'">
                            <field name="width_field_id"
                                   domain="[('matrix_id', '=', matrix_id), ('field_type', '=', 'number')]"
                                   options="{'no_create': True}"/>
                            <field name="height_field_id"
                                   domain="[('matrix_id', '=', matrix_id), ('field_type', '=', 'number')]"
                                   options="{'no_create': True}"/>
                            <field name="unit_conversion"/>
                        </group>

                        <group invisible="calculation_type != 'length'">
                            <field name="length_field_id"
                                   domain="[('matrix_id', '=', matrix_id), ('field_type', '=', 'number')]"
                                   options="{'no_create': True}"/>
                            <field name="unit_conversion"/>
                        </group>

                        <group invisible="calculation_type != 'custom'">
                            <field name="custom_calculation" placeholder="e.g., width * height / 1000000"/>
                        </group>
                    </div>

                    <!-- Conditional quantity -->
                    <div invisible="formula_type != 'conditional'">
                        <group>
                            <field name="condition_field_id"
                                   domain="[('matrix_id', '=', matrix_id)]"
                                   options="{'no_create': True}"/>
                            <field name="condition_operator"/>
                            <field name="condition_value" placeholder="Value or comma-separated list for 'in' operators"
                                   invisible="condition_operator in ('is_set', 'is_not_set')"/>
                        </group>
                        <group>
                            <field name="true_quantity" string="Quantity if condition is true"/>
                            <field name="false_quantity" string="Quantity if condition is false"/>
                        </group>
                    </div>

                    <!-- Advanced formula -->
                    <div invisible="formula_type != 'advanced'">
                        <group>
                            <field name="advanced_formula" placeholder="Python expression, e.g., math.ceil(width * height / 1000000)" nolabel="1"/>
                        </group>
                        <div class="alert alert-secondary" role="alert">
                            <p><strong>Examples:</strong></p>
                            <ul>
                                <li><code>1</code> - Fixed quantity of 1</li>
                                <li><code>width * height / 1000000</code> - Calculate area in square meters</li>
                                <li><code>math.ceil(width / 1000)</code> - Round up to nearest whole number</li>
                                <li><code>2 if door_type == 'double' else 1</code> - Conditional quantity</li>
                            </ul>
                        </div>
                    </div>

                    <group>
                        <field name="result_formula" readonly="1"/>
                    </group>
                </sheet>
                <footer>
                    <button name="action_apply_formula" string="Apply" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="action_config_matrix_component_builder" model="ir.actions.act_window">
        <field name="name">Build Component Mapping</field>
        <field name="res_model">config.matrix.component.builder</field>
        <field name="view_mode">form</field>
    </record>
</odoo>
