# add import wizard to replace text in config matrix template import csv file

from odoo import models, fields, api
import base64
import csv
from io import StringIO
import logging
from odoo.exceptions import ValidationError
import os

class ReplaceTextWizard(models.TransientModel):
    _name = 'replace.text.wizard'
    _description = 'Replace Text Wizard'

    file = fields.Binary(string='File')
    template_id = fields.Many2one(
        'config.matrix.template',
        string='Template',
        default=lambda self: self.env.context.get('default_template_id'),
    )
    
    def action_replace_text(self):
        # Decode the uploaded file
        if not self.file:
            raise ValidationError('Please upload a file to replace text.')
        
        file_content = base64.b64decode(self.file)
        file_str = file_content.decode('utf-8')

        # Auto-detect delimiter (comma or tab)
        import csv
        from io import StringIO
        _logger = logging.getLogger(__name__)
        sample = file_str[:1024]
        try:
            dialect = csv.Sniffer().sniff(sample, delimiters=",\t")
            delimiter = dialect.delimiter
        except Exception:
            delimiter = ','  # fallback to comma
        csv_lines = list(csv.reader(StringIO(file_str), delimiter=delimiter))

        # Expect: first row is header, second row is template codes, rest are mappings
        if len(csv_lines) < 3:
            raise ValueError('CSV must have at least 3 rows: header, template code row, and at least one mapping row.')

        # Extract template codes from the second row
        template_code_row = csv_lines[1]
        original_template_code = template_code_row[0].strip() if len(template_code_row) > 0 else ''
        new_template_code = template_code_row[1].strip() if len(template_code_row) > 1 else ''

        template = self.env['config.matrix.template'].browse(self.template_id.id)
        current_code = template.code
        techname_map = {}

        # Only process if the selected template matches the original_template_code
        if current_code != original_template_code:
            raise ValueError(f"Selected template code ({current_code}) does not match the CSV's original_template_code ({original_template_code})")

        # Build mapping from the rest of the rows
        for row in csv_lines[2:]:
            if len(row) < 4:
                continue
            orig_tech = row[2].strip()
            new_tech = row[3].strip()
            if orig_tech and new_tech:
                techname_map[orig_tech] = new_tech
                _logger.info(f"Added to techname_map: {orig_tech} -> {new_tech}")

        _logger.info(f"Final techname_map: {techname_map}")
        _logger.info(f"New template code: {new_template_code}")

        # Update template code if needed
        if new_template_code and new_template_code != current_code:
            template.code = new_template_code

        # Step 1: Update technical names in config.matrix.field
        fields = self.env['config.matrix.field'].search([
            ('matrix_id', '=', template.id),
            ('technical_name', 'in', list(techname_map.keys()))
        ])
        for field in fields:
            new_name = techname_map.get(field.technical_name)
            if new_name:
                field.technical_name = new_name

        # Step 2: Update expression in config.matrix.visibility.condition
        conditions = self.env['config.matrix.visibility.condition'].search([
            ('field_id.matrix_id', '=', template.id)
        ])
        replace_count = 0
        for cond in conditions:
            original = cond.expression
            if not original:
                continue
            updated = original
            for old, new in techname_map.items():
                if old in updated:
                    updated = updated.replace(old, new)
            if updated != original:
                cond.expression = updated
                replace_count += 1

        # Step 3: Update use-case fields in config.matrix.field
        use_case_fields = [
            # Visibility
            'check_measure_visible', 'sales_visible', 'online_visible',
            # Default Value
            'check_measure_default_value', 'sales_default_value', 'online_default_value',
            # Default Option
            'check_measure_default_option_id', 'sales_default_option_id', 'online_default_option_id',
            # Dynamic Default
            'check_measure_use_dynamic_default', 'sales_use_dynamic_default', 'online_use_dynamic_default',
            'check_measure_dynamic_default_template', 'sales_dynamic_default_template', 'online_dynamic_default_template',
            # Help Text
            'check_measure_help_text', 'sales_help_text', 'online_help_text',
            # Dynamic Help
            'check_measure_use_dynamic_help', 'sales_use_dynamic_help', 'online_use_dynamic_help',
            'check_measure_dynamic_help_template', 'sales_dynamic_help_template', 'online_dynamic_help_template',
            # Error Message
            'check_measure_error_text', 'sales_error_text', 'online_error_text',
            # Error Condition
            'check_measure_error_condition', 'sales_error_condition', 'online_error_condition',
            # Dynamic Error
            'check_measure_use_dynamic_error', 'sales_use_dynamic_error', 'online_use_dynamic_error',
            'check_measure_dynamic_error_template', 'sales_dynamic_error_template', 'online_dynamic_error_template',
            # Min/Max Value
            'check_measure_min_value', 'sales_min_value', 'online_min_value',
            'check_measure_max_value', 'sales_max_value', 'online_max_value',
        ]
        fields_to_update = self.env['config.matrix.field'].search([
            ('matrix_id', '=', template.id)
        ])
        use_case_replace_count = 0
        for field in fields_to_update:
            for field_name in use_case_fields:
                value = getattr(field, field_name, None)
                if isinstance(value, str) and value:
                    updated = value
                    for old, new in techname_map.items():
                        if old in updated:
                            updated = updated.replace(old, new)
                    if updated != value:
                        setattr(field, field_name, updated)
                        use_case_replace_count += 1
        # but we need to replace the default option id with the new option id
        for field in fields_to_update:
            if field.check_measure_default_option_id:
                # Find the option by its value and field_id
                option = field.check_measure_default_option_id
                # Look for a new option with the same value but potentially different technical_name
                new_option = self.env['config.matrix.option'].search([
                    ('field_id', '=', field.id),
                    ('value', '=', option.value)
                ], limit=1)
                if new_option:
                    field.check_measure_default_option_id = new_option.id

        # replace same in  config.matrix.option.visibility object from template_id
        option_visibility_replace_count = 0
        option_visibilities = self.env['config.matrix.option.visibility'].search([
            ('option_id.matrix_id', '=', template.id)
        ])
        for option_visibility in option_visibilities:
            original = option_visibility.expression
            if not original:
                continue
            updated = original
            for old, new in techname_map.items():
                if old in updated:
                    updated = updated.replace(old, new)
            if updated != original:
                option_visibility.expression = updated
                option_visibility_replace_count += 1




        # replace same in config.matrix.option.component.mapping.extended
        option_component_mappings = self.env['config.matrix.option.component.mapping.extended'].search([
            ('option_id.matrix_id', '=', template.id)
        ])
        option_component_mapping_replace_count = 0
        for option_component_mapping in option_component_mappings:
            original = option_component_mapping.condition
            if not original:
                continue
            updated = original
            for old, new in techname_map.items():
                if old in updated:
                    updated = updated.replace(old, new)
            if updated != original:
                option_component_mapping.condition = updated
                option_component_mapping_replace_count += 1


        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Success',
                'message': f'Replacement completed. Updated {len(fields)} fields, {replace_count} conditions, {use_case_replace_count} use-case values, {option_visibility_replace_count} option visibilities, and {option_component_mapping_replace_count} option component mappings.' + (f' Template code changed to {new_template_code}.' if new_template_code else ''),
                'type': 'success',
                'sticky': False,
            }
        }

    def download_template(self):
        # Provide the sample_import.csv as a downloadable attachment
        # current file path
        file_path = os.path.join(os.path.dirname(__file__), 'sample_import.csv')
        with open(file_path, 'rb') as f:
            file_content = f.read()
        encoded_file = base64.b64encode(file_content)
        return {
            'type': 'ir.actions.act_url',
            'url': f"/web/content/?model=replace.text.wizard&id={self.id}&field=file&download=true&filename=sample_import.csv",
            'target': 'self',
        }

        