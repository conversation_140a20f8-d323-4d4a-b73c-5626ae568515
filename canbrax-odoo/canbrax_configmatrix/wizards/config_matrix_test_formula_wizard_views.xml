<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <!-- Test Formula Wizard Form View -->
        <record id="view_config_matrix_test_formula_wizard_form" model="ir.ui.view">
            <field name="name">config.matrix.test.formula.wizard.form</field>
            <field name="model">config.matrix.test.formula.wizard</field>
            <field name="arch" type="xml">
                <form string="Test Formula">
                    <sheet>
                        <group>
                            <field name="formula" required="1"/>
                            <field name="test_values" widget="json_text"/>
                        </group>
                        <group>
                            <field name="result" readonly="1"/>
                            <field name="result_type" readonly="1"/>
                            <field name="result_value" readonly="1" invisible="result_type not in ['int', 'float']"/>
                            <field name="error" readonly="1" invisible="error == False"/>
                        </group>
                        <group>
                            <field name="field_id" invisible="1"/>
                            <field name="option_id" invisible="1"/>
                            <field name="template_id" invisible="1"/>
                        </group>
                        <div class="alert alert-info" role="alert">
                            <p><strong>Formula Help:</strong></p>
                            <p>You can use any field technical name from the template as a variable in your formula.</p>
                            <p>Examples:</p>
                            <ul>
                                <li>Numeric calculation: <code>width * height * 2</code></li>
                                <li>Conditional logic: <code>5 if color == 'red' else 3</code></li>
                                <li>Boolean expression: <code>width > 1000 and height > 500</code></li>
                            </ul>
                            <p>You can also use math functions like <code>ceil()</code>, <code>floor()</code>, <code>round()</code>, etc.</p>
                            <p>For testing, provide test values as a JSON object, e.g. <code>{"width": 10, "height": 5, "color": "red"}</code></p>
                        </div>
                    </sheet>
                    <footer>
                        <button name="test_formula" string="Test Formula" type="object" class="btn-primary"/>
                        <button string="Close" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Test Formula Action -->
        <record id="action_config_matrix_test_formula" model="ir.actions.act_window">
            <field name="name">Test Formula</field>
            <field name="res_model">config.matrix.test.formula.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>
    </data>
</odoo>
