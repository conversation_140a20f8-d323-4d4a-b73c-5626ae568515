# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class ConfigMatrixManageDependenciesWizard(models.TransientModel):
    _name = 'config.matrix.manage.dependencies.wizard'
    _description = 'Manage Field Dependencies'

    field_id = fields.Many2one('config.matrix.field', string="Field", required=True, readonly=True)
    matrix_id = fields.Many2one('config.matrix.template', related='field_id.matrix_id', string="Template", readonly=True)

    # Simple dependency fields
    controlling_field_id = fields.Many2one('config.matrix.field', string="Depends On Field",
                                         domain="[('matrix_id', '=', matrix_id), ('id', '!=', field_id)]")
    operator = fields.Selection([
        ('==', 'Equals'),
        ('!=', 'Not Equals'),
        ('>', 'Greater Than'),
        ('<', 'Less Than'),
        ('>=', 'Greater Than or Equal'),
        ('<=', 'Less Than or Equal'),
        ('in', 'In List'),
        ('not in', 'Not In List')
    ], string="Operator", default="==")

    value_type = fields.Selection([
        ('text', 'Text Value'),
        ('number', 'Number Value'),
        ('boolean', 'Boolean Value'),
        ('selection', 'Selection Value'),
        ('list', 'List of Values')
    ], string="Value Type", compute='_compute_value_type', store=True)

    # Value fields based on type
    text_value = fields.Char("Text Value")
    number_value = fields.Float("Number Value")
    boolean_value = fields.Boolean("Boolean Value")
    selection_value = fields.Selection(selection='_get_selection_values', string="Selection Value")
    list_value = fields.Char("List Values", help="Comma-separated list of values")

    # Available options for the controlling field
    available_options = fields.Text("Available Options", compute='_compute_available_options')

    # Options for selection fields
    option_ids = fields.Many2many('config.matrix.option', string="Available Options", compute='_compute_option_ids')
    selected_option_ids = fields.Many2many('config.matrix.option', 'wizard_selected_option_rel',
                                          'wizard_id', 'option_id', string="Selected Options")

    # Existing dependencies
    dependency_ids = fields.One2many('config.matrix.dependency', 'field_id', string="Current Dependencies", readonly=True)

    @api.depends('controlling_field_id')
    def _compute_value_type(self):
        for wizard in self:
            if wizard.controlling_field_id:
                field_type = wizard.controlling_field_id.field_type
                if field_type == 'text':
                    wizard.value_type = 'text'
                elif field_type == 'number':
                    wizard.value_type = 'number'
                elif field_type == 'boolean':
                    wizard.value_type = 'boolean'
                elif field_type == 'selection':
                    wizard.value_type = 'selection'
                else:
                    wizard.value_type = 'text'
            else:
                wizard.value_type = 'text'

    @api.depends('controlling_field_id')
    def _compute_available_options(self):
        """Compute available options for the controlling field"""
        for wizard in self:
            options_text = ""
            if wizard.controlling_field_id and wizard.controlling_field_id.field_type == 'selection':
                options = self.env['config.matrix.option'].search([
                    ('field_id', '=', wizard.controlling_field_id.id)
                ], order='sequence')

                if options:
                    options_text = "Available options:\n"
                    for option in options:
                        options_text += f"- {option.name}\n"

            wizard.available_options = options_text

    @api.depends('controlling_field_id')
    def _compute_option_ids(self):
        """Compute available options for selection fields"""
        for wizard in self:
            options = self.env['config.matrix.option']
            if wizard.controlling_field_id and wizard.controlling_field_id.field_type == 'selection':
                options = self.env['config.matrix.option'].search([
                    ('field_id', '=', wizard.controlling_field_id.id)
                ], order='sequence')
            wizard.option_ids = options

    def _get_selection_values(self):
        """Get selection values for the controlling field"""
        selection = [('', '')]

        # Handle empty recordset case
        if not self or not self.controlling_field_id:
            return selection

        # Get options for the controlling field
        if self.controlling_field_id.field_type == 'selection':
            options = self.env['config.matrix.option'].search([
                ('field_id', '=', self.controlling_field_id.id)
            ], order='sequence')

            for option in options:
                # Use value as the key and name as the display text
                selection.append((option.value, option.name))

            # Log the options for debugging
            _logger = logging.getLogger(__name__)
            _logger.info(f"Selection options for {self.controlling_field_id.name}: {selection}")

        return selection

    def action_add_dependency(self):
        """Add a new dependency"""
        self.ensure_one()

        if not self.controlling_field_id:
            raise UserError(_("Please select a field that this field depends on."))

        # Validate input based on field type
        if self.value_type == 'text' and not self.text_value:
            raise UserError(_("Please enter a text value."))
        elif self.value_type == 'number' and self.number_value == 0:
            # Allow 0 as a valid value, but show a confirmation
            pass
        elif self.value_type == 'selection':
            if self.operator in ('in', 'not in'):
                if not self.selected_option_ids:
                    raise UserError(_("Please select at least one option from the list."))
            elif not self.selection_value:
                raise UserError(_("Please select a value from the dropdown."))
        elif self.value_type == 'list' and not self.list_value:
            raise UserError(_("Please enter a comma-separated list of values."))

        # Check if selection value exists in options
        if self.value_type == 'selection' and self.selection_value:
            options = self.env['config.matrix.option'].search([
                ('field_id', '=', self.controlling_field_id.id),
                ('value', '=', self.selection_value)
            ])
            if not options:
                raise UserError(_("The selected value '%s' is not a valid option for the field '%s'.") %
                              (self.selection_value, self.controlling_field_id.name))

        # Create the dependency
        vals = {
            'field_id': self.field_id.id,
            'controlling_field_id': self.controlling_field_id.id,
            'operator': self.operator,
        }

        # Add the appropriate value field based on type
        if self.value_type == 'text':
            vals['text_value'] = self.text_value
        elif self.value_type == 'number':
            vals['number_value'] = self.number_value
        elif self.value_type == 'boolean':
            vals['boolean_value'] = self.boolean_value
        elif self.value_type == 'selection':
            if self.operator in ('in', 'not in'):
                # For 'in' and 'not in' operators, use the selected options
                if self.selected_option_ids:
                    option_values = self.selected_option_ids.mapped('value')
                    vals['list_value'] = ', '.join(option_values)
            else:
                # For other operators, use the single selection value
                vals['selection_value'] = self.selection_value
        elif self.value_type == 'list':
            vals['list_value'] = self.list_value

        self.env['config.matrix.dependency'].create(vals)

        # Reload the wizard to show the new dependency
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.manage.dependencies.wizard',
            'view_mode': 'form',
            'res_id': self.id,
            'target': 'new',
            'context': self.env.context,
        }

    def action_remove_all_dependencies(self):
        """Remove all dependencies for this field"""
        self.ensure_one()

        if self.dependency_ids:
            self.dependency_ids.unlink()

        # Reload the wizard with a new instance to refresh the dependency_ids
        new_wizard = self.env['config.matrix.manage.dependencies.wizard'].create({
            'field_id': self.field_id.id,
        })

        return {
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.manage.dependencies.wizard',
            'view_mode': 'form',
            'res_id': new_wizard.id,
            'target': 'new',
            'context': self.env.context,
        }

    def action_remove_selected_dependency(self):
        """Remove the selected dependency"""
        self.ensure_one()

        # Get the active_id from the context (this will be the selected dependency)
        active_id = self._context.get('active_id')
        active_model = self._context.get('active_model')

        # If we have an active dependency in the context
        if active_model == 'config.matrix.dependency' and active_id:
            dependency = self.env['config.matrix.dependency'].browse(active_id)
            if dependency.exists() and dependency.field_id.id == self.field_id.id:
                dependency.unlink()

        # Reload the wizard to refresh the dependency_ids
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.manage.dependencies.wizard',
            'view_mode': 'form',
            'res_id': self.id,
            'target': 'new',
            'context': self.env.context,
        }
