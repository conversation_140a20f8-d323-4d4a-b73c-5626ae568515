# -*- coding: utf-8 -*-

import json
import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)

class ConfigMatrixJsonImport(models.TransientModel):
    _name = 'config.matrix.json.import'
    _description = 'Import ConfigMatrix from JSON'

    name = fields.Char("Template Name", required=True)
    json_file = fields.Binary("JSON File")
    json_filename = fields.Char("Filename")
    json_content = fields.Text("JSON Content", help="Paste JSON content here if not uploading a file")
    product_id = fields.Many2one('product.product', "Product", required=True)
    product_template_id = fields.Many2one('product.template', related='product_id.product_tmpl_id', string="Product Template")

    def action_import(self):
        """Import configuration template from JSON file or content"""
        self.ensure_one()

        if not self.json_file and not self.json_content:
            raise UserError(_("Please select a JSON file or paste JSON content to import."))

        try:
            # Get JSON data from file or content
            if self.json_file:
                try:
                    # Decode the base64 encoded file content
                    import base64
                    file_content = base64.b64decode(self.json_file)
                    json_content = file_content.decode('utf-8')
                    _logger.info(f"Importing JSON from file: {json_content[:100]}...")
                    json_data = json.loads(json_content)
                except UnicodeDecodeError:
                    raise UserError(_("The uploaded file is not a valid UTF-8 text file. Please ensure you're uploading a JSON text file."))
            else:
                _logger.info(f"Importing JSON from content field: {self.json_content[:100]}...")
                # Strip any leading/trailing whitespace that might cause issues
                json_content = self.json_content.strip()
                if not json_content:
                    raise UserError(_("The JSON content is empty. Please provide valid JSON data."))
                json_data = json.loads(json_content)

            # Create template
            template = self._create_template(json_data)

            # Return action to view the created template
            return {
                'name': _('Configuration Template'),
                'type': 'ir.actions.act_window',
                'res_model': 'config.matrix.template',
                'res_id': template.id,
                'view_mode': 'form',
                'target': 'current',
            }
        except json.JSONDecodeError as e:
            _logger.error(f"JSON decode error: {str(e)}")

            # Log the content that caused the error
            if self.json_file:
                _logger.error(f"Error occurred with uploaded file: {self.json_filename}")
            elif self.json_content:
                _logger.error(f"JSON content (first 100 chars): {self.json_content[:100]}")

            # Provide a more helpful error message
            error_message = _(
                "Invalid JSON format: %s\n\n"
                "Please check that your JSON is valid. Common issues include:\n"
                "- Missing or extra commas\n"
                "- Unmatched brackets or braces\n"
                "- Invalid escape sequences\n"
                "- Non-UTF8 characters\n\n"
                "You can validate your JSON using online tools like jsonlint.com"
            ) % str(e)

            raise UserError(error_message)
        except ValidationError as e:
            _logger.error(f"Validation error: {str(e)}")
            if "must have at least one bill of materials" in str(e):
                raise UserError(_(
                    "The product must have at least one bill of materials.\n\n"
                    "Please create a Bill of Materials for this product first:\n"
                    "1. Go to Manufacturing > Master Data > Bills of Materials\n"
                    "2. Create a new BOM for the product\n"
                    "3. Add at least one component to the BOM\n"
                    "4. Save the BOM and try importing again"
                ))
            else:
                raise UserError(_("Validation error: %s") % str(e))
        except Exception as e:
            _logger.error(f"Error importing JSON: {str(e)}")
            raise UserError(_("Error importing JSON: %s") % str(e))

    def _create_template(self, json_data):
        """Create configuration template from JSON data"""
        # Create template
        template_vals = {
            'name': self.name,
            'product_template_id': self.product_template_id.id,
            'code': self.product_id.default_code or self.product_id.name,
            'state': 'draft',
        }

        # If template data is provided in JSON, use it
        if 'template' in json_data and isinstance(json_data['template'], dict):
            if 'name' in json_data['template']:
                template_vals['name'] = json_data['template']['name']
            if 'description' in json_data['template']:
                template_vals['description'] = json_data['template']['description']

        template = self.env['config.matrix.template'].create(template_vals)

        # Process based on JSON format
        if 'sections' in json_data:
            # Process structured format with sections, fields, and options
            self._process_sections(template, json_data['sections'])
        elif 'questions' in json_data:
            # Process legacy format with questions
            self._process_questions(template, json_data['questions'])

        # Process component mappings if present
        if 'component_mappings' in json_data and isinstance(json_data['component_mappings'], list):
            self._process_component_mappings(template, json_data['component_mappings'])

        return template

    def _process_questions(self, template, questions):
        """Process questions and create sections/fields"""
        # Create a default section
        section = self.env['config.matrix.section'].create({
            'name': 'Configuration',
            'matrix_id': template.id,
            'sequence': 10,
        })

        # Track created fields by ID for dependencies
        field_map = {}

        # First pass: Create all fields
        for i, question in enumerate(questions):
            q_id = question.get('id')
            if not q_id:
                continue

            field_type = self._map_field_type(question.get('type', 'text'))

            # Skip conditional questions for now
            if q_id.startswith('q27_') or q_id.startswith('q40_'):
                continue

            field_vals = {
                'name': question.get('text', f"Question {i+1}"),
                'technical_name': q_id,
                'section_id': section.id,
                'field_type': field_type,
                'sequence': (i+1) * 10,
            }

            # Handle help text from legacy format
            legacy_field_data = {
                'help_text': question.get('description', ''),
                'default_value': question.get('default', ''),
                'min_value': question.get('min', ''),
                'max_value': question.get('max', ''),
            }

            # Use the same helper methods as the new format
            self._set_use_case_help_text(field_vals, legacy_field_data)
            self._set_use_case_visibility(field_vals, legacy_field_data)

            # Add field type specific properties using helper methods
            if field_type == 'number':
                self._set_use_case_default_values(field_vals, legacy_field_data, 'number')
                self._set_use_case_min_max_values(field_vals, legacy_field_data)
            elif field_type == 'text':
                self._set_use_case_default_values(field_vals, legacy_field_data, 'text')
            elif field_type == 'boolean':
                field_vals.update({
                    'boolean_true_label': 'Yes',
                    'boolean_false_label': 'No',
                })
                self._set_use_case_default_values(field_vals, legacy_field_data, 'boolean')
            elif field_type == 'selection':
                self._set_use_case_default_values(field_vals, legacy_field_data, 'selection')

            field = self.env['config.matrix.field'].create(field_vals)
            field_map[q_id] = field

            # Add options for selection fields
            if field_type == 'selection' and 'options' in question:
                self._create_options(field, question['options'])

        # Second pass: Add visibility conditions and component mappings
        for question in questions:
            q_id = question.get('id')
            if q_id not in field_map:
                continue

            field = field_map[q_id]

            # Add visibility condition if next is conditional
            if 'next' in question and isinstance(question['next'], dict):
                condition = question['next'].get('condition', '')
                if condition:
                    # Convert condition to Odoo format
                    odoo_condition = self._convert_condition(condition, field_map)
                    field.write({'visibility_condition': odoo_condition})

            # Add component mapping for door type - REMOVED HARD-CODED MAPPING
            # This was causing issues with all options having the same component product
            # Now component mappings should be handled through the proper import process

        return True

    def _map_field_type(self, json_type):
        """Map JSON field type to Odoo field type"""
        type_map = {
            'text': 'text',
            'number': 'number',
            'choice': 'selection',
            'boolean': 'boolean',
        }
        return type_map.get(json_type, 'text')

    def _create_options(self, field, options):
        """Create options for selection field"""
        for i, option in enumerate(options):
            if isinstance(option, dict):
                option_vals = {
                    'field_id': field.id,
                    'name': option.get('label', option.get('value', f"Option {i+1}")),
                    'value': option.get('value', f"option_{i+1}"),
                    'sequence': (i+1) * 10,
                }
                self.env['config.matrix.option'].create(option_vals)

    def _process_sections(self, template, sections):
        """Process sections, fields, and options from structured JSON format"""
        # Track created fields by technical name for dependencies
        field_map = {}

        # First pass: Create all sections and fields
        for section_data in sections:
            # Create section
            section_vals = {
                'name': section_data.get('name', 'Unnamed Section'),
                'matrix_id': template.id,
                'sequence': section_data.get('sequence', 10),
            }
            section = self.env['config.matrix.section'].create(section_vals)

            # Process fields in this section
            if 'fields' in section_data and isinstance(section_data['fields'], list):
                for field_data in section_data['fields']:
                    field = self._create_field(section, field_data)
                    if field and field_data.get('technical_name'):
                        field_map[field_data['technical_name']] = field

        # Second pass: Set visibility conditions
        for section_data in sections:
            if 'fields' in section_data and isinstance(section_data['fields'], list):
                for field_data in section_data['fields']:
                    technical_name = field_data.get('technical_name')
                    if technical_name and technical_name in field_map:
                        field = field_map[technical_name]

                        # Set visibility condition
                        if 'visibility_condition' in field_data:
                            field.write({'visibility_condition': field_data['visibility_condition']})

        return True

    def _create_field(self, section, field_data):
        """Create a field from field data"""
        if not field_data.get('name') or not field_data.get('field_type'):
            return False

        field_vals = {
            'name': field_data['name'],
            'technical_name': field_data.get('technical_name', ''),
            'section_id': section.id,
            'field_type': field_data['field_type'],
            'sequence': field_data.get('sequence', 10),
        }

        # Handle use-case-specific help text
        self._set_use_case_help_text(field_vals, field_data)

        # Handle use-case-specific visibility settings
        self._set_use_case_visibility(field_vals, field_data)

        # Add field type specific properties
        if field_data['field_type'] == 'number':
            # Handle use-case-specific default values
            self._set_use_case_default_values(field_vals, field_data, 'number')

            # Handle use-case-specific min/max values
            self._set_use_case_min_max_values(field_vals, field_data)

        elif field_data['field_type'] == 'text':
            # Handle use-case-specific default values
            self._set_use_case_default_values(field_vals, field_data, 'text')

        elif field_data['field_type'] == 'boolean':
            # Handle boolean-specific properties
            field_vals.update({
                'boolean_true_label': field_data.get('boolean_true_label', 'Yes'),
                'boolean_false_label': field_data.get('boolean_false_label', 'No'),
            })

            # Handle use-case-specific default values
            self._set_use_case_default_values(field_vals, field_data, 'boolean')

        elif field_data['field_type'] == 'selection':
            # Handle use-case-specific default values
            self._set_use_case_default_values(field_vals, field_data, 'selection')

        # Create the field
        field = self.env['config.matrix.field'].create(field_vals)

        # Set visibility condition after field creation (if specified)
        if field_data.get('visibility_condition'):
            field.write({'visibility_condition': field_data['visibility_condition']})

        # Add component mapping if specified
        if field_data.get('component_product') or field_data.get('component_product_id'):
            # Try to find the product by name or ID
            product_name = field_data.get('component_product') or field_data.get('component_product_id')
            product = self._find_product(product_name)
            if product:
                field.write({
                    'component_product_id': product.id,
                    'quantity_formula': field_data.get('quantity_formula', '1')
                })

        # Add options for selection fields
        if field_data['field_type'] == 'selection' and 'options' in field_data:
            self._create_field_options(field, field_data['options'])

        return field

    def _create_field_options(self, field, options_data):
        """Create options for a field from options data"""
        _logger.info(f"Creating {len(options_data)} options for field {field.name}")

        for i, option_data in enumerate(options_data):
            if not isinstance(option_data, dict) or not option_data.get('name'):
                _logger.warning(f"Skipping invalid option data: {option_data}")
                continue

            _logger.info(f"Processing option {i+1}: {option_data.get('name')}")

            option_vals = {
                'field_id': field.id,
                'name': option_data['name'],
                'value': option_data.get('value', option_data['name']),
                'sequence': option_data.get('sequence', (i+1) * 10),
            }

            # Handle help text for options by updating the field's use-case-specific help text
            if option_data.get('help_text'):
                option_help_text = option_data['help_text']
                # Update the field's help text for all use cases if not already set
                field_updates = {}
                if not field.check_measure_help_text:
                    field_updates['check_measure_help_text'] = option_help_text
                if not field.sales_help_text:
                    field_updates['sales_help_text'] = option_help_text
                if not field.online_help_text:
                    field_updates['online_help_text'] = option_help_text

                if field_updates:
                    field.write(field_updates)
                    _logger.info(f"Updated field '{field.name}' help text from option '{option_data['name']}'")

            # Note: Individual option help text is stored at the field level for all use cases

            # Add visibility condition if specified
            if option_data.get('visibility_condition'):
                option_vals['visibility_condition'] = option_data['visibility_condition']

            # Create the option
            option = self.env['config.matrix.option'].create(option_vals)
            _logger.info(f"Created option: {option.name}")

            # Add component mapping if specified
            if option_data.get('component_product') or option_data.get('component_product_id'):
                # Try to find the product by name or ID
                product_name = option_data.get('component_product') or option_data.get('component_product_id')
                _logger.info(f"Looking for component product: {product_name}")

                product = self._find_product(product_name)
                if product:
                    _logger.info(f"Found component product: {product.name} (ID: {product.id})")
                    option.write({
                        'component_product_id': product.id,
                        'quantity_formula': option_data.get('quantity_formula', '1')
                    })
                    _logger.info(f"Added component product {product.name} to option {option.name}")
                else:
                    _logger.warning(f"Could not find component product: {product_name}")
            else:
                _logger.info(f"No component product specified for option {option.name}")

    def _find_product(self, product_name):
        """Find a product by name or code or create a placeholder"""
        if not product_name:
            _logger.warning("Empty product name provided to _find_product")
            return False

        _logger.info(f"Searching for product: {product_name}")

        # First try to find by exact name or code
        product = self.env['product.product'].search([
            '|',
            ('name', '=', product_name),
            ('default_code', '=', product_name)
        ], limit=1)

        if product:
            _logger.info(f"Found product by exact match: {product.name} (ID: {product.id})")
            return product

        # Try with case-insensitive search
        product = self.env['product.product'].search([
            '|', '|', '|',
            ('name', 'ilike', product_name),
            ('default_code', 'ilike', product_name),
            ('name', 'ilike', product_name.replace(' ', '')),  # Try without spaces
            ('default_code', 'ilike', product_name.replace(' ', ''))  # Try without spaces
        ], limit=1)

        if product:
            _logger.info(f"Found product by fuzzy match: {product.name} (ID: {product.id})")
            return product

        # Log that product wasn't found
        _logger.warning(f"Product '{product_name}' not found. Trying to find a generic placeholder.")

        # Try to find a generic product to use as placeholder
        product = self.env['product.product'].search([
            '|',
            ('name', 'ilike', 'generic'),
            ('default_code', 'ilike', 'generic')
        ], limit=1)

        if product:
            _logger.info(f"Using generic placeholder: {product.name} (ID: {product.id})")
        else:
            _logger.error(f"No generic placeholder found for product '{product_name}'")

        return product

    def _find_product_by_color(self, base_product_name, color_name):
        """Find a product that matches both the base product name and color name"""
        _logger.info(f"Searching for product with base name '{base_product_name}' and color '{color_name}'")

        # Normalize the color name for better matching
        normalized_color = color_name.lower()

        # Extract key color terms that might be in the product name
        color_terms = []
        for word in normalized_color.split():
            if word not in ['matt', 'gloss', 'satin', 'anodic', 'powder', 'coat', 'colour', 'color']:
                color_terms.append(word)

        _logger.info(f"Color terms extracted: {color_terms}")

        # Try to find products that match both the base product name and any of the color terms
        products = self.env['product.product'].search([
            ('name', 'ilike', base_product_name)
        ])

        _logger.info(f"Found {len(products)} products matching base name '{base_product_name}'")

        # Filter products that contain any of the color terms
        matching_products = []
        for product in products:
            product_name_lower = product.name.lower()

            # Check if product name contains any of the color terms
            for term in color_terms:
                if term in product_name_lower:
                    _logger.info(f"Found matching product: {product.name} (ID: {product.id})")
                    matching_products.append(product)
                    break

        # If we found multiple matching products, try to find the best match
        if len(matching_products) > 1:
            _logger.info(f"Found multiple matching products, trying to find best match")

            # Try to find a product that contains the exact color code (e.g., GY276A)
            for product in matching_products:
                # Extract color codes (format like GY276A) from the color name
                import re
                color_codes = re.findall(r'[A-Z]{2}\d{3}[A-Z]', color_name)

                if color_codes:
                    for code in color_codes:
                        if code in product.name:
                            _logger.info(f"Found best match by color code: {product.name} (ID: {product.id})")
                            return product

            # If no exact match by color code, return the first matching product
            return matching_products[0]
        elif matching_products:
            return matching_products[0]

        # If no matching product found, return False
        _logger.warning(f"No product found matching both '{base_product_name}' and color '{color_name}'")
        return False

    def _convert_condition(self, condition, field_map):
        """Convert JSON condition to Odoo condition

        Args:
            condition: The condition string from the JSON
            field_map: A dictionary mapping field IDs to field records

        Returns:
            A condition string in Odoo format
        """
        _logger.info(f"Converting condition: {condition}")

        # This is a simplified conversion - would need more complex parsing for real conditions
        # Example: "getAnswer('q1').value == 'Yes'" -> "q1 == 'Yes'"
        converted = condition.replace("getAnswer('", "").replace("').value", "").replace("').choiceValue", "")

        # Replace field IDs with technical names if they exist in the field_map
        for field_id, field in field_map.items():
            if field_id in converted:
                _logger.info(f"Replacing field ID {field_id} with technical name {field.technical_name}")
                converted = converted.replace(field_id, field.technical_name)

        _logger.info(f"Converted condition: {converted}")
        return converted

    def _process_component_mappings(self, template, component_mappings):
        """Process component mappings from JSON data"""
        _logger.info(f"Processing {len(component_mappings)} component mappings")

        # Log all component mappings for debugging
        for i, mapping in enumerate(component_mappings):
            _logger.info(f"Component mapping {i+1}: {mapping}")

        for mapping_data in component_mappings:
            # Skip invalid mappings
            if not mapping_data.get('component_product'):
                _logger.warning("Skipping component mapping without component_product")
                continue

            # Find the component product
            component_product_name = mapping_data.get('component_product')
            component_product = self._find_product(component_product_name)

            if not component_product:
                _logger.warning(f"Could not find component product: {component_product_name}")
                continue

            # Check if this mapping is for a specific field option
            field_name = mapping_data.get('field_name')
            option_value = mapping_data.get('option_value')

            # Special handling for color fields
            if field_name in ['Powder Coat Colour', 'Frame Colour'] and option_value:
                _logger.info(f"Special handling for color field '{field_name}' with option value: {option_value}")
                # Log the component product for debugging
                _logger.info(f"Component product for this option: {component_product_name} (ID: {component_product.id if component_product else 'None'})")

                # Normalize the option value for better matching
                option_value = option_value.lower().replace(' ', '_').replace('-', '_')
                _logger.info(f"Normalized option value for {field_name}: {option_value}")

            if field_name and option_value:
                # This is an option-specific mapping
                _logger.info(f"Processing option-specific mapping for field '{field_name}', option '{option_value}'")

                # Find the field by name or technical name
                field = self.env['config.matrix.field'].search([
                    ('matrix_id', '=', template.id),
                    '|',
                    ('name', '=', field_name),
                    ('technical_name', '=', field_name)
                ], limit=1)

                if not field:
                    _logger.warning(f"Could not find field '{field_name}' for option mapping")
                    continue

                # Find the option by value with more detailed logging
                _logger.info(f"Searching for option with value '{option_value}' in field '{field.name}'")

                # Log all available options for this field to help diagnose issues
                all_options = self.env['config.matrix.option'].search([('field_id', '=', field.id)])
                _logger.info(f"Available options for field '{field.name}':")
                for opt in all_options:
                    _logger.info(f"  - Option: name='{opt.name}', value='{opt.value}'")

                # Try to find the option with exact value match first
                option = self.env['config.matrix.option'].search([
                    ('field_id', '=', field.id),
                    ('value', '=', option_value)
                ], limit=1)

                # If not found, try with name match
                if not option:
                    _logger.info(f"Option with value '{option_value}' not found, trying with name match")
                    option = self.env['config.matrix.option'].search([
                        ('field_id', '=', field.id),
                        ('name', '=', option_value)
                    ], limit=1)

                # If still not found, try with case-insensitive and normalized value
                if not option:
                    _logger.info(f"Option with name '{option_value}' not found, trying with normalized value")
                    normalized_value = option_value.lower().replace(' ', '_').replace('-', '_')
                    for opt in all_options:
                        opt_normalized = opt.value.lower().replace(' ', '_').replace('-', '_')
                        if opt_normalized == normalized_value:
                            option = opt
                            _logger.info(f"Found option with normalized value: {opt.name} (value: {opt.value})")
                            break

                if not option:
                    _logger.warning(f"Could not find option '{option_value}' for field '{field_name}'")
                    continue

                # Update the option with the component product
                _logger.info(f"Updating option '{option.name}' (ID: {option.id}) with component product '{component_product.name}' (ID: {component_product.id})")

                # Special handling for color fields to ensure correct component product mapping
                if field.name in ['Powder Coat Colour', 'Frame Colour']:
                    _logger.info(f"Special handling for color option: {option.name}")

                    # For color fields, we need to find a component product that matches the color
                    # Extract the color name from the option name
                    color_name = option.name

                    # Extract color code from option name if present
                    import re
                    color_codes = re.findall(r'[A-Z]{2}\d{3}[A-Z]', color_name)
                    color_code = color_codes[0] if color_codes else None

                    if color_code:
                        _logger.info(f"Found color code in option name: {color_code}")

                        # First try to find a product with the exact color code
                        color_code_products = self.env['product.product'].search([
                            ('name', 'ilike', component_product_name),
                            ('name', 'ilike', color_code)
                        ])

                        if color_code_products:
                            _logger.info(f"Found product with exact color code: {color_code_products[0].name}")
                            component_product = color_code_products[0]
                        else:
                            # Try to find a product with the color name
                            color_specific_product = self._find_product_by_color(component_product_name, color_name)

                            if color_specific_product:
                                _logger.info(f"Found color-specific product: {color_specific_product.name} (ID: {color_specific_product.id})")
                                component_product = color_specific_product
                            else:
                                _logger.warning(f"Could not find color-specific product for {color_name}, using generic product: {component_product.name}")
                    else:
                        # Try to find a product with the color name
                        color_specific_product = self._find_product_by_color(component_product_name, color_name)

                        if color_specific_product:
                            _logger.info(f"Found color-specific product: {color_specific_product.name} (ID: {color_specific_product.id})")
                            component_product = color_specific_product
                        else:
                            _logger.warning(f"Could not find color-specific product for {color_name}, using generic product: {component_product.name}")

                # Check if the option already has a component product
                if option.component_product_id:
                    _logger.info(f"Option '{option.name}' already has component product '{option.component_product_id.name}' (ID: {option.component_product_id.id})")

                option.write({
                    'component_product_id': component_product.id,
                    'quantity_formula': mapping_data.get('quantity_formula', '1')
                })

                # Verify the update was successful - read the option again to get updated values
                updated_option = self.env['config.matrix.option'].browse(option.id)
                _logger.info(f"Updated option '{updated_option.name}' with component product '{updated_option.component_product_id.name}' (ID: {updated_option.component_product_id.id})")
            else:
                # This is a general template-level mapping
                mapping_vals = {
                    'matrix_id': template.id,
                    'component_product_id': component_product.id,
                    'quantity_formula': mapping_data.get('quantity_formula', '1'),
                    'condition': mapping_data.get('condition', ''),
                }

                # Create the component mapping
                self.env['config.matrix.component.mapping'].create(mapping_vals)
                _logger.info(f"Created template-level component mapping for {component_product_name}")

        return True

    def _set_use_case_help_text(self, field_vals, field_data):
        """Set use-case-specific help text from field data"""
        # Check for use-case-specific help text first
        check_measure_help = field_data.get('check_measure_help_text', '')
        sales_help = field_data.get('sales_help_text', '')
        online_help = field_data.get('online_help_text', '')

        # If no use-case-specific help text, use the general help_text for all
        general_help = field_data.get('help_text', '')

        field_vals.update({
            'check_measure_help_text': check_measure_help or general_help,
            'sales_help_text': sales_help or general_help,
            'online_help_text': online_help or general_help,
        })

    def _set_use_case_visibility(self, field_vals, field_data):
        """Set use-case-specific visibility settings from field data"""
        # Check for use-case-specific visibility settings
        field_vals.update({
            'check_measure_visible': field_data.get('check_measure_visible', True),
            'sales_visible': field_data.get('sales_visible', True),
            'online_visible': field_data.get('online_visible', True),
        })

    def _set_use_case_default_values(self, field_vals, field_data, field_type):
        """Set use-case-specific default values from field data"""
        # Check for use-case-specific default values first
        check_measure_default = field_data.get('check_measure_default_value', '')
        sales_default = field_data.get('sales_default_value', '')
        online_default = field_data.get('online_default_value', '')

        # If no use-case-specific defaults, use the general default_value for all
        general_default = field_data.get('default_value', '')

        if field_type == 'boolean':
            # For boolean fields, convert to string representation
            if check_measure_default == '':
                check_measure_default = 'true' if general_default else 'false'
            if sales_default == '':
                sales_default = 'true' if general_default else 'false'
            if online_default == '':
                online_default = 'true' if general_default else 'false'
        elif field_type == 'number':
            # For number fields, convert to string
            if check_measure_default == '':
                check_measure_default = str(general_default) if general_default != '' else ''
            if sales_default == '':
                sales_default = str(general_default) if general_default != '' else ''
            if online_default == '':
                online_default = str(general_default) if general_default != '' else ''
        else:
            # For text and selection fields, use as-is
            if check_measure_default == '':
                check_measure_default = str(general_default) if general_default != '' else ''
            if sales_default == '':
                sales_default = str(general_default) if general_default != '' else ''
            if online_default == '':
                online_default = str(general_default) if general_default != '' else ''

        field_vals.update({
            'check_measure_default_value': check_measure_default,
            'sales_default_value': sales_default,
            'online_default_value': online_default,
        })

    def _set_use_case_min_max_values(self, field_vals, field_data):
        """Set use-case-specific min/max values from field data"""
        # Check for use-case-specific min/max values first
        check_measure_min = field_data.get('check_measure_min_value', '')
        check_measure_max = field_data.get('check_measure_max_value', '')
        sales_min = field_data.get('sales_min_value', '')
        sales_max = field_data.get('sales_max_value', '')
        online_min = field_data.get('online_min_value', '')
        online_max = field_data.get('online_max_value', '')

        # If no use-case-specific values, use the general min/max values for all
        general_min = field_data.get('min_value', '')
        general_max = field_data.get('max_value', '')

        # Set min values
        if check_measure_min == '' and general_min != '':
            check_measure_min = str(general_min)
        if sales_min == '' and general_min != '':
            sales_min = str(general_min)
        if online_min == '' and general_min != '':
            online_min = str(general_min)

        # Set max values
        if check_measure_max == '' and general_max != '':
            check_measure_max = str(general_max)
        if sales_max == '' and general_max != '':
            sales_max = str(general_max)
        if online_max == '' and general_max != '':
            online_max = str(general_max)

        # Only set non-empty values
        if check_measure_min:
            field_vals['check_measure_min_value'] = check_measure_min
        if check_measure_max:
            field_vals['check_measure_max_value'] = check_measure_max
        if sales_min:
            field_vals['sales_min_value'] = sales_min
        if sales_max:
            field_vals['sales_max_value'] = sales_max
        if online_min:
            field_vals['online_min_value'] = online_min
        if online_max:
            field_vals['online_max_value'] = online_max
