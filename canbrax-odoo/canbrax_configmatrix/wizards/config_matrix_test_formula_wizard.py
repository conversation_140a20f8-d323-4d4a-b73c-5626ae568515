# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging
import json
import re
from odoo.tools.safe_eval import safe_eval
import math

_logger = logging.getLogger(__name__)

class ConfigMatrixTestFormulaWizard(models.TransientModel):
    _name = 'config.matrix.test.formula.wizard'
    _description = 'Test Formula Wizard'

    formula = fields.Text("Formula", required=True)
    field_id = fields.Many2one('config.matrix.field', "Field")
    option_id = fields.Many2one('config.matrix.option', "Option")
    template_id = fields.Many2one('config.matrix.template', "Template")

    test_values = fields.Text("Test Values", help="JSON object with test values")
    result = fields.Text("Result", readonly=True)
    result_value = fields.Float("Numeric Result", readonly=True)
    result_type = fields.Char("Result Type", readonly=True)
    error = fields.Text("Error", readonly=True)

    @api.model
    def default_get(self, fields_list):
        res = super(ConfigMatrixTestFormulaWizard, self).default_get(fields_list)

        # Get active model and ID
        active_model = self.env.context.get('active_model')
        active_id = self.env.context.get('active_id')

        if active_model and active_id:
            if active_model == 'config.matrix.field':
                field = self.env[active_model].browse(active_id)
                res.update({
                    'formula': field.quantity_formula or '',
                    'field_id': field.id,
                    'template_id': field.section_id.matrix_id.id,
                })
            elif active_model == 'config.matrix.option':
                option = self.env[active_model].browse(active_id)
                res.update({
                    'formula': option.quantity_formula or '',
                    'option_id': option.id,
                    'field_id': option.field_id.id,
                    'template_id': option.field_id.section_id.matrix_id.id,
                })

        # Extract variable names from the formula
        formula = res.get('formula', '')
        if formula:
            # Generate sample test values based on variables in the formula
            test_values = self._generate_test_values(formula, res.get('template_id'))
            res['test_values'] = json.dumps(test_values, indent=2)
        else:
            # Initialize with empty JSON object
            res['test_values'] = '{}'

        return res

    def _generate_test_values(self, formula, template_id):
        """
        Generate test values based on variables in the formula

        Args:
            formula (str): The formula to analyze
            template_id (int): The template ID to get field information

        Returns:
            dict: Test values for the formula
        """
        if not formula or not template_id:
            return {}

        # Extract potential variable names from the formula
        # This is a simple regex to find words that might be variables
        potential_vars = set(re.findall(r'[a-zA-Z_][a-zA-Z0-9_]*', formula))

        # Remove common Python keywords and functions
        python_keywords = {
            'if', 'else', 'and', 'or', 'not', 'True', 'False', 'None',
            'ceil', 'floor', 'round', 'min', 'max', 'sum', 'len',
            'int', 'float', 'str', 'bool'
        }
        potential_vars = potential_vars - python_keywords

        # Get field information from the template
        template = self.env['config.matrix.template'].browse(template_id)
        if not template.exists():
            return {}  # Return empty if template doesn't exist

        # Get all fields in the template
        fields_info = {}
        for section in template.section_ids:
            for field in section.field_ids:
                fields_info[field.technical_name] = {
                    'type': field.field_type,
                    'name': field.name,
                    'default': field.default_value,
                    'options': field.option_ids.mapped('value') if field.field_type == 'selection' else [],
                    'field_obj': field,
                }

        # Generate values based on field types
        test_values = {}
        for var in potential_vars:
            if var in fields_info:
                field_info = fields_info[var]
                field_type = field_info['type']

                # Use default value if available
                if field_info['default']:
                    # Convert default value to appropriate type
                    if field_type == 'number':
                        try:
                            test_values[var] = float(field_info['default'])
                        except (ValueError, TypeError):
                            test_values[var] = 0
                    elif field_type == 'boolean':
                        test_values[var] = field_info['default'].lower() in ('true', 't', 'yes', 'y', '1')
                    else:
                        test_values[var] = field_info['default']
                else:
                    # No default value, use appropriate empty/zero value
                    if field_type == 'number':
                        test_values[var] = 0
                    elif field_type == 'text':
                        test_values[var] = ""
                    elif field_type == 'boolean':
                        test_values[var] = False
                    elif field_type == 'selection' and field_info['options']:
                        test_values[var] = field_info['options'][0]
                    else:
                        test_values[var] = ""
            else:
                # Variable not found in fields, leave it out
                pass

        return test_values

    def test_formula(self):
        """Test the formula with the provided test values"""
        self.ensure_one()

        if not self.formula:
            self.write({
                'result': 'No result',
                'result_value': 0,
                'result_type': 'None',
                'error': _("No formula provided")
            })
            return {
                'type': 'ir.actions.act_window',
                'res_model': 'config.matrix.test.formula.wizard',
                'res_id': self.id,
                'view_mode': 'form',
                'target': 'new',
            }

        try:
            # Parse test values
            test_values = json.loads(self.test_values or '{}')

            # Add math functions to context
            math_context = {k: getattr(math, k) for k in dir(math) if not k.startswith('_')}
            test_values.update(math_context)

            # Evaluate formula
            result = safe_eval(self.formula, test_values)

            # Format result based on type
            result_type = type(result).__name__

            # Set numeric result if possible
            numeric_result = 0
            if isinstance(result, (int, float)):
                numeric_result = float(result)

            # Format display result
            if isinstance(result, bool):
                display_result = str(result)
            elif isinstance(result, (int, float)):
                display_result = str(result)
            elif isinstance(result, str):
                display_result = f'"{result}"'
            elif result is None:
                display_result = "None"
            else:
                display_result = repr(result)

            # Update result
            self.write({
                'result': display_result,
                'result_value': numeric_result,
                'result_type': result_type,
                'error': False
            })

            return {
                'type': 'ir.actions.act_window',
                'res_model': 'config.matrix.test.formula.wizard',
                'res_id': self.id,
                'view_mode': 'form',
                'target': 'new',
            }

        except Exception as e:
            self.write({
                'result': 'Error',
                'result_value': 0,
                'result_type': 'Error',
                'error': str(e)
            })

            return {
                'type': 'ir.actions.act_window',
                'res_model': 'config.matrix.test.formula.wizard',
                'res_id': self.id,
                'view_mode': 'form',
                'target': 'new',
            }
