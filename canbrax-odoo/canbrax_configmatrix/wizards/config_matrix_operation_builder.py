# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class ConfigMatrixOperationBuilder(models.TransientModel):
    _name = 'config.matrix.operation.builder'
    _description = 'Operation Mapping Builder'

    field_id = fields.Many2one('config.matrix.field', "Field")
    option_id = fields.Many2one('config.matrix.option', "Option")
    matrix_id = fields.Many2one('config.matrix.template', "Template")

    # Operation template selection
    operation_template_id = fields.Many2one('config.matrix.operation.template', 'Operation Template',
                                           help='Select an operation template')

    # Option to create new template
    create_new_template = fields.<PERSON><PERSON>an('Create New Template', default=False,
                                        help='Create a new operation template instead of using existing one')

    # Fields for new template creation
    new_template_name = fields.Char('Template Name', help='Name for the new operation template')
    new_template_workcenter_id = fields.Many2one('mrp.workcenter', 'Work Center',
                                                 help='Work center for the new template')
    new_template_category = fields.Selection([
        ('cutting', 'Cutting'),
        ('assembly', 'Assembly'),
        ('finishing', 'Finishing'),
        ('quality', 'Quality Control'),
        ('packaging', 'Packaging'),
        ('other', 'Other')
    ], string='Category', default='other', help='Category for the new template')

    # Computed fields from template (for display)
    operation_name = fields.Char('Operation Name', compute='_compute_template_fields', readonly=True)
    workcenter_id = fields.Many2one('mrp.workcenter', 'Work Center', compute='_compute_template_fields', readonly=True)

    formula_type = fields.Selection([
        ('fixed', 'Fixed Duration'),
        ('field', 'Based on Field Value'),
        ('calculation', 'Calculation'),
        ('conditional', 'Conditional'),
        ('advanced', 'Advanced Formula')
    ], default='fixed', required=True, string="Duration Type")

    # Fixed duration
    fixed_duration = fields.Float("Fixed Duration (minutes)", default=60.0)

    # Field-based duration
    reference_field_id = fields.Many2one('config.matrix.field', "Reference Field",
                                        domain="[('matrix_id', '=', matrix_id)]")
    multiplier = fields.Float("Multiplier", default=1.0)
    base_duration = fields.Float("Base Duration (minutes)", default=0.0)

    # Calculation
    calculation_formula = fields.Char("Calculation Formula",
                                     help="e.g., height * width / 1000")

    # Conditional
    condition_field_id = fields.Many2one('config.matrix.field', "Condition Field",
                                        domain="[('matrix_id', '=', matrix_id)]")
    condition_value = fields.Char("Condition Value")
    true_duration = fields.Float("Duration if True (minutes)", default=60.0)
    false_duration = fields.Float("Duration if False (minutes)", default=30.0)

    # Advanced formula
    advanced_formula = fields.Text("Advanced Formula",
                                  help="Python expression for complex duration calculations")

    # Result
    result_formula = fields.Char("Result Formula", readonly=True)
    test_result = fields.Char("Test Result", readonly=True)

    @api.depends('operation_template_id')
    def _compute_template_fields(self):
        """Compute fields from the selected operation template"""
        for wizard in self:
            if wizard.operation_template_id:
                wizard.operation_name = wizard.operation_template_id.name
                wizard.workcenter_id = wizard.operation_template_id.workcenter_id.id
            else:
                wizard.operation_name = False
                wizard.workcenter_id = False

    # Operation details
    setup_time = fields.Float("Setup Time (minutes)", default=0.0)
    cleanup_time = fields.Float("Cleanup Time (minutes)", default=0.0)
    worksheet_type = fields.Selection([
        ('pdf', 'PDF'),
        ('google_slide', 'Google Slide'),
        ('text', 'Text')
    ], string="Worksheet Type", default='text')
    worksheet_content = fields.Text("Worksheet Content")
    notes = fields.Text("Notes")

    @api.onchange('formula_type', 'fixed_duration', 'reference_field_id', 'multiplier', 'base_duration',
                  'calculation_formula', 'condition_field_id', 'condition_value', 'true_duration', 'false_duration',
                  'advanced_formula')
    def _onchange_formula_parameters(self):
        """Update the result formula when parameters change"""
        self._compute_result_formula()

    def _compute_result_formula(self):
        """Compute the result formula based on the selected type and parameters"""
        self.ensure_one()

        if self.formula_type == 'fixed':
            self.result_formula = str(self.fixed_duration)

        elif self.formula_type == 'field':
            if self.reference_field_id:
                field_tech_name = self.reference_field_id.technical_name or f"field_{self.reference_field_id.id}"
                if self.base_duration:
                    self.result_formula = f"({field_tech_name} * {self.multiplier}) + {self.base_duration}"
                else:
                    self.result_formula = f"{field_tech_name} * {self.multiplier}"
            else:
                self.result_formula = str(self.fixed_duration)

        elif self.formula_type == 'calculation':
            if self.calculation_formula:
                self.result_formula = self.calculation_formula
            else:
                self.result_formula = str(self.fixed_duration)

        elif self.formula_type == 'conditional':
            if self.condition_field_id and self.condition_value:
                field_tech_name = self.condition_field_id.technical_name or f"field_{self.condition_field_id.id}"
                self.result_formula = f"{self.true_duration} if {field_tech_name} == '{self.condition_value}' else {self.false_duration}"
            else:
                self.result_formula = str(self.fixed_duration)

        elif self.formula_type == 'advanced':
            if self.advanced_formula:
                self.result_formula = self.advanced_formula
            else:
                self.result_formula = str(self.fixed_duration)

        else:
            self.result_formula = str(self.fixed_duration)

    def action_test_formula(self):
        """Test the formula with sample data"""
        self.ensure_one()
        self._compute_result_formula()

        if not self.result_formula:
            self.test_result = "No formula to test"
            return

        try:
            # Create test context with sample values
            test_context = {
                '__builtins__': {},
                'min': min,
                'max': max,
                'abs': abs,
                'round': round,
                'float': float,
                'int': int,
                'height': 1200,
                'width': 600,
                'area': 720000,  # height * width
            }

            # Add sample values for fields in the matrix
            if self.matrix_id:
                for field in self.matrix_id.field_ids:
                    field_name = field.technical_name or f"field_{field.id}"
                    if field.field_type == 'integer':
                        test_context[field_name] = 100
                    elif field.field_type == 'float':
                        test_context[field_name] = 100.5
                    elif field.field_type == 'boolean':
                        test_context[field_name] = True
                    else:
                        test_context[field_name] = 'test_value'

            result = eval(self.result_formula, test_context)
            self.test_result = f"Test result: {result} minutes"

        except Exception as e:
            self.test_result = f"Error: {str(e)}"

    def action_apply_operation(self):
        """Apply the operation template to the field or option"""
        self.ensure_one()

        # Create new template if requested
        if self.create_new_template:
            if not self.new_template_name or not self.new_template_workcenter_id:
                raise UserError(_("Please provide a name and work center for the new template."))

            # Compute result formula for the new template
            self._compute_result_formula()

            # Create the new operation template
            template = self.env['config.matrix.operation.template'].create({
                'name': self.new_template_name,
                'workcenter_id': self.new_template_workcenter_id.id,
                'category': self.new_template_category,
                'default_duration': self.fixed_duration if self.formula_type == 'fixed' else 60.0,
                'duration_formula': self.result_formula if self.formula_type != 'fixed' else False,
                'description': f'Created from operation builder for {self.field_id.name if self.field_id else self.option_id.name}',
                'worksheet_type': self.worksheet_type,
                'worksheet_content': self.worksheet_content,
                'setup_time': self.setup_time,
                'cleanup_time': self.cleanup_time,
                'notes': self.notes,
            })
            operation_template_id = template.id
        else:
            if not self.operation_template_id:
                raise UserError(_("Please select an operation template or choose to create a new one."))
            operation_template_id = self.operation_template_id.id

        # Determine custom duration formula if different from template
        custom_duration_formula = False
        if not self.create_new_template and self.operation_template_id:
            self._compute_result_formula()
            template_formula = self.operation_template_id.duration_formula or str(self.operation_template_id.default_duration)
            if self.result_formula and self.result_formula != template_formula:
                custom_duration_formula = self.result_formula

        # Check if we're updating an existing operation mapping
        field_operation_mapping_id = self.env.context.get('field_operation_mapping_id')
        option_operation_mapping_id = self.env.context.get('option_operation_mapping_id')

        if field_operation_mapping_id:
            # Update the field operation mapping
            mapping = self.env['config.matrix.field.operation.mapping'].browse(field_operation_mapping_id)
            if mapping.exists():
                mapping.write({
                    'operation_template_id': operation_template_id,
                    'custom_duration_formula': custom_duration_formula,
                })
                return {'type': 'ir.actions.act_window_close'}

        elif option_operation_mapping_id:
            # Update the option operation mapping
            mapping = self.env['config.matrix.option.operation.mapping'].browse(option_operation_mapping_id)
            if mapping.exists():
                mapping.write({
                    'operation_template_id': operation_template_id,
                    'custom_duration_formula': custom_duration_formula,
                })
                return {'type': 'ir.actions.act_window_close'}

        # Create new operation mapping
        if self.option_id:
            # Create option operation mapping
            self.env['config.matrix.option.operation.mapping'].create({
                'option_id': self.option_id.id,
                'operation_template_id': operation_template_id,
                'custom_duration_formula': custom_duration_formula,
            })
        elif self.field_id:
            # Create field operation mapping
            self.env['config.matrix.field.operation.mapping'].create({
                'field_id': self.field_id.id,
                'operation_template_id': operation_template_id,
                'custom_duration_formula': custom_duration_formula,
            })
        else:
            raise UserError(_("No field or option selected."))

        return {'type': 'ir.actions.act_window_close'}
