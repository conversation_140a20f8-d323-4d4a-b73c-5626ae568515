# -*- coding: utf-8 -*-

from odoo import models, fields, api
import json
import logging

_logger = logging.getLogger(__name__)


class ConfigMatrixCalculatedFieldImportWizard(models.TransientModel):
    _name = 'config.matrix.calculated.field.import.wizard'
    _description = 'Import Calculated Fields from JSON'

    template_ids = fields.Many2many('config.matrix.template', 'calc_field_import_template_rel',
                                  'wizard_id', 'template_id', string="Templates",
                                  help="Templates to associate fields with (leave empty for global fields)")
    json_data = fields.Text("JSON Data", required=True,
                          help="JSON object containing calculated field definitions")
    
    def action_import(self):
        """Import calculated fields from JSON"""
        self.ensure_one()
        
        try:
            # Import the fields
            calc_field_model = self.env['config.matrix.calculated.field']
            result = calc_field_model.import_calculated_fields_from_json(
                self.json_data,
                self.template_ids.ids if self.template_ids else None
            )
            
            # Show success message
            message = f"Import completed successfully!\n"
            message += f"Created: {result['created']} fields\n"
            message += f"Updated: {result['updated']} fields\n"
            message += f"Total processed: {result['total']} fields"
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Import Successful',
                    'message': message,
                    'type': 'success',
                    'sticky': False,
                }
            }
            
        except Exception as e:
            _logger.error(f"Error importing calculated fields: {str(e)}")
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Import Failed',
                    'message': f"Error importing calculated fields: {str(e)}",
                    'type': 'danger',
                    'sticky': True,
                }
            }
