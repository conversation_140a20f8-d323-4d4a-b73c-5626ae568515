# -*- coding: utf-8 -*-

import json
import logging
from odoo import models, fields, api, _
from odoo.tools.safe_eval import safe_eval

_logger = logging.getLogger(__name__)

class ConfigMatrixTestConditionWizard(models.TransientModel):
    _name = 'config.matrix.test.condition.wizard'
    _description = 'Test Visibility Condition Wizard'

    condition = fields.Text("Condition", required=True)
    field_id = fields.Many2one('config.matrix.field', "Field")
    option_id = fields.Many2one('config.matrix.option', "Option")
    for_option = fields.Boolean("For Option", default=False)
    template_id = fields.Many2one('config.matrix.template', "Template")

    test_values = fields.Text("Test Values", help="JSON object with test values", groups="base.group_no_one")
    result = fields.Text("Result", readonly=True)
    error = fields.Text("Error", readonly=True)

    @api.model
    def default_get(self, fields_list):
        res = super(ConfigMatrixTestConditionWizard, self).default_get(fields_list)

        # Get active model and ID
        active_model = self.env.context.get('active_model')
        active_id = self.env.context.get('active_id')
        for_option = self.env.context.get('default_for_option', False)

        if active_model and active_id:
            if active_model == 'config.matrix.field':
                field = self.env[active_model].browse(active_id)
                res.update({
                    'condition': field.visibility_condition or '',
                    'field_id': field.id,
                    'template_id': field.matrix_id.id,
                    'for_option': False,
                })
            elif active_model == 'config.matrix.option':
                option = self.env[active_model].browse(active_id)
                res.update({
                    'condition': option.visibility_condition or '',
                    'option_id': option.id,
                    'field_id': option.field_id.id,
                    'template_id': option.matrix_id.id,
                    'for_option': True,
                })

        # Extract variable names from the condition
        condition = res.get('condition', '')
        if condition:
            # Generate sample test values based on variables in the condition
            test_values = self._generate_test_values(condition, res.get('template_id'))
            res['test_values'] = json.dumps(test_values, indent=2)
        else:
            # Initialize with empty JSON object
            res['test_values'] = '{\n    "width": 1000,\n    "height": 2000,\n    "depth": 500,\n    "color": "red",\n    "has_lock": true\n}'

        return res

    def _generate_test_values(self, condition, template_id):
        """Generate sample test values based on variables in the condition"""
        test_values = {}

        # Default values for common fields
        default_values = {
            'width': 1000,
            'height': 2000,
            'depth': 500,
            'color': 'red',
            'has_lock': True,
            'door_type': 'sliding',
            'material': 'wood',
        }

        # Add all default values
        test_values.update(default_values)

        # If we have a template, try to get actual field names
        if template_id:
            template = self.env['config.matrix.template'].browse(template_id)
            fields = self.env['config.matrix.field'].search([('matrix_id', '=', template_id)])

            # Add all field technical names with appropriate default values
            for field in fields:
                if field.field_type == 'text':
                    test_values[field.technical_name] = 'Sample Text'
                elif field.field_type == 'number':
                    test_values[field.technical_name] = 100
                elif field.field_type == 'boolean':
                    test_values[field.technical_name] = True
                elif field.field_type == 'selection' and field.option_ids:
                    # Use the first option value
                    test_values[field.technical_name] = field.option_ids[0].value

        return test_values

    def action_test_condition(self):
        """Test the condition with the provided values"""
        self.ensure_one()

        try:
            # Parse test values
            test_values = json.loads(self.test_values)

            # Add math functions
            import math
            math_context = {k: getattr(math, k) for k in dir(math) if not k.startswith('_')}
            test_values.update(math_context)

            # Evaluate the condition
            result = safe_eval(self.condition, test_values)

            # Format the result
            self.result = f"Result: {result} ({type(result).__name__})"
            self.error = False

            return {
                'type': 'ir.actions.do_nothing',
            }
        except Exception as e:
            self.result = "Error"
            self.error = str(e)
            return {
                'type': 'ir.actions.do_nothing',
            }

    def action_delete_condition(self):
        """Delete the visibility condition"""
        self.ensure_one()

        if self.for_option and self.option_id:
            # Clear option visibility conditions
            self.option_id.action_clear_visibility_conditions()
        elif self.field_id:
            # Clear field visibility conditions
            self.field_id.action_clear_visibility_conditions()

        return {
            'type': 'ir.actions.act_window_close',
        }
