<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_replace_text_wizard_form" model="ir.ui.view">
        <field name="name">replace.text.wizard.form</field>
        <field name="model">replace.text.wizard</field>
        <field name="arch" type="xml">
            <form string="Replace Technical Names in Config Matrix Template" class="o_form_config_wizard">
                <sheet>
                    <h2>Import &amp; Replace Technical Names</h2>
                    <div class="alert alert-info" role="alert" style="margin-bottom: 16px;">
                        <ul style="margin-bottom: 0;">
                            <li>Download the sample import file and fill in your technical name replacements.</li>
                            <li>The first row is the header, the second row is for template codes, and the following rows are for technical name mappings.</li>
                            <li>Only the selected template will be updated.</li>
                        </ul>
                    </div>
                    <group>
                        <field name="template_id" readonly="1" invisible="1"/>
                        <field name="file" filename="import.csv" required="0"/>
                    </group>
                    <footer>
                        <button name="action_replace_text" type="object" string="Import and Replace" class="btn-primary"/>
                        <button name="download_template" type="object" string="Download Sample CSV" class="btn-secondary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </sheet>
            </form>
        </field>
    </record>
</odoo>
