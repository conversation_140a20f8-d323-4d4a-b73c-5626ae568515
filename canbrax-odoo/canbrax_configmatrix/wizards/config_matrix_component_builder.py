# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError

class ConfigMatrixComponentBuilder(models.TransientModel):
    _name = 'config.matrix.component.builder'
    _description = 'Component Mapping Builder'

    field_id = fields.Many2one('config.matrix.field', "Field")
    option_id = fields.Many2one('config.matrix.option', "Option")
    matrix_id = fields.Many2one('config.matrix.template', "Template")
    component_product_id = fields.Many2one('product.product', "Component Product")

    formula_type = fields.Selection([
        ('fixed', 'Fixed Quantity'),
        ('field', 'Based on Field Value'),
        ('calculation', 'Calculation'),
        ('conditional', 'Conditional'),
        ('advanced', 'Advanced Formula')
    ], default='fixed', required=True, string="Quantity Type")

    # Fixed quantity
    fixed_quantity = fields.Float("Fixed Quantity", default=1.0)

    # Field-based quantity
    quantity_field_id = fields.Many2one('config.matrix.field', "Quantity Field")

    # Calculation
    calculation_type = fields.Selection([
        ('area', 'Area (Width × Height)'),
        ('perimeter', 'Perimeter (2 × Width + 2 × Height)'),
        ('length', 'Length'),
        ('custom', 'Custom Calculation')
    ], string="Calculation Type", default='area')
    width_field_id = fields.Many2one('config.matrix.field', "Width Field")
    height_field_id = fields.Many2one('config.matrix.field', "Height Field")
    length_field_id = fields.Many2one('config.matrix.field', "Length Field")
    unit_conversion = fields.Float("Unit Conversion Factor", default=1.0,
                                help="Divide by this factor to convert units (e.g., 1000 to convert mm² to m²)")
    custom_calculation = fields.Char("Custom Calculation",
                                   placeholder="e.g., width * height / 1000000")

    # Conditional quantity
    condition_field_id = fields.Many2one('config.matrix.field', "Condition Field")
    condition_operator = fields.Selection([
        ('==', 'Equals'),
        ('!=', 'Not Equals'),
        ('>', 'Greater Than'),
        ('<', 'Less Than'),
        ('>=', 'Greater Than or Equal'),
        ('<=', 'Less Than or Equal'),
        ('in', 'In List'),
        ('not in', 'Not In List'),
        ('is_set', 'Is Set (Has Value)'),
        ('is_not_set', 'Is Not Set (No Value)')
    ], string="Operator", default="==")
    condition_value = fields.Char("Condition Value", help="Value or comma-separated list for 'in' operators")
    true_quantity = fields.Float("Quantity if True", default=1.0)
    false_quantity = fields.Float("Quantity if False", default=0.0)

    # Advanced formula
    advanced_formula = fields.Char("Advanced Formula",
                                 placeholder="Python expression, e.g., math.ceil(width * height / 1000000)")

    # Result
    result_formula = fields.Char("Result Formula", readonly=True)

    @api.model
    def default_get(self, fields_list):
        """Set default values from the context"""
        res = super(ConfigMatrixComponentBuilder, self).default_get(fields_list)

        # Check if we're coming from a multiple component mapping
        multiple_mapping_id = self.env.context.get('multiple_component_mapping_id')
        mapping_model = self.env.context.get('mapping_model', 'config.matrix.field.component.mapping')

        if multiple_mapping_id:
            # Load values from the multiple component mapping
            mapping = self.env[mapping_model].browse(multiple_mapping_id)
            if mapping.exists():
                if mapping.component_product_id:
                    res['component_product_id'] = mapping.component_product_id.id
                if mapping.quantity_formula:
                    res['advanced_formula'] = mapping.quantity_formula

                # Set the appropriate field or option based on mapping type
                if mapping_model == 'config.matrix.field.component.mapping':
                    res['field_id'] = mapping.field_id.id
                    res['matrix_id'] = mapping.field_id.matrix_id.id
                elif mapping_model == 'config.matrix.option.component.mapping.extended':
                    res['option_id'] = mapping.option_id.id
                    res['matrix_id'] = mapping.option_id.matrix_id.id

                return res

        # Get matrix_id from context or from field/option
        matrix_id = self.env.context.get('default_matrix_id')
        if not matrix_id:
            field_id = self.env.context.get('default_field_id')
            option_id = self.env.context.get('default_option_id')

            if field_id:
                field = self.env['config.matrix.field'].browse(field_id)
                matrix_id = field.matrix_id.id
            elif option_id:
                option = self.env['config.matrix.option'].browse(option_id)
                matrix_id = option.matrix_id.id

        if matrix_id:
            res['matrix_id'] = matrix_id

        # Check if we're coming from an option
        option_id = self.env.context.get('default_option_id')
        if option_id:
            option = self.env['config.matrix.option'].browse(option_id)
            res['option_id'] = option_id

            # Set component product if available
            if option.component_product_id:
                res['component_product_id'] = option.component_product_id.id

            # Set formula if available
            if option.quantity_formula:
                res['advanced_formula'] = option.quantity_formula

        return res

    @api.onchange('field_id', 'option_id')
    def _onchange_field_or_option(self):
        """Set initial values based on existing formula"""
        if not self.field_id and not self.option_id:
            return

        # Determine which object we're working with
        target_obj = self.option_id if self.option_id else self.field_id

        # Set initial values if quantity formula exists
        if target_obj.component_product_id:
            self.component_product_id = target_obj.component_product_id.id

        if target_obj.quantity_formula:
            self.advanced_formula = target_obj.quantity_formula
            # Try to parse the formula to set builder fields
            self._try_parse_formula()

        # Set domain for field selectors
        matrix_id = self.matrix_id.id if self.matrix_id else False

        if not matrix_id:
            if self.field_id and self.field_id.matrix_id:
                matrix_id = self.field_id.matrix_id.id
                self.matrix_id = matrix_id
            elif self.option_id and self.option_id.matrix_id:
                matrix_id = self.option_id.matrix_id.id
                self.matrix_id = matrix_id

        if not matrix_id:
            return

        # Create domain strings that will be evaluated by the client
        # This avoids the comma-separated string issue
        self.env.cr.execute("""
            SELECT id FROM config_matrix_field
            WHERE matrix_id = %s AND field_type = 'number'
        """, (matrix_id,))
        number_field_ids = [r[0] for r in self.env.cr.fetchall()]

        self.env.cr.execute("""
            SELECT id FROM config_matrix_field
            WHERE matrix_id = %s
        """, (matrix_id,))
        any_field_ids = [r[0] for r in self.env.cr.fetchall()]

        # Return domains as lists of IDs
        return {'domain': {
            'quantity_field_id': [('id', 'in', number_field_ids)],
            'width_field_id': [('id', 'in', number_field_ids)],
            'height_field_id': [('id', 'in', number_field_ids)],
            'length_field_id': [('id', 'in', number_field_ids)],
            'condition_field_id': [('id', 'in', any_field_ids)]
        }}

    @api.onchange('formula_type')
    def _onchange_formula_type(self):
        """Handle formula type changes"""
        if self.formula_type == 'fixed':
            self.result_formula = str(self.fixed_quantity)
        elif self.formula_type == 'advanced':
            if not self.advanced_formula:
                self.advanced_formula = '1'
            self.result_formula = self.advanced_formula
        else:
            # For other types, we'll build the formula in the other onchange method
            pass

    @api.onchange('fixed_quantity', 'quantity_field_id',
                 'calculation_type', 'width_field_id', 'height_field_id', 'length_field_id',
                 'unit_conversion', 'custom_calculation', 'condition_field_id',
                 'condition_operator', 'condition_value', 'true_quantity',
                 'false_quantity', 'advanced_formula')
    def _onchange_formula_params(self):
        """Update result formula when parameters change"""
        if self.formula_type == 'advanced':
            self.result_formula = self.advanced_formula or '1'
        elif self.formula_type == 'fixed':
            self.result_formula = str(self.fixed_quantity)
        else:
            self._build_formula()

    def _build_formula(self):
        """Build formula string from builder fields"""
        if self.formula_type == 'fixed':
            self.result_formula = str(self.fixed_quantity)

        elif self.formula_type == 'field':
            if not self.quantity_field_id:
                self.result_formula = ""
                return
            # Make sure we have a valid technical name
            if not self.quantity_field_id.technical_name:
                self.result_formula = "1"  # Default to 1 if no technical name
                return
            self.result_formula = self.quantity_field_id.technical_name

        elif self.formula_type == 'calculation':
            if self.calculation_type == 'area':
                if not self.width_field_id or not self.height_field_id:
                    self.result_formula = "1"  # Default to 1 if fields not selected
                    return

                # Make sure we have valid technical names
                if not self.width_field_id.technical_name or not self.height_field_id.technical_name:
                    self.result_formula = "1"  # Default to 1 if no technical names
                    return

                width = self.width_field_id.technical_name
                height = self.height_field_id.technical_name
                if self.unit_conversion != 1.0:
                    self.result_formula = f"({width} * {height}) / {self.unit_conversion}"
                else:
                    self.result_formula = f"{width} * {height}"

            elif self.calculation_type == 'perimeter':
                if not self.width_field_id or not self.height_field_id:
                    self.result_formula = "1"  # Default to 1 if fields not selected
                    return

                # Make sure we have valid technical names
                if not self.width_field_id.technical_name or not self.height_field_id.technical_name:
                    self.result_formula = "1"  # Default to 1 if no technical names
                    return

                width = self.width_field_id.technical_name
                height = self.height_field_id.technical_name
                if self.unit_conversion != 1.0:
                    self.result_formula = f"(2 * {width} + 2 * {height}) / {self.unit_conversion}"
                else:
                    self.result_formula = f"2 * {width} + 2 * {height}"

            elif self.calculation_type == 'length':
                if not self.length_field_id:
                    self.result_formula = "1"  # Default to 1 if field not selected
                    return

                # Make sure we have a valid technical name
                if not self.length_field_id.technical_name:
                    self.result_formula = "1"  # Default to 1 if no technical name
                    return

                length = self.length_field_id.technical_name
                if self.unit_conversion != 1.0:
                    self.result_formula = f"{length} / {self.unit_conversion}"
                else:
                    self.result_formula = length

            elif self.calculation_type == 'custom':
                if not self.custom_calculation:
                    self.result_formula = ""
                    return
                self.result_formula = self.custom_calculation

        elif self.formula_type == 'conditional':
            if not self.condition_field_id:
                self.result_formula = str(self.true_quantity)  # Default to true quantity if no field selected
                return

            # Make sure we have a valid technical name
            if not self.condition_field_id.technical_name:
                self.result_formula = str(self.true_quantity)  # Default to true quantity if no technical name
                return

            # If no condition value is provided, default to a simple check
            if not self.condition_value and self.condition_operator not in ('is_set', 'is_not_set'):
                self.result_formula = str(self.true_quantity)
                return

            field = self.condition_field_id.technical_name
            operator = self.condition_operator

            # Special operators that don't need a value
            if operator == 'is_set':
                self.result_formula = f"{self.true_quantity} if {field} else {self.false_quantity}"
                return
            elif operator == 'is_not_set':
                self.result_formula = f"{self.true_quantity} if not {field} else {self.false_quantity}"
                return

            # Format the value based on the field type
            if self.condition_field_id.field_type == 'text' or self.condition_field_id.field_type == 'selection':
                if operator in ('in', 'not in'):
                    # Convert comma-separated list to Python list
                    values = [v.strip() for v in self.condition_value.split(',')]
                    quoted_values = [f"'{v}'" for v in values]
                    value = f"[{', '.join(quoted_values)}]"
                else:
                    value = f"'{self.condition_value}'"
            elif self.condition_field_id.field_type == 'boolean':
                value = 'True' if self.condition_value.lower() in ('true', 'yes', '1') else 'False'
            else:
                value = self.condition_value

            self.result_formula = f"{self.true_quantity} if {field} {operator} {value} else {self.false_quantity}"

    def _try_parse_formula(self):
        """Try to parse existing formula to set builder fields"""
        target_obj = self.option_id if self.option_id else self.field_id
        formula = target_obj.quantity_formula
        if not formula:
            return

        # Try to identify formula type
        try:
            # Check if it's a fixed quantity
            fixed_qty = float(formula)
            self.formula_type = 'fixed'
            self.fixed_quantity = fixed_qty
            return
        except ValueError:
            pass

        # Check if it's a field reference
        matrix_id = False
        if self.field_id and self.field_id.matrix_id:
            matrix_id = self.field_id.matrix_id.id
        elif self.option_id and self.option_id.matrix_id:
            matrix_id = self.option_id.matrix_id.id

        if not matrix_id:
            return

        field = self.env['config.matrix.field'].search([
            ('matrix_id', '=', matrix_id),
            ('technical_name', '=', formula),
            ('field_type', '=', 'number')
        ], limit=1)

        if field:
            self.formula_type = 'field'
            self.quantity_field_id = field.id
            return

        # Check if it's a conditional formula
        if ' if ' in formula and ' else ' in formula:
            self.formula_type = 'conditional'
            # This is a simplified parser and won't handle all cases
            try:
                true_part, condition_part = formula.split(' if ', 1)
                condition_part, false_part = condition_part.split(' else ', 1)

                # Parse true and false quantities
                try:
                    self.true_quantity = float(true_part.strip())
                    self.false_quantity = float(false_part.strip())
                except ValueError:
                    # If we can't parse as numbers, default to advanced
                    self.formula_type = 'advanced'
                    return

                # Parse condition
                import re
                condition_match = re.match(r'([a-z][a-z0-9_]*)\s*(==|!=|>|<|>=|<=|in|not in)\s*(.+)', condition_part.strip())
                if condition_match:
                    field_name, operator, value = condition_match.groups()

                    # Find the condition field
                    matrix_id = False
                    if self.field_id and self.field_id.matrix_id:
                        matrix_id = self.field_id.matrix_id.id
                    elif self.option_id and self.option_id.matrix_id:
                        matrix_id = self.option_id.matrix_id.id

                    if not matrix_id:
                        self.formula_type = 'advanced'
                        return

                    condition_field = self.env['config.matrix.field'].search([
                        ('matrix_id', '=', matrix_id),
                        ('technical_name', '=', field_name)
                    ], limit=1)

                    if condition_field:
                        self.condition_field_id = condition_field.id
                        self.condition_operator = operator

                        # Clean up the value
                        if value.startswith("'") and value.endswith("'"):
                            self.condition_value = value[1:-1]  # Remove quotes
                        elif value.startswith('[') and value.endswith(']'):
                            # Extract items from list
                            items = re.findall(r"'([^']*)'", value)
                            self.condition_value = ', '.join(items)
                        else:
                            self.condition_value = value
                    else:
                        self.formula_type = 'advanced'
                else:
                    self.formula_type = 'advanced'
            except Exception:
                self.formula_type = 'advanced'
            return

        # Check for common calculations
        if '*' in formula and '/' in formula:
            # Might be an area calculation with unit conversion
            self.formula_type = 'calculation'
            self.calculation_type = 'custom'
            self.custom_calculation = formula
            return

        if '*' in formula:
            # Might be a simple area calculation
            parts = formula.split('*')
            if len(parts) == 2:
                width_name = parts[0].strip()
                height_name = parts[1].strip()

                matrix_id = False
                if self.field_id and self.field_id.matrix_id:
                    matrix_id = self.field_id.matrix_id.id
                elif self.option_id and self.option_id.matrix_id:
                    matrix_id = self.option_id.matrix_id.id

                if not matrix_id:
                    self.formula_type = 'advanced'
                    return

                width_field = self.env['config.matrix.field'].search([
                    ('matrix_id', '=', matrix_id),
                    ('technical_name', '=', width_name),
                    ('field_type', '=', 'number')
                ], limit=1)

                height_field = self.env['config.matrix.field'].search([
                    ('matrix_id', '=', matrix_id),
                    ('technical_name', '=', height_name),
                    ('field_type', '=', 'number')
                ], limit=1)

                if width_field and height_field:
                    self.formula_type = 'calculation'
                    self.calculation_type = 'area'
                    self.width_field_id = width_field.id
                    self.height_field_id = height_field.id
                    return

        # Default to advanced for anything else
        self.formula_type = 'advanced'

    def action_apply_formula(self):
        """Apply the built formula to the field or option"""
        self.ensure_one()

        # Force build the formula based on current values
        if self.formula_type == 'fixed':
            self.result_formula = str(self.fixed_quantity)
        elif self.formula_type == 'field' and self.quantity_field_id:
            if self.quantity_field_id.technical_name:
                self.result_formula = self.quantity_field_id.technical_name
            else:
                self.result_formula = "1"  # Default to 1 if no technical name
        elif self.formula_type == 'advanced' and self.advanced_formula:
            self.result_formula = self.advanced_formula
        else:
            # For other types, use the build_formula method
            self._build_formula()

        # If we still don't have a formula, use a default
        if not self.result_formula:
            self.result_formula = "1"  # Default to 1 if no formula could be built

        # Validate the formula
        try:
            compile(self.result_formula, '<string>', 'eval')
        except Exception:
            # If compilation fails, use a safe default
            self.result_formula = "1"  # Default to 1 if formula is invalid

        # Check if we're updating a multiple component mapping
        multiple_mapping_id = self.env.context.get('multiple_component_mapping_id')
        mapping_model = self.env.context.get('mapping_model', 'config.matrix.field.component.mapping')

        if multiple_mapping_id:
            # Update the multiple component mapping
            mapping = self.env[mapping_model].browse(multiple_mapping_id)
            if mapping.exists():
                mapping.write({
                    'quantity_formula': self.result_formula,
                    'component_product_id': self.component_product_id.id if self.component_product_id else mapping.component_product_id.id,
                })
                return {'type': 'ir.actions.act_window_close'}

        # Determine which object we're working with (legacy behavior)
        if self.option_id:
            # Apply the formula to the option
            self.option_id.write({
                'quantity_formula': self.result_formula
            })
        elif self.field_id:
            # Apply the formula to the field
            self.field_id.write({
                'quantity_formula': self.result_formula
            })
        else:
            raise UserError(_("No field or option selected."))

        return {'type': 'ir.actions.act_window_close'}
