<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Simple test template -->
        <template id="simple_configurator_template" name="canbrax_configmatrix.simple_configurator_template">
            <html>
                <head>
                    <title>Simple Test Configurator</title>
                </head>
                <body>
                    <h1>Test Configurator Template</h1>
                    <p>If you see this, the template is working!</p>
                    <div t-if="error">
                        <div class="alert alert-danger">
                            <h4>Error:</h4>
                            <p t-esc="error"/>
                        </div>
                    </div>
                    <div t-if="template">
                        <h2>Template: <span t-esc="template.name"/></h2>
                    </div>
                </body>
            </html>
        </template>
    </data>
</odoo>
