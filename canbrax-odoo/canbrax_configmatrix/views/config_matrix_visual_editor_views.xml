<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Visual Matrix Editor Form View -->
    <record id="view_config_matrix_visual_editor_form" model="ir.ui.view">
        <field name="name">config.matrix.visual.editor.form</field>
        <field name="model">config.matrix.visual.editor</field>
        <field name="arch" type="xml">
            <form string="Visual Matrix Editor">

                <!-- Excel-like Matrix Editor Container -->
                <div class="excel-matrix-editor">

                    <!-- Toolbar -->
                    <div class="matrix-toolbar">
                        <div class="toolbar-left">
                            <button name="action_save_matrix"
                                    type="object"
                                    string="Save Matrix"
                                    class="btn btn-primary"
                                    icon="fa-save"/>
                            <button name="action_test_lookup"
                                    type="object"
                                    string="Test Lookup"
                                    class="btn btn-secondary"
                                    icon="fa-search"/>
                            <button name="action_fill_sample_data"
                                    type="object"
                                    string="Fill Sample"
                                    class="btn btn-info"
                                    icon="fa-magic"/>
                            <button name="action_clear_matrix"
                                    type="object"
                                    string="Clear All"
                                    class="btn btn-warning"
                                    icon="fa-trash"/>
                        </div>
                        <div class="toolbar-right">
                            <div class="matrix-stats">
                                <div class="stat-item">
                                    <i class="fa fa-table text-primary"/>
                                    <field name="total_cells" readonly="1"/> cells
                                </div>
                                <div class="stat-item">
                                    <i class="fa fa-check-circle text-success"/>
                                    <field name="filled_cells" readonly="1"/> filled
                                </div>
                                <div class="stat-item">
                                    <i class="fa fa-pie-chart text-info"/>
                                    <field name="completion_rate" readonly="1"/>% complete
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Configuration Panel -->
                    <div class="matrix-config-panel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="config-field">
                                    <label for="matrix_name">Matrix Name</label>
                                    <field name="matrix_name" placeholder="Enter matrix name" id="matrix_name"/>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="config-field">
                                    <label for="currency_name">Currency/Unit</label>
                                    <field name="currency_name" placeholder="AUD / hours" id="currency_name"/>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="config-field">
                                    <label for="heights_text">Heights (comma-separated)</label>
                                    <field name="heights_text"
                                           placeholder="1000,1200,1500,1800,2000"
                                           widget="text"
                                           id="heights_text"/>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="config-field">
                                    <label for="widths_text">Widths (comma-separated)</label>
                                    <field name="widths_text"
                                           placeholder="600,800,1000,1200,1500"
                                           widget="text"
                                           id="widths_text"/>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Excel-like Matrix Grid -->
                    <div class="matrix-grid-section">
                        <h5>Matrix Data Editor</h5>
                        <div class="alert alert-info">
                            <p><strong>Excel-like Interface</strong></p>
                            <p>Edit matrix data in JSON format below, or use the enhanced interface for bulk editing operations.</p>
                        </div>

                        <!-- JSON Editor for Matrix Data -->
                        <div class="matrix-json-editor">
                            <label for="matrix_data_json">Matrix Data (JSON Format)</label>
                            <field name="matrix_data_json"
                                   widget="code"
                                   options="{'mode': 'javascript'}"
                                   placeholder='{"1200_800": 245.50, "1500_1000": 298.75}'
                                   id="matrix_data_json"/>
                        </div>

                        <!-- Interactive Excel-like Matrix Editor -->
                        <div class="matrix-visual-display" style="margin-top: 20px;">
                            <h6>Interactive Matrix Editor</h6>
                            <div class="alert alert-info">
                                <i class="fa fa-lightbulb-o"/>
                                <strong>Excel-like Interface:</strong> Edit your matrix data directly in the grid below.
                                Changes are automatically saved to the JSON field above.
                            </div>

                            <!-- Interactive Matrix Widget -->
                            <field name="matrix_data_json" widget="interactive_matrix" nolabel="1"/>

                        </div>
                    </div>

                    <!-- Hidden Fields -->
                    <field name="matrix_id" invisible="1"/>
                    <field name="labor_matrix_id" invisible="1"/>
                    <field name="matrix_type" invisible="1"/>

                </div>

                <!-- Footer Buttons -->
                <footer>
                    <button name="action_save_matrix"
                            type="object"
                            string="Save &amp; Close"
                            class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>

            </form>
        </field>
    </record>

    <!-- Visual Matrix Editor Action -->
    <record id="action_config_matrix_visual_editor" model="ir.actions.act_window">
        <field name="name">Visual Matrix Editor</field>
        <field name="res_model">config.matrix.visual.editor</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{
            'default_matrix_type': 'price'
        }</field>
    </record>

</odoo>
