<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Operation Template Form View -->
    <record id="view_config_matrix_operation_template_form" model="ir.ui.view">
        <field name="name">config.matrix.operation.template.form</field>
        <field name="model">config.matrix.operation.template</field>
        <field name="arch" type="xml">
            <form string="Operation Template">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_usage" type="object" class="oe_stat_button" icon="fa-list">
                            <field name="usage_count" widget="statinfo" string="Used"/>
                        </button>
                    </div>

                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Operation Name"/>
                        </h1>
                    </div>

                    <group>
                        <group>
                            <field name="active"/>
                            <field name="sequence"/>
                            <field name="category"/>
                        </group>
                        <group>
                            <field name="workcenter_id" required="1"/>
                            <field name="operation_price_id" string="Fixed Price Entry"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Duration" name="duration">
                            <group>
                                <group string="Default Duration">
                                    <field name="default_duration" string="Duration (minutes)"/>
                                    <field name="duration_formula" placeholder="e.g., door_width * 0.5 + 30"/>
                                </group>
                                <group string="Additional Times">
                                    <field name="setup_time"/>
                                    <field name="cleanup_time"/>
                                </group>
                            </group>
                            
                            <div class="alert alert-info mt-3" role="alert">
                                <h6><i class="fa fa-info-circle me-2"></i>Duration Formula Help</h6>
                                <p class="mb-1"><strong>Leave empty</strong> to use the default duration.</p>
                                <p class="mb-1"><strong>Use field names</strong> from your configuration (e.g., door_width, door_height).</p>
                                <p class="mb-0"><strong>Available functions:</strong> round(), ceil(), floor(), abs(), max(), min(), sum()</p>
                            </div>
                        </page>

                        <page string="Worksheet" name="worksheet">
                            <group>
                                <field name="worksheet_type"/>
                                <field name="worksheet_content" widget="text"/>
                            </group>
                        </page>

                        <page string="Details" name="details">
                            <group>
                                <field name="description" widget="text"/>
                                <field name="notes" widget="text"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Operation Template Tree View -->
    <record id="view_config_matrix_operation_template_tree" model="ir.ui.view">
        <field name="name">config.matrix.operation.template.tree</field>
        <field name="model">config.matrix.operation.template</field>
        <field name="arch" type="xml">
            <list string="Operation Templates" default_order="sequence, name">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="workcenter_id"/>
                <field name="category"/>
                <field name="default_duration" string="Duration (min)"/>
                <field name="usage_count" string="Used"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <!-- Operation Template Search View -->
    <record id="view_config_matrix_operation_template_search" model="ir.ui.view">
        <field name="name">config.matrix.operation.template.search</field>
        <field name="model">config.matrix.operation.template</field>
        <field name="arch" type="xml">
            <search string="Search Operation Templates">
                <field name="name" string="Name"/>
                <field name="workcenter_id" string="Work Center"/>
                <field name="category" string="Category"/>
                
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                
                <separator/>
                <filter string="Cutting" name="cutting" domain="[('category', '=', 'cutting')]"/>
                <filter string="Assembly" name="assembly" domain="[('category', '=', 'assembly')]"/>
                <filter string="Finishing" name="finishing" domain="[('category', '=', 'finishing')]"/>
                <filter string="Quality Control" name="quality" domain="[('category', '=', 'quality')]"/>
                <filter string="Packaging" name="packaging" domain="[('category', '=', 'packaging')]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Work Center" name="group_workcenter" context="{'group_by': 'workcenter_id'}"/>
                    <filter string="Category" name="group_category" context="{'group_by': 'category'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Operation Template Kanban View -->
    <record id="view_config_matrix_operation_template_kanban" model="ir.ui.view">
        <field name="name">config.matrix.operation.template.kanban</field>
        <field name="model">config.matrix.operation.template</field>
        <field name="arch" type="xml">
            <kanban default_group_by="category" class="o_kanban_small_column">
                <field name="name"/>
                <field name="workcenter_id"/>
                <field name="category"/>
                <field name="default_duration"/>
                <field name="usage_count"/>
                <field name="active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <field name="name"/>
                                    </strong>
                                    <div class="o_kanban_record_subtitle">
                                        <field name="workcenter_id"/>
                                    </div>
                                </div>
                                <div class="o_kanban_manage_button_section">
                                    <a class="o_kanban_manage_toggle_button" href="#">
                                        <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                    </a>
                                </div>
                            </div>
                            <div class="o_kanban_record_body">
                                <div class="row">
                                    <div class="col-6">
                                        <span class="badge badge-pill badge-info">
                                            <t t-esc="record.default_duration.value"/> min
                                        </span>
                                    </div>
                                    <div class="col-6 text-right">
                                        <span class="badge badge-pill badge-secondary" t-if="record.usage_count.value > 0">
                                            Used <t t-esc="record.usage_count.value"/> times
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_manage_button_section o_kanban_manage_view">
                                <a type="edit" class="btn btn-primary btn-sm">Edit</a>
                                <a name="action_view_usage" type="object" class="btn btn-secondary btn-sm" t-if="record.usage_count.value > 0">
                                    View Usage
                                </a>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Operation Template Action -->
    <record id="action_config_matrix_operation_template" model="ir.actions.act_window">
        <field name="name">Operation Templates</field>
        <field name="res_model">config.matrix.operation.template</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_config_matrix_operation_template_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first operation template!
            </p>
            <p>
                Operation templates define reusable manufacturing operations that can be applied to different products.
                Each template includes a work center, duration settings, and optional worksheets.
            </p>
        </field>
    </record>
</odoo>
