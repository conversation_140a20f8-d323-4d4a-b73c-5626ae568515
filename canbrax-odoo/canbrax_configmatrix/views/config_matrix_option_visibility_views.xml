<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Option Visibility Condition Tree View -->
    <record id="view_config_matrix_option_visibility_tree" model="ir.ui.view">
        <field name="name">config.matrix.option.visibility.tree</field>
        <field name="model">config.matrix.option.visibility</field>
        <field name="arch" type="xml">
            <list string="Option Visibility Conditions">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="option_id"/>
                <field name="matrix_id"/>
                <field name="condition_type"/>
                <field name="dependent_field_id"/>
                <field name="operator"/>
                <field name="generated_condition"/>
            </list>
        </field>
    </record>

    <!-- Option Visibility Condition Form View -->
    <record id="view_config_matrix_option_visibility_form" model="ir.ui.view">
        <field name="name">config.matrix.option.visibility.form</field>
        <field name="model">config.matrix.option.visibility</field>
        <field name="arch" type="xml">
            <form string="Option Visibility Condition">
                <sheet>
                    <div class="oe_title">
                        <h1><field name="name" readonly="1"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="option_id"/>
                            <field name="matrix_id"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="condition_type"/>
                            <field name="logic_operator"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Field Condition" invisible="condition_type != 'field'">
                            <group>
                                <group>
                                    <field name="dependent_field_id" required="condition_type == 'field'"/>
                                    <field name="operator" required="condition_type == 'field'"/>
                                </group>
                                <group>
                                    <field name="value_type" invisible="1"/>
                                    <field name="text_value" invisible="value_type != 'text'" required="value_type == 'text' and operator not in ['is_set', 'is_not_set'] and condition_type != 'expression'"/>
                                    <field name="number_value" invisible="value_type != 'number'" required="value_type == 'number' and operator not in ['is_set', 'is_not_set'] and condition_type != 'expression'"/>
                                    <field name="boolean_value" invisible="value_type != 'boolean'" required="value_type == 'boolean' and operator not in ['is_set', 'is_not_set'] and condition_type != 'expression'"/>
                                    <field name="selection_value" invisible="value_type != 'selection'" required="value_type == 'selection' and operator not in ['is_set', 'is_not_set', 'in', 'not in'] and condition_type != 'expression'"/>
                                    <field name="list_value" invisible="value_type != 'selection' or operator not in ['in', 'not in']" required="value_type == 'selection' and operator in ['in', 'not in'] and condition_type != 'expression'"/>
                                    <field name="selected_option_ids" invisible="value_type != 'selection' or operator not in ['in', 'not in']" required="value_type == 'selection' and operator in ['in', 'not in'] and condition_type != 'expression'" widget="many2many_tags"/>
                                </group>
                            </group>
                        </page>
                        <page string="Custom Expression" invisible="condition_type != 'expression'">
                            <group>
                                <div class="alert alert-info" role="alert">
                                    <strong>Manual Expression Builder</strong><br/>
                                    Enter your custom expression directly in the field below. Use field technical names and JavaScript syntax.
                                </div>



                                <!-- Technical Expression Field -->
                                <group>
                                    <field name="expression" required="condition_type == 'expression'"
                                           placeholder="Technical expression will be generated automatically"
                                           help="This field contains the technical expression with full field names. You can edit it manually if needed."/>
                                </group>
                            </group>
                        </page>
                        <page string="Generated Condition">
                            <group>
                                <field name="generated_condition" readonly="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Option Visibility Condition Search View -->
    <record id="view_config_matrix_option_visibility_search" model="ir.ui.view">
        <field name="name">config.matrix.option.visibility.search</field>
        <field name="model">config.matrix.option.visibility</field>
        <field name="arch" type="xml">
            <search string="Search Option Visibility Conditions">
                <field name="name"/>
                <field name="option_id"/>
                <field name="matrix_id"/>
                <field name="dependent_field_id"/>
                <separator/>
                <filter string="Field Conditions" name="field_conditions" domain="[('condition_type', '=', 'field')]"/>
                <filter string="Custom Expressions" name="custom_expressions" domain="[('condition_type', '=', 'expression')]"/>
                <group expand="0" string="Group By">
                    <filter string="Template" name="group_by_template" context="{'group_by': 'matrix_id'}"/>
                    <filter string="Option" name="group_by_option" context="{'group_by': 'option_id'}"/>
                    <filter string="Dependent Field" name="group_by_dependent_field" context="{'group_by': 'dependent_field_id'}"/>
                    <filter string="Condition Type" name="group_by_condition_type" context="{'group_by': 'condition_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Option Visibility Condition Action -->
    <record id="action_config_matrix_option_visibility" model="ir.actions.act_window">
        <field name="name">Option Visibility Conditions</field>
        <field name="res_model">config.matrix.option.visibility</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'group_by': ['matrix_id', 'option_id']}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No option visibility conditions found
            </p>
            <p>
                Visibility conditions determine when options are shown or hidden in the configurator.
            </p>
        </field>
    </record>
</odoo>
