<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Section Tree View -->
    <record id="view_config_matrix_section_tree" model="ir.ui.view">
        <field name="name">config.matrix.section.tree</field>
        <field name="model">config.matrix.section</field>
        <field name="arch" type="xml">
            <list string="Sections">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="matrix_id"/>
                <field name="description"/>
                <field name="field_count"/>
                <button name="action_view_fields" type="object" string="View Fields" class="btn-sm btn-secondary"/>
                <button name="action_add_field" type="object" string="Add Field" class="btn-sm btn-primary"/>
            </list>
        </field>
    </record>

    <!-- Section Form View -->
    <record id="view_config_matrix_section_form" model="ir.ui.view">
        <field name="name">config.matrix.section.form</field>
        <field name="model">config.matrix.section</field>
        <field name="arch" type="xml">
            <form string="Configuration Section">
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name" placeholder="Section Name"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="matrix_id" options="{'no_create': True}"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="collapsible"/>
                            <field name="collapsed_by_default" invisible="[('collapsible', '=', False)]"/>
                            <field name="show_header"/>
                            <field name="template_state" invisible="1"/>
                        </group>
                    </group>
                    <group>
                        <field name="description" placeholder="Section description..."/>
                    </group>
                    <notebook>
                        <page string="Fields" name="fields">
                            <field name="field_ids" context="{'default_section_id': id}">
                                <list string="Fields">
                                    <field name="sequence" widget="handle"/>
                                    <field name="question_number" string="Question #"/>
                                    <field name="name"/>
                                    <field name="technical_name"/>
                                    <field name="field_type"/>
                                    <field name="visibility_condition"/>
                                    <button name="action_test_visibility_condition" type="object"
                                            string="Test Condition" class="btn-sm btn-secondary"
                                            invisible="not visibility_condition"/>
                                </list>
                            </field>
                            <button name="action_resequence_fields" type="object"
                                    string="Resequence Fields" class="btn-secondary mt-2"/>
                            <button name="action_add_field" type="object"
                                    string="Add Field" class="btn-primary mt-2"/>
                        </page>
                        <page string="Use Cases" name="use_cases">
                            <div class="alert alert-info" role="alert">
                                <p>Control which use cases this section appears in. If a section is hidden in a use case,
                                all its fields will be hidden as well, regardless of their individual visibility settings.</p>
                            </div>

                            <!-- Table-like header -->
                            <div class="row mb-2 mt-3">
                                <div class="col-3">
                                    <span class="o_form_label fw-bold">Use Case</span>
                                </div>
                                <div class="col-3">
                                    <span class="o_form_label fw-bold">Visible</span>
                                </div>
                                <div class="col-6">
                                    <span class="o_form_label fw-bold">Description</span>
                                </div>
                            </div>

                            <!-- Check Measure -->
                            <div class="row mb-2 pb-2 border-bottom">
                                <div class="col-3">
                                    <span class="o_form_label">Check Measure</span>
                                    <div class="text-muted small">(Full Configuration)</div>
                                </div>
                                <div class="col-3">
                                    <field name="check_measure_visible" nolabel="1"/>
                                </div>
                                <div class="col-6">
                                    <p class="text-muted mb-0">Complete configuration with all available options and detailed measurements.</p>
                                </div>
                            </div>

                            <!-- Sales/Quoting -->
                            <div class="row mb-2 pb-2 border-bottom">
                                <div class="col-3">
                                    <span class="o_form_label">Sales/Quoting</span>
                                    <div class="text-muted small">(Simplified)</div>
                                </div>
                                <div class="col-3">
                                    <field name="sales_visible" nolabel="1"/>
                                </div>
                                <div class="col-6">
                                    <p class="text-muted mb-0">Simplified configuration for sales representatives creating quotes.</p>
                                </div>
                            </div>

                            <!-- Online Sales -->
                            <div class="row mb-2 pb-2 border-bottom">
                                <div class="col-3">
                                    <span class="o_form_label">Online Sales</span>
                                    <div class="text-muted small">(Limited)</div>
                                </div>
                                <div class="col-3">
                                    <field name="online_visible" nolabel="1"/>
                                </div>
                                <div class="col-6">
                                    <p class="text-muted mb-0">Limited configuration options for online customers.</p>
                                </div>
                            </div>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Section Search View -->
    <record id="view_config_matrix_section_search" model="ir.ui.view">
        <field name="name">config.matrix.section.search</field>
        <field name="model">config.matrix.section</field>
        <field name="arch" type="xml">
            <search string="Search Sections">
                <field name="name"/>
                <field name="matrix_id"/>
                <group expand="0" string="Group By">
                    <filter string="Template" name="group_by_template" context="{'group_by': 'matrix_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Section Action -->
    <record id="action_config_matrix_section" model="ir.actions.act_window">
        <field name="name">Configuration Sections</field>
        <field name="res_model">config.matrix.section</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first configuration section!
            </p>
            <p>
                Sections group related fields together in the configuration template.
            </p>
        </field>
    </record>

    <!-- Menu Item - Removed as per request -->
</odoo>
