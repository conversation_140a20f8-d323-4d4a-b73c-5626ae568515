<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Field Component Mapping Views -->
    <record id="view_config_matrix_field_component_mapping_form" model="ir.ui.view">
        <field name="name">config.matrix.field.component.mapping.form</field>
        <field name="model">config.matrix.field.component.mapping</field>
        <field name="arch" type="xml">
            <form string="Field Component Mapping">
                <header>
                    <button name="action_test_dynamic_match" type="object" string="Test Dynamic Match"
                            class="btn-primary" invisible="mapping_type != 'dynamic_match'"
                            help="Test the dynamic field matching with sample data"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="o_form_label">Mapping Name</label>
                        <h1>
                            <field name="name" placeholder="e.g., Bug Strip Color Match"/>
                        </h1>
                    </div>

                    <group>
                        <group string="Basic Information">
                            <field name="field_id" readonly="1"/>
                            <field name="mapping_type" widget="radio"/>
                            <field name="sequence"/>
                        </group>
                        <group string="Configuration">
                            <!-- Static Product Configuration -->
                            <field name="component_product_id" invisible="mapping_type != 'static'"
                                   required="mapping_type == 'static'"
                                   help="Select the specific product to use"/>

                            <!-- Dynamic Match Configuration -->
                            <field name="base_component" invisible="mapping_type != 'dynamic_match'"
                                   required="mapping_type == 'dynamic_match'"
                                   placeholder="e.g., Bug Strip 16mm"
                                   help="Base component name to search for in product names"/>

                            <field name="reference_field_id" invisible="mapping_type != 'dynamic_match'"
                                   help="Select the field whose value should be matched"/>

                            <field name="match_product_field" invisible="mapping_type != 'dynamic_match'"
                                   help="Which product field should be matched against the reference field value. 'Reference Chain Match' will find the product with the reference field value as colour, get its internal reference, then find components with the same internal reference."/>

                            <!-- Legacy field for backward compatibility -->
                            <field name="reference_field" invisible="1"/>
                        </group>
                    </group>

                    <notebook invisible="mapping_type != 'dynamic_match'">
                        <page string="Preview &amp; Validation" name="preview">
                            <group>
                                <group string="Available Base Components">
                                    <field name="available_base_components" widget="text" readonly="1" nolabel="1"
                                           help="Potential base component names found in your products"/>
                                </group>
                                <group string="Matching Preview">
                                    <field name="preview_matching_products" widget="text" readonly="1" nolabel="1"
                                           help="Preview of products that would match this configuration"/>
                                </group>
                            </group>
                        </page>
                        <page string="Advanced" name="advanced">
                            <group>
                                <group>
                                    <field name="quantity_formula" widget="code" options="{'mode': 'python'}"
                                           help="Python expression to calculate component quantity"/>
                                    <div>
                                        <button name="action_open_component_builder" type="object"
                                                string="Add Quantity" class="btn-primary"
                                                icon="fa-magic"/>
                                    </div>
                                    <field name="condition" widget="code" options="{'mode': 'python'}"
                                           help="Additional condition for this component"/>
                                </group>
                                <group>
                                    <field name="notes" placeholder="Additional notes about this mapping..."
                                           help="Internal notes about this mapping"/>
                                </group>
                            </group>
                        </page>
                    </notebook>

                    <!-- Show advanced fields for static mappings -->
                    <group invisible="mapping_type != 'static'">
                        <group string="Quantity &amp; Conditions">
                            <field name="quantity_formula" widget="code" options="{'mode': 'python'}"/>
                            <div>
                                <button name="action_open_component_builder" type="object"
                                        string="Add Quantity" class="btn-primary"
                                        icon="fa-magic"/>
                            </div>
                            <field name="condition" widget="code" options="{'mode': 'python'}"/>
                        </group>
                        <group string="Notes">
                            <field name="notes" placeholder="Additional notes about this mapping..."/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_config_matrix_field_component_mapping_list" model="ir.ui.view">
        <field name="name">config.matrix.field.component.mapping.list</field>
        <field name="model">config.matrix.field.component.mapping</field>
        <field name="arch" type="xml">
            <list string="Field Component Mappings" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="name" string="Mapping Name"/>
                <field name="mapping_type" string="Type"/>
                <field name="component_product_id" string="Static Product"
                       readonly="mapping_type != 'static'"/>
                <field name="base_component" string="Base Component"
                       readonly="mapping_type != 'dynamic_match'"/>
                <field name="reference_field_id" string="Reference Field"
                       readonly="mapping_type != 'dynamic_match'"/>
                <field name="match_product_field" string="Match Against"
                       readonly="mapping_type != 'dynamic_match'"/>
                <field name="quantity_formula" string="Quantity"/>
                <field name="condition" string="Condition"/>
                <!-- Hidden fields for functionality -->
                <field name="field_id" column_invisible="1"/>
                <field name="reference_field" column_invisible="1"/>
                <!-- Action buttons -->
                <button name="action_open_component_builder" type="object"
                        string="Add Quantity" icon="fa-magic"
                        class="btn-link text-primary" title="Build quantity formula"/>
            </list>
        </field>
    </record>

    <!-- Option Component Mapping Extended Views -->
    <record id="view_config_matrix_option_component_mapping_extended_form" model="ir.ui.view">
        <field name="name">config.matrix.option.component.mapping.extended.form</field>
        <field name="model">config.matrix.option.component.mapping.extended</field>
        <field name="arch" type="xml">
            <form string="Option Component Mapping">
                <header>
                    <button name="action_test_dynamic_match" type="object" string="Test Dynamic Match"
                            class="btn-primary" invisible="mapping_type != 'dynamic_match'"
                            help="Test the dynamic field matching with sample data"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="o_form_label">Mapping Name</label>
                        <h1>
                            <field name="name" placeholder="e.g., Jamb Adaptor Color Match"/>
                        </h1>
                    </div>

                    <group>
                        <group string="Basic Information">
                            <field name="option_id" readonly="1"/>
                            <field name="mapping_type" widget="radio"/>
                            <field name="sequence"/>
                        </group>
                        <group string="Configuration">
                            <!-- Static Product Configuration -->
                            <field name="component_product_id" invisible="mapping_type != 'static'"
                                   required="mapping_type == 'static'"
                                   help="Select the specific product to use"/>

                            <!-- Dynamic Match Configuration -->
                            <field name="base_component" invisible="mapping_type != 'dynamic_match'"
                                   required="mapping_type == 'dynamic_match'"
                                   placeholder="e.g., Jamb Adaptor 25mm"
                                   help="Base component name to search for in product names"/>

                            <field name="reference_field_id" invisible="mapping_type != 'dynamic_match'"
                                   help="Select the field whose value should be matched"/>

                            <field name="match_product_field" invisible="mapping_type != 'dynamic_match'"
                                   help="Which product field should be matched against the reference field value. 'Reference Chain Match' will find the product with the reference field value as colour, get its internal reference, then find components with the same internal reference."/>

                            <!-- Legacy field for backward compatibility -->
                            <field name="reference_field" invisible="1"/>
                        </group>
                    </group>

                    <notebook invisible="mapping_type != 'dynamic_match'">
                        <page string="Preview &amp; Validation" name="preview">
                            <group>
                                <group string="Available Base Components">
                                    <field name="available_base_components" widget="text" readonly="1" nolabel="1"
                                           help="Potential base component names found in your products"/>
                                </group>
                                <group string="Matching Preview">
                                    <field name="preview_matching_products" widget="text" readonly="1" nolabel="1"
                                           help="Preview of products that would match this configuration"/>
                                </group>
                            </group>
                        </page>
                        <page string="Advanced" name="advanced">
                            <group>
                                <group>
                                    <field name="quantity_formula" widget="code" options="{'mode': 'python'}"
                                           help="Python expression to calculate component quantity"/>
                                    <div>
                                        <button name="action_open_component_builder" type="object"
                                                string="Add Quantity" class="btn-primary"
                                                icon="fa-magic"/>
                                    </div>
                                    <field name="condition" widget="code" options="{'mode': 'python'}"
                                           help="Additional condition for this component"/>
                                </group>
                                <group>
                                    <field name="notes" placeholder="Additional notes about this mapping..."
                                           help="Internal notes about this mapping"/>
                                </group>
                            </group>
                        </page>
                    </notebook>

                    <!-- Show advanced fields for static mappings -->
                    <group invisible="mapping_type != 'static'">
                        <group string="Quantity &amp; Conditions">
                            <field name="quantity_formula" widget="code" options="{'mode': 'python'}"/>
                            <div>
                                <button name="action_open_component_builder" type="object"
                                        string="Add Quantity" class="btn-primary"
                                        icon="fa-magic"/>
                            </div>
                            <field name="condition" widget="code" options="{'mode': 'python'}"/>
                        </group>
                        <group string="Notes">
                            <field name="notes" placeholder="Additional notes about this mapping..."/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_config_matrix_option_component_mapping_extended_list" model="ir.ui.view">
        <field name="name">config.matrix.option.component.mapping.extended.list</field>
        <field name="model">config.matrix.option.component.mapping.extended</field>
        <field name="arch" type="xml">
            <list string="Option Component Mappings" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="name" string="Mapping Name"/>
                <field name="mapping_type" string="Type"/>
                <field name="component_product_id" string="Static Product"
                       readonly="mapping_type != 'static'"/>
                <field name="base_component" string="Base Component"
                       readonly="mapping_type != 'dynamic_match'"/>
                <field name="reference_field_id" string="Reference Field"
                       readonly="mapping_type != 'dynamic_match'"/>
                <field name="match_product_field" string="Match Against"
                       readonly="mapping_type != 'dynamic_match'"/>
                <field name="quantity_formula" string="Quantity"/>
                <field name="condition" string="Condition"/>
                <!-- Hidden fields for functionality -->
                <field name="option_id" column_invisible="1"/>
                <field name="reference_field" column_invisible="1"/>
                <!-- Action buttons -->
                <button name="action_open_component_builder" type="object"
                        string="Add Quantity" icon="fa-magic"
                        class="btn-link text-primary" title="Build quantity formula"/>
            </list>
        </field>
    </record>

    <!-- Actions for Component Mappings -->
    <record id="action_config_matrix_field_component_mapping" model="ir.actions.act_window">
        <field name="name">Field Component Mappings</field>
        <field name="res_model">config.matrix.field.component.mapping</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first field component mapping!
            </p>
            <p>
                Field component mappings allow you to map multiple components to a single field.
                Use <strong>Static Product</strong> to map a specific product, or <strong>Dynamic Field Match</strong>
                to automatically select products based on values from other fields (like color matching).
            </p>
            <p>
                <strong>Dynamic Field Match</strong> is perfect for components like Bug Strips and Jamb Adaptors
                that should inherit properties from other components like doors or frames.
            </p>
        </field>
    </record>

    <record id="action_config_matrix_option_component_mapping_extended" model="ir.actions.act_window">
        <field name="name">Option Component Mappings</field>
        <field name="res_model">config.matrix.option.component.mapping.extended</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first option component mapping!
            </p>
            <p>
                Option component mappings allow you to map multiple components to a single option.
                Use <strong>Static Product</strong> to map a specific product, or <strong>Dynamic Field Match</strong>
                to automatically select products based on values from other fields (like color matching).
            </p>
            <p>
                <strong>Dynamic Field Match</strong> is perfect for components like Bug Strips and Jamb Adaptors
                that should inherit properties from other components like doors or frames.
            </p>
        </field>
    </record>
</odoo>
