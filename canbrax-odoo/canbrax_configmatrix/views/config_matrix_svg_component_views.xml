<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_config_matrix_svg_component_form" model="ir.ui.view">
        <field name="name">config.matrix.svg.component.form</field>
        <field name="model">config.matrix.svg.component</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_preview_svg" type="object" class="oe_stat_button" icon="fa-eye">
                            <div class="o_stat_info">
                                <span class="o_stat_text">Preview</span>
                            </div>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1><field name="name" placeholder="Component Name"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="template_id"/>
                            <field name="component_type"/>
                            <field name="z_index"/>
                        </group>
                        <group>
                            <field name="condition" invisible="component_type == 'base'"/>
                            <div class="text-muted" invisible="component_type == 'base'">
                                Example: width > 1000 &amp;&amp; door_type === 'sliding'
                            </div>
                        </group>
                    </group>
                    <notebook>
                        <page string="SVG Content">
                            <field name="svg_content" widget="code" options="{'mode': 'xml'}"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_config_matrix_svg_component_tree" model="ir.ui.view">
        <field name="name">config.matrix.svg.component.tree</field>
        <field name="model">config.matrix.svg.component</field>
        <field name="arch" type="xml">
            <list default_order="z_index">
                <field name="name"/>
                <field name="template_id"/>
                <field name="component_type" decoration-info="component_type == 'base'" decoration-warning="component_type == 'layer'"/>
                <field name="z_index" decoration-danger="z_index &lt; 10" decoration-warning="z_index &gt;= 10 and z_index &lt; 50" decoration-info="z_index &gt;= 50"/>
                <field name="condition"/>
                <button name="action_preview_svg" type="object" icon="fa-eye" title="Preview"/>
            </list>
        </field>
    </record>

    <!-- SVG Component Kanban View for Sequence Visualization -->
    <record id="view_config_matrix_svg_component_kanban" model="ir.ui.view">
        <field name="name">config.matrix.svg.component.kanban</field>
        <field name="model">config.matrix.svg.component</field>
        <field name="arch" type="xml">
            <kanban default_group_by="template_id" class="o_kanban_small_column" default_order="z_index asc">
                <field name="name"/>
                <field name="component_type"/>
                <field name="z_index"/>
                <field name="condition"/>
                <field name="template_id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_card oe_kanban_global_click
                                           #{record.component_type.raw_value == 'base' ? 'bg-light' : ''}
                                           #{record.component_type.raw_value == 'layer' ? 'bg-white' : ''}">
                            <div class="oe_kanban_content">
                                <div class="row">
                                    <div class="col-12">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <div class="float-end">
                                            <span t-attf-class="badge #{record.component_type.raw_value == 'base' ? 'bg-primary' : 'bg-warning'}">
                                                <field name="component_type"/>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <span class="badge bg-info">Z-Index: <field name="z_index"/></span>
                                    </div>
                                    <div class="col-6 text-end">
                                        <button name="action_preview_svg" type="object" class="btn btn-sm btn-link">
                                            <i class="fa fa-eye"/> Preview
                                        </button>
                                    </div>
                                </div>
                                <div class="row mt-2" t-if="record.condition.raw_value">
                                    <div class="col-12">
                                        <small class="text-muted">Condition: <field name="condition"/></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- SVG Preview Form View -->
    <record id="view_config_matrix_svg_component_preview_form" model="ir.ui.view">
        <field name="name">config.matrix.svg.component.preview.form</field>
        <field name="model">config.matrix.svg.component</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div style="text-align: center; margin-bottom: 10px;">
                        <h2><field name="name" readonly="1"/></h2>
                        <div>
                            <field name="component_type" readonly="1" class="oe_inline"/>
                            <span class="mx-2">|</span>
                            <strong>Z-Index:</strong> <field name="z_index" readonly="1" class="oe_inline"/>
                            <span class="mx-2" invisible="not condition">|</span>
                            <strong invisible="not condition">Condition:</strong> <field name="condition" readonly="1" class="oe_inline"/>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-center mb-1">Component Preview</h5>
                            <div class="text-center mb-1"><small>50% Zoom</small></div>
                            <div class="svg_preview_container" style="height: 625px; border: 1px solid #ddd; border-radius: 4px; padding: 5px; margin: 0px 10px; overflow: auto;">
                                <div style="transform: scale(0.5); transform-origin: top left; width: 200%; height: 200%;">
                                    <field name="svg_preview" widget="html" nolabel="1"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-center mb-1">Combined Preview</h5>
                            <div class="text-center mb-1"><small>50% Zoom</small></div>
                            <div class="svg_preview_container" style="height: 625px; border: 1px solid #ddd; border-radius: 4px; padding: 5px; margin: 0px 10px; overflow: auto;">
                                <div style="transform: scale(0.5); transform-origin: top left; width: 200%; height: 200%;">
                                    <field name="combined_preview" widget="html" nolabel="1"/>
                                </div>
                            </div>
                        </div>
                    </div>

                    <footer>
                        <button string="Close" class="btn-secondary" special="cancel"/>
                    </footer>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_config_matrix_svg_component" model="ir.actions.act_window">
        <field name="name">SVG Components</field>
        <field name="res_model">config.matrix.svg.component</field>
        <field name="view_mode">list,form</field>
    </record>

    <!-- SVG Components menu removed as requested - only accessible from Template -->
</odoo>
