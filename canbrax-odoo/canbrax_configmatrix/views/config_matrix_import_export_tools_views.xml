<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Consolidated Import/Export Tools Form View -->
    <record id="view_config_matrix_import_export_tools_form" model="ir.ui.view">
        <field name="name">config.matrix.import.export.tools.form</field>
        <field name="model">config.matrix.import.export.tools</field>
        <field name="arch" type="xml">
            <form string="Matrix Import/Export Tools">
                <sheet>
                    <div class="oe_title">
                        <h1>Matrix Import/Export Tools</h1>
                        <p class="text-muted">Comprehensive tools for importing and exporting matrix data</p>
                    </div>
                    
                    <notebook>
                        <!-- Excel Import Tab -->
                        <page string="Excel Import" name="excel_import">
                            <group>
                                <div class="alert alert-info" role="alert">
                                    <h4><i class="fa fa-file-excel-o"/> Excel Matrix Import</h4>
                                    <p>Import multiple matrices from Excel workbooks. Automatically detects sheets and creates corresponding matrices.</p>
                                </div>
                            </group>
                            
                            <group>
                                <group string="Import Settings">
                                    <field name="excel_import_product_template_id" options="{'no_create': True}"/>
                                    <field name="excel_import_mode" widget="radio"/>
                                </group>
                                <group string="File Upload">
                                    <field name="excel_import_file_data" filename="excel_import_file_name"/>
                                    <field name="excel_import_file_name" invisible="1"/>
                                </group>
                            </group>
                            
                            <group string="Category Settings">
                                <group>
                                    <field name="excel_target_category_id"/>
                                    <field name="excel_auto_create_categories"/>
                                </group>
                                <group string="Processing Options">
                                    <field name="excel_skip_empty_cells"/>
                                    <field name="excel_validate_data"/>
                                    <field name="excel_create_backup"/>
                                </group>
                            </group>
                            
                            <group string="Sheet Analysis" invisible="not excel_sheet_info">
                                <field name="excel_sheet_info" widget="text" nolabel="1"/>
                            </group>
                            
                            <div class="oe_button_box">
                                <button string="Analyze Excel File" name="action_excel_analyze" type="object" class="btn-secondary"/>
                                <button string="Import All Sheets" name="action_excel_import" type="object" class="btn-primary"/>
                            </div>
                        </page>
                        
                        <!-- Excel Export Tab -->
                        <page string="Excel Export" name="excel_export">
                            <group>
                                <div class="alert alert-success" role="alert">
                                    <h4><i class="fa fa-download"/> Excel Matrix Export</h4>
                                    <p>Export matrices to Excel format. Choose from templates, categories, or all matrices with customizable formatting options.</p>
                                </div>
                            </group>
                            
                            <group>
                                <group string="Export Scope">
                                    <field name="excel_export_type" widget="radio"/>
                                    <field name="excel_export_template_id" invisible="excel_export_type != 'template'"/>
                                    <field name="excel_export_category_id" invisible="excel_export_type != 'category'"/>
                                </group>
                                <group string="Format Options">
                                    <field name="excel_include_metadata"/>
                                    <field name="excel_include_empty_cells"/>
                                    <field name="excel_separate_sheets"/>
                                </group>
                            </group>
                            
                            <group string="Recent Downloads" invisible="not recent_exports">
                                <field name="recent_exports" widget="text" nolabel="1" readonly="1"/>
                            </group>
                            
                            <group string="Download" invisible="not excel_export_file_ready">
                                <field name="excel_export_file_data" filename="excel_export_file_name" readonly="1"/>
                                <field name="excel_export_file_name" readonly="1"/>
                                <field name="excel_export_file_ready" invisible="1"/>
                            </group>
                            
                            <div class="oe_button_box">
                                <button string="Generate Excel Export" name="action_excel_export" type="object" class="btn-primary"/>
                            </div>
                        </page>
                        
                        <!-- Bulk Operations Tab -->
                        <page string="Bulk Operations" name="bulk_operations">
                            <group>
                                <div class="alert alert-warning" role="alert">
                                    <h4><i class="fa fa-cogs"/> Bulk Operations</h4>
                                    <p>Perform bulk operations on multiple matrices. Use with caution as these operations affect multiple records.</p>
                                </div>
                            </group>
                            
                            <group string="Bulk Export">
                                <group>
                                    <field name="bulk_export_filter" widget="radio"/>
                                </group>
                                <group>
                                    <field name="bulk_include_inactive"/>
                                </group>
                            </group>
                            
                            <div class="oe_button_box">
                                <button string="Export all matrices" name="action_bulk_export" type="object" class="btn-secondary me-3"/>
                                <button string="Bulk import" name="action_bulk_import" type="object" class="btn-warning"/>
                            </div>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Import/Export Tools Action -->
    <record id="action_config_matrix_import_export_tools" model="ir.actions.act_window">
        <field name="name">Matrix Import/Export Tools</field>
        <field name="res_model">config.matrix.import.export.tools</field>
        <field name="view_mode">form</field>
        <field name="target">current</field>
    </record>
</odoo>
