<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Matrix Category List View -->
    <record id="view_config_matrix_category_list" model="ir.ui.view">
        <field name="name">config.matrix.category.list</field>
        <field name="model">config.matrix.category</field>
        <field name="arch" type="xml">
            <list string="Matrix Categories">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="code"/>
                <field name="category_type"/>
                <field name="matrix_count"/>
                <field name="template_count"/>
                <field name="is_required"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Matrix Category Form View -->
    <record id="view_config_matrix_category_form" model="ir.ui.view">
        <field name="name">config.matrix.category.form</field>
        <field name="model">config.matrix.category</field>
        <field name="arch" type="xml">
            <form string="Matrix Category">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_matrices" 
                                type="object" 
                                class="oe_stat_button" 
                                icon="fa-calculator">
                            <field name="matrix_count" widget="statinfo" string="Matrices"/>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name" placeholder="Category Name"/></h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="category_type"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="active"/>
                            <field name="is_required"/>
                            <field name="allow_multiple"/>
                        </group>
                    </group>
                    
                    <group string="Description">
                        <field name="description" nolabel="1"/>
                    </group>
                    
                    <notebook>
                        <page string="Price Matrices" name="price_matrices">
                            <field name="price_matrix_ids">
                                <list string="Price Matrices">
                                    <field name="name"/>
                                    <field name="product_template_id"/>
                                    <field name="matrix_type"/>
                                    <field name="currency_id"/>
                                    <field name="last_imported"/>
                                    <field name="active"/>
                                </list>
                            </field>
                        </page>
                        
                        <page string="Labor Matrices" name="labor_matrices">
                            <field name="labor_matrix_ids">
                                <list string="Labor Matrices">
                                    <field name="name"/>
                                    <field name="operation_type"/>
                                    <field name="product_template_id"/>
                                    <field name="time_unit"/>
                                    <field name="last_imported"/>
                                    <field name="active"/>
                                </list>
                            </field>
                        </page>
                        
                        <page string="Template Assignments" name="template_assignments">
                            <field name="template_assignment_ids">
                                <list string="Template Assignments">
                                    <field name="template_id"/>
                                    <field name="sequence"/>
                                    <field name="max_matrices"/>
                                    <field name="selected_matrix_count"/>
                                    <field name="active"/>
                                    <button name="action_configure_matrices" 
                                            type="object" 
                                            string="Configure" 
                                            class="btn-sm btn-primary"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Matrix Category Search View -->
    <record id="view_config_matrix_category_search" model="ir.ui.view">
        <field name="name">config.matrix.category.search</field>
        <field name="model">config.matrix.category</field>
        <field name="arch" type="xml">
            <search string="Search Matrix Categories">
                <field name="name"/>
                <field name="code"/>
                <field name="category_type"/>
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Required" name="required" domain="[('is_required', '=', True)]"/>
                <separator/>
                <filter string="Door Frame" name="door_frame" domain="[('category_type', '=', 'door_frame')]"/>
                <filter string="Hardware" name="hardware" domain="[('category_type', '=', 'hardware')]"/>
                <filter string="Glass/Panel" name="glass_panel" domain="[('category_type', '=', 'glass_panel')]"/>
                <filter string="Labor" name="labor" domain="[('category_type', '=', 'labor')]"/>
                <group expand="0" string="Group By">
                    <filter string="Category Type" name="group_by_type" context="{'group_by': 'category_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Template Category Assignment List View -->
    <record id="view_config_matrix_template_category_assignment_list" model="ir.ui.view">
        <field name="name">config.matrix.template.category.assignment.list</field>
        <field name="model">config.matrix.template.category.assignment</field>
        <field name="arch" type="xml">
            <list string="Template Category Assignments">
                <field name="sequence" widget="handle"/>
                <field name="template_id"/>
                <field name="category_id"/>
                <field name="is_required" readonly="1"/>
                <field name="max_matrices"/>
                <field name="selected_matrix_count"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <!-- Template Category Assignment Form View -->
    <record id="view_config_matrix_template_category_assignment_form" model="ir.ui.view">
        <field name="name">config.matrix.template.category.assignment.form</field>
        <field name="model">config.matrix.template.category.assignment</field>
        <field name="arch" type="xml">
            <form string="Template Category Assignment">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="category_id" placeholder="Select Category"/>
                        </h1>
                        <h2>
                            for <field name="template_id" placeholder="Select Template"/>
                        </h2>
                    </div>
                    
                    <group>
                        <group>
                            <field name="sequence"/>
                            <field name="max_matrices"/>
                        </group>
                        <group>
                            <field name="active"/>
                            <field name="is_required" readonly="1"/>
                            <field name="selected_matrix_count" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Selected Price Matrices" name="selected_price_matrices">
                            <group>
                                <div class="alert alert-info" role="alert">
                                    <p><strong>Available Price Matrices</strong></p>
                                    <p>Select price matrices from this category to include in the template.</p>
                                </div>
                            </group>
                            <field name="selected_price_matrix_ids">
                                <list string="Selected Price Matrices">
                                    <field name="name"/>
                                    <field name="matrix_type"/>
                                    <field name="currency_id"/>
                                    <field name="last_imported"/>
                                    <field name="active"/>
                                </list>
                            </field>
                        </page>
                        
                        <page string="Selected Labor Matrices" name="selected_labor_matrices">
                            <group>
                                <div class="alert alert-info" role="alert">
                                    <p><strong>Available Labor Matrices</strong></p>
                                    <p>Select labor time matrices from this category to include in the template.</p>
                                </div>
                            </group>
                            <field name="selected_labor_matrix_ids">
                                <list string="Selected Labor Matrices">
                                    <field name="name"/>
                                    <field name="operation_type"/>
                                    <field name="time_unit"/>
                                    <field name="last_imported"/>
                                    <field name="active"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Template Category Configuration Wizard -->
    <record id="view_config_matrix_template_category_wizard_form" model="ir.ui.view">
        <field name="name">config.matrix.template.category.wizard.form</field>
        <field name="model">config.matrix.template.category.wizard</field>
        <field name="arch" type="xml">
            <form string="Configure Category Matrices">
                <group>
                    <field name="template_id" readonly="1"/>
                    <field name="category_id" readonly="1"/>
                </group>
                
                <notebook>
                    <page string="Price Matrices" name="price_matrices">
                        <group>
                            <div class="alert alert-primary" role="alert">
                                <p>Select which price matrices from this category should be used for this template.</p>
                            </div>
                        </group>
                        <field name="price_matrix_ids">
                            <list string="Price Matrices">
                                <field name="name"/>
                                <field name="matrix_type"/>
                                <field name="currency_id"/>
                                <field name="active"/>
                            </list>
                        </field>
                    </page>
                    
                    <page string="Labor Matrices" name="labor_matrices">
                        <group>
                            <div class="alert alert-primary" role="alert">
                                <p>Select which labor time matrices from this category should be used for this template.</p>
                            </div>
                        </group>
                        <field name="labor_matrix_ids">
                            <list string="Labor Matrices">
                                <field name="name"/>
                                <field name="operation_type"/>
                                <field name="time_unit"/>
                                <field name="active"/>
                            </list>
                        </field>
                    </page>
                </notebook>
                
                <footer>
                    <button string="Save Configuration" 
                            name="action_save_configuration" 
                            type="object" 
                            class="btn-primary"/>
                    <button string="Cancel" 
                            special="cancel" 
                            class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Category Matrix Viewer Wizard -->
    <record id="view_config_matrix_category_matrix_wizard_form" model="ir.ui.view">
        <field name="name">config.matrix.category.matrix.wizard.form</field>
        <field name="model">config.matrix.category.matrix.wizard</field>
        <field name="arch" type="xml">
            <form string="View Category Matrices">
                <group>
                    <field name="category_id" readonly="1"/>
                    <field name="matrix_type"/>
                </group>
                
                <footer>
                    <button string="View Price Matrices" 
                            name="action_view_price_matrices" 
                            type="object" 
                            class="btn-primary"
                            invisible="matrix_type == 'labor'"/>
                    <button string="View Labor Matrices" 
                            name="action_view_labor_matrices" 
                            type="object" 
                            class="btn-success"
                            invisible="matrix_type == 'price'"/>
                    <button string="Cancel" 
                            special="cancel" 
                            class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_config_matrix_category" model="ir.actions.act_window">
        <field name="name">Matrix Categories</field>
        <field name="res_model">config.matrix.category</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first matrix category!
            </p>
            <p>
                Matrix categories help organize your pricing and labor matrices by type (Door Frame, Hardware, Glass/Panel, Labor Operations).
            </p>
        </field>
    </record>

    <record id="action_config_matrix_template_category_assignment" model="ir.actions.act_window">
        <field name="name">Template Category Assignments</field>
        <field name="res_model">config.matrix.template.category.assignment</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
    </record>
</odoo>
