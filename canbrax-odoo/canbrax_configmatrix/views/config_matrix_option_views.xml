<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Option Tree View -->
    <record id="view_config_matrix_option_tree" model="ir.ui.view">
        <field name="name">config.matrix.option.tree</field>
        <field name="model">config.matrix.option</field>
        <field name="arch" type="xml">
            <list string="Options">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="value"/>
                <field name="field_id"/>
                <field name="component_product_id"/>
                <field name="quantity_formula"/>
                <button name="action_test_quantity_formula" type="object"
                        string="Test Formula" class="btn-sm btn-secondary"
                        invisible="not component_product_id or not quantity_formula"/>
            </list>
        </field>
    </record>

    <!-- Option Form View -->
    <record id="view_config_matrix_option_form" model="ir.ui.view">
        <field name="name">config.matrix.option.form</field>
        <field name="model">config.matrix.option</field>
        <field name="arch" type="xml">
            <form string="Field Option">
                <sheet>
                    <group>
                        <group>
                            <field name="name" placeholder="Option Text"/>
                            <field name="value" placeholder="Option Value"/>
                            <field name="field_id" options="{'no_create': True}"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="component_product_id"/>
                            <field name="quantity_formula" placeholder="Python expression, e.g. 1"/>
                            <button name="action_test_quantity_formula" type="object"
                                    string="Test Formula" class="btn-secondary"
                                    invisible="not component_product_id or not quantity_formula"/>
                            <field name="template_state" invisible="1"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Operations" name="operations">
                            <!-- Operation Mappings -->
                            <div class="alert alert-info mb-3" role="alert">
                                <h5><i class="fa fa-cogs me-2"></i>Operation Mappings</h5>
                                <p class="mb-0">Configure which manufacturing operations should be included in the routing when this option is selected. You can map multiple operations with different work centers and duration calculations.</p>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Current Operation Mappings</h6>
                                <button name="action_open_operation_builder" type="object"
                                        string="Add Operation" class="btn btn-primary"
                                        icon="fa-plus"/>
                            </div>

                            <field name="operation_mapping_ids" nolabel="1">
                                <list string="Operation Mappings" create="false" edit="false" delete="true">
                                    <field name="sequence" widget="handle"/>
                                    <field name="operation_name"/>
                                    <field name="workcenter_id"/>
                                    <field name="duration_formula"/>
                                    <field name="condition"/>
                                    <!-- Action buttons -->
                                    <button name="action_open_operation_builder" type="object"
                                            string="Add Duration" icon="fa-magic"
                                            class="btn-link text-primary" title="Build duration formula"/>
                                </list>
                            </field>

                            <div class="mt-3" invisible="operation_mapping_ids">
                                <div class="text-center text-muted py-4">
                                    <i class="fa fa-info-circle fa-2x mb-2"></i>
                                    <p>No operation mappings configured yet.</p>
                                    <p class="small">Click "Add Operation" above to add operations.</p>
                                </div>
                            </div>
                        </page>

                        <page string="Visibility" name="visibility">
                            <group>
                                <field name="visibility_condition" invisible="1"/>
                                <field name="matrix_id" invisible="1"/>
                                <div class="d-flex justify-content-between mb-2">
                                    <div>
                                        <button name="action_view_option_visibility_conditions" type="object"
                                                string="View Conditions" class="btn-secondary"/>
                                        <button name="action_clear_visibility_conditions" type="object"
                                                string="Clear All Conditions" class="btn-secondary"
                                                invisible="[('visibility_condition', '=', False)]"/>
                                    </div>
                                </div>
                                <field name="visibility_condition_ids" context="{'default_option_id': id, 'default_matrix_id': matrix_id}">
                                    <list string="Visibility Conditions">
                                        <field name="sequence" widget="handle"/>
                                        <field name="name"/>
                                        <field name="logic_operator"/>
                                        <field name="generated_condition"/>
                                        <button name="unlink" string="Remove" type="object" icon="fa-trash" class="text-danger"/>
                                    </list>
                                </field>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Option Search View -->
    <record id="view_config_matrix_option_search" model="ir.ui.view">
        <field name="name">config.matrix.option.search</field>
        <field name="model">config.matrix.option</field>
        <field name="arch" type="xml">
            <search string="Search Options">
                <field name="name"/>
                <field name="value"/>
                <field name="field_id"/>
                <field name="matrix_id"/>
                <separator/>
                <filter string="With Component" name="with_component" domain="[('component_product_id', '!=', False)]"/>
                <filter string="With Multiple Components" name="with_multiple_components" domain="[('has_multiple_components', '=', True)]"/>
                <group expand="0" string="Group By">
                    <filter string="Field" name="group_by_field" context="{'group_by': 'field_id'}"/>
                    <filter string="Template" name="group_by_template" context="{'group_by': 'matrix_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Option Action -->
    <record id="action_config_matrix_option" model="ir.actions.act_window">
        <field name="name">Field Options</field>
        <field name="res_model">config.matrix.option</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'group_by': 'matrix_id'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first field option!
            </p>
            <p>
                Options are the possible choices for selection fields.
            </p>
        </field>
    </record>

    <!-- Menu Item - Removed as per request -->
</odoo>
