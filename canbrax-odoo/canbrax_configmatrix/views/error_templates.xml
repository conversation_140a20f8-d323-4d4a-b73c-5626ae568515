<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Custom HTTP Error Template -->
        <template id="http_error" name="HTTP Error">
            <t t-call="web.frontend_layout">
                <t t-set="head">
                    <t t-call-assets="web.assets_frontend" t-js="false"/>
                    <t t-call-assets="web.assets_frontend" t-css="false"/>
                </t>
                <div class="container mt-5">
                    <div class="card shadow-sm">
                        <div class="card-header bg-danger text-white">
                            <h4 class="mb-0">
                                <i class="fa fa-exclamation-triangle me-2"></i>
                                <t t-esc="status_message or 'Error'"/>
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger">
                                <p><strong>Error Details:</strong></p>
                                <p t-esc="error_message or 'An unexpected error occurred.'"></p>
                            </div>
                            <div class="mt-4">
                                <a href="javascript:history.back()" class="btn btn-secondary">
                                    <i class="fa fa-arrow-left me-2"></i>Go Back
                                </a>
                                <a href="/web" class="btn btn-primary ms-2">
                                    <i class="fa fa-home me-2"></i>Home
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>
    </data>
</odoo>
