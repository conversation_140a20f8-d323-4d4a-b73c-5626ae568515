<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Unified Component Mapping Tree View -->
    <record id="view_config_matrix_component_mapping_tree" model="ir.ui.view">
        <field name="name">config.matrix.component.mapping.tree</field>
        <field name="model">config.matrix.component.mapping</field>
        <field name="arch" type="xml">
            <list string="Component Mappings">
                <field name="sequence" widget="handle"/>
                <field name="target_model"/>
                <field name="target_name"/>
                <field name="matrix_id"/>
                <field name="mapping_type"/>
                <field name="component_product_id"/>
                <field name="base_component"/>
                <field name="quantity_formula"/>
                <field name="condition"/>
            </list>
        </field>
    </record>

    <!-- Unified Component Mapping Form View -->
    <record id="view_config_matrix_component_mapping_form" model="ir.ui.view">
        <field name="name">config.matrix.component.mapping.form</field>
        <field name="model">config.matrix.component.mapping</field>
        <field name="arch" type="xml">
            <form string="Component Mapping">
                <sheet>
                    <div class="oe_title">
                        <h1><field name="target_name" readonly="1"/></h1>
                        <h2>Component Mapping</h2>
                    </div>

                    <group>
                        <group>
                            <field name="target_model"/>
                            <field name="target_id"/>
                            <field name="matrix_id"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="mapping_type"/>
                            <field name="name"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Static Mapping" invisible="mapping_type != 'static'">
                            <group>
                                <field name="component_product_id" required="mapping_type == 'static'"/>
                                <field name="quantity_formula"/>
                                <field name="condition"/>
                            </group>
                        </page>

                        <page string="Dynamic Mapping" invisible="mapping_type != 'dynamic_match'">
                            <group>
                                <group>
                                    <field name="base_component" required="mapping_type == 'dynamic_match'"/>
                                    <field name="reference_field_id" domain="[('matrix_id', '=', matrix_id)]"/>
                                    <field name="reference_field" invisible="1"/>
                                    <field name="match_product_field"/>
                                </group>
                                <group>
                                    <field name="quantity_formula"/>
                                    <field name="condition"/>
                                </group>
                            </group>

                            <group string="Preview">
                                <field name="available_base_components" widget="text" readonly="1"/>
                                <field name="preview_matching_products" widget="text" readonly="1"/>
                            </group>

                            <div class="oe_button_box">
                                <button name="action_test_dynamic_match"
                                        type="object"
                                        string="Test Dynamic Match"
                                        class="btn-secondary"/>
                            </div>
                        </page>

                        <page string="Notes">
                            <field name="notes" widget="text"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Unified Component Mapping Search View -->
    <record id="view_config_matrix_component_mapping_search" model="ir.ui.view">
        <field name="name">config.matrix.component.mapping.search</field>
        <field name="model">config.matrix.component.mapping</field>
        <field name="arch" type="xml">
            <search string="Search Component Mappings">
                <field name="target_name"/>
                <field name="matrix_id"/>
                <field name="component_product_id"/>
                <field name="base_component"/>
                <separator/>
                <filter string="Static Mappings" name="static_mappings" domain="[('mapping_type', '=', 'static')]"/>
                <filter string="Dynamic Mappings" name="dynamic_mappings" domain="[('mapping_type', '=', 'dynamic_match')]"/>
                <separator/>
                <filter string="Field Targets" name="field_targets" domain="[('target_model', '=', 'config.matrix.field')]"/>
                <filter string="Option Targets" name="option_targets" domain="[('target_model', '=', 'config.matrix.option')]"/>
                <group expand="0" string="Group By">
                    <filter string="Template" name="group_by_template" context="{'group_by': 'matrix_id'}"/>
                    <filter string="Target Type" name="group_by_target_type" context="{'group_by': 'target_model'}"/>
                    <filter string="Mapping Type" name="group_by_mapping_type" context="{'group_by': 'mapping_type'}"/>
                    <filter string="Component Product" name="group_by_component" context="{'group_by': 'component_product_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Unified Component Mapping Action -->
    <record id="action_config_matrix_component_mapping" model="ir.actions.act_window">
        <field name="name">Component Mappings</field>
        <field name="res_model">config.matrix.component.mapping</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'group_by': ['matrix_id', 'target_model']}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No component mappings found
            </p>
            <p>
                Component mappings define which products are used as components for fields and options.
                Use filters to view Field Targets or Option Targets separately.
            </p>
        </field>
    </record>

    <!-- Menu Item for Unified Component Mappings -->
    <menuitem id="menu_config_matrix_component_mapping"
              name="Component Mappings"
              parent="menu_config_matrix_configuration"
              action="action_config_matrix_component_mapping"
              sequence="25"/>

</odoo>
