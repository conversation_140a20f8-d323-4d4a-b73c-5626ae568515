<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- My Projects Template -->
    <template id="portal_my_projects" name="My Projects">
        <t t-call="portal.portal_layout">
            <t t-set="breadcrumbs_searchbar" t-value="True"/>

            <t t-call="portal.portal_searchbar">
                <t t-set="title">My Projects</t>
            </t>
            
            <div class="o_portal_my_home mb-4">
                <div class="row">
                    <div class="col-12">
                        <h3 class="page-header">My Projects</h3>
                        <p class="text-muted">
                            Manage your building projects and door/screen configurations.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="mb-4">
                <a href="/my/start_order" class="btn btn-primary">
                    <i class="fa fa-plus-circle me-2"></i>Start New Order
                </a>
            </div>
            
            <t t-if="not projects">
                <div class="alert alert-info">
                    <p>You don't have any projects yet.</p>
                    <p>Click "Start New Order" to begin configuring doors and screens for your project.</p>
                </div>
            </t>
            
            <t t-if="projects">
                <div class="table-responsive">
                    <table class="table table-hover o_portal_my_doc_table">
                        <thead>
                            <tr class="active">
                                <th>Project</th>
                                <th class="text-center">Doors</th>
                                <th class="text-center">Screens</th>
                                <th class="text-center">Status</th>
                                <th class="text-center">Created On</th>
                                <th class="text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-foreach="projects" t-as="project">
                                <tr>
                                    <td>
                                        <a t-attf-href="/my/project/#{project.id}">
                                            <span t-field="project.name"/>
                                        </a>
                                    </td>
                                    <td class="text-center">
                                        <span t-field="project.door_count" class="badge bg-primary"/>
                                    </td>
                                    <td class="text-center">
                                        <span t-field="project.screen_count" class="badge bg-info"/>
                                    </td>
                                    <td class="text-center">
                                        <t t-if="project.state == 'draft'">
                                            <span class="badge bg-secondary">Draft</span>
                                        </t>
                                        <t t-elif="project.state == 'in_progress'">
                                            <span class="badge bg-primary">In Progress</span>
                                        </t>
                                        <t t-elif="project.state == 'completed'">
                                            <span class="badge bg-success">Completed</span>
                                        </t>
                                        <t t-elif="project.state == 'cancelled'">
                                            <span class="badge bg-danger">Cancelled</span>
                                        </t>
                                    </td>
                                    <td class="text-center">
                                        <span t-field="project.create_date" t-options='{"widget": "date"}'/>
                                    </td>
                                    <td class="text-end">
                                        <a t-attf-href="/my/project/#{project.id}" class="btn btn-sm btn-primary">
                                            <i class="fa fa-eye me-1"></i>View
                                        </a>
                                        <a t-attf-href="/my/start_order?project_id=#{project.id}" class="btn btn-sm btn-secondary">
                                            <i class="fa fa-plus me-1"></i>Add Items
                                        </a>
                                    </td>
                                </tr>
                            </t>
                        </tbody>
                    </table>
                </div>
                <div class="o_portal_pager">
                    <t t-call="portal.pager"/>
                </div>
            </t>
        </t>
    </template>
    
    <!-- Project Detail Template -->
    <template id="portal_project_detail" name="Project Detail">
        <t t-call="portal.portal_layout">
            <div class="container">
                <div class="row mt-4 mb-4">
                    <div class="col-lg-12">
                        <h2>
                            <span t-field="project.name"/>
                            <t t-if="project.state == 'draft'">
                                <span class="badge bg-secondary ms-2">Draft</span>
                            </t>
                            <t t-elif="project.state == 'in_progress'">
                                <span class="badge bg-primary ms-2">In Progress</span>
                            </t>
                            <t t-elif="project.state == 'completed'">
                                <span class="badge bg-success ms-2">Completed</span>
                            </t>
                            <t t-elif="project.state == 'cancelled'">
                                <span class="badge bg-danger ms-2">Cancelled</span>
                            </t>
                        </h2>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h4>Project Information</h4>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>Created On:</strong>
                                    <span t-field="project.create_date" t-options='{"widget": "date"}'/>
                                </div>
                                <div class="mb-3">
                                    <strong>Address:</strong>
                                    <div t-if="project.street">
                                        <span t-field="project.street"/>
                                    </div>
                                    <div t-if="project.street2">
                                        <span t-field="project.street2"/>
                                    </div>
                                    <div>
                                        <t t-if="project.city"><span t-field="project.city"/>, </t>
                                        <t t-if="project.state_id"><span t-field="project.state_id.name"/> </t>
                                        <t t-if="project.zip"><span t-field="project.zip"/></t>
                                    </div>
                                    <div t-if="project.country_id">
                                        <span t-field="project.country_id.name"/>
                                    </div>
                                </div>
                                <div class="mb-3" t-if="project.notes">
                                    <strong>Notes:</strong>
                                    <p t-field="project.notes"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h4>Items Summary</h4>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="h1 mb-0 text-primary">
                                            <span t-field="project.door_count"/>
                                        </div>
                                        <div class="text-muted">Doors</div>
                                    </div>
                                    <div class="col-6">
                                        <div class="h1 mb-0 text-info">
                                            <span t-field="project.screen_count"/>
                                        </div>
                                        <div class="text-muted">Screens</div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between">
                                    <a t-attf-href="/my/start_order?project_id=#{project.id}&amp;door_count=1" class="btn btn-primary">
                                        <i class="fa fa-plus me-1"></i>Add Doors
                                    </a>
                                    <a t-attf-href="/my/start_order?project_id=#{project.id}&amp;screen_count=1" class="btn btn-info">
                                        <i class="fa fa-plus me-1"></i>Add Screens
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>Configurations</h4>
                            </div>
                            <div class="card-body">
                                <t t-if="not project.configuration_ids">
                                    <div class="alert alert-info">
                                        No configurations found for this project.
                                    </div>
                                </t>
                                <t t-if="project.configuration_ids">
                                    <!-- Configurations list will go here -->
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Product</th>
                                                    <th>Category</th>
                                                    <th>Configuration</th>
                                                    <th class="text-end">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <t t-foreach="project.configuration_ids" t-as="config">
                                                    <tr>
                                                        <td>
                                                            <span t-field="config.product_id.name"/>
                                                        </td>
                                                        <td>
                                                            <t t-if="config.product_category == 'door'">
                                                                <span class="badge bg-primary">Door</span>
                                                            </t>
                                                            <t t-elif="config.product_category == 'screen'">
                                                                <span class="badge bg-info">Screen</span>
                                                            </t>
                                                        </td>
                                                        <td>
                                                            <small t-field="config.configuration_summary"/>
                                                        </td>
                                                        <td class="text-end">
                                                            <a t-att-href="'/config_matrix/website_configurator?product_id=%s&amp;config_id=%s' % (config.product_id.id, config.id)" class="btn btn-sm btn-primary">
                                                                <i class="fa fa-cogs me-1"></i>Configure
                                                            </a>
                                                        </td>
                                                    </tr>
                                                </t>
                                            </tbody>
                                        </table>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo>
