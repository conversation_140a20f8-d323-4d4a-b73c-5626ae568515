<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Website Menu Items -->
        <record id="portal_my_configurations_action" model="ir.actions.act_window">
            <field name="name">My Configurations</field>
            <field name="res_model">config.matrix.configuration</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('create_uid', '=', uid)]</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No configurations found
                </p>
                <p>
                    You haven't created any product configurations yet.
                </p>
            </field>
        </record>

        <!-- Website Main Menu Item -->
        <record id="website_menu_my_configurations" model="website.menu">
            <field name="name">My Configurations</field>
            <field name="url">/my/configurations</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">30</field>
        </record>

        <!-- Portal Breadcrumbs -->
        <template id="portal_menu_my_configurations" name="Portal Layout My Configurations" inherit_id="portal.portal_breadcrumbs" priority="30">
            <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
                <li t-if="page_name == 'configurations'" class="breadcrumb-item">My Configurations</li>
            </xpath>
        </template>

        <!-- Portal Home Card -->
        <template id="portal_my_home" name="Portal My Home Configurations" inherit_id="portal.portal_my_home" priority="30">
            <xpath expr="//div[hasclass('o_portal_docs')]" position="inside">
                <div class="col-lg-4">
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <h3 class="h4 mb-0">
                                    <i class="fa fa-cogs text-primary me-2"></i>My Configurations
                                </h3>
                                <a href="/my/configurations" class="btn btn-sm btn-link">
                                    <i class="fa fa-arrow-right"></i>
                                </a>
                            </div>
                            <div t-if="configuration_count" class="mt-3">
                                <t t-esc="configuration_count"/> Saved Configurations
                            </div>
                            <div t-else="" class="mt-3 text-muted">
                                No saved configurations yet
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </template>
    </data>
</odoo>
