<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- My Configurations Template -->
        <template id="portal_my_configurations">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>

                <t t-call="portal.portal_searchbar">
                    <t t-set="title">My Configurations</t>
                </t>

                <div class="o_portal_content">
                    <div class="website-portal-container">
                        <div class="website-portal-header d-flex justify-content-between align-items-center flex-wrap">
                            <div>
                                <h1 class="website-portal-title">My Configurations</h1>
                                <p class="website-portal-subtitle">View and manage your saved product configurations</p>
                            </div>
                            <div class="mt-2 mt-md-0">
                                <a href="/shop" class="btn btn-website btn-website-primary">
                                    <i class="fa fa-plus me-2"></i>Create New Configuration
                                </a>
                            </div>
                        </div>

                        <!-- Configuration filters -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="o_portal_search_panel d-flex flex-wrap justify-content-between">
                                    <!-- Sorting options -->
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="fa fa-sort me-1"></i>
                                            <span>
                                                <t t-if="sortby == 'date'">Newest</t>
                                                <t t-elif="sortby == 'name'">Name</t>
                                                <t t-elif="sortby == 'price'">Price</t>
                                                <t t-else="">Newest</t>
                                            </span>
                                        </button>
                                        <div class="dropdown-menu">
                                            <t t-foreach="searchbar_sortings" t-as="option">
                                                <a t-att-href="request.httprequest.path + '?' + keep_query('*', sortby=option)"
                                                   t-attf-class="dropdown-item#{sortby == option and ' active' or ''}">
                                                    <t t-esc="searchbar_sortings[option]['label']"/>
                                                </a>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- Filter options -->
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="fa fa-filter me-1"></i>
                                            <span>
                                                <t t-if="filterby == 'all'">All</t>
                                                <t t-elif="filterby == 'door'">Doors</t>
                                                <t t-elif="filterby == 'window'">Windows</t>
                                                <t t-elif="filterby == 'cabinet'">Cabinets</t>
                                                <t t-else="">All</t>
                                            </span>
                                        </button>
                                        <div class="dropdown-menu">
                                            <t t-foreach="searchbar_filters" t-as="option">
                                                <a t-att-href="request.httprequest.path + '?' + keep_query('*', filterby=option)"
                                                   t-attf-class="dropdown-item#{filterby == option and ' active' or ''}">
                                                    <t t-esc="searchbar_filters[option]['label']"/>
                                                    <t t-if="product_types.get(option, 0) > 0">
                                                        <span class="badge bg-primary ms-1"><t t-esc="product_types.get(option, 0)"/></span>
                                                    </t>
                                                </a>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- Search box -->
                                    <div class="o_portal_search_panel">
                                        <form class="d-flex" action="/my/configurations" method="get">
                                            <div class="input-group">
                                                <input type="text" class="form-control" name="search" t-att-value="search" placeholder="Search..."/>
                                                <div class="input-group-append">
                                                    <button type="submit" class="btn btn-primary">
                                                        <i class="fa fa-search"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Configuration cards -->
                        <div class="row">
                            <t t-if="configurations">
                                <t t-foreach="configurations" t-as="config">
                                    <div class="col-lg-4 col-md-6 mb-4">
                                        <div class="config-card h-100">
                                            <div class="config-card-header">
                                                <h5 class="config-card-title">
                                                    <t t-if="config.name">
                                                        <t t-esc="config.name"/>
                                                    </t>
                                                    <t t-else="">
                                                        <t t-esc="config.product_id.name"/>
                                                    </t>
                                                </h5>
                                                <span class="badge bg-primary">
                                                    <t t-raw="price_formatted_dict.get(config.id, '')"/>
                                                </span>
                                            </div>
                                            <div class="config-card-body">
                                                <div class="mb-3">
                                                    <div class="d-flex align-items-center mb-2">
                                                        <div class="me-2">
                                                            <t t-if="config.product_id.image_128">
                                                                <img t-att-src="'data:image/png;base64,%s' % config.product_id.image_128" class="img-fluid" style="max-height: 50px;" alt="Product"/>
                                                            </t>
                                                        </div>
                                                        <div>
                                                            <div class="fw-bold"><t t-esc="config.product_id.name"/></div>
                                                            <div class="text-muted small">
                                                                <t t-if="config.create_date">
                                                                    <i class="fa fa-calendar me-1"></i>
                                                                    <t t-esc="config.create_date.strftime('%d %b %Y')"/>
                                                                </t>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-between mt-3">
                                                    <a t-att-href="'/my/configuration/%s' % config.id" class="btn btn-sm btn-website btn-website-primary">
                                                        <i class="fa fa-eye me-1"></i>View
                                                    </a>
                                                    <a t-att-href="'/config_matrix/website_configurator?config_id=%s&amp;template_id=%s&amp;product_id=%s&amp;portal_context=1' % (config.id, config.template_id.id, config.product_id.id)" class="btn btn-sm btn-website btn-website-secondary">
                                                        <i class="fa fa-edit me-1"></i>Edit
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                            </t>
                            <t t-else="">
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <p class="mb-0">You don't have any saved configurations yet.</p>
                                    </div>
                                </div>
                            </t>
                        </div>

                        <!-- Pager -->
                        <t t-if="pager">
                            <div class="o_portal_pager mt-4">
                                <t t-call="portal.pager"/>
                            </div>
                        </t>
                    </div>
                </div>
            </t>
        </template>

        <!-- Configuration Detail Template -->
        <template id="portal_my_configuration">
            <t t-call="portal.portal_layout">
                <div class="container mt-4">
                    <div class="website-portal-container">
                        <div class="website-portal-header d-flex justify-content-between align-items-center flex-wrap">
                            <div>
                                <h1 class="website-portal-title">
                                    <t t-if="config.name">
                                        <t t-esc="config.name"/>
                                    </t>
                                    <t t-else="">
                                        <t t-esc="config.product_id.name"/> Configuration
                                    </t>
                                </h1>
                                <p class="website-portal-subtitle">
                                    <t t-if="config.create_date">
                                        Created on <t t-esc="config.create_date.strftime('%d %b %Y')"/>
                                    </t>
                                </p>
                            </div>
                            <div class="mt-2 mt-md-0">
                                <a href="/my/configurations" class="btn btn-website btn-website-secondary">
                                    <i class="fa fa-arrow-left me-2"></i>Back to Configurations
                                </a>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="mb-0">Product Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="me-3">
                                                <t t-if="config.product_id.image_128">
                                                    <img t-att-src="'data:image/png;base64,%s' % config.product_id.image_128" class="img-fluid" style="max-height: 80px;" alt="Product"/>
                                                </t>
                                            </div>
                                            <div>
                                                <h5 class="mb-1"><t t-esc="config.product_id.name"/></h5>
                                                <div class="text-primary fw-bold">
                                                    <t t-raw="price_formatted"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <a t-att-href="'/config_matrix/website_configurator?config_id=%s&amp;template_id=%s&amp;product_id=%s&amp;portal_context=1' % (config.id, config.template_id.id, config.product_id.id)" class="btn btn-website btn-website-primary w-100">
                                                <i class="fa fa-edit me-1"></i>Edit Configuration
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">Configuration Details</h5>
                                    </div>
                                    <div class="card-body">
                                        <div t-if="config.configuration_summary" class="mb-3">
                                            <div class="configuration-details-formatted">
                                                <t t-foreach="config.configuration_summary.split('\n')" t-as="summary_line">
                                                    <t t-if="'===' in summary_line">
                                                        <!-- Section header -->
                                                        <h6 class="mt-3 mb-2 text-primary"><t t-esc="summary_line.replace('===', '').strip()"/></h6>
                                                    </t>
                                                    <t t-elif="summary_line.startswith('=')">
                                                        <!-- Main header or separator - skip -->
                                                    </t>
                                                    <t t-elif="summary_line.startswith('-')">
                                                        <!-- Separator - render as horizontal rule -->
                                                        <hr class="my-2"/>
                                                    </t>
                                                    <t t-elif="': ' in summary_line and not summary_line.startswith('Configuration Summary')">
                                                        <!-- Field and value -->
                                                        <div class="row mb-1">
                                                            <div class="col-5 fw-medium text-secondary">
                                                                <t t-esc="summary_line.split(': ')[0]"/>:
                                                            </div>
                                                            <div class="col-7">
                                                                <t t-esc="': '.join(summary_line.split(': ')[1:])"/>
                                                            </div>
                                                        </div>
                                                    </t>
                                                    <t t-elif="summary_line.strip() and not summary_line.startswith('Configuration Summary')">
                                                        <!-- Regular text -->
                                                        <p class="mb-1"><t t-esc="summary_line"/></p>
                                                    </t>
                                                </t>
                                            </div>
                                        </div>
                                        <div t-else="" class="text-muted">
                                            <em>No configuration details available.</em>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>
    </data>
</odoo>
