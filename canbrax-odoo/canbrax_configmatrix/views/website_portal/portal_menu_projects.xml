<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Website Menu Items -->
        <record id="portal_my_projects_action" model="ir.actions.act_window">
            <field name="name">My Projects</field>
            <field name="res_model">builder.project</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('partner_id', '=', user.partner_id.id)]</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No projects found
                </p>
                <p>
                    You haven't created any building projects yet.
                </p>
            </field>
        </record>

        <!-- Website Main Menu Item -->
        <record id="website_menu_my_projects" model="website.menu">
            <field name="name">My Projects</field>
            <field name="url">/my/projects</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">25</field>
        </record>

        <!-- Portal Breadcrumbs -->
        <template id="portal_menu_my_projects" name="Portal Layout My Projects" inherit_id="portal.portal_breadcrumbs" priority="25">
            <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
                <li t-if="page_name == 'projects'" class="breadcrumb-item">My Projects</li>
                <li t-if="page_name == 'project_detail'" class="breadcrumb-item">
                    <a t-if="project" t-attf-href="/my/projects">My Projects</a>
                    <t t-else="">My Projects</t>
                </li>
                <li t-if="page_name == 'project_detail' and project" class="breadcrumb-item active">
                    <t t-esc="project.name"/>
                </li>
            </xpath>
        </template>

        <!-- Portal Home Card -->
        <template id="portal_my_home_projects" name="Portal My Home Projects" inherit_id="portal.portal_my_home" priority="25">
            <xpath expr="//div[hasclass('o_portal_docs')]" position="inside">
                <div class="col-lg-4">
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <h3 class="h4 mb-0">
                                    <i class="fa fa-building text-primary me-2"></i>My Projects
                                </h3>
                                <a href="/my/projects" class="btn btn-sm btn-link">
                                    <i class="fa fa-arrow-right"></i>
                                </a>
                            </div>
                            <div t-if="project_count" class="mt-3">
                                <t t-esc="project_count"/> Building Projects
                            </div>
                            <div t-else="" class="mt-3 text-muted">
                                No building projects yet
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </template>
    </data>
</odoo>
