<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Website Portal Template -->
        <template id="website_portal_template">
            <t t-call="website.layout">
                <div id="wrap" class="oe_structure">
                    <section class="pt-3 pb-3">
                        <div class="container">
                            <div class="website-portal-container">
                                <div class="website-portal-header">
                                    <h1 class="website-portal-title">Product Configurator</h1>
                                    <p class="website-portal-subtitle">Configure your custom products with ease</p>
                                </div>

                                <!-- Show error message if any -->
                                <t t-if="error">
                                    <div class="alert alert-danger">
                                        <h4 class="alert-heading">Configuration Error</h4>
                                        <p t-esc="error"/>
                                    </div>
                                </t>

                                <!-- Show error if no template -->
                                <t t-if="not template and not error">
                                    <div class="alert alert-warning">
                                        <h4 class="alert-heading">
                                            <i class="fa fa-exclamation-triangle me-2"></i>
                                            No Configuration Template
                                        </h4>
                                        <p>This product does not have a configuration template defined.</p>
                                    </div>
                                    <div class="mt-4">
                                        <a href="/shop/cart" class="btn btn-website btn-website-secondary">
                                            <i class="fa fa-arrow-left me-2"></i>Back to Cart
                                        </a>
                                    </div>
                                </t>

                                <!-- Show configurator form if template is available -->
                                <t t-if="template and not error">
                                    <form id="config-form" method="post" action="/config_matrix/save_config">
                                        <!-- CSRF token -->
                                        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                        <!-- Website context marker -->
                                        <input type="hidden" name="website_context" value="1"/>
                                        <!-- Website portal marker -->
                                        <input type="hidden" name="website_portal" value="1"/>

                                        <!-- Hidden fields for parameters -->
                                        <input type="hidden" name="template_id" t-att-value="template.id"/>
                                        <t t-if="product">
                                            <input type="hidden" name="product_id" t-att-value="product.id"/>
                                        </t>
                                        <t t-if="order_line">
                                            <input type="hidden" name="order_line_id" t-att-value="order_line.id"/>
                                        </t>
                                        <t t-if="config_id">
                                            <input type="hidden" name="config_id" t-att-value="config_id"/>
                                        </t>

                                        <!-- Product info -->
                                        <div class="card mb-4">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-2">
                                                        <t t-if="product and product.image_128">
                                                            <img t-att-src="'data:image/png;base64,%s' % product.image_128" class="img-fluid" alt="Product Image"/>
                                                        </t>
                                                    </div>
                                                    <div class="col-md-10">
                                                        <h3 t-if="product" t-esc="product.name"/>
                                                        <h4 t-if="price_formatted">
                                                            Price: <span t-raw="price_formatted"/>
                                                        </h4>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Sections and fields -->
                                        <t t-if="template.section_ids">
                                            <t t-foreach="template.section_ids" t-as="section">
                                                <div class="card mb-4 website-form-group">
                                                    <div class="card-header">
                                                        <h3 class="mb-0"><t t-esc="section.name"/></h3>
                                                    </div>
                                                    <div class="card-body">
                                                        <t t-if="section.field_ids">
                                                            <div class="row">
                                                                <t t-foreach="section.field_ids" t-as="field">
                                                                    <div class="col-md-6 mb-3">
                                                                        <div class="form-group">
                                                                            <label class="form-label" t-att-for="'field_' + str(field.id)">
                                                                                <t t-esc="field.name"/>
                                                                            </label>

                                                                            <!-- Render field based on type -->
                                                                            <t t-if="field.field_type == 'text'">
                                                                                <input type="text" class="form-control"
                                                                                       t-att-id="'field_' + str(field.id)"
                                                                                       t-att-name="'field_' + str(field.id)"
                                                                                       t-att-value="config_values.get(str(field.id), field.default_value or '')"/>
                                                                            </t>
                                                                            <t t-elif="field.field_type == 'number'">
                                                                                <input type="number" class="form-control"
                                                                                       t-att-id="'field_' + str(field.id)"
                                                                                       t-att-name="'field_' + str(field.id)"
                                                                                       t-att-value="config_values.get(str(field.id), field.default_value or '')"/>
                                                                            </t>
                                                                            <t t-elif="field.field_type == 'boolean'">
                                                                                <div class="form-check">
                                                                                    <input type="checkbox" class="form-check-input"
                                                                                           t-att-id="'field_' + str(field.id)"
                                                                                           t-att-name="'field_' + str(field.id)"
                                                                                           t-att-checked="config_values.get(str(field.id), field.default_value or False) == 'on'"/>
                                                                                </div>
                                                                            </t>
                                                                            <t t-elif="field.field_type == 'selection'">
                                                                                <select class="form-select"
                                                                                        t-att-id="'field_' + str(field.id)"
                                                                                        t-att-name="'field_' + str(field.id)">
                                                                                    <!-- Add an empty option first -->
                                                                                    <option value="" disabled="disabled" t-att-selected="not config_values.get(str(field.id), field.default_value) and 'selected'">-- Select an option --</option>
                                                                                    <t t-foreach="field.option_ids" t-as="option">
                                                                                        <option t-att-value="option.value"
                                                                                                t-att-selected="config_values.get(str(field.id)) == option.value and 'selected'">
                                                                                            <t t-esc="option.name"/>
                                                                                        </option>
                                                                                    </t>
                                                                                </select>
                                                                            </t>
                                                                            <t t-else="">
                                                                                <input type="text" class="form-control"
                                                                                       t-att-id="'field_' + str(field.id)"
                                                                                       t-att-name="'field_' + str(field.id)"
                                                                                       t-att-value="config_values.get(str(field.id), field.default_value or '')"/>
                                                                            </t>

                                                                            <t t-if="field.help_text">
                                                                                <small class="form-text text-muted"><t t-esc="field.help_text"/></small>
                                                                            </t>
                                                                        </div>
                                                                    </div>
                                                                </t>
                                                            </div>
                                                        </t>
                                                        <t t-else="">
                                                            <p class="text-muted">No fields in this section</p>
                                                        </t>
                                                    </div>
                                                </div>
                                            </t>
                                        </t>
                                        <t t-else="">
                                            <div class="alert alert-info">No sections defined in this template</div>
                                        </t>

                                        <!-- Submit buttons -->
                                        <div class="mt-4">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <a href="/shop/cart" class="btn btn-website btn-website-secondary">
                                                        <i class="fa fa-arrow-left me-2"></i>Back to Cart
                                                    </a>
                                                </div>
                                                <div class="col-md-4 text-center">
                                                    <t t-if="product">
                                                        <a t-att-href="'/shop/product/%s' % product.product_tmpl_id.id" class="btn btn-website btn-website-outline">
                                                            <i class="fa fa-plus-circle me-1"></i>Configure Another
                                                        </a>
                                                    </t>
                                                </div>
                                                <div class="col-md-4 text-end">
                                                    <button type="submit" class="btn btn-website btn-website-primary">
                                                        <t t-if="config_id">Update Configuration</t>
                                                        <t t-else="">Save and Continue</t>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </t>
                            </div>
                        </div>
                    </section>
                </div>
            </t>
        </template>
    </data>
</odoo>
