<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Configure Grid Template -->
    <template id="portal_configure_grid" name="Configure Grid">
        <t t-call="portal.portal_layout">
            <div class="container">
                <div class="row mt-4 mb-4">
                    <div class="col-lg-12">
                        <h2>Product Configurations</h2>
                        <p class="text-muted">
                            Configure your products by selecting from the sections below.
                        </p>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h4>
                                        <t t-if="project">
                                            Project: <span t-field="project.name"/>
                                        </t>
                                        <t t-else="">
                                            New Order
                                        </t>
                                    </h4>
                                    <div>
                                        <button type="button" id="copy-to-all" class="btn btn-secondary me-2" disabled="disabled">
                                            <i class="fa fa-copy me-1"></i>Copy to All
                                        </button>
                                        <button type="button" id="copy-to-selected" class="btn btn-secondary" disabled="disabled">
                                            <i class="fa fa-copy me-1"></i>Copy to Selected
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <i class="fa fa-info-circle me-2"></i>
                                            Select products for each slot, then click "Configure" to customize them.
                                            Once you've configured a product, you can copy its configuration to other slots.
                                        </div>
                                    </div>
                                </div>

                                <!-- Collapsible sections for doors and screens -->
                                <div class="accordion mb-4" id="productCategoriesAccordion">
                                    <!-- Doors Section -->
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="doorHeading">
                                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#doorCollapse" aria-expanded="true" aria-controls="doorCollapse">
                                                <img src="/canbrax_configmatrix/static/src/img/door_icon.png" alt="Doors" class="img-fluid me-3" style="max-height: 40px;"/>
                                                <span class="h4 mb-0">DOORS</span>
                                            </button>
                                        </h2>
                                        <div id="doorCollapse" class="accordion-collapse collapse show" aria-labelledby="doorHeading" data-bs-parent="#productCategoriesAccordion">
                                            <div class="accordion-body">
                                                <div class="row" id="door-configuration-grid">
                                                    <!-- Only show door slots if we're in door category or have door count -->
                                                    <t t-if="category == 'door' or (category == 'screen' and door_count and door_count > 0)">
                                                        <t t-foreach="range(1, (count if category == 'door' else (door_count or 0)) + 1)" t-as="slot_number">
                                                            <div class="col-md-4 mb-4">
                                                                <div class="card h-100 config-slot door-slot" t-att-data-slot-id="'door-' + str(slot_number)">
                                                                    <div class="card-header">
                                                                        <div class="d-flex justify-content-between align-items-center">
                                                                            <h5>Door <t t-esc="slot_number"/></h5>
                                                                            <div class="form-check">
                                                                                <input class="form-check-input slot-checkbox" type="checkbox" t-att-id="'slot-check-door-' + str(slot_number)" t-att-data-slot-id="'door-' + str(slot_number)"/>
                                                                                <label class="form-check-label" t-att-for="'slot-check-door-' + str(slot_number)">
                                                                                    Select
                                                                                </label>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="card-body product-selection">
                                                                        <div class="text-center mb-3">
                                                                            <select class="form-select product-select" t-att-data-slot-id="'door-' + str(slot_number)" data-category="door">
                                                                                <option value="">-- Select a Door Product --</option>
                                                                                <t t-foreach="products.filtered(lambda p: 'door' in p.name.lower())" t-as="product">
                                                                                    <option t-att-value="product.id" t-att-data-matrix-id="product.matrix_id and product.matrix_id.id or ''">
                                                                                        <t t-esc="product.name"/>
                                                                                    </option>
                                                                                </t>
                                                                            </select>
                                                                        </div>
                                                                        <div class="product-details text-center">
                                                                            <div class="placeholder-content">
                                                                                <img src="/canbrax_configmatrix/static/src/img/door_icon.png" alt="Select a door product" class="img-fluid mb-3" style="max-height: 100px;"/>
                                                                                <p>Select a door product to configure</p>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="card-footer text-center">
                                                                        <button type="button" class="btn btn-primary configure-btn" disabled="disabled" t-att-data-slot-id="'door-' + str(slot_number)">
                                                                            <i class="fa fa-cogs me-1"></i>Configure
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </t>
                                                    </t>
                                                    <t t-else="">
                                                        <div class="col-12 text-center py-4">
                                                            <p>No doors selected. Go back to add doors to your order.</p>
                                                        </div>
                                                    </t>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Screens Section -->
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="screenHeading">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#screenCollapse" aria-expanded="false" aria-controls="screenCollapse">
                                                <img src="/canbrax_configmatrix/static/src/img/screen_icon.png" alt="Screens" class="img-fluid me-3" style="max-height: 40px;"/>
                                                <span class="h4 mb-0">SCREENS</span>
                                            </button>
                                        </h2>
                                        <div id="screenCollapse" class="accordion-collapse collapse" aria-labelledby="screenHeading" data-bs-parent="#productCategoriesAccordion">
                                            <div class="accordion-body">
                                                <div class="row" id="screen-configuration-grid">
                                                    <!-- Only show screen slots if we're in screen category or have screen count -->
                                                    <t t-if="category == 'screen' or (category == 'door' and screen_count and screen_count > 0)">
                                                        <t t-foreach="range(1, (count if category == 'screen' else (screen_count or 0)) + 1)" t-as="slot_number">
                                                            <div class="col-md-4 mb-4">
                                                                <div class="card h-100 config-slot screen-slot" t-att-data-slot-id="'screen-' + str(slot_number)">
                                                                    <div class="card-header">
                                                                        <div class="d-flex justify-content-between align-items-center">
                                                                            <h5>Screen <t t-esc="slot_number"/></h5>
                                                                            <div class="form-check">
                                                                                <input class="form-check-input slot-checkbox" type="checkbox" t-att-id="'slot-check-screen-' + str(slot_number)" t-att-data-slot-id="'screen-' + str(slot_number)"/>
                                                                                <label class="form-check-label" t-att-for="'slot-check-screen-' + str(slot_number)">
                                                                                    Select
                                                                                </label>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="card-body product-selection">
                                                                        <div class="text-center mb-3">
                                                                            <select class="form-select product-select" t-att-data-slot-id="'screen-' + str(slot_number)" data-category="screen">
                                                                                <option value="">-- Select a Screen Product --</option>
                                                                                <t t-foreach="products.filtered(lambda p: 'screen' in p.name.lower())" t-as="product">
                                                                                    <option t-att-value="product.id" t-att-data-matrix-id="product.matrix_id and product.matrix_id.id or ''">
                                                                                        <t t-esc="product.name"/>
                                                                                    </option>
                                                                                </t>
                                                                            </select>
                                                                        </div>
                                                                        <div class="product-details text-center">
                                                                            <div class="placeholder-content">
                                                                                <img src="/canbrax_configmatrix/static/src/img/screen_icon.png" alt="Select a screen product" class="img-fluid mb-3" style="max-height: 100px;"/>
                                                                                <p>Select a screen product to configure</p>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="card-footer text-center">
                                                                        <button type="button" class="btn btn-primary configure-btn" disabled="disabled" t-att-data-slot-id="'screen-' + str(slot_number)">
                                                                            <i class="fa fa-cogs me-1"></i>Configure
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </t>
                                                    </t>
                                                    <t t-else="">
                                                        <div class="col-12 text-center py-4">
                                                            <p>No screens selected. Go back to add screens to your order.</p>
                                                        </div>
                                                    </t>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between">
                                    <a t-att-href="'/my/start_order?door_count=%s&amp;screen_count=%s%s' % (
                                        1 if category == 'screen' else count,
                                        1 if category == 'door' else count,
                                        '&amp;project_id=%s' % project.id if project else ''
                                    )" class="btn btn-secondary">
                                        <i class="fa fa-arrow-left me-1"></i>Back
                                    </a>
                                    <button type="button" id="save-button" class="btn btn-primary me-2" disabled="disabled">
                                        <i class="fa fa-save me-1"></i>Save
                                    </button>
                                    <button type="button" id="add-to-cart" class="btn btn-success" disabled="disabled">
                                        <i class="fa fa-shopping-cart me-1"></i>Add to Cart
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hidden form for storing configuration data -->
            <form id="configuration-data" style="display: none;">
                <input type="hidden" name="project_id" t-att-value="project and project.id or 0"/>
                <input type="hidden" name="category" t-att-value="category"/>
                <input type="hidden" name="count" t-att-value="count"/>
                <div id="slot-data-container"></div>
            </form>

            <!-- Load the JavaScript file -->
            <script type="text/javascript" src="/canbrax_configmatrix/static/src/js/website_portal/portal_configure_grid.js"></script>
        </t>
    </template>
</odoo>
