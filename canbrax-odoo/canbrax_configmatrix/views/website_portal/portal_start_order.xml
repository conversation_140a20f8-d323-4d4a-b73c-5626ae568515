<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Start Order Template -->
    <template id="portal_start_order" name="Start Order">
        <t t-call="portal.portal_layout">
            <div class="container">
                <div class="row mt-4 mb-4">
                    <div class="col-lg-12">
                        <h2>Start Order</h2>
                        <p class="text-muted">
                            Select the number of doors and screens you want to configure.
                        </p>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>
                                    <t t-if="project">
                                        Project: <span t-field="project.name"/>
                                    </t>
                                    <t t-else="">
                                        New Order
                                    </t>
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100 category-card" id="door-category">
                                            <div class="card-body text-center py-5">
                                                <img src="/canbrax_configmatrix/static/src/img/door_icon.png" alt="Doors" class="img-fluid mb-3" style="max-height: 100px;"/>
                                                <h3>DOORS</h3>
                                                <div class="quantity-control mt-4">
                                                    <div class="input-group justify-content-center">
                                                        <button type="button" class="btn btn-secondary quantity-btn" id="door-minus-btn" onclick="decrementCount('door_count')">
                                                            <i class="fa fa-minus"></i>
                                                        </button>
                                                        <input type="number" id="door_count" name="door_count" class="form-control text-center quantity-input" value="1" min="0" max="20" style="max-width: 80px;" t-att-value="door_count"/>
                                                        <button type="button" class="btn btn-secondary quantity-btn" id="door-plus-btn" onclick="incrementCount('door_count')">
                                                            <i class="fa fa-plus"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100 category-card" id="screen-category">
                                            <div class="card-body text-center py-5">
                                                <img src="/canbrax_configmatrix/static/src/img/screen_icon.png" alt="Screens" class="img-fluid mb-3" style="max-height: 100px;"/>
                                                <h3>SCREENS</h3>
                                                <div class="quantity-control mt-4">
                                                    <div class="input-group justify-content-center">
                                                        <button type="button" class="btn btn-secondary quantity-btn" id="screen-minus-btn" onclick="decrementCount('screen_count')">
                                                            <i class="fa fa-minus"></i>
                                                        </button>
                                                        <input type="number" id="screen_count" name="screen_count" class="form-control text-center quantity-input" value="1" min="0" max="20" style="max-width: 80px;" t-att-value="screen_count"/>
                                                        <button type="button" class="btn btn-secondary quantity-btn" id="screen-plus-btn" onclick="incrementCount('screen_count')">
                                                            <i class="fa fa-plus"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="form-group mb-3">
                                            <label for="order_name" class="form-label">Order Name</label>
                                            <input type="text" id="order_name" name="order_name" class="form-control"
                                                placeholder="Enter a name for your order (e.g., Smith Residence, Project 123)"
                                                t-att-value="project and project.name or ''" required="required"/>
                                            <small class="form-text text-muted">
                                                Give your order a name to easily identify it later.
                                            </small>
                                        </div>
                                        <div class="alert alert-info">
                                            <i class="fa fa-info-circle me-2"></i>
                                            Select the number of doors and screens you want to configure. You'll be able to choose specific products and configurations in the next step.
                                        </div>
                                        <!-- Hidden field for project ID -->
                                        <input type="hidden" id="hidden_project_id" t-att-value="project and project.id or 0"/>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between">
                                    <a href="/my/projects" class="btn btn-secondary">
                                        <i class="fa fa-arrow-left me-1"></i>Back to Projects
                                    </a>
                                    <div>
                                        <button type="button" id="continue-btn" class="btn btn-primary" onclick="continueWithOrder()">
                                            <i class="fa fa-arrow-right me-1"></i>Continue
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Include the JavaScript functions directly -->
            <script type="text/javascript">
                function incrementCount(inputId) {
                    var input = document.getElementById(inputId);
                    if (input) {
                        var value = parseInt(input.value) || 0;
                        if (value &lt; 20) {
                            input.value = value + 1;
                        }
                    }
                }

                function decrementCount(inputId) {
                    var input = document.getElementById(inputId);
                    if (input) {
                        var value = parseInt(input.value) || 0;
                        if (value &gt; 0) {
                            input.value = value - 1;
                        }
                    }
                }

                function continueWithOrder() {
                    console.log("Continue button clicked");

                    // Validate order name
                    var orderName = document.getElementById('order_name').value.trim();
                    if (!orderName) {
                        alert('Please enter a name for your order.');
                        document.getElementById('order_name').focus();
                        return;
                    }

                    // Get door and screen counts
                    var doorCount = parseInt(document.getElementById('door_count').value) || 0;
                    var screenCount = parseInt(document.getElementById('screen_count').value) || 0;

                    // Check if at least one category has items
                    if (doorCount &lt;= 0 &amp;&amp; screenCount &lt;= 0) {
                        alert('Please select at least 1 door or screen to continue.');
                        return;
                    }

                    // Determine which category to use based on which has more items
                    var category;
                    var count;

                    if (doorCount &gt; 0 &amp;&amp; (doorCount &gt;= screenCount || screenCount &lt;= 0)) {
                        category = 'door';
                        count = doorCount;
                    } else {
                        category = 'screen';
                        count = screenCount;
                    }

                    // Navigate to the configure grid page with both counts
                    var projectId = document.getElementById('hidden_project_id').value || '0';
                    var url = '/my/configure_grid?category=' + category +
                        '&amp;count=' + count +
                        '&amp;door_count=' + doorCount +
                        '&amp;screen_count=' + screenCount +
                        '&amp;project_id=' + projectId +
                        '&amp;order_name=' + encodeURIComponent(orderName);

                    console.log("Navigating to: " + url);
                    window.location.href = url;
                }

                // Initialize when DOM is loaded
                document.addEventListener('DOMContentLoaded', function() {
                    console.log("Portal Start Order: DOM loaded");

                    // Set up event listener for the continue button as a backup
                    var continueBtn = document.getElementById('continue-btn');
                    if (continueBtn) {
                        continueBtn.addEventListener('click', function() {
                            continueWithOrder();
                        });
                    }
                });
            </script>
        </t>
    </template>
</odoo>
