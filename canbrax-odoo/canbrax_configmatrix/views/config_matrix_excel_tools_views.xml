<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Excel Importer Form View -->
    <record id="view_config_matrix_excel_importer_form" model="ir.ui.view">
        <field name="name">config.matrix.excel.importer.form</field>
        <field name="model">config.matrix.excel.importer</field>
        <field name="arch" type="xml">
            <form string="Excel Matrix Importer">
                <header>
                    <button name="action_import_excel" 
                            string="Import Excel File" 
                            type="object" 
                            class="btn-primary"
                            invisible="context.get('show_results', False)"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name" placeholder="Import Name"/></h1>
                    </div>
                    
                    <group invisible="context.get('show_results', False)">
                        <group string="Excel File">
                            <field name="excel_file" filename="filename"/>
                            <field name="filename" invisible="1"/>
                        </group>
                        <group string="Category Settings">
                            <field name="target_category_id"/>
                            <field name="auto_create_categories"/>
                        </group>
                        <group string="Import Options">
                            <field name="product_template_id"/>
                            <field name="import_mode"/>
                        </group>
                        <group string="Processing Options">
                            <field name="skip_empty_cells"/>
                            <field name="validate_data"/>
                            <field name="create_backup"/>
                        </group>
                    </group>
                    
                    <!-- Results Section -->
                    <group invisible="not context.get('show_results', False)">
                        <group string="Import Results">
                            <field name="matrices_created" readonly="1"/>
                            <field name="matrices_updated" readonly="1"/>
                            <field name="errors_count" readonly="1"/>
                        </group>
                    </group>
                    
                    <group invisible="not context.get('show_results', False)">
                        <field name="import_log" widget="text" readonly="1" nolabel="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Excel Exporter Form View -->
    <record id="view_config_matrix_excel_exporter_form" model="ir.ui.view">
        <field name="name">config.matrix.excel.exporter.form</field>
        <field name="model">config.matrix.excel.exporter</field>
        <field name="arch" type="xml">
            <form string="Excel Matrix Exporter">
                <header>
                    <button name="action_export_excel" 
                            string="Export to Excel" 
                            type="object" 
                            class="btn-primary"
                            invisible="context.get('show_download', False)"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name" placeholder="Export Name"/></h1>
                    </div>
                    
                    <group invisible="context.get('show_download', False)">
                        <group string="Export Scope">
                            <field name="export_type"/>
                            <field name="template_id" invisible="export_type != 'template'"/>
                            <field name="category_id" invisible="export_type != 'category'"/>
                        </group>
                        <group string="Format Options">
                            <field name="include_metadata"/>
                            <field name="include_empty_cells"/>
                            <field name="separate_sheets"/>
                        </group>
                    </group>
                    
                    <!-- Download Section -->
                    <group invisible="not context.get('show_download', False)">
                        <group string="Download Excel File">
                            <field name="excel_file" filename="filename" readonly="1"/>
                            <field name="filename" readonly="1"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_config_matrix_excel_importer" model="ir.actions.act_window">
        <field name="name">Import Excel Matrices</field>
        <field name="res_model">config.matrix.excel.importer</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <record id="action_config_matrix_excel_exporter" model="ir.actions.act_window">
        <field name="name">Export Excel Matrices</field>
        <field name="res_model">config.matrix.excel.exporter</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
