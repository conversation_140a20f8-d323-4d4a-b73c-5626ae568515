<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Simple Test Wizard Form View -->
    <record id="view_config_matrix_simple_test_wizard_form" model="ir.ui.view">
        <field name="name">config.matrix.simple.test.wizard.form</field>
        <field name="model">config.matrix.simple.test.wizard</field>
        <field name="arch" type="xml">
            <form string="Simple Matrix Test">
                <sheet>
                    <div class="oe_title">
                        <h1>Simple Matrix Test</h1>
                    </div>
                    <group>
                        <div class="alert alert-info" role="alert">
                            <p><strong>Simple Test - No Product Creation</strong></p>
                            <p>This test will create sample matrices using any existing product in your system:</p>
                            <ul>
                                <li>✅ <strong>Test Price Matrix</strong> - 4x4 grid with sample prices</li>
                                <li>✅ <strong>Test Labor Matrix</strong> - 4x4 grid with sample labor times</li>
                                <li>✅ <strong>Automatic testing</strong> - verifies price lookup works</li>
                                <li>✅ <strong>No product creation</strong> - uses existing products only</li>
                            </ul>
                            <p><strong>Expected result:</strong> Price lookup for 1200x800 → $211.48</p>
                        </div>
                    </group>
                </sheet>
                <footer>
                    <button string="Create Test Matrices" name="action_create_test_matrices" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Simple Test Action -->
    <record id="action_config_matrix_simple_test_wizard" model="ir.actions.act_window">
        <field name="name">Simple Matrix Test</field>
        <field name="res_model">config.matrix.simple.test.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
