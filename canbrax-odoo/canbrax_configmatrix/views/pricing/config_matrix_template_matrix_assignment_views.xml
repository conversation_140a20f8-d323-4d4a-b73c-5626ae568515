<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Template Matrix Assignment Tree View -->
    <record id="view_config_matrix_template_matrix_assignment_tree" model="ir.ui.view">
        <field name="name">config.matrix.template.matrix.assignment.tree</field>
        <field name="model">config.matrix.template.matrix.assignment</field>
        <field name="arch" type="xml">
            <list string="Matrix Assignments" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="matrix_type"/>
                <field name="name"/>
                <field name="price_matrix_id" invisible="matrix_type != 'price'"/>
                <field name="labor_matrix_id" invisible="matrix_type != 'labor'"/>
                <field name="height_field_id"/>
                <field name="width_field_id"/>
                <field name="active"/>
                <field name="product_template_id" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Template Matrix Assignment Form View -->
    <record id="view_config_matrix_template_matrix_assignment_form" model="ir.ui.view">
        <field name="name">config.matrix.template.matrix.assignment.form</field>
        <field name="model">config.matrix.template.matrix.assignment</field>
        <field name="arch" type="xml">
            <form string="Matrix Assignment">
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="template_id"/>
                            <field name="matrix_type"/>
                            <field name="price_matrix_id" invisible="matrix_type != 'price'"/>
                            <field name="labor_matrix_id" invisible="matrix_type != 'labor'"/>
                        </group>
                        <group>
                            <field name="sequence"/>
                            <field name="active"/>
                            <field name="product_template_id" invisible="1"/>
                        </group>
                    </group>
                    <group string="Dimension Field Mapping">
                        <div class="alert alert-info" role="alert">
                            <p><strong>Dimension Field Mapping</strong></p>
                            <p>Select which fields in your configuration template provide the height and width dimensions for matrix lookup.</p>
                        </div>
                        <group>
                            <field name="height_field_id"/>
                            <field name="width_field_id"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Template Matrix Assignment Action -->
    <record id="action_config_matrix_template_matrix_assignment" model="ir.actions.act_window">
        <field name="name">Matrix Assignments</field>
        <field name="res_model">config.matrix.template.matrix.assignment</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Assign matrices to configuration templates!
            </p>
            <p>
                Matrix assignments link price and labor time matrices to configuration templates.
            </p>
        </field>
    </record>
</odoo>
