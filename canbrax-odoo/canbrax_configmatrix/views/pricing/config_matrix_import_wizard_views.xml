<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Matrix Import Wizard Form View -->
    <record id="view_config_matrix_import_wizard_form" model="ir.ui.view">
        <field name="name">config.matrix.import.wizard.form</field>
        <field name="model">config.matrix.import.wizard</field>
        <field name="arch" type="xml">
            <form string="Import Price/Labor Matrix">
                <sheet>
                    <div class="oe_title">
                        <h1>Import Matrix from CSV/Excel</h1>
                    </div>
                    <group>
                        <group>
                            <field name="import_type"/>
                            <field name="matrix_name"/>
                            <field name="product_template_id" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="file_data" filename="file_name"/>
                            <field name="file_name" invisible="1"/>
                        </group>
                    </group>
                    <group string="Labor Matrix Settings" invisible="import_type != 'labor'">
                        <group>
                            <field name="operation_type"/>
                            <field name="operation_name" invisible="operation_type != 'custom'"/>
                        </group>
                        <group>
                            <field name="time_unit"/>
                        </group>
                    </group>
                    <group string="Preview" invisible="not has_preview">
                        <field name="preview_data" widget="text" nolabel="1"/>
                        <field name="has_preview" invisible="1"/>
                    </group>
                </sheet>
                <footer>
                    <button string="Preview" name="action_preview" type="object" class="btn-secondary"/>
                    <button string="Import" name="action_import" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Excel Import Wizard Form View -->
    <record id="view_config_matrix_excel_import_wizard_form" model="ir.ui.view">
        <field name="name">config.matrix.excel.import.wizard.form</field>
        <field name="model">config.matrix.excel.import.wizard</field>
        <field name="arch" type="xml">
            <form string="Import Matrices from Excel">
                <sheet>
                    <div class="oe_title">
                        <h1>Import Multiple Matrices from Excel Workbook</h1>
                    </div>
                    <group>
                        <group>
                            <field name="product_template_id" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="file_data" filename="file_name"/>
                            <field name="file_name" invisible="1"/>
                        </group>
                    </group>
                    <group string="Sheet Analysis" invisible="not sheet_info">
                        <field name="sheet_info" widget="text" nolabel="1"/>
                    </group>
                </sheet>
                <footer>
                    <button string="Analyze File" name="action_analyze_file" type="object" class="btn-secondary"/>
                    <button string="Import All Sheets" name="action_import_sheets" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Matrix Import Actions -->
    <record id="action_config_matrix_import_wizard" model="ir.actions.act_window">
        <field name="name">Import Matrix</field>
        <field name="res_model">config.matrix.import.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <record id="action_config_matrix_excel_import_wizard" model="ir.actions.act_window">
        <field name="name">Import Excel Workbook</field>
        <field name="res_model">config.matrix.excel.import.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
