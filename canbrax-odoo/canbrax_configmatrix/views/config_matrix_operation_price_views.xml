<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Operation Price Tree View -->
    <record id="view_config_matrix_operation_price_tree" model="ir.ui.view">
        <field name="name">config.matrix.operation.price.tree</field>
        <field name="model">config.matrix.operation.price</field>
        <field name="arch" type="xml">
            <list string="Fixed Price Table" default_order="sequence,description" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="description" string="Description"/>
                <field name="sub_description" string="Sub-Description"/>
                <field name="unit_type" string="Unit"/>
                <field name="cost_price" string="Cost" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                <field name="sale_price" string="Sale Price" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                <field name="markup_percentage" string="Markup %" widget="percentage"/>
                <field name="category" string="Category"/>
                <field name="usage_count" string="Usage"/>
                <field name="active" invisible="1"/>
                <field name="currency_id" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Operation Price Form View -->
    <record id="view_config_matrix_operation_price_form" model="ir.ui.view">
        <field name="name">config.matrix.operation.price.form</field>
        <field name="model">config.matrix.operation.price</field>
        <field name="arch" type="xml">
            <form string="Operation Price">
                <sheet>
                    <div class="oe_title">
                        <h1><field name="description" placeholder="Operation Description"/></h1>
                        <h2><field name="sub_description" placeholder="Additional details..."/></h2>
                    </div>

                    <group>
                        <group>
                            <field name="sequence"/>
                            <field name="category"/>
                            <field name="unit_type"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company"/>
                            <field name="usage_count" readonly="1"/>
                        </group>
                    </group>

                    <group string="Pricing Information">
                        <group>
                            <field name="cost_price" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            <field name="sale_price" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                        </group>
                        <group>
                            <field name="markup_percentage" readonly="1" widget="percentage"/>
                            <field name="profit_margin" readonly="1" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                        </group>
                    </group>

                    <group string="Work Centers">
                        <field name="workcenter_ids" widget="many2many_tags" nolabel="1"/>
                    </group>

                    <group string="Notes">
                        <field name="notes" nolabel="1" widget="text"/>
                    </group>

                    <!-- Hidden fields -->
                    <field name="currency_id" invisible="1"/>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Operation Price Search View -->
    <record id="view_config_matrix_operation_price_search" model="ir.ui.view">
        <field name="name">config.matrix.operation.price.search</field>
        <field name="model">config.matrix.operation.price</field>
        <field name="arch" type="xml">
            <search string="Search Operation Prices">
                <field name="description"/>
                <field name="sub_description"/>
                <field name="category"/>
                <field name="workcenter_ids"/>
                <separator/>
                <filter string="Labour" name="labour" domain="[('category', '=', 'labour')]"/>
                <filter string="Fixed Time" name="fixed_time" domain="[('category', '=', 'fixed_time')]"/>
                <filter string="Material Handling" name="material" domain="[('category', '=', 'material')]"/>
                <filter string="Setup/Preparation" name="setup" domain="[('category', '=', 'setup')]"/>
                <filter string="Finishing" name="finishing" domain="[('category', '=', 'finishing')]"/>
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="High Usage" name="high_usage" domain="[('usage_count', '>', 5)]"/>
                <filter string="Unused" name="unused" domain="[('usage_count', '=', 0)]"/>
                <group expand="0" string="Group By">
                    <filter string="Category" name="group_category" context="{'group_by': 'category'}"/>
                    <filter string="Unit Type" name="group_unit_type" context="{'group_by': 'unit_type'}"/>
                    <filter string="Work Center" name="group_workcenter" context="{'group_by': 'workcenter_ids'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action moved to menu_views.xml to ensure proper loading order -->

</odoo>
