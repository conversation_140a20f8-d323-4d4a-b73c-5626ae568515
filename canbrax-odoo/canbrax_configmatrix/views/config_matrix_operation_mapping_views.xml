<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Field Operation Mapping Form View -->
    <record id="view_config_matrix_field_operation_mapping_form" model="ir.ui.view">
        <field name="name">config.matrix.field.operation.mapping.form</field>
        <field name="model">config.matrix.field.operation.mapping</field>
        <field name="arch" type="xml">
            <form string="Field Operation Mapping">
                <sheet>
                    <div class="oe_title">
                        <h1><field name="operation_name"/></h1>
                        <h2><field name="field_name" readonly="1"/></h2>
                    </div>

                    <group>
                        <group>
                            <field name="field_id" readonly="1"/>
                            <field name="workcenter_id" required="1"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="name"/>
                            <field name="matrix_id" readonly="1"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Operation Details" name="operation_details">
                            <group>
                                <group>
                                    <field name="duration_formula" widget="code" options="{'mode': 'python'}"
                                           help="Python expression to calculate operation duration in minutes"/>
                                    <div>
                                        <button name="action_open_operation_builder" type="object"
                                                string="Add Duration" class="btn-primary"
                                                icon="fa-magic"/>
                                    </div>
                                    <field name="condition" widget="code" options="{'mode': 'python'}"
                                           help="Additional condition for this operation"/>
                                </group>
                                <group>
                                    <field name="setup_time"/>
                                    <field name="cleanup_time"/>
                                </group>
                            </group>
                        </page>

                        <page string="Worksheet" name="worksheet">
                            <group>
                                <field name="worksheet_type"/>
                            </group>
                            <group string="Worksheet Content" invisible="worksheet_type == 'pdf'">
                                <field name="worksheet_content" widget="text" nolabel="1"/>
                            </group>
                        </page>

                        <page string="Notes" name="notes">
                            <field name="notes" widget="text" nolabel="1"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Field Operation Mapping List View -->
    <record id="view_config_matrix_field_operation_mapping_list" model="ir.ui.view">
        <field name="name">config.matrix.field.operation.mapping.list</field>
        <field name="model">config.matrix.field.operation.mapping</field>
        <field name="arch" type="xml">
            <list string="Field Operation Mappings" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="operation_name" string="Operation"/>
                <field name="workcenter_id" string="Work Center"/>
                <field name="duration_formula" string="Duration"/>
                <field name="condition" string="Condition"/>
                <!-- Hidden fields for functionality -->
                <field name="field_id" column_invisible="1"/>
                <!-- Action buttons -->
                <button name="action_open_operation_builder" type="object"
                        string="Add Duration" icon="fa-magic"
                        class="btn-link text-primary" title="Build duration formula"/>
            </list>
        </field>
    </record>

    <!-- Option Operation Mapping Form View -->
    <record id="view_config_matrix_option_operation_mapping_form" model="ir.ui.view">
        <field name="name">config.matrix.option.operation.mapping.form</field>
        <field name="model">config.matrix.option.operation.mapping</field>
        <field name="arch" type="xml">
            <form string="Option Operation Mapping">
                <sheet>
                    <div class="oe_title">
                        <h1><field name="operation_name"/></h1>
                        <h2><field name="option_name" readonly="1"/></h2>
                    </div>

                    <group>
                        <group>
                            <field name="option_id" readonly="1"/>
                            <field name="workcenter_id" required="1"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="name"/>
                            <field name="matrix_id" readonly="1"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Operation Details" name="operation_details">
                            <group>
                                <group>
                                    <field name="duration_formula" widget="code" options="{'mode': 'python'}"
                                           help="Python expression to calculate operation duration in minutes"/>
                                    <div>
                                        <button name="action_open_operation_builder" type="object"
                                                string="Add Duration" class="btn-primary"
                                                icon="fa-magic"/>
                                    </div>
                                    <field name="condition" widget="code" options="{'mode': 'python'}"
                                           help="Additional condition for this operation"/>
                                </group>
                                <group>
                                    <field name="setup_time"/>
                                    <field name="cleanup_time"/>
                                </group>
                            </group>
                        </page>

                        <page string="Worksheet" name="worksheet">
                            <group>
                                <field name="worksheet_type"/>
                            </group>
                            <group string="Worksheet Content" invisible="worksheet_type == 'pdf'">
                                <field name="worksheet_content" widget="text" nolabel="1"/>
                            </group>
                        </page>

                        <page string="Notes" name="notes">
                            <field name="notes" widget="text" nolabel="1"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Option Operation Mapping List View -->
    <record id="view_config_matrix_option_operation_mapping_list" model="ir.ui.view">
        <field name="name">config.matrix.option.operation.mapping.list</field>
        <field name="model">config.matrix.option.operation.mapping</field>
        <field name="arch" type="xml">
            <list string="Option Operation Mappings" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="operation_name" string="Operation"/>
                <field name="workcenter_id" string="Work Center"/>
                <field name="duration_formula" string="Duration"/>
                <field name="condition" string="Condition"/>
                <!-- Hidden fields for functionality -->
                <field name="option_id" column_invisible="1"/>
                <!-- Action buttons -->
                <button name="action_open_operation_builder" type="object"
                        string="Add Duration" icon="fa-magic"
                        class="btn-link text-primary" title="Build duration formula"/>
            </list>
        </field>
    </record>

    <!-- Unified Operation Mapping Tree View -->
    <record id="view_config_matrix_operation_mapping_tree" model="ir.ui.view">
        <field name="name">config.matrix.operation.mapping.tree</field>
        <field name="model">config.matrix.operation.mapping</field>
        <field name="arch" type="xml">
            <list string="Operation Mappings">
                <field name="sequence" widget="handle"/>
                <field name="target_model"/>
                <field name="target_name"/>
                <field name="operation_name"/>
                <field name="workcenter_id"/>
                <field name="duration_formula"/>
                <field name="condition"/>
            </list>
        </field>
    </record>

    <!-- Unified Operation Mapping Form View -->
    <record id="view_config_matrix_operation_mapping_form" model="ir.ui.view">
        <field name="name">config.matrix.operation.mapping.form</field>
        <field name="model">config.matrix.operation.mapping</field>
        <field name="arch" type="xml">
            <form string="Operation Mapping">
                <sheet>
                    <div class="oe_title">
                        <h1><field name="operation_name"/></h1>
                        <h2><field name="target_name" readonly="1"/></h2>
                    </div>

                    <group>
                        <group>
                            <field name="target_model"/>
                            <field name="target_id"/>
                            <field name="workcenter_id" required="1"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="name"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Operation Details" name="operation_details">
                            <group>
                                <group>
                                    <field name="duration_formula" widget="code" options="{'mode': 'python'}"/>
                                    <div>
                                        <button name="action_open_operation_builder" type="object"
                                                string="Add Duration" class="btn-primary"
                                                icon="fa-magic"/>
                                    </div>
                                    <field name="condition" widget="code" options="{'mode': 'python'}"/>
                                </group>
                                <group>
                                    <field name="setup_time"/>
                                    <field name="cleanup_time"/>
                                </group>
                            </group>
                        </page>

                        <page string="Worksheet" name="worksheet">
                            <group>
                                <field name="worksheet_type"/>
                            </group>
                            <group string="Worksheet Content" invisible="worksheet_type == 'pdf'">
                                <field name="worksheet_content" widget="text" nolabel="1"/>
                            </group>
                        </page>

                        <page string="Notes" name="notes">
                            <field name="notes" widget="text" nolabel="1"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Unified Operation Mapping Action -->
    <record id="action_config_matrix_operation_mapping" model="ir.actions.act_window">
        <field name="name">All Operation Mappings</field>
        <field name="res_model">config.matrix.operation.mapping</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'group_by': ['target_model']}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No operation mappings found
            </p>
            <p>
                This view shows all operation mappings across all configuration templates.
                Operation mappings define which manufacturing operations are triggered when specific fields or options are selected.
                For easier management, edit operations directly from the field/option forms or use Operation Templates.
            </p>
        </field>
    </record>

    <!-- Menu Item for Operation Mappings - REMOVED as requested -->
    <!-- <menuitem id="menu_config_matrix_operation_mapping"
              name="All Operation Mappings"
              parent="menu_config_matrix_configurations_root"
              action="action_config_matrix_operation_mapping"
              sequence="15"/> -->

</odoo>
