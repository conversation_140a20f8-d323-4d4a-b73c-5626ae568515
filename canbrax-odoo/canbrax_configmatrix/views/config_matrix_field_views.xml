<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Field Tree View -->
    <record id="view_config_matrix_field_tree" model="ir.ui.view">
        <field name="name">config.matrix.field.tree</field>
        <field name="model">config.matrix.field</field>
        <field name="arch" type="xml">
            <list string="Fields">
                <field name="sequence" widget="handle"/>
                <field name="question_number" string="Question #"/>
                <field name="name"/>
                <field name="matrix_id" width="200"/>
                <field name="field_type"/>
                <field name="has_multiple_components" string="Multi Components"/>
                <button name="action_test_visibility_condition" type="object"
                        string="Test Condition" class="btn-sm btn-secondary"
                        invisible="[('visibility_condition', '=', False)]"/>
                <button name="action_convert_visibility_condition" type="object"
                        string="Convert" class="btn-sm btn-secondary"
                        invisible="[('visibility_condition', '=', False)]"/>
            </list>
        </field>
    </record>

    <!-- Field Form View -->
    <record id="view_config_matrix_field_form" model="ir.ui.view">
        <field name="name">config.matrix.field.form</field>
        <field name="model">config.matrix.field</field>
        <field name="arch" type="xml">
            <form string="Configuration Field">
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name" placeholder="Field Label"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="section_id" options="{'no_create': True}"/>
                            <field name="technical_name"/>
                            <field name="sequence"/>
                            <field name="question_number" readonly="1"/>
                        </group>
                        <group>
                            <field name="field_type"/>
                            <field name="template_state" column_invisible="1"/>
                            <field name="has_multiple_components" readonly="1"/>
                            <field name="is_searchable_dropdown" invisible="field_type != 'selection'"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Field Options" name="field_options">
                            <!-- Text field options -->
                            <group string="Text Options" invisible="field_type != 'text'">
                                <field name="pattern" invisible="field_type != 'text'"/>
                            </group>

                            <!-- Number field options -->
                            <group string="Number Options" invisible="field_type != 'number'">
                                <field name="decimal_precision" invisible="field_type != 'number'"/>
                                <field name="uom_id" invisible="field_type != 'number'" options="{'no_create': True}"/>
                            </group>

                            <!-- Boolean field options -->
                            <group string="Boolean Options" invisible="field_type != 'boolean'">
                                <field name="boolean_true_label" invisible="field_type != 'boolean'"/>
                                <field name="boolean_false_label" invisible="field_type != 'boolean'"/>
                            </group>

                            <!-- Selection field options -->
                            <group string="Selection Options" invisible="field_type != 'selection'">
                                <field name="option_ids" invisible="field_type != 'selection'">
                                    <list string="Options" editable="bottom">
                                        <field name="sequence" widget="handle"/>
                                        <field name="name"/>
                                        <field name="value"/>
                                        <field name="component_product_id"/>
                                        <field name="quantity_formula" string="Quantity" width="80px"/>
                                        <field name="has_multiple_components" string="Multi" readonly="1"/>
                                        <button name="action_manage_component_mappings" type="object"
                                                string="Manage Components" class="btn-sm btn-primary"
                                                title="Manage multiple component mappings"/>
                                        <button name="action_view_option_visibility_conditions" type="object"
                                                string="Add Visibility" class="btn-sm btn-secondary"
                                                icon="fa-plus" title="Add visibility condition for this option"/>
                                        <button name="action_view_option_visibility_conditions" type="object"
                                                string="View Conditions" class="btn-sm btn-secondary"
                                                icon="fa-eye" title="View visibility conditions for this option"/>
                                    </list>
                                </field>
                            </group>
                        </page>

                        <page string="Components" name="components">
                            <!-- Component Mappings -->
                            <div class="alert alert-info mb-3" role="alert">
                                <h5><i class="fa fa-cogs me-2"></i>Component Mappings</h5>
                                <p class="mb-0">Configure which products should be included in the Bill of Materials when this field is used. You can map multiple components with different quantity calculations.</p>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Current Component Mappings</h6>
                                <button name="action_manage_component_mappings" type="object"
                                        string="Manage Component Mappings" class="btn btn-primary"
                                        icon="fa-cogs"/>
                            </div>

                            <field name="component_mapping_ids" nolabel="1">
                                <list string="Component Mappings" create="false" edit="false" delete="false">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="mapping_type"/>
                                    <field name="component_product_id" string="Static Product"
                                           readonly="mapping_type != 'static'"/>
                                    <field name="base_component" string="Base Component"
                                           readonly="mapping_type != 'dynamic_match'"/>
                                    <field name="quantity_formula"/>
                                    <field name="condition"/>
                                </list>
                            </field>

                            <div class="mt-3" invisible="component_mapping_ids">
                                <div class="text-center text-muted py-4">
                                    <i class="fa fa-info-circle fa-2x mb-2"></i>
                                    <p>No component mappings configured yet.</p>
                                    <p class="small">Click "Manage Component Mappings" above to add components.</p>
                                </div>
                            </div>
                        </page>

                        <page string="Operations" name="operations">
                            <!-- Operation Mappings -->
                            <div class="alert alert-info mb-3" role="alert">
                                <h5><i class="fa fa-cogs me-2"></i>Operation Mappings</h5>
                                <p class="mb-0">Configure which manufacturing operations should be included in the routing when this field is used. You can map multiple operations with different work centers and duration calculations.</p>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Current Operation Mappings</h6>
                                <button name="action_open_operation_builder" type="object"
                                        string="Add Operation" class="btn btn-primary"
                                        icon="fa-plus"/>
                            </div>

                            <field name="operation_mapping_ids" nolabel="1">
                                <list string="Operation Mappings" create="false" edit="false" delete="true">
                                    <field name="sequence" widget="handle"/>
                                    <field name="operation_name"/>
                                    <field name="workcenter_id"/>
                                    <field name="duration_formula"/>
                                    <field name="condition"/>
                                    <!-- Action buttons -->
                                    <button name="action_open_operation_builder" type="object"
                                            string="Add Duration" icon="fa-magic"
                                            class="btn-link text-primary" title="Build duration formula"/>
                                </list>
                            </field>

                            <div class="mt-3" invisible="operation_mapping_ids">
                                <div class="text-center text-muted py-4">
                                    <i class="fa fa-info-circle fa-2x mb-2"></i>
                                    <p>No operation mappings configured yet.</p>
                                    <p class="small">Click "Add Operation" above to add operations.</p>
                                </div>
                            </div>
                        </page>

                        <page string="Visibility" name="visibility">
                            <!-- Visibility Conditions -->
                            <div class="alert alert-info mb-3" role="alert">
                                <h5><i class="fa fa-eye me-2"></i>Visibility Conditions</h5>
                                <p class="mb-0">Configure when this field should be shown or hidden in the configurator based on values from other fields. You can create multiple conditions with different logic operators.</p>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Current Visibility Conditions</h6>
                                <div>
                                    <button name="action_view_field_visibility_conditions" type="object"
                                            string="Manage Visibility Conditions" class="btn btn-primary me-2"
                                            icon="fa-cogs"/>
                                    <button name="action_clear_visibility_conditions" type="object"
                                            string="Clear All" class="btn btn-secondary"
                                            invisible="[('visibility_condition', '=', False)]"
                                            icon="fa-trash"/>
                                </div>
                            </div>

                            <field name="visibility_condition_ids" nolabel="1" context="{'default_field_id': id, 'default_matrix_id': matrix_id}">
                                <list string="Visibility Conditions" create="false" edit="false" delete="false">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="logic_operator"/>
                                    <field name="generated_condition"/>
                                </list>
                            </field>

                            <div class="mt-3" invisible="visibility_condition_ids">
                                <div class="text-center text-muted py-4">
                                    <i class="fa fa-info-circle fa-2x mb-2"></i>
                                    <p>No visibility conditions configured yet.</p>
                                    <p class="small">Click "Manage Visibility Conditions" above to add conditions.</p>
                                </div>
                            </div>

                            <!-- Hidden fields for functionality -->
                            <field name="visibility_condition" column_invisible="1"/>
                            <field name="matrix_id" column_invisible="1"/>
                        </page>


                        <page string="Instructional Image" name="instructional_image">
                            <!-- Instructional Image -->
                            <div class="alert alert-info mb-3" role="alert">
                                <h5><i class="fa fa-image me-2"></i>Instructional Image</h5>
                                <p class="mb-0">Upload an image to help users understand this field. The image will appear below the question like helper text. If "Show in Preview Panel" is checked, it will also appear below the Product Preview in the left column when this question is focused.</p>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Image Configuration</h6>
                            </div>

                            <group>
                                <field name="instructional_image" widget="image" options="{'size': [500, 500]}"/>
                                <field name="instructional_image_filename" column_invisible="1"/>
                                <field name="show_in_preview"/>
                            </group>
                        </page>

                        <page string="Use Cases" name="use_cases">
                            <div class="row mt-2 mb-2">
                                <div class="col-12">
                                    <div class="alert alert-info" role="alert">
                                        <p>Configure how this field behaves in different use cases. Each use case can have different visibility, default values, and constraints.</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Table-like header -->
                            <div class="row mb-2 mt-3">
                                <div class="col-3">
                                    <span class="o_form_label fw-bold">Property</span>
                                </div>
                                <div class="col-3">
                                    <span class="o_form_label fw-bold">Check Measure</span>
                                    <div class="text-muted small">(Full Configuration)</div>
                                </div>
                                <div class="col-3">
                                    <span class="o_form_label fw-bold">Sales/Quoting</span>
                                    <div class="text-muted small">(Simplified)</div>
                                </div>
                                <div class="col-3">
                                    <span class="o_form_label fw-bold">Online Sales</span>
                                    <div class="text-muted small">(Limited)</div>
                                </div>
                            </div>

                            <!-- Visible -->
                            <div class="row mb-2 pb-2 border-bottom">
                                <div class="col-3">
                                    <span class="o_form_label">Visible</span>
                                </div>
                                <div class="col-3">
                                    <field name="check_measure_visible" nolabel="1"/>
                                </div>
                                <div class="col-3">
                                    <field name="sales_visible" nolabel="1"/>
                                </div>
                                <div class="col-3">
                                    <field name="online_visible" nolabel="1"/>
                                </div>
                            </div>

                            <!-- Default Value -->
                            <div class="row mb-2 pb-2 border-bottom">
                                <div class="col-3">
                                    <span class="o_form_label">Default Value</span>
                                </div>
                                <div class="col-3">
                                    <!-- Check Measure default value -->
                                    <field name="check_measure_default_value" nolabel="1"
                                           invisible="field_type == 'selection' or field_type == 'boolean'"/>
                                    <field name="check_measure_default_value" nolabel="1"
                                           widget="boolean"
                                           invisible="field_type != 'boolean'"/>
                                    <field name="check_measure_default_option_id" nolabel="1"
                                           invisible="field_type != 'selection'"/>
                                </div>
                                <div class="col-3">
                                    <!-- Sales default value -->
                                    <field name="sales_default_value" nolabel="1"
                                           invisible="field_type == 'selection' or field_type == 'boolean'"/>
                                    <field name="sales_default_value" nolabel="1"
                                           widget="boolean"
                                           invisible="field_type != 'boolean'"/>
                                    <field name="sales_default_option_id" nolabel="1"
                                           invisible="field_type != 'selection'"/>
                                </div>
                                <div class="col-3">
                                    <!-- Online default value -->
                                    <field name="online_default_value" nolabel="1"
                                           invisible="field_type == 'selection' or field_type == 'boolean'"/>
                                    <field name="online_default_value" nolabel="1"
                                           widget="boolean"
                                           invisible="field_type != 'boolean'"/>
                                    <field name="online_default_option_id" nolabel="1"
                                           invisible="field_type != 'selection'"/>
                                </div>
                            </div>

                            <!-- Dynamic Default Value Options -->
                            <div class="row mb-2 pb-2 border-bottom">
                                <div class="col-3">
                                    <span class="o_form_label">Dynamic Default Value</span>
                                    <div class="text-muted small">Use {variable} syntax for conditional defaults</div>
                                </div>
                                <div class="col-3">
                                    <field name="check_measure_use_dynamic_default" nolabel="1" widget="boolean"/>
                                    <field name="check_measure_dynamic_default_template" nolabel="1" widget="text"
                                           placeholder="{sws_wscrn_frame_colour == 'Western Red Cedar MZ306A4489V5' ? '11 mm' : ''}"
                                           invisible="not check_measure_use_dynamic_default"/>
                                </div>
                                <div class="col-3">
                                    <field name="sales_use_dynamic_default" nolabel="1" widget="boolean"/>
                                    <field name="sales_dynamic_default_template" nolabel="1" widget="text"
                                           placeholder="{sws_wscrn_frame_colour == 'Western Red Cedar MZ306A4489V5' ? '11 mm' : ''}"
                                           invisible="not sales_use_dynamic_default"/>
                                </div>
                                <div class="col-3">
                                    <field name="online_use_dynamic_default" nolabel="1" widget="boolean"/>
                                    <field name="online_dynamic_default_template" nolabel="1" widget="text"
                                           placeholder="{sws_wscrn_frame_colour == 'Western Red Cedar MZ306A4489V5' ? '11 mm' : ''}"
                                           invisible="not online_use_dynamic_default"/>
                                </div>
                            </div>

                            <!-- Help Text -->
                            <div class="row mb-2 pb-2 border-bottom">
                                <div class="col-3">
                                    <span class="o_form_label">Help Text</span>
                                </div>
                                <div class="col-3">
                                    <field name="check_measure_help_text" nolabel="1"/>
                                </div>
                                <div class="col-3">
                                    <field name="sales_help_text" nolabel="1"/>
                                </div>
                                <div class="col-3">
                                    <field name="online_help_text" nolabel="1"/>
                                </div>
                            </div>

                            <!-- Dynamic Help Text Options -->
                            <div class="row mb-2 pb-2 border-bottom">
                                <div class="col-3">
                                    <span class="o_form_label">Dynamic Help Text</span>
                                    <div class="text-muted small">Use {variable} syntax for real-time updates</div>
                                </div>
                                <div class="col-3">
                                    <field name="check_measure_use_dynamic_help" nolabel="1" widget="boolean"/>
                                    <field name="check_measure_dynamic_help_template" nolabel="1" widget="text"
                                           placeholder="Your door height is {door_height || '[not set]'}mm, therefore..."
                                           invisible="not check_measure_use_dynamic_help"/>
                                </div>
                                <div class="col-3">
                                    <field name="sales_use_dynamic_help" nolabel="1" widget="boolean"/>
                                    <field name="sales_dynamic_help_template" nolabel="1" widget="text"
                                           placeholder="Your door height is {door_height || '[not set]'}mm, therefore..."
                                           invisible="not sales_use_dynamic_help"/>
                                </div>
                                <div class="col-3">
                                    <field name="online_use_dynamic_help" nolabel="1" widget="boolean"/>
                                    <field name="online_dynamic_help_template" nolabel="1" widget="text"
                                           placeholder="Your door height is {door_height || '[not set]'}mm, therefore..."
                                           invisible="not online_use_dynamic_help"/>
                                </div>
                            </div>

                            <!-- Error Message -->
                            <div class="row mb-2 pb-2 border-bottom">
                                <div class="col-3">
                                    <span class="o_form_label">Error Message</span>
                                    <div class="text-muted small">Message shown when error condition is met</div>
                                </div>
                                <div class="col-3">
                                    <field name="check_measure_error_text" nolabel="1" widget="text" placeholder="Error message for Check Measure"/>
                                </div>
                                <div class="col-3">
                                    <field name="sales_error_text" nolabel="1" widget="text" placeholder="Error message for Sales"/>
                                </div>
                                <div class="col-3">
                                    <field name="online_error_text" nolabel="1" widget="text" placeholder="Error message for Online"/>
                                </div>
                            </div>

                            <!-- Error Condition -->
                            <div class="row mb-2 pb-2 border-bottom">
                                <div class="col-3">
                                    <span class="o_form_label">Error Condition</span>
                                    <div class="text-muted small">Python expression that determines when to show error</div>
                                </div>
                                <div class="col-3">
                                    <field name="check_measure_error_condition" nolabel="1" widget="text" placeholder="e.g., width &gt; 2000 or height &lt; 500"/>
                                </div>
                                <div class="col-3">
                                    <field name="sales_error_condition" nolabel="1" widget="text" placeholder="e.g., width &gt; 2000 or height &lt; 500"/>
                                </div>
                                <div class="col-3">
                                    <field name="online_error_condition" nolabel="1" widget="text" placeholder="e.g., width &gt; 2000 or height &lt; 500"/>
                                </div>
                            </div>

                            <!-- Dynamic Error Message Options -->
                            <div class="row mb-2 pb-2 border-bottom">
                                <div class="col-3">
                                    <span class="o_form_label">Dynamic Error Message</span>
                                    <div class="text-muted small">Use {variable} syntax for real-time error messages</div>
                                </div>
                                <div class="col-3">
                                    <field name="check_measure_use_dynamic_error" nolabel="1" widget="boolean"/>
                                    <field name="check_measure_dynamic_error_template" nolabel="1" widget="text"
                                           placeholder="Width {width}mm exceeds maximum of 2000mm"
                                           invisible="not check_measure_use_dynamic_error"/>
                                </div>
                                <div class="col-3">
                                    <field name="sales_use_dynamic_error" nolabel="1" widget="boolean"/>
                                    <field name="sales_dynamic_error_template" nolabel="1" widget="text"
                                           placeholder="Width {width}mm exceeds maximum of 2000mm"
                                           invisible="not sales_use_dynamic_error"/>
                                </div>
                                <div class="col-3">
                                    <field name="online_use_dynamic_error" nolabel="1" widget="boolean"/>
                                    <field name="online_dynamic_error_template" nolabel="1" widget="text"
                                           placeholder="Width {width}mm exceeds maximum of 2000mm"
                                           invisible="not online_use_dynamic_error"/>
                                </div>
                            </div>

                            <!-- Min Value (only for number fields) -->
                            <div class="row mb-2 pb-2 border-bottom" invisible="field_type != 'number'">
                                <div class="col-3">
                                    <span class="o_form_label">Minimum Value</span>
                                    <div class="text-muted small">Number or expression (e.g., "300" or "field &gt; 100 ? 200 : 150")</div>
                                </div>
                                <div class="col-3">
                                    <field name="check_measure_min_value" nolabel="1" widget="text" placeholder="e.g., 300"/>
                                </div>
                                <div class="col-3">
                                    <field name="sales_min_value" nolabel="1" widget="text" placeholder="e.g., 300"/>
                                </div>
                                <div class="col-3">
                                    <field name="online_min_value" nolabel="1" widget="text" placeholder="e.g., 300"/>
                                </div>
                            </div>

                            <!-- Max Value (only for number fields) -->
                            <div class="row mb-2 pb-2 border-bottom" invisible="field_type != 'number'">
                                <div class="col-3">
                                    <span class="o_form_label">Maximum Value</span>
                                    <div class="text-muted small">Number or expression (e.g., "2000" or "door_height &gt; 2510 ? 1310 : 2000")</div>
                                </div>
                                <div class="col-3">
                                    <field name="check_measure_max_value" nolabel="1" widget="text" placeholder="e.g., door_height &gt; 2510 ? 1310 : 2000"/>
                                </div>
                                <div class="col-3">
                                    <field name="sales_max_value" nolabel="1" widget="text" placeholder="e.g., door_height &gt; 2510 ? 1310 : 2000"/>
                                </div>
                                <div class="col-3">
                                    <field name="online_max_value" nolabel="1" widget="text" placeholder="e.g., door_height &gt; 2510 ? 1310 : 2000"/>
                                </div>
                            </div>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Field Search View -->
    <record id="view_config_matrix_field_search" model="ir.ui.view">
        <field name="name">config.matrix.field.search</field>
        <field name="model">config.matrix.field</field>
        <field name="arch" type="xml">
            <search string="Search Fields">
                <field name="name"/>
                <field name="technical_name"/>
                <field name="section_id"/>
                <field name="matrix_id" width="200"/>
                <separator/>
                <filter string="With Visibility Condition" name="with_condition" domain="[('visibility_condition', '!=', False)]"/>
                <filter string="With Component" name="with_component" domain="[('component_product_id', '!=', False)]"/>
                <filter string="With Multiple Components" name="with_multiple_components" domain="[('has_multiple_components', '=', True)]"/>
                <group expand="0" string="Group By">
                    <filter string="Section" name="group_by_section" context="{'group_by': 'section_id'}"/>
                    <filter string="Template" name="group_by_template" context="{'group_by': 'matrix_id'}"/>
                    <filter string="Field Type" name="group_by_type" context="{'group_by': 'field_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Field Action -->
    <record id="action_config_matrix_field" model="ir.actions.act_window">
        <field name="name">Configuration Fields</field>
        <field name="res_model">config.matrix.field</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'group_by': 'matrix_id'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first configuration field!
            </p>
            <p>
                Fields are the individual questions in the configuration process.
            </p>
        </field>
    </record>
</odoo>
