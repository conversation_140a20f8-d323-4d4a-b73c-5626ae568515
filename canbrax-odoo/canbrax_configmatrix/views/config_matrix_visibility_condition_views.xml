<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Field Visibility Condition Tree View -->
    <record id="view_config_matrix_visibility_condition_tree" model="ir.ui.view">
        <field name="name">config.matrix.visibility.condition.tree</field>
        <field name="model">config.matrix.visibility.condition</field>
        <field name="arch" type="xml">
            <list string="Field Visibility Conditions">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="field_id"/>
                <field name="matrix_id"/>
                <field name="condition_type"/>
                <field name="dependent_field_id"/>
                <field name="operator"/>
                <field name="logic_operator"/>
                <field name="generated_condition"/>
            </list>
        </field>
    </record>

    <!-- Field Visibility Condition Form View -->
    <record id="view_config_matrix_visibility_condition_form" model="ir.ui.view">
        <field name="name">config.matrix.visibility.condition.form</field>
        <field name="model">config.matrix.visibility.condition</field>
        <field name="arch" type="xml">
            <form string="Field Visibility Condition">
                <sheet>
                    <div class="oe_title">
                        <h1><field name="name" readonly="1"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="field_id"/>
                            <field name="matrix_id"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="condition_type"/>
                            <field name="logic_operator"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Field Condition" invisible="condition_type != 'field'">
                            <group>
                                <group>
                                    <field name="dependent_field_id" required="condition_type == 'field'"/>
                                    <field name="operator" required="condition_type == 'field'"/>
                                </group>
                                <group>
                                    <field name="value_type" invisible="1"/>
                                    <field name="text_value" invisible="value_type != 'text'" required="value_type == 'text' and operator not in ['is_set', 'is_not_set'] and condition_type != 'expression'"/>
                                    <field name="number_value" invisible="value_type != 'number'" required="value_type == 'number' and operator not in ['is_set', 'is_not_set'] and condition_type != 'expression'"/>
                                    <field name="boolean_value" invisible="value_type != 'boolean'" required="value_type == 'boolean' and operator not in ['is_set', 'is_not_set'] and condition_type != 'expression'"/>
                                    <field name="selection_value" invisible="value_type != 'selection'" required="value_type == 'selection' and operator not in ['is_set', 'is_not_set', 'in', 'not in'] and condition_type != 'expression'"/>
                                    <field name="list_value" invisible="value_type != 'selection' or operator not in ['in', 'not in']" required="value_type == 'selection' and operator in ['in', 'not in'] and condition_type != 'expression'"/>
                                    <field name="selected_option_ids" invisible="value_type != 'selection' or operator not in ['in', 'not in']" required="value_type == 'selection' and operator in ['in', 'not in'] and condition_type != 'expression'" widget="many2many_tags"/>
                                </group>
                            </group>
                        </page>
                        <page string="Custom Expression" invisible="condition_type != 'expression'">
                            <group>
                                <field name="expression" required="condition_type == 'expression'" placeholder="e.g., door_type == 'sliding' and width > 1000"/>
                            </group>
                        </page>
                        <page string="Generated Condition">
                            <group>
                                <field name="generated_condition" readonly="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Field Visibility Condition Search View -->
    <record id="view_config_matrix_visibility_condition_search" model="ir.ui.view">
        <field name="name">config.matrix.visibility.condition.search</field>
        <field name="model">config.matrix.visibility.condition</field>
        <field name="arch" type="xml">
            <search string="Search Field Visibility Conditions">
                <field name="name"/>
                <field name="field_id"/>
                <field name="matrix_id"/>
                <field name="dependent_field_id"/>
                <separator/>
                <filter string="Field Conditions" name="field_conditions" domain="[('condition_type', '=', 'field')]"/>
                <filter string="Custom Expressions" name="custom_expressions" domain="[('condition_type', '=', 'expression')]"/>
                <filter string="AND Logic" name="and_logic" domain="[('logic_operator', '=', 'and')]"/>
                <filter string="OR Logic" name="or_logic" domain="[('logic_operator', '=', 'or')]"/>
                <group expand="0" string="Group By">
                    <filter string="Template" name="group_by_template" context="{'group_by': 'matrix_id'}"/>
                    <filter string="Field" name="group_by_field" context="{'group_by': 'field_id'}"/>
                    <filter string="Dependent Field" name="group_by_dependent_field" context="{'group_by': 'dependent_field_id'}"/>
                    <filter string="Condition Type" name="group_by_condition_type" context="{'group_by': 'condition_type'}"/>
                    <filter string="Logic Operator" name="group_by_logic_operator" context="{'group_by': 'logic_operator'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Field Visibility Condition Action -->
    <record id="action_config_matrix_visibility_condition" model="ir.actions.act_window">
        <field name="name">Field Visibility Conditions</field>
        <field name="res_model">config.matrix.visibility.condition</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'group_by': ['matrix_id', 'field_id']}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No field visibility conditions found
            </p>
            <p>
                Visibility conditions determine when fields are shown or hidden in the configurator.
            </p>
        </field>
    </record>
</odoo>
