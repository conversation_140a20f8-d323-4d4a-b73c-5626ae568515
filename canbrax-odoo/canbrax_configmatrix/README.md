# ConfigMatrix - Dynamic Product Configuration & BOM Generation

ConfigMatrix is an advanced product configuration module for Odoo 18 that allows you to create dynamic, rule-based product configurators with automatic BOM generation.

## Key Features

- **Dynamic Configuration Interface**: Intuitive Q&A format that dynamically shows/hides questions based on previous answers
- **Automatic BOM Generation**: Creates accurate Bills of Materials based on configuration choices
- **Visual Previews**: Shows real-time visual representations of configured products
- **Sales Integration**: Seamlessly integrated with Odoo's sales workflow
- **Manufacturing Integration**: Configured BOMs flow directly to manufacturing orders
- **Reusable Configurations**: Save and reuse configurations for repeat orders
- **Admin Interface**: Comprehensive interface for creating and managing configuration templates

## Documentation

For detailed information, please refer to the following documentation:

- [Overview](docs/00_OVERVIEW.md): High-level overview and concepts
- [Data Models](docs/01_DATA_MODELS.md): Database structure and model relationships
- [User Interface](docs/02_USER_INTERFACE.md): Frontend interface details
- [BOM Generation](docs/03_BOM_GENERATION.md): How BOMs are generated from configurations
- [Admin Interface](docs/04_ADMIN_INTERFACE.md): Setting up and managing configuration templates
- [Sales Integration](docs/05_SALES_INTEGRATION.md): Integration with Odoo's sales workflow
- [Developer Guide](docs/06_DEVELOPER_GUIDE.md): Technical information for developers
- [User Guide](docs/07_USER_GUIDE.md): Instructions for end users

## Requirements

- Odoo 18.0
- Depends on: `product`, `sale`, `mrp`, `web`

## Installation

1. Clone this repository into your Odoo addons directory:

2. Update your Odoo addons list:
   - Go to Apps > Update Apps List
   - Click "Update"

3. Install the module:
   - Go to Apps
   - Search for "ConfigMatrix"
   - Click "Install"

## Quick Start

1. **Enable product configuration**:
   - Go to a product
   - Check "Configurable Product"
   - Click "Create Configuration Template"

2. **Build your configuration template**:
   - Add sections for grouping related questions
   - Create fields for each configuration option
   - Set up visibility rules and dependencies
   - Map components to configuration choices

3. **Test your configuration**:
   - Use the "Test Template" button
   - Fill out the configuration form
   - View the generated BOM and price

4. **Use in sales**:
   - Add configurable product to a sales order
   - Click "Configure" button
   - Complete the configuration
   - Proceed with order

## Changelog

### 2025-07-14
- Refactored QWeb template logic for configurator fields to avoid forbidden opcode errors in Odoo 18+.
- Moved all complex inline Python logic for checkboxes, selects, and input values into helper methods on the `config.matrix.configuration` model:
  - `_is_field_checked` for boolean fields
  - `_has_default_value` for placeholder/select logic
  - `_get_field_default_value` for default value fallback
  - `_is_option_selected` for select/option logic
- Updated the template to use these helpers via `request.env['config.matrix.configuration'].sudo()` calls.
- This change improves maintainability and ensures compatibility with Odoo's QWeb compiler.

