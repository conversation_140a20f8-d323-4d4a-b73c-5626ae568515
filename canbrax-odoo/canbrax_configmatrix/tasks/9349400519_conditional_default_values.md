# Task 9349400519: Add Condition for Default Value

## Priority: LOW - Enhancement

## Issue Summary
Request to implement conditional default values where a default value (like "11mm") only appears when specific conditions are met (e.g., Frame Colour is "Western Red Cedar MZ306A4489V5"). Currently, once a default value is set, it appears regardless of conditions.

## Impact
- **Severity**: Enhancement
- **User Impact**: More intelligent default behavior
- **Business Impact**: Improved user experience, fewer configuration errors

## Technical Details
- **Template**: Subcategory - Window Screen SWS
- **Field**: Q25 Frame Size
- **Condition**: Frame Colour = "Western Red Cedar MZ306A4489V5"
- **Desired Default**: "11mm" only when condition is met
- **Current Behavior**: Default appears regardless of condition
- **Effort Estimate**: Medium (solution already provided)

## Solution Already Provided
The issue includes a detailed solution with two approaches:

### Solution 1: Use Calculated Field for Conditional Default
```python
# Calculated Field Configuration
Field Name: _CALCULATED_frame_size_default
Data Type: String
Formula: sws_wscrn_frame_colour == 'Western Red Cedar MZ306A4489V5' ? '11 mm' : ''

# Frame Size Field Update
Dynamic Default Value: {_CALCULATED_frame_size_default}
```

### Solution 2: Use Dynamic Default Value Directly
```python
# Direct Dynamic Default
Dynamic Default Value: {sws_wscrn_frame_colour == 'Western Red Cedar MZ306A4489V5' ? '11 mm' : ''}
```

## Implementation Steps

### Phase 1: Verify Current Capability (1-2 hours)
1. **Check Existing Features**
   - Verify if dynamic default values are already supported
   - Test calculated field functionality
   - Check expression evaluation system

2. **Test Provided Solutions**
   - Implement Solution 1 (calculated field approach)
   - Test Solution 2 (direct dynamic default)
   - Verify both approaches work as expected

### Phase 2: Enhancement (if needed) (4-6 hours)
1. **Implement Dynamic Defaults (if not existing)**
   - Add support for conditional default expressions
   - Implement expression evaluation for defaults
   - Add UI for configuring dynamic defaults

2. **Enhance Calculated Fields (if needed)**
   - Ensure calculated fields can be used as defaults
   - Add proper dependency tracking
   - Implement real-time updates

### Phase 3: Testing (2-3 hours)
1. **Functionality Testing**
   - Test conditional default behavior
   - Test with multiple conditions
   - Verify performance impact

2. **User Experience Testing**
   - Test field initialization with conditions
   - Test condition changes affecting defaults
   - Verify intuitive behavior

## Specific Test Cases

### Test Case 1: Basic Conditional Default
1. Configure Frame Size with conditional default
2. Set Frame Colour to "Western Red Cedar MZ306A4489V5"
3. Expected: Frame Size defaults to "11mm"
4. Change Frame Colour to different value
5. Expected: Frame Size shows no default

### Test Case 2: Multiple Conditional Defaults
1. Configure multiple fields with conditional defaults
2. Test various condition combinations
3. Verify each field behaves independently
4. Test interdependent conditions

### Test Case 3: Complex Conditions
1. Test with multiple condition criteria
2. Test with calculated field dependencies
3. Test with nested conditions
4. Verify proper evaluation order

## Files to Review
- `models/config_matrix_field.py`
- `static/src/js/config_matrix.js`
- `views/config_matrix_field_views.xml`
- Calculated field models and views

## Implementation Details

### Backend Enhancement
```python
class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    
    # Add dynamic default support
    dynamic_default_value = fields.Text(
        string="Dynamic Default Value",
        help="Expression for conditional default value"
    )
    
    def get_default_value(self, context=None):
        """Get default value based on conditions"""
        if self.dynamic_default_value:
            return self.evaluate_expression(self.dynamic_default_value, context)
        return self.default_value
```

### Frontend Enhancement
```javascript
// Update default when conditions change
function updateFieldDefaults(changedField) {
    document.querySelectorAll('[data-dynamic-default]').forEach(field => {
        const expression = field.dataset.dynamicDefault;
        const newDefault = evaluateExpression(expression);
        if (newDefault !== field.defaultValue) {
            field.defaultValue = newDefault;
            updateFieldDisplay(field);
        }
    });
}
```

## Testing Checklist
- [ ] Conditional defaults work as specified
- [ ] Default updates when conditions change
- [ ] Performance impact is minimal
- [ ] No regression in existing default behavior
- [ ] UI clearly shows conditional default behavior
- [ ] Complex conditions evaluate correctly

## Dependencies
- May depend on calculated field functionality
- Requires expression evaluation system
- May need UI updates for configuration

## Notes
- Solution is already well-defined in the issue
- Implementation should be straightforward
- Focus on testing and validation
- Consider generalizing for other conditional scenarios
- Good candidate for documentation and training material
- Could be extended to other field properties beyond defaults
