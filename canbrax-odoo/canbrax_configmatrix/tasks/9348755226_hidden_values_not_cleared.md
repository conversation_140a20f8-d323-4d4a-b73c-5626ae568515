# Task 9348755226: Hidden Question Values Not Cleared

## Priority: HIGH - Data Integrity Issue

## Issue Summary
When questions become hidden due to condition changes, their previously selected values are not cleared, leading to incorrect data being saved in configurations.

## Impact
- **Severity**: High - Data Integrity
- **User Impact**: Hidden field values incorrectly persist in saved configurations
- **Business Impact**: Incorrect product configurations, potential manufacturing errors

## Technical Details
- **Steps to Reproduce**:
  1. Q60: Select Outswing → Q62: Select No
  2. Q60: Change Outswing to Inswing → Q61: Select Yes
  3. Q60: Change Inswing to Outswing → Q62: Display as "Select an option"
  4. Save Configuration and Click Show Condition to check → Q61: value is still stored

- **Reference**: Loom video provided
- **Effort Estimate**: Medium

## Root Cause Investigation Areas
1. **State Management**: Hidden field values not properly cleared
2. **Condition Evaluation**: When conditions change, dependent fields not reset
3. **Data Persistence**: Hidden values incorrectly saved to database
4. **Frontend State**: JavaScript state not properly synchronized

## Recommended Solution Steps

### Phase 1: Investigation (2-3 hours)
1. **Reproduce Issue**
   - Follow exact steps from bug report
   - Document current behavior vs expected behavior
   - Check both frontend display and backend data

2. **Analyze Current Logic**
   - Review condition change handling
   - Check field visibility and value clearing logic
   - Examine save/load data flow

### Phase 2: Code Analysis (3-4 hours)
1. **Frontend Analysis**
   - Check `static/src/js/configurator.js` onFieldChange() method (line 320)
   - Review updateHiddenFieldsWithValues() method (line 270)
   - Examine clearField() method for proper value clearing
   - Check saveConfiguration() method for hidden field clearing

2. **Backend Analysis**
   - Check `models/config_matrix_field.py` for value persistence
   - Review save/load logic for hidden fields
   - Verify data validation rules

### Phase 3: Fix Implementation (4-5 hours)
1. **Frontend Fixes**
   - Add logic to clear values when fields become hidden
   - Implement proper state management for condition changes
   - Ensure form data excludes hidden field values

2. **Backend Fixes**
   - Add validation to exclude hidden field values during save
   - Implement proper data cleaning for hidden fields
   - Add safeguards against saving invalid hidden data

### Phase 4: Testing (3-4 hours)
1. **State Management Testing**
   - Test condition changes clear hidden values
   - Test multiple condition changes in sequence
   - Verify saved data integrity

2. **Regression Testing**
   - Test existing functionality still works
   - Test complex condition scenarios
   - Verify no impact on visible fields

## Specific Test Scenarios

### Scenario 1: Basic Hidden Field Clearing
1. Set field A to value X
2. Change condition so field A becomes hidden
3. Verify field A value is cleared
4. Save configuration
5. Verify field A value not in saved data

### Scenario 2: Multiple Condition Changes
1. Follow exact steps from bug report
2. Verify each step behaves correctly
3. Check final saved configuration
4. Verify no hidden values persist

### Scenario 3: Complex Dependencies
1. Test fields with multiple dependencies
2. Test cascading condition changes
3. Verify all affected hidden fields are cleared

## Files to Review
- `static/src/js/configurator.js` (lines 270-300: updateHiddenFieldsWithValues)
- `models/config_matrix_field.py` (visibility condition evaluation)
- `controllers/configuration_controller.py` (save configuration logic)
- `static/src/xml/configurator.xml`

## Implementation Details

### Frontend Changes
```javascript
// When field becomes hidden, clear its value
function onFieldHidden(fieldId) {
    // Clear field value
    clearFieldValue(fieldId);
    // Remove from form data
    removeFromFormData(fieldId);
    // Update UI
    updateFieldDisplay(fieldId);
}
```

### Backend Changes
```python
# Filter out hidden fields during save
def save_configuration(self, values):
    visible_fields = self.get_visible_fields()
    filtered_values = {k: v for k, v in values.items() 
                      if k in visible_fields}
    return super().save_configuration(filtered_values)
```

## Testing Checklist
- [ ] Hidden fields values are cleared when conditions change
- [ ] Saved configurations don't contain hidden field data
- [ ] Multiple condition changes work correctly
- [ ] Complex dependency scenarios work
- [ ] No regression in existing functionality
- [ ] Performance impact is minimal

## Dependencies
- May impact condition evaluation logic
- Should be coordinated with visibility fixes (Task 9389800433)

## Notes
- This is a critical data integrity issue
- Should be thoroughly tested with real user scenarios
- Consider adding audit logging for configuration changes
- May need database cleanup script for existing bad data
