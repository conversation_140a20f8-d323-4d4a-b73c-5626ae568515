# Task 9348606566: Display Required Message for Text Fields

## Priority: MEDIUM - Validation Issue

## Issue Summary
Text type fields do not display required validation messages ("This field is required. Please enter a value.") when users attempt to submit without entering a value.

## Impact
- **Severity**: Medium - Validation Issue
- **User Impact**: Users can submit incomplete forms without proper feedback
- **Business Impact**: Incomplete configurations, potential errors downstream

## Technical Details
- **Field Type**: Text
- **Issue**: Required validation message not displayed
- **Example**: Q7 needs to be required when "No" is selected in Q6
- **Expected**: Show "This field is required. Please enter a value."
- **Actual**: No validation message displayed
- **Effort Estimate**: Low

## Root Cause Investigation Areas
1. **Validation Logic**: Text fields missing required validation
2. **Field Type Handling**: Different validation for different field types
3. **Form Submission**: Validation not triggered for text fields
4. **UI Feedback**: Error messages not displayed for text fields

## Recommended Solution Steps

### Phase 1: Investigation (1 hour)
1. **Identify Current Validation**
   - Check existing validation for other field types
   - Review text field validation implementation
   - Identify validation framework being used

2. **Reproduce Issue**
   - Test with Q6/Q7 scenario
   - Test with other text fields
   - Check form submission behavior

### Phase 2: Code Analysis (1-2 hours)
1. **Frontend Analysis**
   - Check `static/src/js/config_matrix.js` for validation logic
   - Review form submission handling
   - Examine error message display system

2. **Backend Analysis**
   - Check `models/config_matrix_field.py` for field validation
   - Review required field validation logic
   - Verify validation rules configuration

### Phase 3: Fix Implementation (2-3 hours)
1. **Frontend Fixes**
   - Add required validation for text fields
   - Implement error message display for text fields
   - Ensure validation triggers on form submission

2. **Backend Fixes (if needed)**
   - Add server-side validation for text fields
   - Ensure proper error response format
   - Add validation rule configuration

### Phase 4: Testing (1-2 hours)
1. **Validation Testing**
   - Test required text fields show validation messages
   - Test form submission blocked when validation fails
   - Test error message display and clearing

2. **User Experience Testing**
   - Test Q6/Q7 specific scenario
   - Test various text field configurations
   - Verify consistent validation behavior

## Specific Test Cases

### Test Case 1: Basic Required Text Field
1. Configure text field as required
2. Leave field empty
3. Submit form
4. Expected: Show "This field is required. Please enter a value."

### Test Case 2: Conditional Required Text Field
1. Configure Q6/Q7 scenario
2. Select "No" in Q6
3. Leave Q7 empty
4. Submit form
5. Expected: Q7 shows required validation message

### Test Case 3: Multiple Required Text Fields
1. Configure multiple text fields as required
2. Leave some empty
3. Submit form
4. Expected: All empty required fields show validation messages

## Files to Review
- `static/src/js/config_matrix.js`
- `models/config_matrix_field.py`
- `static/src/xml/config_matrix_templates.xml`
- `views/config_matrix_field_views.xml`

## Implementation Details

### Frontend Validation Example
```javascript
// Add text field validation
function validateTextField(field) {
    if (field.required && !field.value) {
        showValidationError(field, "This field is required. Please enter a value.");
        return false;
    }
    return true;
}

// Form submission validation
function validateForm() {
    let isValid = true;
    document.querySelectorAll('.text-field').forEach(field => {
        if (!validateTextField(field)) {
            isValid = false;
        }
    });
    return isValid;
}
```

### Backend Validation Example
```python
# Add text field validation
def validate_field_value(self, value):
    if self.field_type == 'text' and self.required and not value:
        raise ValidationError("This field is required. Please enter a value.")
    return super().validate_field_value(value)
```

## Testing Checklist
- [ ] Required text fields show validation messages
- [ ] Validation messages are user-friendly
- [ ] Form submission is blocked when validation fails
- [ ] Validation messages clear when field is filled
- [ ] Conditional required fields work correctly
- [ ] No regression in existing field validation

## Dependencies
- None identified - can be worked on independently

## Notes
- This is a straightforward validation enhancement
- Should follow existing validation patterns in the codebase
- Consider standardizing validation messages across field types
- May need to update validation framework configuration
