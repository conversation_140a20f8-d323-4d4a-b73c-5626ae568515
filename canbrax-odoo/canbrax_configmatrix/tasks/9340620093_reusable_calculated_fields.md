# Task 9340620093: Reuse Calculated Fields Across Templates

## Priority: LOW - Technical Debt

## Issue Summary
Calculated fields such as `_CALCULATED_door_split_type` and `_CALCULATED_largest_door_height` rely on specified technical names of each template, making them difficult to reuse across multiple templates. This creates code duplication and maintenance overhead.

## Impact
- **Severity**: Low - Technical Debt
- **User Impact**: Development inefficiency, harder to maintain
- **Business Impact**: Increased development time, code duplication

## Technical Details
- **Affected Fields**: 
  - `_CALCULATED_door_split_type`
  - `_CALCULATED_largest_door_height`
  - Other template-specific calculated fields
- **Issue**: Formula relies on template-specific technical names
- **Current State**: Each template needs its own calculated field
- **Desired State**: Reusable calculated fields across templates
- **Effort Estimate**: High (major refactoring)

## Root Cause Investigation Areas
1. **Field Reference System**: How calculated fields reference other fields
2. **Template Architecture**: How templates are structured and isolated
3. **Technical Name Conventions**: Naming patterns across templates
4. **Expression Language**: How formulas resolve field references

## Recommended Solution Steps

### Phase 1: Analysis (1-2 weeks)
1. **Current System Analysis**
   - Document all calculated fields across templates
   - Identify common patterns and dependencies
   - Map field reference relationships

2. **Template Structure Review**
   - Analyze template inheritance patterns
   - Review field naming conventions
   - Identify reusable field patterns

### Phase 2: Architecture Design (1-2 weeks)
1. **Design New Reference System**
   - Create template-agnostic field reference system
   - Design field aliasing mechanism
   - Plan backward compatibility strategy

2. **Migration Strategy**
   - Plan phased migration approach
   - Identify breaking changes
   - Design rollback procedures

### Phase 3: Implementation (4-6 weeks)
1. **Core Infrastructure**
   - Implement new field reference system
   - Create field aliasing mechanism
   - Update expression parser

2. **Template Updates**
   - Migrate existing calculated fields
   - Update template configurations
   - Test cross-template functionality

### Phase 4: Testing & Migration (2-3 weeks)
1. **Comprehensive Testing**
   - Test all template combinations
   - Verify calculated field accuracy
   - Performance testing

2. **Data Migration**
   - Migrate existing configurations
   - Update user data
   - Verify data integrity

## Potential Solutions

### Solution 1: Field Aliasing System
Create a mapping system where calculated fields use generic aliases that map to template-specific field names.

```python
# Field alias configuration
FIELD_ALIASES = {
    'door_height': {
        'template_a': 'template_a_door_height',
        'template_b': 'template_b_door_height_mm'
    },
    'door_width': {
        'template_a': 'template_a_door_width', 
        'template_b': 'template_b_door_width_mm'
    }
}

# Calculated field using aliases
_CALCULATED_largest_door_height = "MAX(door_height, door_width)"
```

### Solution 2: Template Inheritance
Create a base template with common calculated fields that other templates inherit.

```python
class BaseTemplate(models.Model):
    _name = 'config.matrix.template.base'
    
    def _get_calculated_largest_door_height(self):
        # Generic implementation using standardized field names
        return max(self.get_door_dimensions())

class SpecificTemplate(BaseTemplate):
    _name = 'config.matrix.template.specific'
    _inherit = 'config.matrix.template.base'
```

### Solution 3: Dynamic Field Resolution
Implement runtime field resolution based on template context.

```python
def resolve_field_reference(field_name, template_context):
    """Resolve field reference based on template context"""
    mapping = get_template_field_mapping(template_context)
    return mapping.get(field_name, field_name)
```

## Files to Review
- `models/config_matrix_field.py`
- `models/config_matrix_template.py`
- `models/config_matrix_calculated_field.py`
- All template-specific model files
- Expression parser components

## Implementation Phases

### Phase 1: Foundation (Week 1-2)
- [ ] Document current calculated field usage
- [ ] Identify reusable patterns
- [ ] Design field reference system

### Phase 2: Core Changes (Week 3-4)
- [ ] Implement field aliasing system
- [ ] Update expression parser
- [ ] Create migration utilities

### Phase 3: Template Migration (Week 5-6)
- [ ] Migrate common calculated fields
- [ ] Update template configurations
- [ ] Test cross-template functionality

### Phase 4: Validation (Week 7-8)
- [ ] Comprehensive testing
- [ ] Performance optimization
- [ ] Documentation updates

## Testing Checklist
- [ ] Calculated fields work across all templates
- [ ] No regression in existing functionality
- [ ] Performance impact is acceptable
- [ ] Migration completes successfully
- [ ] Backward compatibility maintained
- [ ] New templates can easily reuse fields

## Dependencies
- Major refactoring project
- Requires coordination with all template development
- May impact ongoing template customizations

## Notes
- This is a significant architectural change
- Should be planned as a major version update
- Consider impact on existing customizations
- May require user retraining on new field reference system
- Could significantly improve long-term maintainability
- Should include comprehensive documentation updates
