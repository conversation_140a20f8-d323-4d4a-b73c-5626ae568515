# Task 9389800433: All Selections Showing Despite Conditions

## Priority: HIGH - Core Logic Issue

## Issue Summary
For lock type questions, users can see all options even though there are conditions that should hide them. Users can only select the valid options based on conditions, but inappropriate options remain visible.

## Impact
- **Severity**: High - Core Logic Issue
- **User Impact**: Confusing UX, users see options they shouldn't
- **Business Impact**: Poor user experience, potential confusion during configuration

## Technical Details
- **Affected Fields**: 
  - Lock type questions
  - Pet Door Colour
  - Pet Door flexible flap
- **Symptoms**: All options visible but only valid ones selectable
- **User Report**: Works correctly for some users but not others
- **Effort Estimate**: Medium

## Root Cause Investigation Areas
1. **Condition Evaluation Logic**: Frontend condition processing
2. **Option Visibility**: DOM manipulation for hiding/showing options
3. **User-Specific Behavior**: Different behavior for different users suggests state/permission issues
4. **Race Conditions**: Timing issues in condition evaluation

## Recommended Solution Steps

### Phase 1: Investigation (2-3 hours)
1. **Reproduce Issue**
   - Test with different user accounts
   - Document exact conditions that should hide options
   - Test with different browsers/devices

2. **Analyze Condition Logic**
   - Review condition evaluation in `static/src/js/`
   - Check if conditions are properly applied to option visibility
   - Verify condition syntax and field references

### Phase 2: Code Review (3-4 hours)
1. **Frontend Analysis**
   - Check `static/src/js/configurator.js` evaluateVisibility() method (line 345)
   - Review updateVisibility() method for proper field hiding
   - Examine syncStateToDOM() method for DOM updates (line 280)
   - Check buildDependencyGraph() method for field relationships

2. **Backend Analysis**
   - Check `models/config_matrix_field.py` for condition processing
   - Review option filtering logic
   - Verify condition data structure

### Phase 3: Fix Implementation (4-5 hours)
1. **Frontend Fixes**
   - Ensure options are properly hidden when conditions evaluate to false
   - Add proper DOM manipulation for option visibility
   - Implement proper state management for condition changes

2. **Condition Evaluation**
   - Fix condition evaluation timing
   - Ensure conditions are re-evaluated when dependencies change
   - Add proper error handling for condition evaluation

### Phase 4: Testing (3-4 hours)
1. **User Testing**
   - Test with multiple user accounts
   - Verify consistent behavior across users
   - Test with different field configurations

2. **Condition Testing**
   - Test all condition types (equals, greater than, etc.)
   - Test nested conditions
   - Test condition dependency chains

## Specific Test Cases
1. **Lock Type Scenario**
   - Set door dimensions: 2040 x 800
   - Set lock height: 1500
   - Expected: Only single or DBL w/ Btm aux visible
   - Actual: All 4 options visible

2. **Pet Door Scenarios**
   - Test Pet Door Colour conditions
   - Test Pet Door flexible flap conditions
   - Verify proper option filtering

## Files to Review
- `static/src/js/configurator.js` (lines 400-500: updateVisibility method)
- `models/config_matrix_field.py` (evaluateVisibility method)
- `views/config_matrix_field_views.xml`
- `static/src/xml/configurator.xml`

## Testing Checklist
- [ ] Options properly hidden when conditions are false
- [ ] Options properly shown when conditions are true
- [ ] Consistent behavior across different users
- [ ] No regression in existing functionality
- [ ] Performance impact is minimal
- [ ] Works with nested conditions

## Dependencies
- None identified - can be worked on independently

## Notes
- This affects core configurator functionality
- User-specific behavior suggests potential permission or state issues
- Should be thoroughly tested with different user roles
- Consider adding logging for condition evaluation debugging
