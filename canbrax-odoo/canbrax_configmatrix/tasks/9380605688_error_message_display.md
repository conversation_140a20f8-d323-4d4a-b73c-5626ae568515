# Task 9380605688: Dynamic Error Message Display Issue

## Priority: MEDIUM - Display Issue

## Issue Summary
Dynamic error messages are not displaying correctly - the word "not" is being replaced with an exclamation mark "!" symbol instead of displaying the actual word.

## Impact
- **Severity**: Medium - Display Issue
- **User Impact**: Confusing error messages, poor UX
- **Business Impact**: Users may misunderstand validation requirements

## Technical Details
- **Template**: DBL Hinged BX (IMPORT)
- **Field**: Q27
- **Configured Message**: `*Bottom Width must not vary more than 7mm from Middle Width.`
- **Expected Display**: `*Bottom Width must not vary more than 7mm from Middle Width.`
- **Actual Display**: `*Bottom Width must ! vary more than 7mm from Middle Width.`
- **Effort Estimate**: Low

## Root Cause Investigation Areas
1. **Character Encoding**: Text encoding issues
2. **HTML Escaping**: Improper HTML entity handling
3. **Template Rendering**: Template engine escaping issues
4. **String Processing**: Text processing replacing characters

## Recommended Solution Steps

### Phase 1: Investigation (30 minutes)
1. **Reproduce Issue**
   - Access DBL Hinged BX template Q27
   - Check dynamic error message display
   - Test with different error message content

2. **Check Character Encoding**
   - Verify database character encoding
   - Check HTTP response encoding
   - Test with different characters

### Phase 2: Code Analysis (1-2 hours)
1. **Frontend Analysis**
   - Check `static/src/js/config_matrix.js` for error message rendering
   - Review HTML template processing
   - Examine character escaping logic

2. **Backend Analysis**
   - Check `models/config_matrix_field.py` for error message processing
   - Review string handling in error message generation
   - Verify database field character encoding

### Phase 3: Fix Implementation (1-2 hours)
1. **Identify Root Cause**
   - Determine where character replacement occurs
   - Check if it's encoding, escaping, or processing issue
   - Verify character set configuration

2. **Implement Fix**
   - Fix character encoding/escaping issue
   - Ensure proper text processing
   - Add safeguards against character replacement

### Phase 4: Testing (1 hour)
1. **Character Testing**
   - Test with various special characters
   - Test with different error message formats
   - Verify no other character replacements

2. **Cross-Browser Testing**
   - Test in different browsers
   - Verify consistent character display
   - Test with different language characters

## Specific Test Cases

### Test Case 1: Basic Character Display
1. Configure error message with "not" 
2. Trigger error message display
3. Expected: "not" displays correctly
4. Actual: Should show "not" instead of "!"

### Test Case 2: Other Special Characters
1. Configure error messages with various characters
2. Test: quotes, apostrophes, special symbols
3. Verify all characters display correctly

### Test Case 3: Different Error Message Formats
1. Test short error messages
2. Test long error messages  
3. Test messages with HTML entities
4. Verify consistent character handling

## Files to Review
- `static/src/js/config_matrix.js`
- `models/config_matrix_field.py`
- `static/src/xml/config_matrix_templates.xml`
- `views/config_matrix_field_views.xml`

## Implementation Details

### Potential Fix Areas
```javascript
// Frontend - Check for improper escaping
function displayErrorMessage(message) {
    // Ensure proper HTML escaping without character replacement
    const escapedMessage = escapeHtml(message);
    // Don't replace characters
    return escapedMessage;
}
```

```python
# Backend - Check string processing
def get_error_message(self):
    message = self.error_message
    # Don't replace characters in error messages
    return message  # Return as-is
```

### Character Encoding Check
```python
# Check field character encoding
def _check_encoding(self):
    if isinstance(self.error_message, str):
        # Ensure proper UTF-8 encoding
        return self.error_message.encode('utf-8').decode('utf-8')
    return self.error_message
```

## Testing Checklist
- [ ] Word "not" displays correctly in error messages
- [ ] Other special characters display correctly
- [ ] Error messages render properly across browsers
- [ ] No regression in existing error message functionality
- [ ] Character encoding is consistent
- [ ] HTML entities are handled correctly

## Dependencies
- None identified - can be worked on independently

## Notes
- This is likely a simple character encoding or escaping issue
- Should be a quick fix once root cause is identified
- Consider adding comprehensive character testing
- May need to review all error message processing
- Could be related to HTML template rendering or database encoding
