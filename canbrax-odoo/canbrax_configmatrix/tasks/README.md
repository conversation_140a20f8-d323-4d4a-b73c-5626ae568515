# Odoo 18 Configurator Tasks

This directory contains detailed task documentation for fixing and enhancing the Odoo 18 configurator application.

## Task Priority Overview

### 🔴 HIGH PRIORITY (Fix Immediately)
**Critical issues that need immediate attention**

1. **[9367790053_dynamic_default_error.md](./9367790053_dynamic_default_error.md)** - Product Configurator Error with Dynamic Default
   - **Impact**: System crashes, complete feature breakdown
   - **Effort**: Medium (investigate error logs, fix validation/processing)

2. **[9389800433_condition_visibility.md](./9389800433_condition_visibility.md)** - All Selections Showing Despite Conditions  
   - **Impact**: Confusing UX, core logic issue
   - **Effort**: Medium (review condition evaluation in frontend)

3. **[9348755226_hidden_values_not_cleared.md](./9348755226_hidden_values_not_cleared.md)** - Hidden Question Values Not Cleared
   - **Impact**: Data integrity issues, incorrect configurations saved
   - **Effort**: Medium (fix state management logic)

### 🟡 MEDIUM PRIORITY (Plan for Next Sprint)
**Important UX and validation improvements**

4. **[9389371688_default_value_not_working.md](./9389371688_default_value_not_working.md)** - Default Value Not Working
   - **Impact**: Poor UX, users must manually select obvious defaults
   - **Effort**: Low-Medium (likely initialization timing issue)

5. **[9348606566_text_field_validation.md](./9348606566_text_field_validation.md)** - Display Required Message for Text Fields
   - **Impact**: Users can submit incomplete forms without proper feedback
   - **Effort**: Low (add validation for text field types)

6. **[9380605688_error_message_display.md](./9380605688_error_message_display.md)** - Dynamic Error Message Display Issue
   - **Impact**: Confusing error messages, poor UX
   - **Effort**: Low (likely character encoding/escaping issue)

### 🟢 LOW PRIORITY (Future Improvements)
**Enhancement and technical debt items**

7. **[9409474219_expression_length_error.md](./9409474219_expression_length_error.md)** - Maximum Value Expression Length Error
   - **Impact**: Limits complex validation rules
   - **Effort**: Medium (increase field length or optimize expression parsing)

8. **[9340620093_reusable_calculated_fields.md](./9340620093_reusable_calculated_fields.md)** - Reuse Calculated Fields Across Templates
   - **Impact**: Development inefficiency, code duplication
   - **Effort**: High (major refactoring of calculated field system)

9. **[9349400519_conditional_default_values.md](./9349400519_conditional_default_values.md)** - Add Condition for Default Value
   - **Impact**: Enhanced functionality
   - **Effort**: Medium (solution already provided)

## Recommended Development Approach

### Week 1-2: Critical Fixes
Focus on HIGH priority tasks that affect core functionality:
- Fix dynamic default system crash
- Resolve condition visibility issues  
- Fix hidden value persistence problems

### Week 3-4: UX Improvements
Address MEDIUM priority UX and validation issues:
- Fix default value initialization
- Add text field validation
- Fix error message display

### Week 5+: Future Enhancements
Plan and implement LOW priority improvements:
- Expression length limits
- Calculated field reusability
- Conditional default values

## Technical Investigation Areas

### Frontend JavaScript (`static/src/js/configurator.js`)
- **Condition evaluation logic**: `evaluateVisibility()` method (line 345)
- **State management**: `updateVisibility()`, `syncStateToDOM()` methods
- **Form validation**: Text field validation in form submission
- **Field initialization**: `updateDynamicDefaults()` method (line 900)
- **Hidden field tracking**: `updateHiddenFieldsWithValues()` method

### Backend Processing (`models/config_matrix_field.py`)
- **Dynamic default validation**: Lines 50-80 dynamic default fields
- **Expression parsing**: `generate_dynamic_default()` method (line 1150)
- **Template architecture**: `config_matrix_template.py` inheritance system
- **Visibility conditions**: `evaluateVisibility()` method

### Data Layer
- **Value persistence**: Configuration save/load in `models/config_matrix_configuration.py`
- **Field configuration**: Use case-specific properties (check_measure, sales, online)

## Resource Allocation Suggestions

### Senior Developer
- Tasks 1, 2, 3, 8 (complex logic issues)
- Architectural decisions for Task 8

### Mid-level Developer  
- Tasks 4, 5, 7, 9 (standard fixes and enhancements)
- Implementation support for complex tasks

### Junior Developer
- Task 6 (simple display fix)
- Testing and documentation support

## Testing Strategy

### Unit Tests
- Condition evaluation logic
- State management functions
- Expression parsing

### Integration Tests
- Form submission workflows
- Validation flows
- Cross-template functionality

### User Acceptance Tests
- Complete configuration workflows
- Real user scenarios
- Performance testing

### Regression Tests
- Ensure fixes don't break existing functionality
- Test with existing configurations
- Verify data integrity

## Getting Started

1. **Review the specific task file** for detailed implementation guidance
2. **Set up development environment** with the configurator codebase
3. **Start with HIGH priority tasks** in order of business impact
4. **Test thoroughly** before moving to next priority level
5. **Document any architectural decisions** for future reference

## Key Technical Architecture Notes

### Current System Structure
- **Frontend**: Owl.js component-based configurator (`configurator.js`)
- **Backend**: Odoo models with use-case specific field properties
- **State Management**: Component state with dependency graph tracking
- **Visibility Logic**: Expression-based conditions with JSON support
- **Dynamic Features**: Template-based dynamic defaults, help text, and error messages

### Critical System Behaviors
- **Use Cases**: Fields have different properties for check_measure, sales, and online use cases
- **Dependency Tracking**: Configurator builds dependency graphs to track field relationships
- **Hidden Field Management**: System tracks hidden fields with values for data integrity
- **Expression Evaluation**: Supports both simple conditions and complex JavaScript expressions

### Development Guidelines
- Test with multiple use cases (check_measure is primary)
- Always verify hidden field clearing behavior
- Check visibility condition evaluation thoroughly
- Validate dynamic expressions with complex scenarios
- Ensure proper state synchronization between frontend and backend

## Notes

- Each task file contains specific implementation guidance and test cases
- Tasks are designed to be worked on independently where possible
- Dependencies are noted in individual task files
- Consider creating feature branches for each major task
- Regular testing and code review recommended throughout development
