# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request, Response
import json
import logging

_logger = logging.getLogger(__name__)

class ConfigMatrixBackendController(http.Controller):
    """Backend controller for configuration matrix admin interface"""
    
    @http.route('/config_matrix/configurator', type='http', auth='user')
    def configurator(self, **kw):
        """Render the backend configurator page with template data"""
        # Log all parameters for debugging
        _logger.info(f"Configurator parameters: {kw}")

        # Get template_id from either template_id or matrix_id parameter
        template_id_str = kw.get('template_id') or kw.get('matrix_id')
        product_id_str = kw.get('product_id')
        order_line_id_str = kw.get('order_line_id')
        config_id_str = kw.get('config_id')

        # Convert to integers safely
        try:
            template_id = int(template_id_str) if template_id_str else 0
        except (ValueError, TypeError):
            template_id = 0

        try:
            product_id = int(product_id_str) if product_id_str else 0
        except (ValueError, TypeError):
            product_id = 0

        try:
            order_line_id = int(order_line_id_str) if order_line_id_str else 0
        except (ValueError, TypeError):
            order_line_id = 0

        try:
            config_id = int(config_id_str) if config_id_str else 0
        except (ValueError, TypeError):
            config_id = 0

        _logger.info(f"Parsed IDs: template_id={template_id}, product_id={product_id}, order_line_id={order_line_id}, config_id={config_id}")

        if not template_id:
            return request.render('canbrax_configmatrix.configurator_template', {
                'error': 'No template ID provided',
                'template': None,
                'config_values': {},
                'params': kw
            })

        # For the Product Configurator, always use 'check_measure' use case
        # Get template with context to ensure we get the right use case
        template = request.env['config.matrix.template'].sudo().browse(template_id)
        if not template.exists():
            return request.render('canbrax_configmatrix.configurator_template', {
                'error': f'Template not found with ID {template_id}',
                'template': None,
                'config_values': {},
                'params': kw
            })

        # Get the template structure with explicit check_measure use case
        template_structure = request.env['config.matrix.template'].sudo().get_template_structure(template.id, 'check_measure')

        # Get configuration values if config_id is provided - use sudo()
        config_values = {}
        if config_id:
            config = request.env['config.matrix.configuration'].sudo().browse(config_id)
            if config.exists():
                try:
                    config_values = json.loads(config.config_data or '{}')
                except Exception as e:
                    _logger.error(f"Error parsing config data: {str(e)}")
                    config_values = {}

        # Get product - use sudo()
        product = None
        if product_id:
            product = request.env['product.product'].sudo().browse(product_id)

        # Get order line - use sudo()
        order_line = None
        if order_line_id:
            order_line = request.env['sale.order.line'].sudo().browse(order_line_id)

        return request.render('canbrax_configmatrix.configurator_template', {
            'template': template,
            'product': product,
            'order_line': order_line,
            'config_values': config_values,
            'params': kw,
            'error': None,
            'config_id': config_id,
            'active_use_case': template_structure.get('active_use_case', 'check_measure')
        })

    @http.route('/config_matrix/get_template/<int:template_id>', type='json', auth='user')
    def get_template(self, template_id, **kw):
        """Get template structure"""
        try:
            # Determine which use case to use based on the context
            use_case = kw.get('use_case')

            # If no use case specified, determine based on context
            if not use_case:
                # Default to check_measure for sales order, online for website
                from_sale_order = kw.get('from_sale_order', False)
                use_case = 'check_measure' if from_sale_order else 'online'

            template = request.env['config.matrix.template'].sudo().browse(template_id)
            if not template.exists():
                return {'error': 'Template not found'}

            # Get template structure with explicit use case
            result = template.get_template_structure(template_id, use_case)
            return {'result': result}
        except Exception as e:
            _logger.error(f"Error getting template: {str(e)}")
            return {'error': str(e)}
