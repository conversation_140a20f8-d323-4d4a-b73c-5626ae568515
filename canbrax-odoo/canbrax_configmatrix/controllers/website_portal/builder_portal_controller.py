# -*- coding: utf-8 -*-

from odoo import http, _
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
from odoo.exceptions import AccessError, MissingError, UserError
from odoo.tools import groupby as groupbyelem
from operator import itemgetter
import json
import logging

_logger = logging.getLogger(__name__)

class BuilderPortalController(CustomerPortal):
    """Controller for the builder portal interface"""

    def _prepare_home_portal_values(self, counters):
        values = super()._prepare_home_portal_values(counters)

        if 'project_count' in counters:
            user = request.env.user
            values['project_count'] = request.env['builder.project'].sudo().search_count([
                ('partner_id', '=', user.partner_id.id)
            ])

        return values

    @http.route(['/my/projects', '/my/projects/page/<int:page>'], type='http', auth="user", website=True)
    def portal_my_projects(self, page=1, date_begin=None, date_end=None, sortby=None, filterby=None, search=None, search_in='name', **kw):
        """Display projects dashboard"""
        values = self._prepare_portal_layout_values()

        # Get the partner (builder)
        partner = request.env.user.partner_id

        # Get all projects for this partner
        Project = request.env['builder.project']
        domain = [('partner_id', '=', partner.id)]

        # Search
        if search and search_in:
            search_domain = []
            if search_in == 'name':
                search_domain = [('name', 'ilike', search)]
            domain += search_domain

        # Count for pager
        project_count = Project.search_count(domain)

        # Pager
        pager = portal_pager(
            url="/my/projects",
            url_args={'date_begin': date_begin, 'date_end': date_end, 'sortby': sortby, 'filterby': filterby, 'search': search, 'search_in': search_in},
            total=project_count,
            page=page,
            step=self._items_per_page
        )

        # Sort
        if sortby == 'date':
            order = 'create_date desc'
        elif sortby == 'name':
            order = 'name'
        else:
            order = 'create_date desc'

        # Fetch projects
        projects = Project.search(domain, order=order, limit=self._items_per_page, offset=pager['offset'])

        # Prepare values for rendering
        values.update({
            'projects': projects,
            'page_name': 'projects',
            'pager': pager,
            'default_url': '/my/projects',
            'searchbar_sortings': {
                'date': {'label': _('Newest'), 'order': 'create_date desc'},
                'name': {'label': _('Name'), 'order': 'name'},
            },
            'sortby': sortby or 'date',
            'searchbar_inputs': {
                'name': {'input': 'name', 'label': _('Search in Project Name')},
            },
            'search_in': search_in or 'name',
            'search': search or '',
        })

        return request.render("canbrax_configmatrix.portal_my_projects", values)

    @http.route('/my/project/<int:project_id>', type='http', auth="user", website=True)
    def portal_project_detail(self, project_id, **kw):
        """Display project details"""
        try:
            project_sudo = self._document_check_access('builder.project', project_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        values = self._prepare_portal_layout_values()
        values.update({
            'project': project_sudo,
            'page_name': 'project_detail',
        })

        return request.render("canbrax_configmatrix.portal_project_detail", values)

    @http.route('/my/start_order', type='http', auth="user", website=True)
    def portal_start_order(self, **kw):
        """Display the start order page with category boxes"""
        values = self._prepare_portal_layout_values()

        # Get the partner (builder)
        partner = request.env.user.partner_id

        # Get active project or create a new one
        project_id = int(kw.get('project_id', 0))
        if project_id:
            try:
                project = request.env['builder.project'].sudo().browse(project_id)
                if project.partner_id.id != partner.id:
                    project = False
            except:
                project = False
        else:
            project = False

        values.update({
            'project': project,
            'page_name': 'start_order',
            'door_count': int(kw.get('door_count', 1)),
            'screen_count': int(kw.get('screen_count', 1)),
        })

        return request.render("canbrax_configmatrix.portal_start_order", values)

    @http.route('/my/configure_grid', type='http', auth="user", website=True)
    def portal_configure_grid(self, **kw):
        """Display the configuration grid"""
        values = self._prepare_portal_layout_values()

        # Get parameters
        category = kw.get('category', 'door')

        # Safely convert count to integer
        try:
            count = int(kw.get('count', 1))
        except (ValueError, TypeError):
            count = 1

        # Safely convert project_id to integer
        try:
            project_id = int(kw.get('project_id', 0))
        except (ValueError, TypeError):
            project_id = 0

        order_name = kw.get('order_name', f"New {category.capitalize()} Order")

        # Validate count
        if count < 1:
            count = 1
        elif count > 20:  # Limit to 20 items for performance
            count = 20

        # Get current user
        user = request.env.user
        partner = user.partner_id

        # Get or create project and sales order
        project = False
        sale_order = False

        if project_id:
            try:
                # Get existing project
                project = request.env['builder.project'].sudo().browse(project_id)
                if project.exists() and project.partner_id.id == partner.id:
                    # Get linked sales order
                    sale_order = project.sale_order_id

                    # Update project name if provided
                    if order_name and project.name != order_name:
                        project.write({'name': order_name})
                        if sale_order:
                            sale_order.write({'name': order_name})
                else:
                    project = False
            except Exception as e:
                _logger.error(f"Error getting project {project_id}: {str(e)}")
                project = False

        # Create new project and sales order if needed
        if not project:
            try:
                # Import fields module
                from odoo import fields

                # Create a new sales order
                SaleOrder = request.env['sale.order'].sudo()

                # Create the sales order
                sale_order = SaleOrder.create({
                    'partner_id': partner.id,
                    'partner_invoice_id': partner.id,
                    'partner_shipping_id': partner.id,
                    'user_id': user.id,
                    'name': order_name,
                    'state': 'draft',
                    'date_order': fields.Datetime.now(),
                })

                # Create a project linked to the sales order
                project = request.env['builder.project'].sudo().create({
                    'name': order_name,
                    'partner_id': partner.id,
                    'user_id': user.id,
                    'sale_order_id': sale_order.id,
                    'state': 'draft',
                })

                # Link project back to sales order
                sale_order.write({
                    'project_id': project.id
                })

                project_id = project.id

            except Exception as e:
                _logger.error(f"Error creating sales order and project: {str(e)}")
                # Continue without project

        # Get all products for this category
        domain = []
        if category == 'door':
            domain.append(('name', 'ilike', 'door'))
        elif category == 'screen':
            domain.append(('name', 'ilike', 'screen'))

        # Search for products with the specific criteria
        products = request.env['product.template'].sudo().search(domain)

        # If no products found, fallback to all products
        if not products:
            products = request.env['product.template'].sudo().search([], limit=10)

        # For each product, ensure it has a matrix_id
        for product in products:
            if not product.matrix_id:
                try:
                    # Create a matrix for this product
                    matrix = request.env['config.matrix.template'].sudo().create({
                        'name': f"{product.name} Configuration",
                        'product_template_id': product.id,
                        'code': f"MATRIX-{product.id}",
                        'state': 'draft',
                    })

                    # Link matrix to product
                    product.sudo().write({
                        'is_configurable': True,
                        'matrix_id': matrix.id,
                    })
                except Exception as e:
                    _logger.error(f"Error creating matrix for product {product.id}: {str(e)}")
                    # Continue with next product

        # Get door and screen counts
        door_count = count if category == 'door' else int(kw.get('door_count', 0))
        screen_count = count if category == 'screen' else int(kw.get('screen_count', 0))

        values.update({
            'category': category,
            'count': count,
            'door_count': door_count,
            'screen_count': screen_count,
            'project': project,
            'products': products,
            'page_name': 'configure_grid',
        })

        return request.render("canbrax_configmatrix.portal_configure_grid", values)

    @http.route('/config_matrix/get_product_matrix_id', type='json', auth="public", website=True)
    def get_product_matrix_id(self, **kw):
        """Get the configuration matrix ID for a product"""
        try:
            product_id = kw.get('product_id')
            if not product_id:
                return {'error': 'Missing product ID'}

            product = request.env['product.product'].sudo().browse(int(product_id))
            if not product.exists():
                return {'error': 'Product not found'}

            # Get the matrix ID from the product template
            matrix_id = product.product_tmpl_id.matrix_id.id if product.product_tmpl_id.matrix_id else False

            # If matrix ID not found, try to find a matrix with the product as the source
            if not matrix_id:
                matrix = request.env['config.matrix.template'].sudo().search([
                    ('product_template_id', '=', product.product_tmpl_id.id)
                ], limit=1)

                if matrix:
                    matrix_id = matrix.id

            return {
                'matrix_id': matrix_id
            }
        except Exception as e:
            _logger.error(f"Error getting product matrix ID: {str(e)}")
            return {'error': str(e)}

    @http.route('/config_matrix/create_product_matrix', type='json', auth="public", website=True)
    def create_product_matrix(self, **kw):
        """Create a configuration matrix for a product"""
        try:
            product_id = kw.get('product_id')
            if not product_id:
                return {'error': 'Missing product ID'}

            product = request.env['product.product'].sudo().browse(int(product_id))
            if not product.exists():
                return {'error': 'Product not found'}

            # Check if the product already has a matrix
            if product.product_tmpl_id.matrix_id:
                return {
                    'success': True,
                    'matrix_id': product.product_tmpl_id.matrix_id.id
                }

            # Create a new matrix for the product
            matrix = request.env['config.matrix.template'].sudo().create({
                'name': f"{product.name} Configuration",
                'product_template_id': product.product_tmpl_id.id,
                'state': 'draft',
            })

            # Link matrix to product
            product.product_tmpl_id.sudo().write({
                'is_configurable': True,
                'matrix_id': matrix.id
            })

            return {
                'success': True,
                'matrix_id': matrix.id
            }
        except Exception as e:
            _logger.error(f"Error creating product matrix: {str(e)}")
            return {'success': False, 'error': str(e)}

    @http.route('/my/copy_configuration', type='json', auth="user", website=True)
    def copy_configuration(self, **kw):
        """Copy configuration data between slots"""
        try:
            source_config_id = int(kw.get('source_config_id', 0))
            target_slot_ids = kw.get('target_slot_ids', [])

            if not source_config_id or not target_slot_ids:
                return {'success': False, 'error': _('Missing source or target information')}

            # Get the source configuration
            source_config = request.env['config.matrix.configuration'].sudo().browse(source_config_id)
            if not source_config.exists():
                return {'success': False, 'error': _('Source configuration not found')}

            # Copy to each target slot
            copied_count = 0
            for slot_id in target_slot_ids:
                try:
                    # Create a copy of the configuration
                    new_config = source_config.copy({
                        'configuration_slot': slot_id,
                        'parent_config_id': source_config.id
                    })
                    copied_count += 1
                except Exception as e:
                    _logger.error(f"Error copying configuration to slot {slot_id}: {str(e)}")

            return {
                'success': True,
                'copied_count': copied_count,
                'message': _('%s configurations copied successfully') % copied_count
            }

        except Exception as e:
            _logger.error(f"Error in copy_configuration: {str(e)}")
            return {'success': False, 'error': str(e)}
