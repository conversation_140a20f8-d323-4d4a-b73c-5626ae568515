# -*- coding: utf-8 -*-

from odoo import http, _
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
from odoo.osv import expression
import logging

_logger = logging.getLogger(__name__)

class WebsitePortalCustomerPortal(CustomerPortal):

    def _prepare_home_portal_values(self, counters):
        values = super()._prepare_home_portal_values(counters)

        if 'configuration_count' in counters:
            user = request.env.user
            values['configuration_count'] = request.env['config.matrix.configuration'].sudo().search_count([
                ('sale_order_line_id.order_id.partner_id', '=', user.partner_id.id)
            ])

        return values

    @http.route(['/my/configurations', '/my/configurations/page/<int:page>'], type='http', auth="user", website=True)
    def portal_my_configurations(self, page=1, date_begin=None, date_end=None, sortby='date', filterby=None, search=None, search_in='all', **kw):
        values = self._prepare_portal_layout_values()
        user = request.env.user
        partner = user.partner_id
        ConfigMatrix = request.env['config.matrix.configuration'].sudo()

        domain = [
            ('sale_order_line_id.order_id.partner_id', '=', partner.id)
        ]

        # Count for pager
        configuration_count = ConfigMatrix.search_count(domain)

        # Pager
        pager = portal_pager(
            url="/my/configurations",
            url_args={'date_begin': date_begin, 'date_end': date_end, 'sortby': sortby, 'filterby': filterby},
            total=configuration_count,
            page=page,
            step=self._items_per_page
        )

        # Sorting
        if sortby == 'date':
            order = 'create_date desc'
        elif sortby == 'name':
            order = 'name'
        elif sortby == 'price':
            order = 'price desc'
        else:
            order = 'create_date desc'

        # Search
        if search and search_in:
            search_domain = []
            if search_in in ('all', 'name'):
                search_domain = expression.OR([search_domain, [('name', 'ilike', search)]])
            if search_in in ('all', 'product'):
                search_domain = expression.OR([search_domain, [('product_id.name', 'ilike', search)]])
            domain = expression.AND([domain, search_domain])

        # Content
        configurations = ConfigMatrix.search(domain, order=order, limit=self._items_per_page, offset=pager['offset'])

        # Create product type mapping for filtering (door, window, cabinet)
        product_types = {}
        for config in configurations:
            product_type = 'other'
            if config.product_id and config.product_id.categ_id:
                category_name = config.product_id.categ_id.name.lower()
                if 'door' in category_name:
                    product_type = 'door'
                elif 'window' in category_name:
                    product_type = 'window'
                elif 'cabinet' in category_name:
                    product_type = 'cabinet'

            if product_type not in product_types:
                product_types[product_type] = 0
            product_types[product_type] += 1

        # Filter by product type
        if filterby and filterby in product_types:
            filter_domain = []
            if filterby == 'door':
                filter_domain = [('product_id.categ_id.name', 'ilike', 'door')]
            elif filterby == 'window':
                filter_domain = [('product_id.categ_id.name', 'ilike', 'window')]
            elif filterby == 'cabinet':
                filter_domain = [('product_id.categ_id.name', 'ilike', 'cabinet')]

            if filter_domain:
                domain = expression.AND([domain, filter_domain])
                configurations = ConfigMatrix.search(domain, order=order, limit=self._items_per_page, offset=pager['offset'])
                configuration_count = ConfigMatrix.search_count(domain)

        # Format prices
        price_formatted_dict = {}
        website = request.env['website'].get_current_website()
        if website and website.pricelist_id and website.pricelist_id.currency_id:
            currency = website.pricelist_id.currency_id
            for config in configurations:
                try:
                    price_formatted_dict[config.id] = request.env['ir.qweb.field.monetary'].value_to_html(
                        config.price, {'display_currency': currency}
                    )
                except Exception:
                    price_formatted_dict[config.id] = str(config.price)
        else:
            # Fallback if no currency is available
            for config in configurations:
                price_formatted_dict[config.id] = str(config.price)

        values.update({
            'configurations': configurations,
            'price_formatted_dict': price_formatted_dict,
            'page_name': 'configurations',
            'pager': pager,
            'default_url': '/my/configurations',
            'searchbar_sortings': {
                'date': {'label': _('Newest'), 'order': 'create_date desc'},
                'name': {'label': _('Name'), 'order': 'name'},
                'price': {'label': _('Price'), 'order': 'price desc'},
            },
            'searchbar_filters': {
                'all': {'label': _('All'), 'domain': []},
                'door': {'label': _('Doors'), 'domain': [('product_id.categ_id.name', 'ilike', 'door')]},
                'window': {'label': _('Windows'), 'domain': [('product_id.categ_id.name', 'ilike', 'window')]},
                'cabinet': {'label': _('Cabinets'), 'domain': [('product_id.categ_id.name', 'ilike', 'cabinet')]},
            },
            'searchbar_inputs': {
                'all': {'input': 'all', 'label': _('Search in All')},
                'name': {'input': 'name', 'label': _('Search in Name')},
                'product': {'input': 'product', 'label': _('Search in Product')},
            },
            'sortby': sortby,
            'filterby': filterby,
            'search': search,
            'search_in': search_in,
            'product_types': product_types,
        })

        return request.render('canbrax_configmatrix.portal_my_configurations', values)

    @http.route(['/my/configuration/<int:config_id>'], type='http', auth="user", website=True)
    def portal_my_configuration(self, config_id=None, **kw):
        """Display a specific configuration"""
        if not config_id:
            return request.redirect('/my/configurations')

        # Get configuration
        config = request.env['config.matrix.configuration'].sudo().browse(config_id)
        if not config.exists() or (config.sale_order_line_id and config.sale_order_line_id.order_id.partner_id != request.env.user.partner_id):
            return request.redirect('/my/configurations')

        # For viewing, redirect to the website configurator with portal context
        # This ensures we use the same template for both viewing and editing
        redirect_url = f'/config_matrix/website_configurator?config_id={config_id}&template_id={config.template_id.id}&portal_context=1'
        if config.product_id:
            redirect_url += f'&product_id={config.product_id.id}'

        return request.redirect(redirect_url)
