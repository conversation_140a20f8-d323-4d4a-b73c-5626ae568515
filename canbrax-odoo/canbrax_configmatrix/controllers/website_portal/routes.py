# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request
import logging

_logger = logging.getLogger(__name__)

def register_website_portal_routes():
    """Register website portal routes on module load"""
    _logger.info("Registering website portal routes")

    # This function is called from __init__.py to register any routes
    # that need to be available when the module loads

    # Register builder portal routes
    from . import builder_portal_controller
