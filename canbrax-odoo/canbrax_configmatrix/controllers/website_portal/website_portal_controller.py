# -*- coding: utf-8 -*-

from odoo import http, fields, _
from odoo.http import request
from odoo.addons.website_sale.controllers.main import WebsiteSale
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
from odoo.osv import expression
import json
import logging
from werkzeug.exceptions import NotFound

_logger = logging.getLogger(__name__)

class WebsitePortalController(http.Controller):

    @http.route(['/config_matrix/website_portal'], type='http', auth='public', website=True)
    def website_portal_configurator(self, **kw):
        """Redirect to the product configurator with portal context"""
        # Add a portal context parameter to indicate this is from the portal
        kw['portal_context'] = '1'

        # Redirect to the product configurator
        return request.redirect('/config_matrix/configurator?' + '&'.join([f"{k}={v}" for k, v in kw.items()]))

    @http.route('/config_matrix/batch_split_lines', type='json', auth='public', website=True)
    def batch_split_lines(self, **kw):
        """Split all configurable product lines with quantity > 1"""
        try:
            # Get current order
            order = request.website.sale_get_order()
            if not order:
                return {'success': False, 'error': 'No active order found'}

            # Find configurable lines with quantity > 1
            lines_to_split = order.order_line.filtered(
                lambda l: l.is_configurable and l.product_uom_qty > 1
            )

            if not lines_to_split:
                return {'success': False, 'error': 'No lines found that need splitting'}

            # Split each line
            split_count = 0
            for line in lines_to_split:
                try:
                    # Get current quantity
                    qty = int(line.product_uom_qty)

                    # Update the original line to quantity 1
                    line.write({'product_uom_qty': 1})

                    # Create qty-1 new lines with quantity 1
                    for _ in range(qty - 1):  # Using _ as we don't need the index
                        # Copy all relevant fields from the original line
                        new_line_vals = {
                            'order_id': order.id,
                            'product_id': line.product_id.id,
                            'product_uom_qty': 1,
                            'product_uom': line.product_uom.id,
                            'price_unit': line.price_unit,
                            'is_configurable': True,  # Ensure this is set to prevent merging
                            # Copy any other relevant fields from the original line
                            'discount': line.discount,
                            'tax_id': [(6, 0, line.tax_id.ids)] if line.tax_id else False,
                        }

                        # Create new line
                        new_line = request.env['sale.order.line'].sudo().create(new_line_vals)

                        # If there's a configuration, duplicate it for the new line
                        if line.config_id:
                            # Create a copy of the configuration with all its data
                            new_config = line.config_id.sudo().copy({
                                'sale_order_line_id': new_line.id,
                                'parent_config_id': line.config_id.id  # Reference to original config
                            })

                            # Link the new configuration to the new line
                            new_line.write({
                                'config_id': new_config.id,
                                'is_configured': True,
                                'original_config_line_id': line.id  # Reference to original line
                            })

                            # Force recompute of configuration_summary to ensure it's properly set
                            new_line._compute_configuration_summary()

                        split_count += 1
                except Exception as e:
                    _logger.error(f"Error splitting line {line.id}: {str(e)}")
                    continue

            _logger.info(f"Batch split {split_count} lines with identical configurations")
            return {
                'success': True,
                'split_count': split_count
            }
        except Exception as e:
            _logger.error(f"Error in batch split: {str(e)}")
            # Return a more user-friendly error message
            return {'success': False, 'error': 'An error occurred while splitting the lines. Please try again or contact support.'}

    @http.route('/config_matrix/calculate_price_preview', type='json', auth='public', website=True)
    def calculate_price_preview(self, **kw):
        """Calculate price preview based on form data"""
        try:
            form_data = kw.get('form_data', {})
            product_id = int(form_data.get('product_id', 0) or kw.get('product_id', 0))
            template_id = int(form_data.get('template_id', 0) or kw.get('template_id', 0))

            if not product_id or not template_id:
                return {'success': False, 'error': 'Missing product or template ID'}

            # Extract field values
            config_values = {}
            for key, value in form_data.items():
                if key.startswith('field_'):
                    field_id = key.replace('field_', '')
                    config_values[field_id] = value

            # Create temporary configuration for price calculation
            ConfigMatrix = request.env['config.matrix.configuration'].sudo()
            temp_config = ConfigMatrix.create({
                'template_id': template_id,
                'product_id': product_id,
                'config_data': json.dumps(config_values),
            })

            # Calculate price
            try:
                price = temp_config.calculate_price()

                # Format price
                website = request.env['website'].get_current_website()
                if website and website.pricelist_id and website.pricelist_id.currency_id:
                    currency = website.pricelist_id.currency_id
                    try:
                        price_formatted = request.env['ir.qweb.field.monetary'].value_to_html(
                            price, {'display_currency': currency}
                        )
                    except Exception:
                        price_formatted = str(price)
                else:
                    # Fallback if no currency is available
                    price_formatted = str(price)

                # Delete temporary configuration
                temp_config.unlink()

                return {
                    'success': True,
                    'price': price,
                    'price_formatted': price_formatted
                }
            finally:
                # Make sure we always delete the temp config even if there's an error
                if temp_config.exists():
                    temp_config.unlink()

        except Exception as e:
            _logger.error(f"Error calculating price preview: {str(e)}")
            return {'success': False, 'error': str(e)}

    @http.route('/config_matrix/compare_configs', type='json', auth='public', website=True)
    def compare_configs(self, config_ids, **kw):
        """Generate comparison data for multiple configurations"""
        try:
            if not config_ids or len(config_ids) < 2:
                return {'success': False, 'error': 'Please select at least two configurations to compare'}

            # Get configurations
            configurations = request.env['config.matrix.configuration'].sudo().browse(config_ids)
            if len(configurations) < 2:
                return {'success': False, 'error': 'At least one configuration was not found'}

            # Ensure configurations are for comparable products (same template)
            templates = configurations.mapped('template_id')
            if len(templates) > 1:
                return {'success': False, 'error': 'Configurations must be for the same product template to compare'}

            template = templates[0]

            # Build comparison data
            comparison_data = []

            # Parse config data for each configuration
            config_data_list = []
            for config in configurations:
                try:
                    config_data = json.loads(config.config_data or '{}')
                    config_data_list.append(config_data)
                except Exception as e:
                    _logger.error(f"Error parsing config data for ID {config.id}: {str(e)}")
                    config_data_list.append({})

            # Compare section by section, field by field
            for section in template.section_ids:
                # Check if section is relevant (visible in at least one config)
                section_visible = False
                for config_data in config_data_list:
                    if self._is_section_visible(section, config_data):
                        section_visible = True
                        break

                if not section_visible:
                    continue

                # Add section header
                comparison_data.append({
                    'field_name': section.name,
                    'is_header': True,
                    'values': [{} for _ in range(len(configurations))]
                })

                # Compare fields
                for field in section.field_ids:
                    # Check if field is relevant (visible in at least one config)
                    field_visible = False
                    for config_data in config_data_list:
                        if self._is_field_visible(field, config_data):
                            field_visible = True
                            break

                    if not field_visible:
                        continue

                    # Get field values from each configuration
                    field_values = []
                    values_differ = False
                    previous_value = None

                    for i, config_data in enumerate(config_data_list):
                        # Skip fields that aren't visible in this configuration
                        if not self._is_field_visible(field, config_data):
                            field_values.append({
                                'display_value': 'N/A',
                                'different': True
                            })
                            values_differ = True
                            continue

                        # Get field value
                        field_value = config_data.get(field.technical_name)

                        # Format display value based on field type
                        display_value = self._format_field_value(field, field_value)

                        # Check if values differ
                        if i > 0 and display_value != previous_value:
                            values_differ = True

                        previous_value = display_value

                        field_values.append({
                            'display_value': display_value,
                            'different': False  # Will update after checking all
                        })

                    # Mark differing values
                    if values_differ:
                        for value in field_values:
                            value['different'] = True

                    # Add field comparison row
                    comparison_data.append({
                        'field_name': field.name,
                        'is_header': False,
                        'values': field_values
                    })

            # Add pricing information
            price_values = []
            prices_differ = False
            previous_price = None

            for i, config in enumerate(configurations):
                price = config.price
                website = request.env['website'].get_current_website()
                if website and website.pricelist_id and website.pricelist_id.currency_id:
                    currency = website.pricelist_id.currency_id
                    try:
                        price_formatted = request.env['ir.qweb.field.monetary'].value_to_html(
                            price, {'display_currency': currency}
                        )
                    except Exception:
                        price_formatted = str(price)
                else:
                    # Fallback if no currency is available
                    price_formatted = str(price)

                # Check if prices differ
                if i > 0 and price != previous_price:
                    prices_differ = True

                previous_price = price

                price_values.append({
                    'display_value': price_formatted,
                    'different': False  # Will update after checking all
                })

            # Mark differing prices
            if prices_differ:
                for value in price_values:
                    value['different'] = True

            # Add price comparison row
            comparison_data.append({
                'field_name': 'Total Price',
                'is_header': False,
                'values': price_values
            })

            # Render comparison modal
            values = {
                'configurations': configurations,
                'comparison_data': comparison_data
            }

            html = request.env['ir.ui.view'].sudo()._render_template(
                'canbrax_configmatrix.website_portal_config_comparison_modal', values
            )

            return {
                'success': True,
                'html': html
            }
        except Exception as e:
            _logger.error(f"Error comparing configurations: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _is_section_visible(self, section, config_data):
        """Check if a section should be visible based on field visibility conditions"""
        for field in section.field_ids:
            if self._is_field_visible(field, config_data):
                return True
        return False

    def _is_field_visible(self, field, config_data):
        """Check if a field is visible based on configuration values - PERFORMANCE OPTIMIZED"""
        # Use the cached evaluation method from the field model
        return field.evaluate_visibility(config_data)

    def _format_field_value(self, field, field_value):
        """Format field value for display in comparison"""
        if field_value is None:
            return '-'

        if field.field_type == 'boolean':
            return field.boolean_true_label if field_value else field.boolean_false_label or 'No'

        elif field.field_type == 'selection':
            option = field.option_ids.filtered(lambda o: o.value == field_value)
            return option.name if option else field_value or '-'

        elif field.field_type == 'number':
            # Format number with appropriate precision
            formatted = f"{float(field_value):g}" if field_value else '0'
            if field.unit_of_measure:
                formatted += f" {field.unit_of_measure}"
            return formatted

        return str(field_value) if field_value else '-'
