# -*- coding: utf-8 -*-

from odoo import http, _
from odoo.http import request
from odoo.addons.website_sale.controllers.main import WebsiteSale
import logging

_logger = logging.getLogger(__name__)

class WebsiteSaleExtended(WebsiteSale):
    """Extend the WebsiteSale controller to handle project references"""

    @http.route(['/shop/cart/update'], type='http', auth="public", methods=['POST'], website=True, csrf=False)
    def cart_update(self, product_id, add_qty=1, set_qty=0, **kw):
        """Override cart_update to handle project_id and slot_id"""
        # Get project_id and slot_id from kw if available
        project_id = kw.get('project_id', False)
        slot_id = kw.get('slot_id', False)

        # Always force quantity to 1 to ensure products are added as individual entries
        add_qty = 1
        set_qty = 0

        # Call the original method to add the product to the cart
        result = super(WebsiteSaleExtended, self).cart_update(product_id, add_qty, set_qty, **kw)

        # If project_id is provided, link the order line to the project
        if project_id:
            try:
                # Get the current order
                order = request.website.sale_get_order()
                if order:
                    # Get the last added order line (should be the one we just added)
                    order_line = order.order_line[-1] if order.order_line else False
                    if order_line and str(order_line.product_id.id) == str(product_id):
                        # Link the order line to the project
                        project = request.env['builder.project'].sudo().browse(int(project_id))
                        if project.exists():
                            # Update the order line with project reference
                            order_line.sudo().write({
                                'project_id': int(project_id),
                                'configuration_slot': slot_id if slot_id else False,
                                # Ensure the product is not merged with other entries
                                'product_custom_attribute_value_ids': [(0, 0, {
                                    'custom_value': f"Slot: {slot_id}",
                                    'custom_product_template_attribute_value_id': order_line.product_id.product_template_attribute_value_ids[0].id if order_line.product_id.product_template_attribute_value_ids else False
                                })] if order_line.product_id.product_template_attribute_value_ids else []
                            })

                            # If the order doesn't have a project reference yet, add it
                            if not order.project_id:
                                order.sudo().write({
                                    'project_id': int(project_id)
                                })

                            _logger.info(f"Linked order line {order_line.id} to project {project_id}, slot {slot_id}")
            except Exception as e:
                _logger.error(f"Error linking order line to project: {str(e)}")

        return result
