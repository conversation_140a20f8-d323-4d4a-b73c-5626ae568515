{"_CALCULATED_manual_left_top_width": {"description": "Input: Manual top width for the left door leaf.", "formula": "parseFloat(bx_dbl_hinge_make_left_door_top_width_mm_manual) || 0"}, "_CALCULATED_manual_left_middle_width": {"description": "Input: Manual middle width for the left door leaf.", "formula": "parseFloat(bx_dbl_hinge_make_left_door_middle_width_mm_manual) || 0"}, "_CALCULATED_manual_left_bottom_width": {"description": "Input: Manual bottom width for the left door leaf.", "formula": "parseFloat(bx_dbl_hinge_make_left_door_bottom_width_mm_manual) || 0"}, "_CALCULATED_manual_right_top_width": {"description": "Input: Manual top width for the right door leaf.", "formula": "parseFloat(bx_dbl_hinge_make_right_door_top_width_mm_manual) || 0"}, "_CALCULATED_manual_right_middle_width": {"description": "Input: Manual middle width for the right door leaf.", "formula": "parseFloat(bx_dbl_hinge_make_right_door_middle_width_mm_manual) || 0"}, "_CALCULATED_manual_right_bottom_width": {"description": "Input: Manual bottom width for the right door leaf.", "formula": "parseFloat(bx_dbl_hinge_make_right_door_bottom_width_mm_manual) || 0"}, "_CALCULATED_even_top_width": {"description": "Input: Even split top width for each door leaf.", "formula": "parseFloat(bx_dbl_hinge_make_each_door_top_width_mm_even) || 0"}, "_CALCULATED_even_middle_width": {"description": "Input: Even split middle width for each door leaf.", "formula": "parseFloat(bx_dbl_hinge_make_each_door_middle_width_mm_even) || 0"}, "_CALCULATED_even_bottom_width": {"description": "Input: Even split bottom width for each door leaf.", "formula": "parseFloat(bx_dbl_hinge_make_each_door_bottom_width_mm_even) || 0"}, "_CALCULATED_uneven_left_top_width": {"description": "Input: Uneven split left door top width.", "formula": "parseFloat(bx_dbl_hinge_make_left_door_top_width_mm_uneven) || 0"}, "_CALCULATED_uneven_left_middle_width": {"description": "Input: Uneven split left door middle width.", "formula": "parseFloat(bx_dbl_hinge_make_left_door_middle_width_mm_uneven) || 0"}, "_CALCULATED_uneven_left_bottom_width": {"description": "Input: Uneven split left door bottom width.", "formula": "parseFloat(bx_dbl_hinge_make_left_door_bottom_width_mm_uneven) || 0"}, "_CALCULATED_uneven_right_top_width": {"description": "Input: Uneven split right door top width.", "formula": "parseFloat(bx_dbl_hinge_make_right_door_top_width_mm_uneven) || 0"}, "_CALCULATED_uneven_right_middle_width": {"description": "Input: Uneven split right door middle width.", "formula": "parseFloat(bx_dbl_hinge_make_right_door_middle_width_mm_uneven) || 0"}, "_CALCULATED_uneven_right_bottom_width": {"description": "Input: Uneven split right door bottom width.", "formula": "parseFloat(bx_dbl_hinge_make_right_door_bottom_width_mm_uneven) || 0"}, "_CALCULATED_largest_door_width": {"description": "Maximum width of all door measurements across all modes and positions.", "formula": "_CALCULATED_height_calculation_method === 'manual' ? Math.max(_CALCULATED_manual_left_top_width, _CALCULATED_manual_left_middle_width, _CALCULATED_manual_left_bottom_width, _CALCULATED_manual_right_top_width, _CALCULATED_manual_right_middle_width, _CALCULATED_manual_right_bottom_width) : (_CALCULATED_height_calculation_method === 'even' ? Math.max(_CALCULATED_even_top_width, _CALCULATED_even_middle_width, _CALCULATED_even_bottom_width) : Math.max(_CALCULATED_uneven_left_top_width, _CALCULATED_uneven_left_middle_width, _CALCULATED_uneven_left_bottom_width, _CALCULATED_uneven_right_top_width, _CALCULATED_uneven_right_middle_width, _CALCULATED_uneven_right_bottom_width))"}, "_CALCULATED_smallest_door_width": {"description": "Minimum width of all door measurements across all modes and positions.", "formula": "_CALCULATED_height_calculation_method === 'manual' ? Math.min(_CALCULATED_manual_left_top_width, _CALCULATED_manual_left_middle_width, _CALCULATED_manual_left_bottom_width, _CALCULATED_manual_right_top_width, _CALCULATED_manual_right_middle_width, _CALCULATED_manual_right_bottom_width) : (_CALCULATED_height_calculation_method === 'even' ? Math.min(_CALCULATED_even_top_width, _CALCULATED_even_middle_width, _CALCULATED_even_bottom_width) : Math.min(_CALCULATED_uneven_left_top_width, _CALCULATED_uneven_left_middle_width, _CALCULATED_uneven_left_bottom_width, _CALCULATED_uneven_right_top_width, _CALCULATED_uneven_right_middle_width, _CALCULATED_uneven_right_bottom_width))"}}