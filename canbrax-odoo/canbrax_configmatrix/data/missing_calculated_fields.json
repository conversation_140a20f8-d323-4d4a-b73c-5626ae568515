{"_CALCULATED_lock_height": {"description": "Input: The specified lock height.", "formula": "parseFloat(bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out) || 1100"}, "_CALCULATED_deduction_assistance": {"description": "Input: Whether deduction assistance is used (e.g., 'yes', 'no').", "formula": "bx_dbl_hinge_deduction_assistance"}, "_CALCULATED_door_split_type": {"description": "Input: Type of door split (e.g., 'even', 'uneven').", "formula": "bx_dbl_hinge_door_split_type"}, "_CALCULATED_is_manual_mode": {"description": "Boolean: True if deduction assistance is disabled (manual mode).", "formula": "bx_dbl_hinge_deduction_assistance === 'no'"}, "_CALCULATED_is_even_split": {"description": "Boolean: True if door split type is even.", "formula": "_CALCULATED_door_split_type === 'even'"}, "_CALCULATED_is_uneven_split": {"description": "Boolean: True if door split type is uneven.", "formula": "_CALCULATED_door_split_type === 'uneven'"}, "_CALCULATED_height_minus_940": {"description": "Smallest Door Height - 940.", "formula": "_CALCULATED_smallest_door_height - 940"}, "_CALCULATED_height_minus_987": {"description": "Smallest Door Height - 987.", "formula": "_CALCULATED_smallest_door_height - 987"}, "_CALCULATED_height_minus_1019": {"description": "Smallest Door Height - 1019.", "formula": "_CALCULATED_smallest_door_height - 1019"}, "_CALCULATED_lock_height_above_halfway": {"description": "Boolean: True if lock height is above halfway point", "formula": "_CALCULATED_lock_height >= _CALCULATED_halfway_point"}, "_CALCULATED_lock_height_above_minimum": {"description": "Boolean: True if lock height is above 1171mm minimum", "formula": "_CALCULATED_lock_height >= 1171"}, "_CALCULATED_lock_height_below_maximum": {"description": "Boolean: True if lock height is below maximum (smallest_door_height - 1019mm)", "formula": "_CALCULATED_lock_height <= _CALCULATED_height_minus_1019"}, "_CALCULATED_lock_height_valid_range": {"description": "Boolean: True if lock height meets all range requirements (>= halfway AND >= 1171 AND <= smallest_door_height - 1019)", "formula": "_CALCULATED_lock_height >= _CALCULATED_halfway_point && _CALCULATED_lock_height >= 1171 && _CALCULATED_lock_height <= (_CALCULATED_smallest_door_height - 1019)"}}