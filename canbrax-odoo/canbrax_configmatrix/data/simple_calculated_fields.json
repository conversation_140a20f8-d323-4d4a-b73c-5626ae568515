{"_CALCULATED_door_width": {"description": "Door width in mm", "formula": "parseFloat(door_width) || 0"}, "_CALCULATED_door_height": {"description": "Door height in mm", "formula": "parseFloat(door_height) || 0"}, "_CALCULATED_smallest_door_height": {"description": "Minimum height of the door leaves (simplified)", "formula": "Math.min(parseFloat(left_door_height) || 2100, parseFloat(right_door_height) || 2000)"}, "_CALCULATED_largest_door_height": {"description": "Maximum height of the door leaves (simplified)", "formula": "Math.max(parseFloat(left_door_height) || 2100, parseFloat(right_door_height) || 2000)"}, "_CALCULATED_halfway_point": {"description": "Halfway point of smallest door height", "formula": "_CALCULATED_smallest_door_height / 2"}, "_CALCULATED_halfway_plus_32": {"description": "Halfway point plus 32mm", "formula": "_CALCULATED_halfway_point + 32"}, "_CALCULATED_halfway_plus_79": {"description": "Halfway point plus 79mm", "formula": "_CALCULATED_halfway_point + 79"}, "_CALCULATED_halfway_minus_79": {"description": "Halfway point minus 79mm", "formula": "_CALCULATED_halfway_point - 79"}, "_CALCULATED_door_area": {"description": "Door area in square mm", "formula": "_CALCULATED_door_width * _CALCULATED_door_height"}, "_CALCULATED_door_area_m2": {"description": "Door area in square meters", "formula": "_CALCULATED_door_area / 1000000"}, "_CALCULATED_lock_height": {"description": "Lock height in mm", "formula": "parseFloat(lock_height) || 1200"}}