<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Default Matrix Categories -->
        
        <!-- Door Frame Category -->
        <record id="matrix_category_door_frame" model="config.matrix.category">
            <field name="name">Door Frame Matrices</field>
            <field name="code">DOOR_FRAME</field>
            <field name="category_type">door_frame</field>
            <field name="description">Pricing matrices for door frame components including standard sizes, custom dimensions, and material variations.</field>
            <field name="sequence">10</field>
            <field name="is_required">True</field>
            <field name="allow_multiple">True</field>
        </record>
        
        <!-- Hardware Category -->
        <record id="matrix_category_hardware" model="config.matrix.category">
            <field name="name">Hardware Components</field>
            <field name="code">HARDWARE</field>
            <field name="category_type">hardware</field>
            <field name="description">Pricing for hardware components like handles, locks, hinges, and other fittings based on specifications and installation requirements.</field>
            <field name="sequence">20</field>
            <field name="is_required">False</field>
            <field name="allow_multiple">True</field>
        </record>
        
        <!-- Glass/Panel Category -->
        <record id="matrix_category_glass_panel" model="config.matrix.category">
            <field name="name">Glass &amp; Panel Matrices</field>
            <field name="code">GLASS_PANEL</field>
            <field name="category_type">glass_panel</field>
            <field name="description">Pricing matrices for glass panels, mesh, and other infill materials including different types, thicknesses, and insulation ratings.</field>
            <field name="sequence">30</field>
            <field name="is_required">False</field>
            <field name="allow_multiple">True</field>
        </record>
        
        <!-- Labor Operations Category -->
        <record id="matrix_category_labor" model="config.matrix.category">
            <field name="name">Labor Operations</field>
            <field name="code">LABOR_OPS</field>
            <field name="category_type">labor</field>
            <field name="description">Labor time matrices for manufacturing operations like cutting, assembly, powder coating, and quality control based on product dimensions.</field>
            <field name="sequence">40</field>
            <field name="is_required">False</field>
            <field name="allow_multiple">True</field>
        </record>
        
        <!-- Custom Category -->
        <record id="matrix_category_custom" model="config.matrix.category">
            <field name="name">Custom Matrices</field>
            <field name="code">CUSTOM</field>
            <field name="category_type">custom</field>
            <field name="description">Custom pricing matrices for specialized components or unique requirements not covered by standard categories.</field>
            <field name="sequence">50</field>
            <field name="is_required">False</field>
            <field name="allow_multiple">True</field>
        </record>
    </data>
</odoo>
