Question,Field Name,Technical Name,Type,Required,Seq,Help Text,Expression.
1,Location,bx_dbl_hinge_location,Selection,Yes,10,,
2,Location (Other),bx_dbl_hinge_location_other,Text,Yes,11,,
3,Door Jamb Reveal is Greater than 20mm or No Reveal?,bx_dbl_hinge_jamb_reveal_gt_20mm,Selection,Yes,20,,
4,Will New Screen Door Clear Handle on Existing External Door?,bx_dbl_hinge_screen_clears_handle,Selection,Yes,30,,
5,<PERSON> Handle on New Screen Door Clear Existing External Door or Existing Pull/Push Bar Handle?,bx_dbl_hinge_new_handle_clears_existing,Selection,Yes,40,,
6,New Screen Door Swing Path is Clear from any Obstructions?,bx_dbl_hinge_swing_path_clear,Selection,Yes,50,,
7,Description of Rectification Required or Desctiption of Obstruction Preventing Install,bx_dbl_hinge_obstruction_description,Text,Yes,51,,
8,Quantity,bx_dbl_hinge_quantity,Number,Yes,60,,
9,Frame Colour,bx_dbl_hinge_frame_colour,Selection,Yes,70,,
10,Powder Coat Colour,bx_dbl_hinge_powder_coat_colour,Selection,Yes,71,,
11,Custom Powder Coat - Colour Name,bx_dbl_hinge_custom_powder_coat_name,Text,Yes,72,,
12,Custom Powder Coat - Colour Finish,bx_dbl_hinge_custom_powder_coat_finish,Text,Yes,73,,
13,Custom Powder Coat - Colour Code,bx_dbl_hinge_custom_powder_coat_code,Text,Yes,74,,
14,Door Size Deduction Assistance Required?,bx_dbl_hinge_deduction_assistance,Selection,Yes,80,,
15,Door Split Type,bx_dbl_hinge_door_split_type,Selection,Yes,81,,
16,Opening Height (mm) (Even Split),bx_dbl_hinge_opening_height_mm_even,Number,Yes,82,,
17,Height Deduction (mm) (Even Split),bx_dbl_hinge_height_deduction_mm_even,Number,Yes,83,Client can set default Height Deduction in their profile,
18,MAKE Each Door Height (mm) (Even Split),bx_dbl_hinge_make_each_door_height_mm_even,Number,Yes,84,*Door Height must be between 700 - 3100mm,
19,Opening TOP Width (mm) (Even Split),bx_dbl_hinge_opening_top_width_mm_even,Number,Yes,85,,
20,TOP Width Deduction (mm) (Even Split),bx_dbl_hinge_top_width_deduction_mm_even,Number,Yes,86,Client can set default Even Split TOP Width Deduction in their profile,
21,MAKE Each Door TOP Width (mm) (Even Split),bx_dbl_hinge_make_each_door_top_width_mm_even,Number,Yes,87,*Door Width must be between 300 - 2000mm\n*Please select Hand Built Door in previous question or enter a Middle Width that varies less than 7mm from Top & Bottom Width.,
22,Opening MIDDLE Width (mm) (Even Split),bx_dbl_hinge_opening_middle_width_mm_even,Number,Yes,88,,
23,MIDDLE Width Deduction (mm) (Even Split),bx_dbl_hinge_middle_width_deduction_mm_even,Number,Yes,89,Client can set default Even Split MIDDLE Width Deduction in their profile,
24,MAKE Each Door MIDDLE Width (mm) (Even Split),bx_dbl_hinge_make_each_door_middle_width_mm_even,Number,Yes,90,*Door Width must be between 300 - 2000mm\n*Please select Hand Built Door in previous question or enter a Middle Width that varies less than 7mm from Top & Bottom Width.,
25,Opening BOTTOM Width (mm) (Even Split),bx_dbl_hinge_opening_bottom_width_mm_even,Number,Yes,91,,
26,BOTTOM Width Deduction (mm) (Even Split),bx_dbl_hinge_bottom_width_deduction_mm_even,Number,Yes,92,Client can set default Even Split BOTTOM Width Deduction in their profile,
27,MAKE Each Door BOTTOM Width (mm) (Even Split),bx_dbl_hinge_make_each_door_bottom_width_mm_even,Number,Yes,93,*Door Width must be between 300 - 2000mm\n*Please select Hand Built Door in previous question or enter a Bottom Width that varies less than 7mm from Middle Width.,
28,LEFT Opening Height (mm) (Uneven Split),bx_dbl_hinge_left_opening_height_mm_uneven,Number,Yes,94,,
29,Height Deduction (mm) (Uneven Split - Left),bx_dbl_hinge_height_deduction_mm_uneven_left,Number,Yes,95,Client can set default Height Deduction in their profile,
30,MAKE LEFT Door Height (mm) (Uneven Split),bx_dbl_hinge_make_left_door_height_mm_uneven,Number,Yes,96,*Door Height must be between 700 - 3100mm,
31,LEFT Jamb to Centre TOP Measurement (mm) (Uneven Split),bx_dbl_hinge_left_jamb_centre_top_mm_uneven,Number,Yes,97,,
32,TOP LEFT Jamb to Centre Deduction (mm) (Uneven Split),bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven,Number,Yes,98,Client can set default Uneven Split TOP Width Deduction in their profile,
33,MAKE LEFT Door TOP Width (mm) (Uneven Split),bx_dbl_hinge_make_left_door_top_width_mm_uneven,Number,Yes,99,*Door Width must be between 300 - 2000mm\n*Please enter a Top Width that varies less than 7mm from Middle Width.,
34,LEFT Jamb to Centre MIDDLE Measurement (mm) (Uneven Split),bx_dbl_hinge_left_jamb_centre_middle_mm_uneven,Number,Yes,100,,
35,MIDDLE LEFT Jamb to Centre Deduction (mm) (Uneven Split),bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven,Number,Yes,101,Client can set default Uneven Split MIDDLE Width Deduction in their profile,
36,MAKE LEFT Door MIDDLE Width (mm) (Uneven Split),bx_dbl_hinge_make_left_door_middle_width_mm_uneven,Number,Yes,102,*Door Width must be between 300 - 2000mm\n*Please enter a Middle Width that varies less than 7mm from Top & Bottom Width.,
37,LEFT Jamb to Centre BOTTOM Measurement (mm) (Uneven Split),bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven,Number,Yes,103,,
38,BOTTOM LEFT Jamb to Centre Deduction (mm) (Uneven Split),bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven,Number,Yes,104,Client can set default Uneven Split BOTTOM Width Deduction in their profile,
39,MAKE LEFT Door BOTTOM Width (mm) (Uneven Split),bx_dbl_hinge_make_left_door_bottom_width_mm_uneven,Number,Yes,105,*Door Width must be between 300 - 2000mm\n*Please enter a Bottom Width that varies less than 7mm from Middle Width.,
40,RIGHT Opening Height (mm) (Uneven Split),bx_dbl_hinge_right_opening_height_mm_uneven,Number,Yes,106,,
41,Height Deduction (mm) (Uneven Split - Right),bx_dbl_hinge_height_deduction_mm_uneven_right,Number,Yes,107,Client can set default Height Deduction in their profile,
42,MAKE RIGHT Door Height (mm) (Uneven Split),bx_dbl_hinge_make_right_door_height_mm_uneven,Number,Yes,108,*Door Height must be between 700 - 3100mm,
43,RIGHT Jamb to Centre TOP Measurement (mm) (Uneven Split),bx_dbl_hinge_right_jamb_centre_top_mm_uneven,Number,Yes,109,,
44,TOP RIGHT Jamb to Centre Deduction (mm) (Uneven Split),bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven,Number,Yes,110,Client can set default Uneven Split TOP Width Deduction in their profile,
45,MAKE RIGHT Door TOP Width (mm) (Uneven Split),bx_dbl_hinge_make_right_door_top_width_mm_uneven,Number,Yes,111,*Door Width must be between 300 - 2000mm\n*Please enter a Top Width that varies less than 7mm from Middle Width.,
46,RIGHT Jamb to Centre MIDDLE Measurement (mm) (Uneven Split),bx_dbl_hinge_right_jamb_centre_middle_mm_uneven,Number,Yes,112,,
47,MIDDLE RIGHT Jamb to Centre Deduction (mm) (Uneven Split),bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven,Number,Yes,113,Client can set default Uneven Split MIDDLE Width Deduction in their profile,
48,MAKE RIGHT Door MIDDLE Width (mm) (Uneven Split),bx_dbl_hinge_make_right_door_middle_width_mm_uneven,Number,Yes,114,*Door Width must be between 300 - 2000mm\n*Please enter a Middle Width that varies less than 7mm from Top & Bottom Width.,
49,RIGHT Jamb to Centre BOTTOM Measurement (mm) (Uneven Split),bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven,Number,Yes,115,,
50,BOTTOM RIGHT Jamb to Centre Deduction (mm) (Uneven Split),bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven,Number,Yes,116,Client can set default Uneven Split BOTTOM Width Deduction in their profile,
51,MAKE RIGHT Door BOTTOM Width (mm) (Uneven Split),bx_dbl_hinge_make_right_door_bottom_width_mm_uneven,Number,Yes,117,*Door Width must be between 300 - 2000mm\n*Please enter a Bottom Width that varies less than 7mm from Middle Width.,
52,MAKE LEFT Door Height (mm) (Manual),bx_dbl_hinge_make_left_door_height_mm_manual,Number,Yes,118,*Door Height must be between 700 - 3100mm,
53,MAKE LEFT Door TOP Width (mm) (Manual),bx_dbl_hinge_make_left_door_top_width_mm_manual,Number,Yes,119,*Door Width must be between 300 - 2000mm\n*Please enter a Top Width that varies less than 7mm from Middle Width.,
54,MAKE LEFT Door MIDDLE Width (mm) (Manual),bx_dbl_hinge_make_left_door_middle_width_mm_manual,Number,Yes,120,*Door Width must be between 300 - 2000mm\n*Please enter a Middle Width that varies less than 7mm from Top & Bottom Width.,
55,MAKE LEFT Door BOTTOM Width (mm) (Manual),bx_dbl_hinge_make_left_door_bottom_width_mm_manual,Number,Yes,121,*Door Width must be between 300 - 2000mm\n*Please enter a Bottom Width that varies less than 7mm from Middle Width.,
56,MAKE RIGHT Door Height (mm) (Manual),bx_dbl_hinge_make_right_door_height_mm_manual,Number,Yes,122,*Door Height must be between 700 - 3100mm,
57,MAKE RIGHT Door TOP Width (mm) (Manual),bx_dbl_hinge_make_right_door_top_width_mm_manual,Number,Yes,123,*Door Width must be between 300 - 2000mm\n*Please enter a Top Width that varies less than 7mm from Middle Width.,
58,MAKE RIGHT Door MIDDLE Width (mm) (Manual),bx_dbl_hinge_make_right_door_middle_width_mm_manual,Number,Yes,124,*Door Width must be between 300 - 2000mm\n*Please enter a Middle Width that varies less than 7mm from Top & Bottom Width.,
59,MAKE RIGHT Door BOTTOM Width (mm) (Manual),bx_dbl_hinge_make_right_door_bottom_width_mm_manual,Number,Yes,125,*Door Width must be between 300 - 2000mm\n*Please enter a Bottom Width that varies less than 7mm from Middle Width.,
60,Swing,bx_dbl_hinge_swing,Selection,Yes,126,,
61,T-Section Mullion w/ Mohair (Inswing),bx_dbl_hinge_t_section_mullion_inswing,Selection,Yes,127,,
62,French Door Mullion w/ 2 Caps & Mohair (Outswing),bx_dbl_hinge_french_door_mullion_outswing,Selection,Yes,128,,
63,Lock Brand,bx_dbl_hinge_lock_brand,Selection,Yes,10,,
64,Austral Elegance XC - Lock Colour,bx_dbl_hinge_austral_elegance_xc_lock_colour,Selection,Yes,11,,
65,Austral Elegance XC - Cylinder,bx_dbl_hinge_austral_elegance_xc_cylinder,Selection,Yes,12,,
66,Austral Elegance XC - Lock Height Location,bx_dbl_hinge_austral_elegance_xc_lock_height_location,Selection,Yes,13,,
67,Austral Elegance XC - Lock Height (mm) (Top of Cut Out),bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out,Number,Yes,14,*Lock Height must be greater than 430mm and less than door height - 270mm,
68,Austral Elegance XC - Lock Type (Top of Cut Out),bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out,Selection,Yes,15,,
69,Austral Elegance XC - Lock Height (mm) (Centre of Cut Out),bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out,Number,Yes,16,*Lock Height must be greater than 351mm and less than door height - 349mm,
70,Austral Elegance XC - Lock Type (Centre of Cut Out),bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out,Selection,Yes,17,,
71,Austral Elegance XC - Lock Height (mm) (Bottom of Cut Out),bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out,Number,Yes,18,*Lock Height must be greater than 272mm and less than door height - 428mm,
72,Austral Elegance XC - Lock Type (Bottom of Cut Out),bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out,Selection,Yes,19,,
73,Austral Elegance XC - Lock Height (mm) (Centre of Handle Lever),bx_dbl_hinge_austral_elegance_xc_lh_centre_handle,Number,Yes,20,*Lock Height must be greater than 383mm and less than door height - 317mm,
74,Austral Elegance XC - Lock Type (Centre of Handle Lever),bx_dbl_hinge_austral_elegance_xc_lt_centre_handle,Selection,Yes,21,,
75,Austral Elegance XC - Lock Type (Centre Handle Lever to Centre of Midrail)),bx_dbl_hinge_austral_elegance_xc__lt_centre_midrail,Selection,Yes,22,,
76,CommandeX Hinged - Lock Colour,bx_dbl_hinge_commandex_hinged_lock_colour,Selection,Yes,23,,
77,CommandeX Hinged - Cylinder,bx_dbl_hinge_commandex_hinged_cylinder,Selection,Yes,24,,
78,CommandeX Hinged - Lock Height Location,bx_dbl_hinge_commandex_hinged_lock_height_location,Selection,Yes,25,,
79,CommandeX Hinged - Lock Height (mm) (Top of Cut Out),bx_dbl_hinge_commandex_hinged_lh_top_cut_out,Number,Yes,26,*Lock Height must be greater than 430mm and less than door height - 270mm,
80,CommandeX Hinged - Lock Type (Top of Cut Out),bx_dbl_hinge_commandex_hinged_lt_top_cut_out,Selection,Yes,27,,
81,CommandeX Hinged - Lock Height (mm) (Centre of Cut Out),bx_dbl_hinge_commandex_hinged_lh_centre_cut_out,Number,Yes,28,*Lock Height must be greater than 351mm and less than door height - 349mm,
82,CommandeX Hinged - Lock Type (Centre of Cut Out),bx_dbl_hinge_commandex_hinged_lt_centre_cut_out,Selection,Yes,29,,
83,CommandeX Hinged - Lock Height (mm) (Bottom of Cut Out),bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out,Number,Yes,30,*Lock Height must be greater than 272mm and less than door height - 428mm,
84,CommandeX Hinged - Lock Type (Bottom of Cut Out),bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out,Selection,Yes,31,,
85,CommandeX Hinged - Lock Height (mm) (Centre of Handle Lever),bx_dbl_hinge_commandex_hinged_lh_centre_handle,Number,Yes,32,*Lock Height must be greater than 351mm and less than door height - 349mm,
86,CommandeX Hinged - Lock Type (Centre of Handle Lever),bx_dbl_hinge_commandex_hinged_lt_centre_handle,Selection,Yes,33,,
87,CommandeX Hinged - Lock Type (Centre Handle Lever to Centre of Midrail),bx_dbl_hinge_commandex_hinged_lt_centre_midrail,Selection,Yes,34,,
88,Lockwood 8654 - Lock Colour,bx_dbl_hinge_lockwood_8654_lock_colour,Selection,Yes,35,,
89,Lockwood 8654 - Cylinder,bx_dbl_hinge_lockwood_8654_cylinder,Selection,Yes,36,,
90,Lockwood 8654 - Lock Height Location,bx_dbl_hinge_lockwood_8654_lock_height_location,Selection,Yes,37,,
91,Lockwood 8654 - Lock Height (mm) (Top of Cut Out),bx_dbl_hinge_lockwood_8654_lh_top_cut_out,Number,Yes,38,*Lock Height must be greater than 430mm and less than door height - 270mm,
92,Lockwood 8654 - Lock Type (Top of Cut Out),bx_dbl_hinge_lockwood_8654_lt_top_cut_out,Selection,Yes,39,,
93,Lockwood 8654 - Lock Height (mm) (Centre of Cut Out),bx_dbl_hinge_lockwood_8654_lh_centre_cut_out,Number,Yes,40,*Lock Height must be greater than 351mm and less than door height - 349mm,
94,Lockwood 8654 - Lock Type (Centre of Cut Out),bx_dbl_hinge_lockwood_8654_lt_centre_cut_out,Selection,Yes,41,,
95,Lockwood 8654 - Lock Height (mm) (Bottom of Cut Out),bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out,Number,Yes,42,*Lock Height must be greater than 272mm and less than door height - 428mm,
96,Lockwood 8654 - Lock Type (Bottom of Cut Out),bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out,Selection,Yes,43,,
97,Lockwood 8654 - Lock Height (mm) (Centre of Handle Lever),bx_dbl_hinge_lockwood_8654_lh_centre_handle,Number,Yes,44,*Lock Height must be greater than 370mm and less than door height - 330mm,
98,Lockwood 8654 - Lock Type (Centre of Handle Lever),bx_dbl_hinge_lockwood_8654_lt_centre_handle,Selection,Yes,45,,
99,Lockwood 8654 - Lock Type (Centre Handle Lever to Centre of Midrail),bx_dbl_hinge_lockwood_8654_lt_centre_midrail,Selection,Yes,46,,
100,Whitco MK2 - Lock Colour,bx_dbl_hinge_whitco_mk2_lock_colour,Selection,Yes,47,,
101,Whitco MK2 - Cylinder,bx_dbl_hinge_whitco_mk2_cylinder,Selection,Yes,48,,
102,Whitco MK2 - Lock Height Location,bx_dbl_hinge_whitco_mk2_lock_height_location,Selection,Yes,49,,
103,Whitco MK2 - Lock Height (mm) (Top of Cut Out),bx_dbl_hinge_whitco_mk2_lh_top_cut_out,Number,Yes,50,*Lock Height must be greater than 430mm and less than door height - 270mm,
104,Whitco MK2 - Lock Type (Top of Cut Out),bx_dbl_hinge_whitco_mk2_lt_top_cut_out,Selection,Yes,51,,
105,Whitco MK2 - Lock Height (mm) (Centre of Cut Out),bx_dbl_hinge_whitco_mk2_lh_centre_cut_out,Number,Yes,52,*Lock Height must be greater than 351mm and less than door height - 349mm,
106,Whitco MK2 - Lock Type (Centre of Cut Out),bx_dbl_hinge_whitco_mk2_lt_centre_cut_out,Selection,Yes,53,,
107,Whitco MK2 - Lock Height (mm) (Bottom of Cut Out),bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out,Number,Yes,54,*Lock Height must be greater than 272mm and less than door height - 428mm,
108,Whitco MK2 - Lock Type (Bottom of Cut Out),bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out,Selection,Yes,55,,
109,Whitco MK2 - Lock Height (mm) (Centre of Handle Lever),bx_dbl_hinge_whitco_mk2_lh_centre_handle,Number,Yes,56,*Lock Height must be greater than 367mm and less than door height - 333mm,
110,Whitco MK2 - Lock Type (Centre of Handle Lever),bx_dbl_hinge_whitco_mk2_lt_centre_handle,Selection,Yes,57,,
111,Whitco MK2 - Lock Type (Centre Handle Lever to Centre of Midrail),bx_dbl_hinge_whitco_mk2_lt_centre_midrail,Selection,Yes,58,,
112,Whitco Tasman ESCAPE - Lock Colour,bx_dbl_hinge_whitco_tasman_escape_lock_colour,Selection,Yes,59,,
113,Whitco Tasman ESCAPE - Cylinder,bx_dbl_hinge_whitco_tasman_escape_cylinder,Selection,Yes,60,,
114,Whitco Tasman ESCAPE - Lock Height Location,bx_dbl_hinge_whitco_tasman_escape_lock_height_location,Selection,Yes,61,,
115,Whitco Tasman ESCAPE - Lock Height (mm) (Top of Cut Out),bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out,Number,Yes,62,*Lock Height must be greater than 430mm and less than door height - 270mm,
116,Whitco Tasman ESCAPE - Lock Type (Top of Cut Out),bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out,Selection,Yes,63,,
117,Whitco Tasman ESCAPE - Lock Height (mm) (Centre of Cut Out),bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out,Number,Yes,64,*Lock Height must be greater than 351mm and less than door height - 349mm,
118,Whitco Tasman ESCAPE - Lock Type (Centre of Cut Out),bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out,Selection,Yes,65,,
119,Whitco Tasman ESCAPE - Lock Height (mm) (Bottom of Cut Out),bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out,Number,Yes,66,*Lock Height must be greater than 272mm and less than door height - 428mm,
120,Whitco Tasman ESCAPE - Lock Type (Bottom of Cut Out),bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out,Selection,Yes,67,,
121,Whitco Tasman ESCAPE - Lock Height (mm) (Centre of Handle Lever),bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle,Number,Yes,68,*Lock Height must be greater than 370mm and less than door height - 330mm,
122,Whitco Tasman ESCAPE - Lock Type (Centre of Handle Lever),bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle,Selection,Yes,69,,
123,Whitco Tasman ESCAPE - Lock Type (Centre Handle Lever to Centre of Midrail),bx_dbl_hinge_whitco_tasman_escape_lt_centre_midrail,Selection,Yes,70,,
124,Austral Elegance XC CUT OUT ONLY - Cylinder,bx_dbl_hinge_austral_elegance_xc_co_cylinder,Selection,Yes,71,,
125,Austral Elegance XC CUT OUT ONLY - Lock Height Location,bx_dbl_hinge_austral_elegance_xc_co_lock_height_location,Selection,Yes,72,,
126,Austral Elegance XC CUT OUT ONLY - Lock Height (mm) (Top of Cut Out),bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out,Number,Yes,73,*Lock Height must be greater than 430mm and less than door height - 270mm,
127,Austral Elegance XC CUT OUT ONLY - Lock Type (Top of Cut Out),bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out,Selection,Yes,74,,
128,Austral Elegance XC CUT OUT ONLY - Lock Height (mm) (Centre of Cut Out),bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out,Number,Yes,75,*Lock Height must be greater than 351mm and less than door height - 349mm,
129,Austral Elegance XC CUT OUT ONLY - Lock Type (Centre of Cut Out),bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out,Selection,Yes,76,,
130,Austral Elegance XC CUT OUT ONLY - Lock Height (mm) (Bottom of Cut Out),bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out,Number,Yes,77,*Lock Height must be greater than 272mm and less than door height - 428mm,
131,Austral Elegance XC CUT OUT ONLY - Lock Type (Bottom of Cut Out),bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out,Selection,Yes,78,,
132,Austral Elegance XC CUT OUT ONLY - Lock Height (mm) (Centre of Handle Lever),bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle,Number,Yes,79,*Lock Height must be greater than 383mm and less than door height - 317mm,
133,Austral Elegance XC CUT OUT ONLY - Lock Type (Centre of Handle Lever),bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle,Selection,Yes,80,,
134,Austral Elegance XC CUT OUT ONLY - Lock Type (Centre Handle Lever to Centre of Midrail),bx_dbl_hinge_austral_elegance_xc_co_lt_centre_midrail,Selection,Yes,81,,
135,CommandeX Hinged CUT OUT ONLY - Cylinder,bx_dbl_hinge_commandex_hinged_co_cylinder,Selection,Yes,82,,
136,CommandeX Hinged CUT OUT ONLY - Lock Height Location,bx_dbl_hinge_commandex_hinged_co_lock_height_location,Selection,Yes,83,,
137,CommandeX Hinged CUT OUT ONLY - Lock Height (mm) (Top of Cut Out),bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out,Number,Yes,84,*Lock Height must be greater than 430mm and less than door height - 270mm,
138,CommandeX Hinged CUT OUT ONLY - Lock Type (Top of Cut Out),bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out,Selection,Yes,85,,
139,CommandeX Hinged CUT OUT ONLY - Lock Height (mm) (Centre of Cut Out),bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out,Number,Yes,86,*Lock Height must be greater than 351mm and less than door height - 349mm,
140,CommandeX Hinged CUT OUT ONLY - Lock Type (Centre of Cut Out),bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out,Selection,Yes,87,,
141,CommandeX Hinged CUT OUT ONLY - Lock Height (mm) (Bottom of Cut Out),bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out,Number,Yes,88,*Lock Height must be greater than 272mm and less than door height - 428mm,
142,CommandeX Hinged CUT OUT ONLY - Lock Type (Bottom of Cut Out),bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out,Selection,Yes,89,,
143,CommandeX Hinged CUT OUT ONLY - Lock Height (mm) (Centre of Handle Lever),bx_dbl_hinge_commandex_hinged_co_lh_centre_handle,Number,Yes,90,*Lock Height must be greater than 370mm and less than door height - 330mm,
144,CommandeX Hinged CUT OUT ONLY - Lock Type (Centre of Handle Lever),bx_dbl_hinge_commandex_hinged_co_lt_centre_handle,Selection,Yes,91,,
145,CommandeX Hinged CUT OUT ONLY - Lock Type (Centre Handle Lever to Centre of Midrail),bx_dbl_hinge_commandex_hinged_co_lt_centre_midrail,Selection,Yes,92,,
146,Lockwood 8654 CUT OUT ONLY - Cylinder,bx_dbl_hinge_lockwood_8654_co_cylinder,Selection,Yes,93,,
147,Lockwood 8654 CUT OUT ONLY - Lock Height Location,bx_dbl_hinge_lockwood_8654_co_lock_height_location,Selection,Yes,94,,
148,Lockwood 8654 CUT OUT ONLY - Lock Height (mm) (Top of Cut Out),bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out,Number,Yes,95,*Lock Height must be greater than 430mm and less than door height - 270mm,
149,Lockwood 8654 CUT OUT ONLY - Lock Type (Top of Cut Out),bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out,Selection,Yes,96,,
150,Lockwood 8654 CUT OUT ONLY - Lock Height (mm) (Centre of Cut Out),bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out,Number,Yes,97,*Lock Height must be greater than 351mm and less than door height - 349mm,
151,Lockwood 8654 CUT OUT ONLY - Lock Type (Centre of Cut Out),bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out,Selection,Yes,98,,
152,Lockwood 8654 CUT OUT ONLY - Lock Height (mm) (Bottom of Cut Out),bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out,Number,Yes,99,*Lock Height must be greater than 272mm and less than door height - 428mm,
153,Lockwood 8654 CUT OUT ONLY - Lock Type (Bottom of Cut Out),bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out,Selection,Yes,100,,
154,Lockwood 8654 CUT OUT ONLY - Lock Height (mm) (Centre of Handle Lever),bx_dbl_hinge_lockwood_8654_co_lh_centre_handle,Number,Yes,101,*Lock Height must be greater than 370mm and less than door height - 330mm,
155,Lockwood 8654 CUT OUT ONLY - Lock Type (Centre of Handle Lever),bx_dbl_hinge_lockwood_8654_co_lt_centre_handle,Selection,Yes,102,,
156,Lockwood 8654 CUT OUT ONLY - Lock Type (Centre Handle Lever to Centre of Midrail),bx_dbl_hinge_lockwood_8654_co_lt_centre_midrail,Selection,Yes,103,,
157,Whitco MK2 CUT OUT ONLY - Cylinder,bx_dbl_hinge_whitco_mk2_co_cylinder,Selection,Yes,104,,
158,Whitco MK2 CUT OUT ONLY - Lock Height Location,bx_dbl_hinge_whitco_mk2_co_lock_height_location,Selection,Yes,105,,
159,Whitco MK2 CUT OUT ONLY - Lock Height (mm) (Top of Cut Out),bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out,Number,Yes,106,*Lock Height must be greater than 430mm and less than door height - 270mm,
160,Whitco MK2 CUT OUT ONLY - Lock Type (Top of Cut Out),bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out,Selection,Yes,107,,
161,Whitco MK2 CUT OUT ONLY - Lock Height (mm) (Centre of Cut Out),bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out,Number,Yes,108,*Lock Height must be greater than 351mm and less than door height - 349mm,
162,Whitco MK2 CUT OUT ONLY - Lock Type (Centre of Cut Out),bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out,Selection,Yes,109,,
163,Whitco MK2 CUT OUT ONLY - Lock Height (mm) (Bottom of Cut Out),bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out,Number,Yes,110,*Lock Height must be greater than 272mm and less than door height - 428mm,
164,Whitco MK2 CUT OUT ONLY - Lock Type (Bottom of Cut Out),bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out,Selection,Yes,111,,
165,Whitco MK2 CUT OUT ONLY - Lock Height (mm) (Centre of Handle Lever),bx_dbl_hinge_whitco_mk2_co_lh_centre_handle,Number,Yes,112,*Lock Height must be greater than 367mm and less than door height - 333mm,
166,Whitco MK2 CUT OUT ONLY - Lock Type (Centre of Handle Lever),bx_dbl_hinge_whitco_mk2_co_lt_centre_handle,Selection,Yes,113,,
167,Whitco MK2 CUT OUT ONLY - Lock Type (Centre Handle Lever to Centre of Midrail),bx_dbl_hinge_whitco_mk2_co_lt_centre_midrail,Selection,Yes,114,,
168,Lock or Cut Out Side - OUTSIDE VIEW (Double Door),bx_dbl_hinge_lock_cut_out_side_dd,Selection,Yes,115,,
169,Non-Lock Door Striker Cut Outs,bx_dbl_hinge_non_lock_door_striker_cutouts,Selection,Yes,116,,
170,Flush Bolts & Patio Bolts (Non-Lock Door),bx_dbl_hinge_non_lock_door_bolts,Selection,Yes,117,,
171,Patio Bolt Colour (Non-Lock Door),bx_dbl_hinge_patio_bolt_colour_non_lock,Selection,Yes,118,,
172,Midrail (Conditional Case 1),bx_dbl_hinge_midrail_case1,Selection,Yes,119,,
173,Midrail Location (Conditional Case 1),bx_dbl_hinge_midrail_location_case_1,Selection,Yes,120,,
174,Midrail Height (mm) - TOP OF MIDRAIL (Conditional Case 1),bx_dbl_hinge_midrail_height_mm_case1,Number,Yes,121,,
175,"Midrail Colour (Standard Frame, Conditional Case 1)",bx_dbl_hinge_midrail_colour_std_case1,Selection,Yes,122,,
176,"Midrail Colour (Special Frame, Conditional Case 1)",bx_dbl_hinge_midrail_colour_special_case1,Selection,Yes,123,,
177,Kickplate or Bottom Infill Type (Conditional Case 1),bx_dbl_hinge_kickplate_or_bottom_infill_type_case1,Selection,Yes,124,,
178,Infill Colour (Conditional Case 1),bx_dbl_hinge_infill_colour_conditional_case_1,Selection,Yes,125,,
179,Kickplate Height (Conditional Case 1),bx_dbl_hinge_kickplate_height_conditional_case1,Selection,Yes,126,,
180,Kickplate Height (mm) - TOP OF SUPPORT MIDRAIL,bx_dbl_hinge_kickplate_height_mm__top_of_supp,Number,Yes,127,*Min: 170mm - Max: Door Midrail Height,
181,Kickplate & Support Midrail Colour (Conditional Case 1),bx_dbl_hinge_kickplate__support_midrail_colour_case1,Selection,Yes,128,,
182,Midrail (Conditional Case 2),bx_dbl_hinge_midrail_case2,Selection,Yes,129,,
183,Midrail Height (mm) - TOP OF MIDRAIL (Conditional Case 2),bx_dbl_hinge_midrail_height_mm_case2,Number,Yes,130,,
184,"Midrail Colour (Standard Frame, Conditional Case 2)",bx_dbl_hinge_midrail_colour_std_case2,Selection,Yes,131,,
185,"Midrail Colour (Special Frame, Conditional Case 2)",bx_dbl_hinge_midrail_colour_special_case2,Selection,Yes,132,,
186,Number of Security Hinges (Pick Up),bx_dbl_hinge_num_sec_hinges_pickup,Selection,Yes,133,,
187,Number of Security Hinges (Deliver/Site),bx_dbl_hinge_num_sec_hinges_deliver,Selection,Yes,134,,
188,"Specific Hinge Locations? (2 Hinges, Pick Up, Double Door)",bx_dbl_hinge_spec_hinge_loc_2_pickup_dd,Selection,Yes,135,,
189,"Top of Door to Top of Top Hinge (mm) (2 Hinges, Pick Up, Double Door)",bx_dbl_hinge_top_hinge_pos_2_pickup_dd,Number,Yes,136,,
190,"Bottom of Door to Bottom of Bottom Hinge (mm) (2 Hinges, Pick Up, Double Door)",bx_dbl_hinge_bottom_hinge_pos_2_pickup_dd,Number,Yes,137,,
191,"Specific Hinge Locations? (3 Hinges, Pick Up, Double Door)",bx_dbl_hinge_spec_hinge_loc_3_pickup_dd,Selection,Yes,138,,
192,"Top of Door to Top of Top Hinge (mm) (3 Hinges, Pick Up, Double Door)",bx_dbl_hinge_top_hinge_pos_3_pickup_dd,Number,Yes,139,,
193,"Bottom of Door to Centre of Centre Hinge (mm) (3 Hinges, Pick Up, Double Door)",bx_dbl_hinge_center_hinge_pos_3_pickup_dd,Number,Yes,140,,
194,"Bottom of Door to Bottom of Bottom Hinge (mm) (3 Hinges, Pick Up, Double Door)",bx_dbl_hinge_bottom_hinge_pos_3_pickup_dd,Number,Yes,141,,
195,"Specific Hinge Locations? (4 Hinges, Pick Up, Double Door)",bx_dbl_hinge_spec_hinge_loc_4_pickup_dd,Selection,Yes,142,,
196,"Top of Door to Top of Top Hinge (mm) (4 Hinges, Pick Up, Double Door)",bx_dbl_hinge_top_hinge_pos_4_pickup_dd,Number,Yes,143,,
197,"Top of Door to Top of Second Hinge (mm) (4 Hinges, Pick Up, Double Door)",bx_dbl_hinge_second_hinge_pos_4_pickup_dd,Number,Yes,144,,
198,"Bottom of Door to Bottom of Third Hinge (mm) (4 Hinges, Pick Up, Double Door)",bx_dbl_hinge_third_hinge_pos_4_pickup_dd,Number,Yes,145,,
199,"Bottom of Door to Bottom of Bottom Hinge (mm) (4 Hinges, Pick Up, Double Door)",bx_dbl_hinge_bottom_hinge_pos_4_pickup_dd,Number,Yes,146,,
200,"Specific Hinge Locations? (2 Hinges, Deliver/Site, Double Door)",bx_dbl_hinge_spec_hinge_loc_2_deliver_dd,Selection,Yes,147,,
201,"Top of Door to Top of Top Hinge (mm) (2 Hinges, Deliver/Site, Double Door)",bx_dbl_hinge_top_hinge_pos_2_deliver_dd,Number,Yes,148,,
202,"Bottom of Door to Bottom of Bottom Hinge (mm) (2 Hinges, Deliver/Site, Double Door)",bx_dbl_hinge_bottom_hinge_pos_2_deliver_dd,Number,Yes,149,,
203,"Specific Hinge Locations? (3 Hinges, Deliver/Site, Double Door)",bx_dbl_hinge_spec_hinge_loc_3_deliver_dd,Selection,Yes,150,,
204,"Top of Door to Top of Top Hinge (mm) (3 Hinges, Deliver/Site, Double Door)",bx_dbl_hinge_top_hinge_pos_3_deliver_dd,Number,Yes,151,,
205,"Bottom of Door to Centre of Centre Hinge (mm) (3 Hinges, Deliver/Site, Double Door)",bx_dbl_hinge_center_hinge_pos_3_deliver_dd,Number,Yes,152,,
206,"Bottom of Door to Bottom of Bottom Hinge (mm) (3 Hinges, Deliver/Site, Double Door)",bx_dbl_hinge_bottom_hinge_pos_3_deliver_dd,Number,Yes,153,,
207,"Specific Hinge Locations? (4 Hinges, Deliver/Site, Double Door)",bx_dbl_hinge_spec_hinge_loc_4_deliver_dd,Selection,Yes,154,,
208,"Top of Door to Top of Top Hinge (mm) (4 Hinges, Deliver/Site, Double Door)",bx_dbl_hinge_top_hinge_pos_4_deliver_dd,Number,Yes,155,,
209,"Top of Door to Top of Second Hinge (mm) (4 Hinges, Deliver/Site, Double Door)",bx_dbl_hinge_second_hinge_pos_4_deliver_dd,Number,Yes,156,,
210,"Bottom of Door to Bottom of Third Hinge (mm) (4 Hinges, Deliver/Site, Double Door)",bx_dbl_hinge_third_hinge_pos_4_deliver_dd,Number,Yes,157,,
211,"Bottom of Door to Bottom of Bottom Hinge (mm) (4 Hinges, Deliver/Site, Double Door)",bx_dbl_hinge_bottom_hinge_pos_4_deliver_dd,Number,Yes,158,,
212,Security Hinge Type,bx_dbl_hinge_sec_hinge_type,Selection,Yes,159,,
213,Security Hinge Colour (Standard Hinge),bx_dbl_hinge_sec_hinge_colour_std,Selection,Yes,160,,
214,Security Hinge Colour (Prong/Step Hinge),bx_dbl_hinge_sec_hinge_colour_prong_step,Selection,Yes,161,,
215,Security Hinge Packers Quantity,bx_dbl_hinge_sec_hinge_packers_qty,Number,Yes,162,,
216,Closer,bx_dbl_hinge_closer,Selection,Yes,163,,
217,Closer Colour (Austral),bx_dbl_hinge_closer_colour_austral,Selection,Yes,164,,
218,Closer Colour (Whitco),bx_dbl_hinge_closer_colour_whitco,Selection,Yes,165,,
219,Pet Door,bx_dbl_hinge_pet_door,Selection,Yes,166,,
220,Pet Door Colour,bx_dbl_hinge_pet_door_colour,Selection,Yes,167,,
221,Pet Door Location,bx_dbl_hinge_pet_door_location,Selection,Yes,168,,
222,Pet Door Flexible Flap,bx_dbl_hinge_pet_door_flexible_flap,Selection,Yes,169,,
223,Pet Door Surround Infill,bx_dbl_hinge_pet_door_surround_infill,Selection,Yes,170,,
224,Centre Striker Plate Packer,bx_dbl_hinge_center_striker_packer,Selection,Yes,171,,
225,Auxiliary Striker Plate Packers,bx_dbl_hinge_aux_striker_packers,Selection,Yes,172,,
226,Bugstrip Size,bx_dbl_hinge_bugstrip_size,Selection,Yes,173,,
227,Left Bugstrip Length (mm),bx_dbl_hinge_left_bugstrip_length_mm,Number,Yes,174,,
228,Right Bugstrip Length (mm),bx_dbl_hinge_right_bugstrip_length_mm,Number,Yes,175,,
229,Stop Bead Colour,bx_dbl_hinge_stop_bead_colour,Selection,Yes,176,,
230,Stop Bead LEFT Length (mm),bx_dbl_hinge_stop_bead_left_length_mm,Number,Yes,177,,
231,Stop Bead TOP Length (mm),bx_dbl_hinge_stop_bead_top_length_mm,Number,Yes,178,,
232,Stop Bead RIGHT Length (mm),bx_dbl_hinge_stop_bead_right_length_mm,Number,Yes,179,,
233,Adhesive Mohair,bx_dbl_hinge_adhesive_mohair,Selection,Yes,180,,
234,Adhesive Mohair Length (mm),bx_dbl_hinge_adhesive_mohair_length_mm,Number,Yes,181,,
235,Jamb Adaptor Type (Case 1: No Stop Bead/Mohair),bx_dbl_hinge_jamb_adaptor_type_case1,Selection,Yes,182,,
236,Jamb Adaptor Type (Case 2: Short Leg Required),bx_dbl_hinge_jamb_adaptor_type_case2,Selection,Yes,183,,
237,Jamb Adaptor Type (Case 3: General Jamb Adaptor Required),bx_dbl_hinge_jamb_adaptor_type_case3,Selection,Yes,184,,
238,Opening LEFT Height (mm) - SWING VIEW (Jamb Adaptor),bx_dbl_hinge_jamb_opening_left_height_mm,Number,Yes,185,,
239,Height Addition (mm) (Jamb Adaptor),bx_dbl_hinge_jamb_height_addition_mm,Number,Yes,186,,
240,Jamb Adaptor LEFT Length (mm),bx_dbl_hinge_jamb_adaptor_left_length_mm,Number,Yes,187,,
241,Opening TOP Width (mm) (Jamb Adaptor),bx_dbl_hinge_jamb_opening_top_width_mm,Number,Yes,188,,
242,Jamb Adaptor RIGHT Length (mm),bx_dbl_hinge_jamb_adaptor_right_length_mm,Number,Yes,189,,
243,Width Addition (mm) (Jamb Adaptor),bx_dbl_hinge_jamb_width_addition_mm,Number,Yes,190,,
244,Opening RIGHT Height (mm) - SWING VIEW (Jamb Adaptor),bx_dbl_hinge_jamb_opening_right_height_mm,Number,Yes,190,,
245,Jamb Adaptor TOP Length (mm),bx_dbl_hinge_jamb_adaptor_top_length_mm,Number,Yes,191,,