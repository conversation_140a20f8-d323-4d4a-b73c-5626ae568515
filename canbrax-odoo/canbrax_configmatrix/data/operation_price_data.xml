<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Sample Fixed Price Table Data -->
        
        <!-- Labour Operations -->
        <record id="operation_price_superannuation" model="config.matrix.operation.price">
            <field name="sequence">10</field>
            <field name="description">Superannuation Rate</field>
            <field name="sub_description">%</field>
            <field name="unit_type">hr</field>
            <field name="category">labour</field>
            <field name="cost_price">0.0</field>
            <field name="sale_price">12.00</field>
        </record>

        <record id="operation_price_install_wage" model="config.matrix.operation.price">
            <field name="sequence">20</field>
            <field name="description">Install Wage</field>
            <field name="sub_description">$/yr</field>
            <field name="unit_type">hr</field>
            <field name="category">labour</field>
            <field name="cost_price">74.19</field>
            <field name="sale_price">87437.87</field>
        </record>

        <record id="operation_price_hours_year" model="config.matrix.operation.price">
            <field name="sequence">30</field>
            <field name="description">Hours / Year</field>
            <field name="sub_description">hr</field>
            <field name="unit_type">hr</field>
            <field name="category">labour</field>
            <field name="cost_price">0.0</field>
            <field name="sale_price">1760.0</field>
        </record>

        <record id="operation_price_travel_load_time" model="config.matrix.operation.price">
            <field name="sequence">40</field>
            <field name="description">Travel &amp; Load Time / Day</field>
            <field name="sub_description">hr</field>
            <field name="unit_type">hr</field>
            <field name="category">labour</field>
            <field name="cost_price">0.0</field>
            <field name="sale_price">2.0</field>
        </record>

        <!-- Fixed Time Operations -->
        <record id="operation_price_receive_material" model="config.matrix.operation.price">
            <field name="sequence">100</field>
            <field name="description">Receive Material Time</field>
            <field name="sub_description">hr / Item Manufactured</field>
            <field name="unit_type">each</field>
            <field name="category">fixed_time</field>
            <field name="cost_price">0.0</field>
            <field name="sale_price">0.035</field>
        </record>

        <record id="operation_price_cut_extrusions_double_mitre" model="config.matrix.operation.price">
            <field name="sequence">110</field>
            <field name="description">Cut Extrusions on Double Mitre Saw</field>
            <field name="sub_description">hr/length</field>
            <field name="unit_type">length</field>
            <field name="category">fixed_time</field>
            <field name="cost_price">0.0</field>
            <field name="sale_price">0.050</field>
        </record>

        <record id="operation_price_man2_cut_mesh" model="config.matrix.operation.price">
            <field name="sequence">120</field>
            <field name="description">2nd Man to Help Cut SelfWaterSeries Mesh or Carry CTS</field>
            <field name="sub_description">hr</field>
            <field name="unit_type">hr</field>
            <field name="category">fixed_time</field>
            <field name="cost_price">0.0</field>
            <field name="sale_price">0.071</field>
        </record>

        <record id="operation_price_extra_time_hand_cut" model="config.matrix.operation.price">
            <field name="sequence">130</field>
            <field name="description">Extra Time to Hand Cut Mesh/Grille under 610mm</field>
            <field name="sub_description">hr</field>
            <field name="unit_type">hr</field>
            <field name="category">fixed_time</field>
            <field name="cost_price">0.0</field>
            <field name="sale_price">0.134</field>
        </record>

        <record id="operation_price_cut_extrusions_upcut" model="config.matrix.operation.price">
            <field name="sequence">140</field>
            <field name="description">Cut Extrusions on Upcut Saw</field>
            <field name="sub_description">hr/length</field>
            <field name="unit_type">length</field>
            <field name="category">fixed_time</field>
            <field name="cost_price">0.0</field>
            <field name="sale_price">0.075</field>
        </record>

        <!-- CNC Operations -->
        <record id="operation_price_cnc_load_unload" model="config.matrix.operation.price">
            <field name="sequence">200</field>
            <field name="description">CNC Load / Unload Extrusions &amp; Set Up</field>
            <field name="sub_description">hr</field>
            <field name="unit_type">hr</field>
            <field name="category">setup</field>
            <field name="cost_price">0.0</field>
            <field name="sale_price">0.033</field>
        </record>

        <record id="operation_price_cnc_centre_lock" model="config.matrix.operation.price">
            <field name="sequence">210</field>
            <field name="description">CNC Centre Lock Cut Outs</field>
            <field name="sub_description">hr</field>
            <field name="unit_type">each</field>
            <field name="category">fixed_time</field>
            <field name="cost_price">0.0</field>
            <field name="sale_price">0.033</field>
        </record>

        <record id="operation_price_cnc_3pt_double_aux" model="config.matrix.operation.price">
            <field name="sequence">220</field>
            <field name="description">CNC 3PT / Double Auxiliary Lock Cut Outs</field>
            <field name="sub_description">hr</field>
            <field name="unit_type">each</field>
            <field name="category">fixed_time</field>
            <field name="cost_price">0.0</field>
            <field name="sale_price">0.050</field>
        </record>

        <record id="operation_price_cnc_hinge_holes" model="config.matrix.operation.price">
            <field name="sequence">230</field>
            <field name="description">CNC Hinge Holes</field>
            <field name="sub_description">hr/hinge</field>
            <field name="unit_type">hinge</field>
            <field name="category">fixed_time</field>
            <field name="cost_price">0.0</field>
            <field name="sale_price">0.004</field>
        </record>

        <!-- Assembly Operations -->
        <record id="operation_price_bend_frame" model="config.matrix.operation.price">
            <field name="sequence">300</field>
            <field name="description">Bend Frame</field>
            <field name="sub_description">hr/pair</field>
            <field name="unit_type">pair</field>
            <field name="category">finishing</field>
            <field name="cost_price">0.0</field>
            <field name="sale_price">0.117</field>
        </record>

        <record id="operation_price_cnc_fire_escape_notch" model="config.matrix.operation.price">
            <field name="sequence">310</field>
            <field name="description">CNC Fire Escape Notch Both Ends on Hinge Side Sash</field>
            <field name="sub_description">hr</field>
            <field name="unit_type">hr</field>
            <field name="category">fixed_time</field>
            <field name="cost_price">0.0</field>
            <field name="sale_price">0.083</field>
        </record>

        <!-- Installation Operations -->
        <record id="operation_price_install_flydoor" model="config.matrix.operation.price">
            <field name="sequence">400</field>
            <field name="description">Install Flydoor Midrail</field>
            <field name="sub_description">hr</field>
            <field name="unit_type">hr</field>
            <field name="category">fixed_time</field>
            <field name="cost_price">0.0</field>
            <field name="sale_price">0.084</field>
        </record>

    </data>
</odoo>
