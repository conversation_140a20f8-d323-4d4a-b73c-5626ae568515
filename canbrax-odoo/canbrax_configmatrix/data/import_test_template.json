{"name": "Subcategory - DBL Hinged BX2", "code": "dbl_hinged_bx2", "version": "1.0", "product_template": "Subcategory - DBL Hinged BX2", "description": "", "export_date": "2025-05-25 23:17:38", "export_version": "2.0", "sections": [{"name": "Initial Setup & Pre-Checks", "sequence": 10, "description": "", "fields": [{"name": "Location", "technical_name": "bx_dbl_hinge_location", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "", "sequence": 10, "question_number": 1, "quantity_formula": "1", "options": [{"name": "Alfresco", "value": "alfresco", "visibility_condition": ""}, {"name": "Art Studio", "value": "art_studio", "visibility_condition": ""}, {"name": "Balcony", "value": "balcony", "visibility_condition": ""}, {"name": "Bathroom 1", "value": "bathroom_1", "visibility_condition": ""}, {"name": "Bathroom 2", "value": "bathroom_2", "visibility_condition": ""}, {"name": "Bathroom 3", "value": "bathroom_3", "visibility_condition": ""}, {"name": "Bathroom 4", "value": "bathroom_4", "visibility_condition": ""}, {"name": "Bathroom 5", "value": "bathroom_5", "visibility_condition": ""}, {"name": "BBQ", "value": "bbq", "visibility_condition": ""}, {"name": "Bed 1", "value": "bed_1", "visibility_condition": ""}, {"name": "Bed 2", "value": "bed_2", "visibility_condition": ""}, {"name": "Bed 3", "value": "bed_3", "visibility_condition": ""}, {"name": "Bed 4", "value": "bed_4", "visibility_condition": ""}, {"name": "Bed 5", "value": "bed_5", "visibility_condition": ""}, {"name": "Bed 6", "value": "bed_6", "visibility_condition": ""}, {"name": "Bed 7", "value": "bed_7", "visibility_condition": ""}, {"name": "Bed 8", "value": "bed_8", "visibility_condition": ""}, {"name": "Bed 9", "value": "bed_9", "visibility_condition": ""}, {"name": "Billiards Room", "value": "billiards_room", "visibility_condition": ""}, {"name": "Bonus Room", "value": "bonus_room", "visibility_condition": ""}, {"name": "Breakfast Nook", "value": "breakfast_nook", "visibility_condition": ""}, {"name": "<PERSON><PERSON>", "value": "butlers_pantry", "visibility_condition": ""}, {"name": "Cellar", "value": "cellar", "visibility_condition": ""}, {"name": "Clerestory", "value": "clerestory", "visibility_condition": ""}, {"name": "Closet", "value": "closet", "visibility_condition": ""}, {"name": "Conservatory", "value": "conservatory", "visibility_condition": ""}, {"name": "Corridor", "value": "corridor", "visibility_condition": ""}, {"name": "Courtyard", "value": "courtyard", "visibility_condition": ""}, {"name": "Craft Room", "value": "craft_room", "visibility_condition": ""}, {"name": "Den", "value": "den", "visibility_condition": ""}, {"name": "Dining Room", "value": "dining_room", "visibility_condition": ""}, {"name": "Dressing Room", "value": "dressing_room", "visibility_condition": ""}, {"name": "Ensuite Bed 1", "value": "ensuite_bed_1", "visibility_condition": ""}, {"name": "Ensuite Bed 2", "value": "ensuite_bed_2", "visibility_condition": ""}, {"name": "Ensuite Bed 3", "value": "ensuite_bed_3", "visibility_condition": ""}, {"name": "Ensuite Bed 4", "value": "ensuite_bed_4", "visibility_condition": ""}, {"name": "Ensuite Bed 5", "value": "ensuite_bed_5", "visibility_condition": ""}, {"name": "Ensuite Bed 6", "value": "ensuite_bed_6", "visibility_condition": ""}, {"name": "Ensuite Bed 7", "value": "ensuite_bed_7", "visibility_condition": ""}, {"name": "Ensuite Bed 8", "value": "ensuite_bed_8", "visibility_condition": ""}, {"name": "Ensuite Bed 9", "value": "ensuite_bed_9", "visibility_condition": ""}, {"name": "Ensuite Hers", "value": "ensuite_hers", "visibility_condition": ""}, {"name": "Ensuite His", "value": "ensuite_his", "visibility_condition": ""}, {"name": "Entry", "value": "entry", "visibility_condition": ""}, {"name": "Entryway", "value": "entryway", "visibility_condition": ""}, {"name": "Exercise Room", "value": "exercise_room", "visibility_condition": ""}, {"name": "Family Room", "value": "family_room", "visibility_condition": ""}, {"name": "Formal Dining Room", "value": "formal_dining_room", "visibility_condition": ""}, {"name": "<PERSON><PERSON><PERSON>", "value": "foyer", "visibility_condition": ""}, {"name": "Front Door", "value": "front_door", "visibility_condition": ""}, {"name": "Games Room", "value": "games_room", "visibility_condition": ""}, {"name": "Games Room Bathroom", "value": "games_room_bathroom", "visibility_condition": ""}, {"name": "Games Room Storage", "value": "games_room_storage", "visibility_condition": ""}, {"name": "Games Room Toilet", "value": "games_room_toilet", "visibility_condition": ""}, {"name": "Games Room WC", "value": "games_room_wc", "visibility_condition": ""}, {"name": "Games Room WIR", "value": "games_room_wir", "visibility_condition": ""}, {"name": "Garage", "value": "garage", "visibility_condition": ""}, {"name": "Granny Flat", "value": "granny_flat", "visibility_condition": ""}, {"name": "Granny Flat Bathroom", "value": "granny_flat_bathroom", "visibility_condition": ""}, {"name": "Granny Flat Bedroom", "value": "granny_flat_bedroom", "visibility_condition": ""}, {"name": "Granny Flat Ensuite", "value": "granny_flat_ensuite", "visibility_condition": ""}, {"name": "Granny Flat Kitchen", "value": "granny_flat_kitchen", "visibility_condition": ""}, {"name": "Granny Flat Laundry", "value": "granny_flat_laundry", "visibility_condition": ""}, {"name": "Granny Flat Living", "value": "granny_flat_living", "visibility_condition": ""}, {"name": "Granny Flat Lounge", "value": "granny_flat_lounge", "visibility_condition": ""}, {"name": "<PERSON> Flat Toilet", "value": "granny_flat_toilet", "visibility_condition": ""}, {"name": "Granny Flat WC", "value": "granny_flat_wc", "visibility_condition": ""}, {"name": "Granny Flat WIR", "value": "granny_flat_wir", "visibility_condition": ""}, {"name": "Guest Bed", "value": "guest_bed", "visibility_condition": ""}, {"name": "Guest <PERSON>", "value": "guest_bed_ensutie", "visibility_condition": ""}, {"name": "Guest <PERSON>", "value": "guest_bed_toilet", "visibility_condition": ""}, {"name": "Guest Bed WIR", "value": "guest_bed_wir", "visibility_condition": ""}, {"name": "Gym", "value": "gym", "visibility_condition": ""}, {"name": "Hallway", "value": "hallway", "visibility_condition": ""}, {"name": "Home Bar", "value": "home_bar", "visibility_condition": ""}, {"name": "Home Office", "value": "home_office", "visibility_condition": ""}, {"name": "Home Spa", "value": "home_spa", "visibility_condition": ""}, {"name": "Home Theatre", "value": "home_theatre", "visibility_condition": ""}, {"name": "Kids Bed", "value": "kids_bed", "visibility_condition": ""}, {"name": "Kids Living Room", "value": "kids_living_room", "visibility_condition": ""}, {"name": "Kids Lounge Room", "value": "kids_lounge_room", "visibility_condition": ""}, {"name": "Kids Rumpus Room", "value": "kids_rumpus_room", "visibility_condition": ""}, {"name": "Kitchen", "value": "kitchen", "visibility_condition": ""}, {"name": "<PERSON><PERSON><PERSON>", "value": "laundry", "visibility_condition": ""}, {"name": "Library", "value": "library", "visibility_condition": ""}, {"name": "Linen Room", "value": "linen_room", "visibility_condition": ""}, {"name": "Living Room", "value": "living_room", "visibility_condition": ""}, {"name": "Loft", "value": "loft", "visibility_condition": ""}, {"name": "Lounge Room", "value": "lounge_room", "visibility_condition": ""}, {"name": "Man Cave", "value": "man_cave", "visibility_condition": ""}, {"name": "Man Cave Bathroom", "value": "man_cave_bathroom", "visibility_condition": ""}, {"name": "Man Cave Storage", "value": "man_cave_storage", "visibility_condition": ""}, {"name": "Man <PERSON> Toilet", "value": "man_cave_toilet", "visibility_condition": ""}, {"name": "Man Cave WC", "value": "man_cave_wc", "visibility_condition": ""}, {"name": "Master Bed", "value": "master_bed", "visibility_condition": ""}, {"name": "Master Bed Ensuite", "value": "master_bed_ensuite", "visibility_condition": ""}, {"name": "Master <PERSON>", "value": "master_bed_toilet", "visibility_condition": ""}, {"name": "Master Bed WC", "value": "master_bed_wc", "visibility_condition": ""}, {"name": "Master Bed WIR", "value": "master_bed_wir", "visibility_condition": ""}, {"name": "Meals Room", "value": "meals_room", "visibility_condition": ""}, {"name": "Mechanical Room", "value": "mechanical_room", "visibility_condition": ""}, {"name": "Media Room", "value": "media_room", "visibility_condition": ""}, {"name": "Meditation Room", "value": "meditation_room", "visibility_condition": ""}, {"name": "Mezzanine", "value": "mezzanine", "visibility_condition": ""}, {"name": "<PERSON><PERSON><PERSON>", "value": "mudroom", "visibility_condition": ""}, {"name": "Music Room", "value": "music_room", "visibility_condition": ""}, {"name": "Nursery", "value": "nursery", "visibility_condition": ""}, {"name": "Observatory", "value": "observatory", "visibility_condition": ""}, {"name": "Office", "value": "office", "visibility_condition": ""}, {"name": "Outdoor Kitchen", "value": "outdoor_kitchen", "visibility_condition": ""}, {"name": "Pantry", "value": "pantry", "visibility_condition": ""}, {"name": "<PERSON><PERSON>", "value": "patio", "visibility_condition": ""}, {"name": "Pet Room", "value": "pet_room", "visibility_condition": ""}, {"name": "Playroom", "value": "playroom", "visibility_condition": ""}, {"name": "Pool", "value": "pool", "visibility_condition": ""}, {"name": "Pool House", "value": "pool_house", "visibility_condition": ""}, {"name": "Porch", "value": "porch", "visibility_condition": ""}, {"name": "Powder Room", "value": "powder_room", "visibility_condition": ""}, {"name": "Rumpus Room", "value": "rumpus_room", "visibility_condition": ""}, {"name": "Safe Room", "value": "safe_room", "visibility_condition": ""}, {"name": "Scullery", "value": "scullery", "visibility_condition": ""}, {"name": "<PERSON><PERSON>", "value": "servery", "visibility_condition": ""}, {"name": "She Shed", "value": "she_shed", "visibility_condition": ""}, {"name": "She Shed Bathroom", "value": "she_shed_bathroom", "visibility_condition": ""}, {"name": "She Shed Storage", "value": "she_shed_storage", "visibility_condition": ""}, {"name": "She Shed <PERSON>", "value": "she_shed_toilet", "visibility_condition": ""}, {"name": "She Shed WC", "value": "she_shed_wc", "visibility_condition": ""}, {"name": "Shed", "value": "shed", "visibility_condition": ""}, {"name": "Spare Room", "value": "spare_room", "visibility_condition": ""}, {"name": "Spare Room Ensuite", "value": "spare_room_ensuite", "visibility_condition": ""}, {"name": "Spare Room Toilet", "value": "spare_room_toilet", "visibility_condition": ""}, {"name": "Spare Room WC", "value": "spare_room_wc", "visibility_condition": ""}, {"name": "Spare Room WIR", "value": "spare_room_wir", "visibility_condition": ""}, {"name": "Stairs", "value": "stairs", "visibility_condition": ""}, {"name": "Storage Room", "value": "storage_room", "visibility_condition": ""}, {"name": "Study", "value": "study", "visibility_condition": ""}, {"name": "Study Nook", "value": "study_nook", "visibility_condition": ""}, {"name": "Sun Room", "value": "sun_room", "visibility_condition": ""}, {"name": "Toilet 1", "value": "toilet_1", "visibility_condition": ""}, {"name": "Toilet 2", "value": "toilet_2", "visibility_condition": ""}, {"name": "Toilet 3", "value": "toilet_3", "visibility_condition": ""}, {"name": "Toilet 4", "value": "toilet_4", "visibility_condition": ""}, {"name": "Toilet 5", "value": "toilet_5", "visibility_condition": ""}, {"name": "Toilet Bed 1", "value": "toilet_bed_1", "visibility_condition": ""}, {"name": "Toilet Bed 2", "value": "toilet_bed_2", "visibility_condition": ""}, {"name": "Toilet Bed 3", "value": "toilet_bed_3", "visibility_condition": ""}, {"name": "Toilet Bed 4", "value": "toilet_bed_4", "visibility_condition": ""}, {"name": "Toilet Bed 5", "value": "toilet_bed_5", "visibility_condition": ""}, {"name": "Toilet Bed 6", "value": "toilet_bed_6", "visibility_condition": ""}, {"name": "Toilet Bed 7", "value": "toilet_bed_7", "visibility_condition": ""}, {"name": "Toilet Bed 8", "value": "toilet_bed_8", "visibility_condition": ""}, {"name": "Toilet Bed 9", "value": "toilet_bed_9", "visibility_condition": ""}, {"name": "Utility Room", "value": "utility_room", "visibility_condition": ""}, {"name": "WC 1", "value": "wc_1", "visibility_condition": ""}, {"name": "WC 2", "value": "wc_2", "visibility_condition": ""}, {"name": "WC 3", "value": "wc_3", "visibility_condition": ""}, {"name": "WC 4", "value": "wc_4", "visibility_condition": ""}, {"name": "WC 5", "value": "wc_5", "visibility_condition": ""}, {"name": "WC Bed 1", "value": "wc_bed_1", "visibility_condition": ""}, {"name": "WC Bed 2", "value": "wc_bed_2", "visibility_condition": ""}, {"name": "WC Bed 3", "value": "wc_bed_3", "visibility_condition": ""}, {"name": "WC Bed 4", "value": "wc_bed_4", "visibility_condition": ""}, {"name": "WC Bed 5", "value": "wc_bed_5", "visibility_condition": ""}, {"name": "WC Bed 6", "value": "wc_bed_6", "visibility_condition": ""}, {"name": "WC Bed 7", "value": "wc_bed_7", "visibility_condition": ""}, {"name": "WC Bed 8", "value": "wc_bed_8", "visibility_condition": ""}, {"name": "WC Bed 9", "value": "wc_bed_9", "visibility_condition": ""}, {"name": "Wine Cellar", "value": "wine_cellar", "visibility_condition": ""}, {"name": "Wine Tasting Room", "value": "wine_tasting_room", "visibility_condition": ""}, {"name": "WIR Bed 1", "value": "wir_bed_1", "visibility_condition": ""}, {"name": "WIR Bed 2", "value": "wir_bed_2", "visibility_condition": ""}, {"name": "WIR Bed 3", "value": "wir_bed_3", "visibility_condition": ""}, {"name": "WIR Bed 4", "value": "wir_bed_4", "visibility_condition": ""}, {"name": "WIR Bed 5", "value": "wir_bed_5", "visibility_condition": ""}, {"name": "WIR Bed 6", "value": "wir_bed_6", "visibility_condition": ""}, {"name": "WIR Bed 7", "value": "wir_bed_7", "visibility_condition": ""}, {"name": "WIR Bed 8", "value": "wir_bed_8", "visibility_condition": ""}, {"name": "WIR Bed 9", "value": "wir_bed_9", "visibility_condition": ""}, {"name": "Work Shop", "value": "work_shop", "visibility_condition": ""}, {"name": "Other (Specify)", "value": "other_specify", "visibility_condition": ""}]}, {"name": "Location (Other)", "technical_name": "bx_dbl_hinge_location_other", "field_type": "text", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_location == 'other_specify'\", \"logic\": \"and\"}]", "sequence": 11, "question_number": 2, "quantity_formula": "1", "min_length": 0, "max_length": 0, "pattern": ""}, {"name": "Door Jamb Reveal is Greater than 20mm or No Reveal?", "technical_name": "bx_dbl_hinge_jamb_reveal_gt_20mm", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "", "sequence": 20, "question_number": 3, "quantity_formula": "1", "options": [{"name": "Yes", "value": "yes", "visibility_condition": ""}, {"name": "No - Less Than 20mm", "value": "no_lt_20mm", "visibility_condition": ""}]}, {"name": "Will New Screen Door Clear Handle on Existing External Door?", "technical_name": "bx_dbl_hinge_screen_clears_handle", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "", "sequence": 30, "question_number": 4, "quantity_formula": "1", "options": [{"name": "Yes - Greater than 23mm of Space", "value": "yes_gt_23mm", "visibility_condition": ""}, {"name": "No - Less Than 23mm of Space", "value": "no_lt_23mm", "visibility_condition": ""}]}, {"name": "Will Handle on New Screen Door Clear Existing External Door or Existing Pull/Push Bar Handle?", "technical_name": "bx_dbl_hinge_new_handle_clears_existing", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "", "sequence": 40, "question_number": 5, "quantity_formula": "1", "options": [{"name": "Yes - Greater than 56mm of Space", "value": "yes_gt_56mm", "visibility_condition": ""}, {"name": "No - Less Than 56mm of Space", "value": "no_lt_56mm", "visibility_condition": ""}]}, {"name": "New Screen Door Swing Path is Clear from any Obstructions?", "technical_name": "bx_dbl_hinge_swing_path_clear", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "", "sequence": 50, "question_number": 6, "quantity_formula": "1", "options": [{"name": "Yes", "value": "yes", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Description of Rectification Required or Obstruction", "technical_name": "bx_dbl_hinge_obstruction_description", "field_type": "text", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_swing_path_clear == 'no'\", \"logic\": \"and\"}]", "sequence": 51, "question_number": 7, "quantity_formula": "1", "min_length": 0, "max_length": 0, "pattern": ""}]}, {"name": "Door Specifications", "sequence": 20, "description": "", "fields": [{"name": "Quantity", "technical_name": "bx_dbl_hinge_quantity", "field_type": "number", "required": true, "default_value": "1", "help_text": "", "visibility_condition": "", "sequence": 60, "question_number": 8, "quantity_formula": "1"}, {"name": "Frame Colour", "technical_name": "bx_dbl_hinge_frame_colour", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "", "sequence": 70, "question_number": 9, "quantity_formula": "1", "options": [{"name": "Black Custom Matt GN248A", "value": "black_custom_matt_gn248a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-BLK"}, {"name": "Clear Anodise 15mm", "value": "clear_anodise_15mm", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-CA"}, {"name": "Light Bronze Anodise 15mm", "value": "light_bronze_anodise_15mm", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-LBZ"}, {"name": "Monument Matt GL229A", "value": "monument_matt_gl229a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-MON"}, {"name": "Pearl White Gloss GA078A", "value": "pearl_white_gloss_ga078a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PW"}, {"name": "Primrose Gloss GD037A", "value": "primrose_gloss_gd037a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PR"}, {"name": "Stone Beige Matt GD247A", "value": "stone_beige_matt_gd247a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-STB"}, {"name": "Surfmist Matt GA236A", "value": "surfmist_matt_ga236a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-SFM"}, {"name": "Ultra Silver Gloss GY070A", "value": "ultra_silver_gloss_gy070a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-US"}, {"name": "White Birch Gloss GA057A", "value": "white_birch_gloss_ga057a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-WB"}, {"name": "Woodland Grey Matt GL205A", "value": "woodland_grey_matt_gl205a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-WG"}, {"name": "Mill Finish", "value": "mill_finish", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-MF"}, {"name": "-------------------------------------------------------------------", "value": "separator_fc_bx_dbl1", "visibility_condition": ""}, {"name": "Anodic Bronze Satin GY114A", "value": "anodic_bronze_satin_gy114a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-ABZ"}, {"name": "Anodic <PERSON> Grey Matt GL213A", "value": "anodic_dark_grey_matt_gl213a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-ADG"}, {"name": "Anodic Natural Matt GY235A", "value": "anodic_natural_matt_gy235a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-AN"}, {"name": "Anodic Off White Matt GD227A", "value": "anodic_off_white_matt_gd227a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-AOW"}, {"name": "Anodic Silver Grey Matt GL237A", "value": "anodic_silver_grey_matt_gl237a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-ASG"}, {"name": "APO Grey Satin GL107A", "value": "apo_grey_satin_gl107a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-APO"}, {"name": "Charcoal Satin GL180A", "value": "charcoal_satin_gl180a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-CHL"}, {"name": "Deep Ocean Matt GJ203A", "value": "deep_ocean_matt_gj203a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-DPO"}, {"name": "Doeskin Satin GD188A", "value": "doeskin_satin_gd188a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-DOE"}, {"name": "<PERSON>ne Matt GL252A", "value": "dune_matt_gl252a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-DUN"}, {"name": "Hamersley Brown Satin GM100A", "value": "hamersley_brown_satin_gm100a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-HMB"}, {"name": "Hawthorn Green Gloss GK030A", "value": "hawthorn_green_gloss_gk030a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-HWG"}, {"name": "Ironstone Matt GL236A", "value": "ironstone_matt_gl236a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-IRS"}, {"name": "Jasper Satin GT114A", "value": "jasper_satin_gt114a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-JSP"}, {"name": "Paperbark Satin GU114A", "value": "paperbark_satin_gu114a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PBK"}, {"name": "Pottery Satin GM175A", "value": "pottery_satin_gm175a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-POT"}, {"name": "Precious Silver Kinetic Pearl Satin 971-7043K", "value": "precious_silver_kinetic_pearl_satin_971_7043k", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PSK"}, {"name": "Shale Grey Satin GP184A", "value": "shale_grey_satin_gp184a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-SHG"}, {"name": "Textura Black GN305A", "value": "textura_black_gn305a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-TXB"}, {"name": "Western Red Cedar MZ306A4489V5", "value": "western_red_cedar_mz306a4489v5", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-WRC"}, {"name": "Powder Coat", "value": "powder_coat", "visibility_condition": ""}]}, {"name": "Powder Coat Colour", "technical_name": "bx_dbl_hinge_powder_coat_colour", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour == 'powder_coat'\", \"logic\": \"and\"}]", "sequence": 71, "question_number": 10, "quantity_formula": "1", "options": [{"name": "Admiralty Gloss 6131G", "value": "admiralty_gloss_6131g", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Almond Ivory Gloss GD016A", "value": "almond_ivory_gloss_gd016a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Anodic Champagne Matt GY276A", "value": "anodic_champagne_matt_gy276a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Anodic Clear Matt GY221A", "value": "anodic_clear_matt_gy221a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Asteroid Peral Matt GY270A", "value": "asteroid_peral_matt_gy270a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Aubergine Satin 84736", "value": "aubergine_satin_84736", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Azure Grey Satin 36603", "value": "azure_grey_satin_36603", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Barley Gloss GD007A", "value": "barley_gloss_gd007a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Barrister White Satin 84672", "value": "barrister_white_satin_84672", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Basalt Matt GP208A", "value": "basalt_matt_gp208a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Berry Grey Gloss 7262G", "value": "berry_grey_gloss_7262g", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Black Onyx Gloss GY042A", "value": "black_onyx_gloss_gy042a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Black Satin GN150A", "value": "black_satin_gn150a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Blue Ridge Satin 88480", "value": "blue_ridge_satin_88480", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Bright White Gloss GA030A", "value": "bright_white_gloss_ga030a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Bronze <PERSON> 32348", "value": "bronze_olive_matt_32348", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Bronze Pearl Satin 94686", "value": "bronze_pearl_satin_94686", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3"}, {"name": "Bushland Matt GK203A", "value": "bushland_matt_gk203a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Canola Cream Gloss 81796", "value": "canola_cream_gloss_81796", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Champagne Kinetic  3059K", "value": "champagne_kinetic_3059k", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3"}, {"name": "Charcoal Metallic Gloss GM019A", "value": "charcoal_metallic_gloss_gm019a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Charcoal Metallic Pearl Gloss 18796", "value": "charcoal_metallic_pearl_gloss_18796", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3"}, {"name": "Charcoal Pearl Matt GY237A", "value": "charcoal_pearl_matt_gy237a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Citi Matt GL211A", "value": "citi_matt_gl211a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Citi Pearl Matt 88471", "value": "citi_pearl_matt_88471", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Claret Satin GG142A", "value": "claret_satin_gg142a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Classic Cream Matt GD245A", "value": "classic_cream_matt_gd245a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Claypot Satin 72165", "value": "claypot_satin_72165", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Copper Kinetic  7183K", "value": "copper_kinetic_7183k", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Cottage Green Matt GK274A", "value": "cottage_green_matt_gk274a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Cove Matt GD274A", "value": "cove_matt_gd274a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Deep Brunswick Green Gloss 6134G", "value": "deep_brunswick_green_gloss_6134g", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Estate Mtt 89743", "value": "estate_mtt_89743", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Evening Haze Matt GM235A", "value": "evening_haze_matt_gm235a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "French Champagne Matt GX207C", "value": "french_champagne_matt_gx207c", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Gold Pearl Satin 96604", "value": "gold_pearl_satin_96604", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Grey Nurse Gloss 50060", "value": "grey_nurse_gloss_50060", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Gully Matt GM242A", "value": "gully_matt_gm242a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Gunmetal Kinetic  8115K", "value": "gunmetal_kinetic_8115k", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Headland Matt GG219A", "value": "headland_matt_gg219a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "He<PERSON> 87336", "value": "hedge_matt_87336", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Heritage Green Gloss GK044A", "value": "heritage_green_gloss_gk044a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Horizon Blue Gloss 33344", "value": "horizon_blue_gloss_33344", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Hunter Red Satin 84209", "value": "hunter_red_satin_84209", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Ivory Coast Gloss 3061G", "value": "ivory_coast_gloss_3061g", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Light Grey Gloss 7263G", "value": "light_grey_gloss_7263g", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Loft Matt 8106M", "value": "loft_matt_8106m", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Magnolia Gloss GD025A", "value": "magnolia_gloss_gd025a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Mangrove Matt GK277A", "value": "mangrove_matt_gk277a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Manor Red Matt GG262A", "value": "manor_red_matt_gg262a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Mass Vale Sands Satin 33019", "value": "mass_vale_sands_satin_33019", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Metropolis Bronze Pearl Satin 59003", "value": "metropolis_bronze_pearl_satin_59003", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Metropolis Silver Glow Pearl Gloss 84678", "value": "metropolis_silver_glow_pearl_gloss_84678", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Metropolis Storm Pearl Satin 88471", "value": "metropolis_storm_pearl_satin_88471", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "<PERSON> of Pearl Gloss 84684", "value": "mother_of_pearl_gloss_84684", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Natural Pearl Matt 89119", "value": "natural_pearl_matt_89119", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Natural Pearl Matt 97189119", "value": "natural_pearl_matt_97189119", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Nickel Pearl Matt 88360", "value": "nickel_pearl_matt_88360", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Night Sky Matt GN231A", "value": "night_sky_matt_gn231a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Nobel Silver Pearl Satin GY119A", "value": "nobel_silver_pearl_satin_gy119a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Notre Dame Gloss GL040A", "value": "notre_dame_gloss_gl040a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Ocean Mist Satin 6132S", "value": "ocean_mist_satin_6132s", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Off White Matt GA211A", "value": "off_white_matt_ga211a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Olde Pewter Satin GL175A", "value": "olde_pewter_satin_gl175a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Onyx Pearl Gloss 52052", "value": "onyx_pearl_gloss_52052", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Oyster Grey Matt GL258A", "value": "oyster_grey_matt_gl258a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Pale Eucalypt Matt GK236A", "value": "pale_eucalypt_matt_gk236a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Palladium Silver Pearl Satin GY184C", "value": "palladium_silver_pearl_satin_gy184c", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Pandemonium Silver Flat 7004F", "value": "pandemonium_silver_flat_7004f", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Periwinkle Gloss 50276", "value": "periwinkle_gloss_50276", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Pewter Pearl Satin 88202", "value": "pewter_pearl_satin_88202", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Platypus Kinetic  7214K", "value": "platypus_kinetic_7214k", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3"}, {"name": "Regency Grey Matt 50278", "value": "regency_grey_matt_50278", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Rivergum Beige Gloss 36991", "value": "rivergum_beige_gloss_36991", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Rivergum Gloss GD042A", "value": "rivergum_gloss_gd042a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Riversand Matt 36656", "value": "riversand_matt_36656", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Riversand Matt GM204A", "value": "riversand_matt_gm204a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Roseberry Grey Gloss GL015A", "value": "roseberry_grey_gloss_gl015a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Sand Satin 51438", "value": "sand_satin_51438", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Sandbank Matt 3003M", "value": "sandbank_matt_3003m", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Shoji White Satin GA160A", "value": "shoji_white_satin_ga160a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Smokey Glass Satin 78292", "value": "smokey_glass_satin_78292", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Space Blue Gloss MJ008A", "value": "space_blue_gloss_mj008a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "St Elmos Fire Kinetic  7208K", "value": "st_elmos_fire_kinetic_7208k", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Steel Pearl Satin 57127", "value": "steel_pearl_satin_57127", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Stone Grey Satin 87126", "value": "stone_grey_satin_87126", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Storm Front Matt GL249A", "value": "storm_front_matt_gl249a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2"}, {"name": "Storm Grey N42 Gloss GL060A", "value": "storm_grey_n42_gloss_gl060a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Stromboli Satin GK148A", "value": "stromboli_satin_gk148a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Teal Gloss 6133G", "value": "teal_gloss_6133g", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Terrain Satin GM143A", "value": "terrain_satin_gm143a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Textura Deep Ocean GJ301A", "value": "textura_deep_ocean_gj301a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3"}, {"name": "Textura Dune GL352A", "value": "textura_dune_gl352a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3"}, {"name": "Textura Ironstone GL336A", "value": "textura_ironstone_gl336a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3"}, {"name": "Textura Jasper GM314A", "value": "textura_jasper_gm314a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3"}, {"name": "Textura Monument GL329A", "value": "textura_monument_gl329a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3"}, {"name": "Textura Paperbark GD314A", "value": "textura_paperbark_gd314a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3"}, {"name": "Textura Primrose GD331A", "value": "textura_primrose_gd331a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3"}, {"name": "Textura Sable Bass GN297A", "value": "textura_sable_bass_gn297a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3"}, {"name": "Textura Silver GY35LA", "value": "textura_silver_gy35la", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3"}, {"name": "Textura White GA330A", "value": "textura_white_ga330a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3"}, {"name": "Textura Woodland Grey GL333A", "value": "textura_woodland_grey_gl333a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3"}, {"name": "Transformer Grey Gloss GL031A", "value": "transformer_grey_gloss_gl031a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Wallaby Matt GL210A", "value": "wallaby_matt_gl210a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Wedgewood Satin 50279", "value": "wedgewood_satin_50279", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "White Satin Satin GA124A", "value": "white_satin_satin_ga124a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Wilderness Matt GK289A", "value": "wilderness_matt_gk289a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Windspray Matt GL266A", "value": "windspray_matt_gl266a", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Wizard Gloss 50283", "value": "wizard_gloss_50283", "visibility_condition": "", "component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1"}, {"name": "Custom Powder Coat", "value": "custom_powder_coat", "visibility_condition": ""}]}, {"name": "Custom Powder Coat - Colour Name", "technical_name": "bx_dbl_hinge_custom_powder_coat_name", "field_type": "text", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour == 'powder_coat' && bx_dbl_hinge_powder_coat_colour == 'custom_powder_coat'\", \"logic\": \"and\"}]", "sequence": 72, "question_number": 11, "quantity_formula": "1", "min_length": 0, "max_length": 0, "pattern": ""}, {"name": "Custom Powder Coat - Colour Finish", "technical_name": "bx_dbl_hinge_custom_powder_coat_finish", "field_type": "text", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour == 'powder_coat' && bx_dbl_hinge_powder_coat_colour == 'custom_powder_coat'\", \"logic\": \"and\"}]", "sequence": 73, "question_number": 12, "quantity_formula": "1", "min_length": 0, "max_length": 0, "pattern": ""}, {"name": "Custom Powder Coat - Colour Code", "technical_name": "bx_dbl_hinge_custom_powder_coat_code", "field_type": "text", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour == 'powder_coat' && bx_dbl_hinge_powder_coat_colour == 'custom_powder_coat'\", \"logic\": \"and\"}]", "sequence": 74, "question_number": 13, "quantity_formula": "1", "min_length": 0, "max_length": 0, "pattern": ""}, {"name": "Door Size Deduction Assistance Required?", "technical_name": "bx_dbl_hinge_deduction_assistance", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "", "sequence": 80, "question_number": 14, "quantity_formula": "1", "options": [{"name": "Yes", "value": "yes", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Door Split Type", "technical_name": "bx_dbl_hinge_door_split_type", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes'\", \"logic\": \"and\"}]", "sequence": 81, "question_number": 15, "quantity_formula": "1", "options": [{"name": "Even", "value": "even", "visibility_condition": ""}, {"name": "Uneven", "value": "uneven", "visibility_condition": ""}]}, {"name": "Opening Height (mm) (Even Split)", "technical_name": "bx_dbl_hinge_opening_height_mm_even", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_door_split_type in ['even']\", \"logic\": \"and\"}]", "sequence": 82, "question_number": 16, "quantity_formula": "1"}, {"name": "Height Deduction (mm) (Even Split)", "technical_name": "bx_dbl_hinge_height_deduction_mm_even", "field_type": "number", "required": true, "default_value": "10", "help_text": "Client can set default Height Deduction in their profile", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_door_split_type == 'even'\", \"logic\": \"and\"}]", "sequence": 82, "question_number": 17, "quantity_formula": "1"}, {"name": "MAKE Each Door Height (mm) (Even Split)", "technical_name": "bx_dbl_hinge_make_each_door_height_mm_even", "field_type": "number", "required": true, "default_value": "", "help_text": "*Min: 700mm - Max: 3100mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_door_split_type == 'even'\", \"logic\": \"and\"}]", "sequence": 84, "question_number": 18, "quantity_formula": "1"}, {"name": "Opening TOP Width (mm) (Even Split)", "technical_name": "bx_dbl_hinge_opening_top_width_mm_even", "field_type": "number", "required": true, "default_value": "", "help_text": "Min 300 Max 700", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_door_split_type == 'even'\", \"logic\": \"and\"}]", "sequence": 85, "question_number": 19, "quantity_formula": "1"}, {"name": "TOP Width Deduction (mm) (Even Split)", "technical_name": "bx_dbl_hinge_top_width_deduction_mm_even", "field_type": "number", "required": true, "default_value": "", "help_text": "Client can set default Even Split TOP Width Deduction in their profile", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_door_split_type == 'even'\", \"logic\": \"and\"}]", "sequence": 86, "question_number": 20, "quantity_formula": "1"}, {"name": "MAKE Each Door TOP Width (mm) (Even Split)", "technical_name": "bx_dbl_hinge_make_each_door_top_width_mm_even", "field_type": "number", "required": true, "default_value": "", "help_text": "*Min: 300mm - Max: Door Height>2510mm = 1310mm, Door Height<=2510 = 2000mm. +/- 7mm difference from MIDDLE Width", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_door_split_type == 'even'\", \"logic\": \"and\"}]", "sequence": 87, "question_number": 21, "quantity_formula": "1"}, {"name": "Opening MIDDLE Width (mm) (Even Split)", "technical_name": "bx_dbl_hinge_opening_middle_width_mm_even", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'even'\", \"logic\": \"and\"}]", "sequence": 88, "question_number": 22, "quantity_formula": "1"}, {"name": "MIDDLE Width Deduction (mm) (Even Split)", "technical_name": "bx_dbl_hinge_middle_width_deduction_mm_even", "field_type": "number", "required": true, "default_value": "Client canet default Even Split MIDDLE Width Deduction in their profile", "help_text": "Client can set default Even Split MIDDLE Width Deduction in their profile", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'even'\", \"logic\": \"and\"}]", "sequence": 89, "question_number": 23, "quantity_formula": "1"}, {"name": "MAKE Each Door MIDDLE Width (mm) (Even Split)", "technical_name": "bx_dbl_hinge_make_each_door_middle_width_mm_even", "field_type": "number", "required": true, "default_value": "", "help_text": "*Min: 300mm - Max: Door Height>2510mm = 1310mm, Door Height<=2510 = 2000mm. +/- 7mm difference from TOP & BOTTOM Width.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'even'\", \"logic\": \"and\"}]", "sequence": 90, "question_number": 24, "quantity_formula": "1"}, {"name": "Opening BOTTOM Width (mm) (Even Split)", "technical_name": "bx_dbl_hinge_opening_bottom_width_mm_even", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'even'\", \"logic\": \"and\"}]", "sequence": 91, "question_number": 25, "quantity_formula": "1"}, {"name": "BOTTOM Width Deduction (mm) (Even Split)", "technical_name": "bx_dbl_hinge_bottom_width_deduction_mm_even", "field_type": "number", "required": true, "default_value": "", "help_text": "Client can set default Even Split BOTTOM Width Deduction in their profile", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'even'\", \"logic\": \"and\"}]", "sequence": 92, "question_number": 26, "quantity_formula": "1"}, {"name": "MAKE Each Door BOTTOM Width (mm) (Even Split)", "technical_name": "bx_dbl_hinge_make_each_door_bottom_width_mm_even", "field_type": "number", "required": true, "default_value": "", "help_text": "*Min: 300mm - Max: Door Height>2510mm = 1310mm, Door Height<=2510 = 2000mm. +/- 7mm difference from MIDDLE Width", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'even'\", \"logic\": \"and\"}]", "sequence": 93, "question_number": 27, "quantity_formula": "1"}, {"name": "LEFT Opening Height (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_left_opening_height_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 94, "question_number": 28, "quantity_formula": "1"}, {"name": "Height Deduction (mm) (Uneven Split - Left)", "technical_name": "bx_dbl_hinge_height_deduction_mm_uneven_left", "field_type": "number", "required": true, "default_value": "", "help_text": "Client can set default Height Deduction in their profile", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 95, "question_number": 29, "quantity_formula": "1"}, {"name": "MAKE LEFT Door Height (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_make_left_door_height_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "*Door Height must be between 700 - 3100mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 96, "question_number": 30, "quantity_formula": "1"}, {"name": "LEFT Jamb to Centre TOP Measurement (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_left_jamb_centre_top_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 97, "question_number": 31, "quantity_formula": "1"}, {"name": "TOP LEFT Jamb to Centre Deduction (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "Client can set default Uneven Split TOP Width Deduction in their profile", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 98, "question_number": 32, "quantity_formula": "1"}, {"name": "MAKE LEFT Door TOP Width (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_make_left_door_top_width_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "*See Wholesale Book for Max Door Width Restricions. Min Door Width = 300.\n*Please enter a Top Width that varies less than 7mm from Middle Width.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 99, "question_number": 33, "quantity_formula": "1"}, {"name": "LEFT Jamb to Centre MIDDLE Measurement (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_left_jamb_centre_middle_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 100, "question_number": 34, "quantity_formula": "1"}, {"name": "MIDDLE LEFT Jamb to Centre Deduction (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "Client can set default Uneven Split MIDDLE Width Deduction in their profile", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 101, "question_number": 35, "quantity_formula": "1"}, {"name": "MAKE LEFT Door MIDDLE Width (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_make_left_door_middle_width_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "*See Wholesale Book for Max Door Width Restricions. Min Door Width = 300.\n*Please enter a Middle Width that varies less than 7mm from Top & Bottom Width.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 102, "question_number": 36, "quantity_formula": "1"}, {"name": "LEFT Jamb to Centre BOTTOM Measurement (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 103, "question_number": 37, "quantity_formula": "1"}, {"name": "BOTTOM LEFT Jamb to Centre Deduction (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "Client can set default Uneven Split BOTTOM Width Deduction in their profile", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 104, "question_number": 38, "quantity_formula": "1"}, {"name": "MAKE LEFT Door BOTTOM Width (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_make_left_door_bottom_width_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "*See Wholesale Book for Max Door Width Restricions. Min Door Width = 300\n*Please enter a Bottom Width that varies less than 7mm from Middle Width.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 105, "question_number": 39, "quantity_formula": "1"}, {"name": "RIGHT Opening Height (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_right_opening_height_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 106, "question_number": 40, "quantity_formula": "1"}, {"name": "Height Deduction (mm) (Uneven Split - Right)", "technical_name": "bx_dbl_hinge_height_deduction_mm_uneven_right", "field_type": "number", "required": true, "default_value": "", "help_text": "Client can set default Height Deduction in their profile", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 107, "question_number": 41, "quantity_formula": "1"}, {"name": "MAKE RIGHT Door Height (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_make_right_door_height_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "*Door Height must be between 700 - 3100mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 108, "question_number": 42, "quantity_formula": "1"}, {"name": "RIGHT Jamb to Centre TOP Measurement (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_right_jamb_centre_top_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 109, "question_number": 43, "quantity_formula": "1"}, {"name": "TOP RIGHT Jamb to Centre Deduction (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "Client can set default Uneven Split TOP Width Deduction in their profile", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 110, "question_number": 44, "quantity_formula": "1"}, {"name": "MAKE RIGHT Door TOP Width (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_make_right_door_top_width_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "*See Wholesale Book for Max Door Width Restricions. Min Door Width = 300\n*Please enter a Top Width that varies less than 7mm from Middle Width.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 111, "question_number": 45, "quantity_formula": "1"}, {"name": "RIGHT Jamb to Centre MIDDLE Measurement (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_right_jamb_centre_middle_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 112, "question_number": 46, "quantity_formula": "1"}, {"name": "MIDDLE RIGHT Jamb to Centre Deduction (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "Client can set default Uneven Split MIDDLE Width Deduction in their profile", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 113, "question_number": 47, "quantity_formula": "1"}, {"name": "MAKE RIGHT Door MIDDLE Width (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_make_right_door_middle_width_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "*See Wholesale Book for Max Door Width Restricions. Min Door Width = 300\n*Please enter a Middle Width that varies less than 7mm from Top & Bottom Width.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 114, "question_number": 48, "quantity_formula": "1"}, {"name": "RIGHT Jamb to Centre BOTTOM Measurement (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 115, "question_number": 49, "quantity_formula": "1"}, {"name": "BOTTOM RIGHT Jamb to Centre Deduction (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "Client can set default Uneven Split BOTTOM Width Deduction in their profile", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 116, "question_number": 50, "quantity_formula": "1"}, {"name": "MAKE RIGHT Door BOTTOM Width (mm) (Uneven Split)", "technical_name": "bx_dbl_hinge_make_right_door_bottom_width_mm_uneven", "field_type": "number", "required": true, "default_value": "", "help_text": "*See Wholesale Book for Max Door Width Restricions. Min Door Width = 300\n*Please enter a Bottom Width that varies less than 7mm from Middle Width.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'yes' && bx_dbl_hinge_door_split_type == 'uneven'\", \"logic\": \"and\"}]", "sequence": 117, "question_number": 51, "quantity_formula": "1"}, {"name": "MAKE LEFT Door Height (mm) (Manual)", "technical_name": "bx_dbl_hinge_make_left_door_height_mm_manual", "field_type": "number", "required": true, "default_value": "", "help_text": "*Door Height must be between 700 - 3100mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'no'\", \"logic\": \"and\"}]", "sequence": 118, "question_number": 52, "quantity_formula": "1"}, {"name": "MAKE LEFT Door TOP Width (mm) (Manual)", "technical_name": "bx_dbl_hinge_make_left_door_top_width_mm_manual", "field_type": "number", "required": true, "default_value": "", "help_text": "*See Wholesale Book for Max Door Width Restricions. Min Door Width = 300\n*Please enter a Top Width that varies less than 7mm from Middle Width.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'no'\", \"logic\": \"and\"}]", "sequence": 119, "question_number": 53, "quantity_formula": "1"}, {"name": "MAKE LEFT Door MIDDLE Width (mm) (Manual)", "technical_name": "bx_dbl_hinge_make_left_door_middle_width_mm_manual", "field_type": "number", "required": true, "default_value": "", "help_text": "*See Wholesale Book for Max Door Width Restricions. Min Door Width = 300\n*Please enter a Middle Width that varies less than 7mm from Top & Bottom Width.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'no'\", \"logic\": \"and\"}]", "sequence": 120, "question_number": 54, "quantity_formula": "1"}, {"name": "MAKE LEFT Door BOTTOM Width (mm) (Manual)", "technical_name": "bx_dbl_hinge_make_left_door_bottom_width_mm_manual", "field_type": "number", "required": true, "default_value": "", "help_text": "*See Wholesale Book for Max Door Width Restricions. Min Door Width = 300\n*Please enter a Bottom Width that varies less than 7mm from Middle Width.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'no'\", \"logic\": \"and\"}]", "sequence": 121, "question_number": 55, "quantity_formula": "1"}, {"name": "MAKE RIGHT Door Height (mm) (Manual)", "technical_name": "bx_dbl_hinge_make_right_door_height_mm_manual", "field_type": "number", "required": true, "default_value": "", "help_text": "*Door Height must be between 700 - 3100mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'no'\", \"logic\": \"and\"}]", "sequence": 122, "question_number": 56, "quantity_formula": "1"}, {"name": "MAKE RIGHT Door TOP Width (mm) (Manual)", "technical_name": "bx_dbl_hinge_make_right_door_top_width_mm_manual", "field_type": "number", "required": true, "default_value": "", "help_text": "*See Wholesale Book for Max Door Width Restricions. Min Door Width = 300\n*Please enter a Top Width that varies less than 7mm from Middle Width.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'no'\", \"logic\": \"and\"}]", "sequence": 123, "question_number": 57, "quantity_formula": "1"}, {"name": "MAKE RIGHT Door MIDDLE Width (mm) (Manual)", "technical_name": "bx_dbl_hinge_make_right_door_middle_width_mm_manual", "field_type": "number", "required": true, "default_value": "", "help_text": "*See Wholesale Book for Max Door Width Restricions. Min Door Width = 300\n*Please enter a Middle Width that varies less than 7mm from Top & Bottom Width.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'no'\", \"logic\": \"and\"}]", "sequence": 124, "question_number": 58, "quantity_formula": "1"}, {"name": "MAKE RIGHT Door BOTTOM Width (mm) (Manual)", "technical_name": "bx_dbl_hinge_make_right_door_bottom_width_mm_manual", "field_type": "number", "required": true, "default_value": "", "help_text": "*See Wholesale Book for Max Door Width Restricions. Min Door Width = 300\n*Please enter a Bottom Width that varies less than 7mm from Middle Width.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_deduction_assistance == 'no'\", \"logic\": \"and\"}]", "sequence": 125, "question_number": 59, "quantity_formula": "1"}, {"name": "Swing", "technical_name": "bx_dbl_hinge_swing", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "", "sequence": 126, "question_number": 60, "quantity_formula": "1", "options": [{"name": "Inswing", "value": "inswing", "visibility_condition": ""}, {"name": "Outswing", "value": "outswing", "visibility_condition": ""}]}, {"name": "T-Section Mullion w/ <PERSON><PERSON><PERSON> (Inswing)", "technical_name": "bx_dbl_hinge_t_section_mullion_inswing", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_swing == 'inswing'\", \"logic\": \"and\"}]", "sequence": 127, "question_number": 61, "quantity_formula": "1", "options": [{"name": "Yes", "value": "yes", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "French Door Mullion w/ 2 Caps & Mohair (Outswing)", "technical_name": "bx_dbl_hinge_french_door_mullion_outswing", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_swing == 'outswing'\", \"logic\": \"and\"}]", "sequence": 128, "question_number": 62, "quantity_formula": "1", "options": [{"name": "Yes", "value": "yes", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}]}, {"name": "Lock & Hardware Configuration", "sequence": 30, "description": "", "fields": [{"name": "Lock Brand", "technical_name": "bx_dbl_hinge_lock_brand", "field_type": "selection", "required": true, "default_value": "commandex_hinged", "help_text": "", "visibility_condition": "", "sequence": 130, "question_number": 63, "quantity_formula": "1", "options": [{"name": "Austral Elegance XC", "value": "austral_elegance_xc", "visibility_condition": ""}, {"name": "CommandeX Hinged", "value": "commandex_hinged", "visibility_condition": ""}, {"name": "Lockwood 8654", "value": "lockwood_8654", "visibility_condition": ""}, {"name": "Whitco MK2", "value": "whitco_mk2", "visibility_condition": ""}, {"name": "Whitco Tasman ESCAPE", "value": "whit<PERSON>_tasman_escape", "visibility_condition": ""}, {"name": "Austral Elegance XC CUT OUT ONLY", "value": "austral_elegance_xc_cut_out_only", "visibility_condition": ""}, {"name": "CommandeX Hinged CUT OUT ONLY", "value": "commandex_hinged_cut_out_only", "visibility_condition": ""}, {"name": "Lockwood 8654 CUT OUT ONLY", "value": "lockwood_8654_cut_out_only", "visibility_condition": ""}, {"name": "Whitco MK2 CUT OUT ONLY", "value": "whitco_mk2_cut_out_only", "visibility_condition": ""}]}, {"name": "Austral Elegance XC - Lock Colour", "technical_name": "bx_dbl_hinge_austral_elegance_xc_lock_colour", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc'\", \"logic\": \"and\"}]", "sequence": 131, "question_number": 64, "quantity_formula": "1", "options": [{"name": "Black", "value": "black", "visibility_condition": "", "component_product": "Austral Elegance XC Hinged Lock", "component_product_code": "AUHL-BLK"}, {"name": "<PERSON>", "value": "pearl_white", "visibility_condition": "", "component_product": "Austral Elegance XC Hinged Lock", "component_product_code": "AUHL-PW"}, {"name": "<PERSON><PERSON><PERSON>", "value": "primrose", "visibility_condition": "", "component_product": "Austral Elegance XC Hinged Lock", "component_product_code": "AUHL-PR"}, {"name": "Silver Paint", "value": "silver_paint", "visibility_condition": "", "component_product": "Austral Elegance XC Hinged Lock", "component_product_code": "AUHL-SLV"}, {"name": "White Birch", "value": "white_birch", "visibility_condition": "", "component_product": "Austral Elegance XC Hinged Lock", "component_product_code": "AUHL-WB"}]}, {"name": "Austral Elegance XC - Cylinder", "technical_name": "bx_dbl_hinge_austral_elegance_xc_cylinder", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc'\", \"logic\": \"and\"}]", "sequence": 132, "question_number": 65, "quantity_formula": "1", "options": [{"name": "Austral 5 Pin DBL", "value": "austral_5_pin_dbl", "visibility_condition": "", "component_product": "Austral 5 Pin Double Cylinder", "component_product_code": "AU5PIN"}, {"name": "NO Cylinder", "value": "no_cylinder", "visibility_condition": ""}]}, {"name": "Austral Elegance XC - Lock Height Location", "technical_name": "bx_dbl_hinge_austral_elegance_xc_lock_height_location", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc'\", \"logic\": \"and\"}]", "sequence": 133, "question_number": 66, "quantity_formula": "1", "options": [{"name": "Top of Cut Out", "value": "top_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Cut Out", "value": "centre_of_cut_out", "visibility_condition": ""}, {"name": "Bottom of Cut Out", "value": "bottom_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Handle Lever", "value": "centre_of_handle_lever", "visibility_condition": ""}]}, {"name": "Austral Elegance XC - Lock Height (mm) (Top of Cut Out)", "technical_name": "bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 430mm and less than door height - 270mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc' && bx_dbl_hinge_austral_elegance_xc_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 134, "question_number": 67, "quantity_formula": "1"}, {"name": "Austral Elegance XC - Lock Type (Top of Cut Out)", "technical_name": "bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc' && bx_dbl_hinge_austral_elegance_xc_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 135, "question_number": 68, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "3PT", "value": "3pt", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "3PT with TOP Auxiliary CUT DOWN", "value": "3pt_top_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW"}, {"name": "3PT with BOTTOM Auxiliary CUT DOWN", "value": "3pt_bottom_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW"}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "3PT with TOP & BOTTOM Auxiliary CUT DOWN", "value": "3pt_top_bottom_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "Double with BOTTOM Auxiliary CUT DOWN", "value": "double_bottom_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW"}, {"name": "Double with TOP Auxiliary CUT DOWN", "value": "double_top_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}]}, {"name": "Austral Elegance XC - Lock Height (mm) (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 351mm and less than door height - 349mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc' && bx_dbl_hinge_austral_elegance_xc_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 136, "question_number": 69, "quantity_formula": "1"}, {"name": "Austral Elegance XC - Lock Type (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc' && bx_dbl_hinge_austral_elegance_xc_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 137, "question_number": 70, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "3PT", "value": "3pt", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "3PT with TOP Auxiliary CUT DOWN", "value": "3pt_top_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW"}, {"name": "3PT with BOTTOM Auxiliary CUT DOWN", "value": "3pt_bottom_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW"}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "3PT with TOP & BOTTOM Auxiliary CUT DOWN", "value": "3pt_top_bottom_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "Double with BOTTOM Auxiliary CUT DOWN", "value": "double_bottom_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW"}, {"name": "Double with TOP Auxiliary CUT DOWN", "value": "double_top_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}]}, {"name": "Austral Elegance XC - Lock Height (mm) (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 272mm and less than door height - 428mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc' && bx_dbl_hinge_austral_elegance_xc_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 138, "question_number": 71, "quantity_formula": "1"}, {"name": "Austral Elegance XC - Lock Type (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc' && bx_dbl_hinge_austral_elegance_xc_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 139, "question_number": 72, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "3PT", "value": "3pt", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "3PT with TOP Auxiliary CUT DOWN", "value": "3pt_top_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW"}, {"name": "3PT with BOTTOM Auxiliary CUT DOWN", "value": "3pt_bottom_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW"}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "3PT with TOP & BOTTOM Auxiliary CUT DOWN", "value": "3pt_top_bottom_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "Double with BOTTOM Auxiliary CUT DOWN", "value": "double_bottom_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW"}, {"name": "Double with TOP Auxiliary CUT DOWN", "value": "double_top_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}]}, {"name": "Austral Elegance XC - Lock Height (mm) (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_austral_elegance_xc_lh_centre_handle", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 383mm and less than door height - 317mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc' && bx_dbl_hinge_austral_elegance_xc_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 140, "question_number": 73, "quantity_formula": "1"}, {"name": "Austral Elegance XC - Lock Type (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_austral_elegance_xc_lt_centre_handle", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc' && bx_dbl_hinge_austral_elegance_xc_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 141, "question_number": 74, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "3PT", "value": "3pt", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "3PT with TOP Auxiliary CUT DOWN", "value": "3pt_top_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW"}, {"name": "3PT with BOTTOM Auxiliary CUT DOWN", "value": "3pt_bottom_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW"}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "3PT with TOP & BOTTOM Auxiliary CUT DOWN", "value": "3pt_top_bottom_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}, {"name": "Double with BOTTOM Auxiliary CUT DOWN", "value": "double_bottom_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW"}, {"name": "Double with TOP Auxiliary CUT DOWN", "value": "double_top_aux_cut", "visibility_condition": "", "component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT"}]}, {"name": "CommandeX Hinged - Lock Colour", "technical_name": "bx_dbl_hinge_commandex_hinged_lock_colour", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged'\", \"logic\": \"and\"}]", "sequence": 142, "question_number": 75, "quantity_formula": "1", "options": [{"name": "Black", "value": "black", "visibility_condition": "", "component_product": "Commandex Hinged Lock", "component_product_code": "CXHL-BLK"}, {"name": "Hamersley Brown", "value": "hamersley_brown", "visibility_condition": "", "component_product": "Commandex Hinged Lock", "component_product_code": "CXHL-HMB"}, {"name": "<PERSON>", "value": "pearl_white", "visibility_condition": "", "component_product": "Commandex Hinged Lock", "component_product_code": "CXHL-PW"}, {"name": "<PERSON><PERSON><PERSON>", "value": "primrose", "visibility_condition": "", "component_product": "Commandex Hinged Lock", "component_product_code": "CXHL-PR"}, {"name": "Silver Paint", "value": "silver_paint", "visibility_condition": "", "component_product": "Commandex Hinged Lock", "component_product_code": "CXHL-SLV"}]}, {"name": "CommandeX Hinged - <PERSON><PERSON><PERSON>", "technical_name": "bx_dbl_hinge_commandex_hinged_cylinder", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged'\", \"logic\": \"and\"}]", "sequence": 143, "question_number": 76, "quantity_formula": "1", "options": [{"name": "CommandeX 5 Pin", "value": "commandex_5_pin", "visibility_condition": "", "component_product": "Commandex 5 <PERSON><PERSON>er Keyed <PERSON>", "component_product_code": "CX5PIN"}, {"name": "Whitco 5 Pin", "value": "whitco_5_pin", "visibility_condition": "", "component_product": "Whitco 5 Pin <PERSON>", "component_product_code": "WT5PIN"}, {"name": "Whitco 5 Disc", "value": "whitco_5_disc", "visibility_condition": "", "component_product": "Whitco 5 Disc Cylinder", "component_product_code": "WT5DISC"}, {"name": "NO Cylinder", "value": "no_cylinder", "visibility_condition": ""}]}, {"name": "CommandeX Hinged - Lock Height Location", "technical_name": "bx_dbl_hinge_commandex_hinged_lock_height_location", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged'\", \"logic\": \"and\"}]", "sequence": 144, "question_number": 77, "quantity_formula": "1", "options": [{"name": "Top of Cut Out", "value": "top_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Cut Out", "value": "centre_of_cut_out", "visibility_condition": ""}, {"name": "Bottom of Cut Out", "value": "bottom_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Handle Lever", "value": "centre_of_handle_lever", "visibility_condition": ""}]}, {"name": "CommandeX Hinged - Lock Height (mm) (Top of Cut Out)", "technical_name": "bx_dbl_hinge_commandex_hinged_lh_top_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 430mm and less than door height - 270mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged' && bx_dbl_hinge_commandex_hinged_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 145, "question_number": 78, "quantity_formula": "1"}, {"name": "CommandeX Hinged - Lock Type (Top of Cut Out)", "technical_name": "bx_dbl_hinge_commandex_hinged_lt_top_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged' && bx_dbl_hinge_commandex_hinged_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 146, "question_number": 79, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT"}, {"name": "3PT", "value": "3pt", "visibility_condition": "", "component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT"}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": "", "component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT"}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": "", "component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT"}]}, {"name": "CommandeX Hinged - Lock Height (mm) (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_commandex_hinged_lh_centre_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 351mm and less than door height - 191mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged' && bx_dbl_hinge_commandex_hinged_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 147, "question_number": 80, "quantity_formula": "1"}, {"name": "CommandeX Hinged - Lock Type (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_commandex_hinged_lt_centre_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged' && bx_dbl_hinge_commandex_hinged_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 148, "question_number": 81, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT"}, {"name": "3PT", "value": "3pt", "visibility_condition": "", "component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT"}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": "", "component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT"}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": "", "component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT"}]}, {"name": "CommandeX Hinged - Lock Height (mm) (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 272mm and less than door height - 428mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged' && bx_dbl_hinge_commandex_hinged_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 149, "question_number": 82, "quantity_formula": "1"}, {"name": "CommandeX Hinged - Lock Type (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged' && bx_dbl_hinge_commandex_hinged_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 150, "question_number": 83, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT"}, {"name": "3PT", "value": "3pt", "visibility_condition": "", "component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT"}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": "", "component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT"}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": "", "component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT"}]}, {"name": "CommandeX Hinged - Lock Height (mm) (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_commandex_hinged_lh_centre_handle", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 370mm and less than door height - 330mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged' && bx_dbl_hinge_commandex_hinged_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 151, "question_number": 84, "quantity_formula": "1"}, {"name": "CommandeX Hinged - Lock Type (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_commandex_hinged_lt_centre_handle", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged' && bx_dbl_hinge_commandex_hinged_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 152, "question_number": 85, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT"}, {"name": "3PT", "value": "3pt", "visibility_condition": "", "component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT"}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": "", "component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT"}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": "", "component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT"}]}, {"name": "Lockwood 8654 - <PERSON> Colour", "technical_name": "bx_dbl_hinge_lockwood_8654_lock_colour", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654'\", \"logic\": \"and\"}]", "sequence": 153, "question_number": 86, "quantity_formula": "1", "options": [{"name": "Black", "value": "black", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged Lock", "component_product_code": "LW8654HL-BLK"}, {"name": "Hamersley Brown", "value": "hamersley_brown", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged Lock", "component_product_code": "LW8654HL-HMB"}, {"name": "<PERSON>", "value": "pearl_white", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged Lock", "component_product_code": "LW8654HL-PW"}, {"name": "<PERSON><PERSON><PERSON>", "value": "primrose", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged Lock", "component_product_code": "LW8654HL-PR"}, {"name": "Satin Chrome", "value": "satin_chrome", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged Lock", "component_product_code": "LW8654HL-SCH"}, {"name": "Silver Paint", "value": "silver_paint", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged Lock", "component_product_code": "LW8654HL-SLV"}, {"name": "Stone Beige", "value": "stone_beige", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged Lock", "component_product_code": "LW8654HL-STB"}, {"name": "White Birch", "value": "white_birch", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged Lock", "component_product_code": "LW8654HL-WB"}]}, {"name": "Lockwood 8654 - <PERSON><PERSON><PERSON>", "technical_name": "bx_dbl_hinge_lockwood_8654_cylinder", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654'\", \"logic\": \"and\"}]", "sequence": 154, "question_number": 87, "quantity_formula": "1", "options": [{"name": "Whitco 5 Pin", "value": "whitco_5_pin", "visibility_condition": "", "component_product": "Whitco 5 Pin <PERSON>", "component_product_code": "WT5PIN"}, {"name": "Whitco 5 Disc", "value": "whitco_5_disc", "visibility_condition": "", "component_product": "Whitco 5 Disc Cylinder", "component_product_code": "WT5DISC"}, {"name": "NO Cylinder", "value": "no_cylinder", "visibility_condition": ""}]}, {"name": "Lockwood 8654 - Lock Height Location", "technical_name": "bx_dbl_hinge_lockwood_8654_lock_height_location", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654'\", \"logic\": \"and\"}]", "sequence": 155, "question_number": 88, "quantity_formula": "1", "options": [{"name": "Top of Cut Out", "value": "top_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Cut Out", "value": "centre_of_cut_out", "visibility_condition": ""}, {"name": "Bottom of Cut Out", "value": "bottom_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Handle Lever", "value": "centre_of_handle_lever", "visibility_condition": ""}]}, {"name": "Lockwood 8654 - Lock Height (mm) (Top of Cut Out)", "technical_name": "bx_dbl_hinge_lockwood_8654_lh_top_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 430mm and less than door height - 270mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654' && bx_dbl_hinge_lockwood_8654_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 156, "question_number": 89, "quantity_formula": "1"}, {"name": "Lockwood 8654 - Lock Type (Top of Cut Out)", "technical_name": "bx_dbl_hinge_lockwood_8654_lt_top_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654' && bx_dbl_hinge_lockwood_8654_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 157, "question_number": 90, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT"}, {"name": "3PT <PERSON> Bolt", "value": "3pt_hook_bolt", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT"}, {"name": "3PT Projection Bolt", "value": "3pt_projection_bolt", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB"}, {"name": "Double with BOTTOM Auxiliary Hook Bolt ONLY", "value": "double_bottom_aux_hook_bolt_only", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT"}, {"name": "Double with BOTTOM Auxiliary Projection Bolt ONLY", "value": "double_bottom_aux_projection_bolt_only", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB"}, {"name": "Double with TOP Auxiliary Hook Bolt ONLY", "value": "double_top_aux_hook_bolt_only", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT"}, {"name": "Double with TOP Auxiliary Projection Bolt ONLY", "value": "double_top_aux_projection_bolt_only", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB"}]}, {"name": "Lockwood 8654 - Lock Height (mm) (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_lockwood_8654_lh_centre_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 351mm and less than door height - 349mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654' && bx_dbl_hinge_lockwood_8654_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 158, "question_number": 91, "quantity_formula": "1"}, {"name": "Lockwood 8654 - Lock Type (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_lockwood_8654_lt_centre_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654' && bx_dbl_hinge_lockwood_8654_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 159, "question_number": 92, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT"}, {"name": "3PT <PERSON> Bolt", "value": "3pt_hook_bolt", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT"}, {"name": "3PT Projection Bolt", "value": "3pt_projection_bolt", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB"}, {"name": "Double with BOTTOM Auxiliary Hook Bolt ONLY", "value": "double_bottom_aux_hook_bolt_only", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT"}, {"name": "Double with BOTTOM Auxiliary Projection Bolt ONLY", "value": "double_bottom_aux_projection_bolt_only", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB"}, {"name": "Double with TOP Auxiliary Hook Bolt ONLY", "value": "double_top_aux_hook_bolt_only", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT"}, {"name": "Double with TOP Auxiliary Projection Bolt ONLY", "value": "double_top_aux_projection_bolt_only", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB"}]}, {"name": "Lockwood 8654 - Lock Height (mm) (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 272mm and less than door height - 428mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654' && bx_dbl_hinge_lockwood_8654_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 160, "question_number": 93, "quantity_formula": "1"}, {"name": "Lockwood 8654 - Lock Type (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654' && bx_dbl_hinge_lockwood_8654_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 161, "question_number": 94, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT"}, {"name": "3PT <PERSON> Bolt", "value": "3pt_hook_bolt", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT"}, {"name": "3PT Projection Bolt", "value": "3pt_projection_bolt", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB"}, {"name": "Double with BOTTOM Auxiliary Hook Bolt ONLY", "value": "double_bottom_aux_hook_bolt_only", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT"}, {"name": "Double with BOTTOM Auxiliary Projection Bolt ONLY", "value": "double_bottom_aux_projection_bolt_only", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB"}, {"name": "Double with TOP Auxiliary Hook Bolt ONLY", "value": "double_top_aux_hook_bolt_only", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT"}, {"name": "Double with TOP Auxiliary Projection Bolt ONLY", "value": "double_top_aux_projection_bolt_only", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB"}]}, {"name": "Lockwood 8654 - Lock Height (mm) (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_lockwood_8654_lh_centre_handle", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 370mm and less than door height - 330mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654' && bx_dbl_hinge_lockwood_8654_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 162, "question_number": 95, "quantity_formula": "1"}, {"name": "Lockwood 8654 - Lock Type (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_lockwood_8654_lt_centre_handle", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654' && bx_dbl_hinge_lockwood_8654_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 163, "question_number": 96, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT"}, {"name": "3PT <PERSON> Bolt", "value": "3pt_hook_bolt", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT"}, {"name": "3PT Projection Bolt", "value": "3pt_projection_bolt", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB"}, {"name": "Double with BOTTOM Auxiliary Hook Bolt ONLY", "value": "double_bottom_aux_hook_bolt_only", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT"}, {"name": "Double with BOTTOM Auxiliary Projection Bolt ONLY", "value": "double_bottom_aux_projection_bolt_only", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB"}, {"name": "Double with TOP Auxiliary Hook Bolt ONLY", "value": "double_top_aux_hook_bolt_only", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT"}, {"name": "Double with TOP Auxiliary Projection Bolt ONLY", "value": "double_top_aux_projection_bolt_only", "visibility_condition": "", "component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB"}]}, {"name": "Whitco MK2 - Lock Colour", "technical_name": "bx_dbl_hinge_whitco_mk2_lock_colour", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2'\", \"logic\": \"and\"}]", "sequence": 164, "question_number": 97, "quantity_formula": "1", "options": [{"name": "Black", "value": "black", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged Lock", "component_product_code": "WTMK2HL-BLK"}, {"name": "<PERSON>", "value": "pearl_white", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged Lock", "component_product_code": "WTMK2HL-PW"}, {"name": "<PERSON><PERSON><PERSON>", "value": "primrose", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged Lock", "component_product_code": "WTMK2HL-PR"}]}, {"name": "Whitco MK2 - <PERSON><PERSON>er", "technical_name": "bx_dbl_hinge_whitco_mk2_cylinder", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2'\", \"logic\": \"and\"}]", "sequence": 165, "question_number": 98, "quantity_formula": "1", "options": [{"name": "Whitco 5 Pin", "value": "whitco_5_pin", "visibility_condition": "", "component_product": "Whitco 5 Pin <PERSON>", "component_product_code": "WT5PIN"}, {"name": "Whitco 5 Disc", "value": "whitco_5_disc", "visibility_condition": "", "component_product": "Whitco 5 Disc Cylinder", "component_product_code": "WT5DISC"}, {"name": "NO Cylinder", "value": "no_cylinder", "visibility_condition": ""}]}, {"name": "Whitco MK2 - Lock Height Location", "technical_name": "bx_dbl_hinge_whitco_mk2_lock_height_location", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2'\", \"logic\": \"and\"}]", "sequence": 166, "question_number": 99, "quantity_formula": "1", "options": [{"name": "Top of Cut Out", "value": "top_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Cut Out", "value": "centre_of_cut_out", "visibility_condition": ""}, {"name": "Bottom of Cut Out", "value": "bottom_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Handle Lever", "value": "centre_of_handle_lever", "visibility_condition": ""}]}, {"name": "Whitco MK2 - Lock Height (mm) (Top of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_mk2_lh_top_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 430mm and less than door height - 270mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2' && bx_dbl_hinge_whitco_mk2_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 167, "question_number": 100, "quantity_formula": "1"}, {"name": "Whitco MK2 - Lock Type (Top of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_mk2_lt_top_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2' && bx_dbl_hinge_whitco_mk2_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 168, "question_number": 101, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "3PT", "value": "3pt", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "3PT with TOP Auxiliary CUT DOWN", "value": "3pt_top_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW"}, {"name": "3PT with BOTTOM Auxiliary CUT DOWN", "value": "3pt_bottom_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW"}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "3PT with TOP & BOTTOM Auxiliary CUT DOWN", "value": "3pt_top_bottom_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "Double with BOTTOM Auxiliary CUT DOWN", "value": "double_bottom_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW"}, {"name": "Double with TOP Auxiliary CUT DOWN", "value": "double_top_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}]}, {"name": "Whitco MK2 - Lock Height (mm) (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_mk2_lh_centre_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 351mm and less than door height - 349mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2' && bx_dbl_hinge_whitco_mk2_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 169, "question_number": 102, "quantity_formula": "1"}, {"name": "Whitco MK2 - Lock Type (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_mk2_lt_centre_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2' && bx_dbl_hinge_whitco_mk2_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 170, "question_number": 103, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "3PT", "value": "3pt", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "3PT with TOP Auxiliary CUT DOWN", "value": "3pt_top_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW"}, {"name": "3PT with BOTTOM Auxiliary CUT DOWN", "value": "3pt_bottom_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW"}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "3PT with TOP & BOTTOM Auxiliary CUT DOWN", "value": "3pt_top_bottom_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "Double with BOTTOM Auxiliary CUT DOWN", "value": "double_bottom_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW"}, {"name": "Double with TOP Auxiliary CUT DOWN", "value": "double_top_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}]}, {"name": "Whitco MK2 - Lock Height (mm) (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 272mm and less than door height - 428mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2' && bx_dbl_hinge_whitco_mk2_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 171, "question_number": 104, "quantity_formula": "1"}, {"name": "Whitco MK2 - Lock Type (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2' && bx_dbl_hinge_whitco_mk2_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 172, "question_number": 105, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "3PT", "value": "3pt", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "3PT with TOP Auxiliary CUT DOWN", "value": "3pt_top_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW"}, {"name": "3PT with BOTTOM Auxiliary CUT DOWN", "value": "3pt_bottom_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW"}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "3PT with TOP & BOTTOM Auxiliary CUT DOWN", "value": "3pt_top_bottom_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "Double with BOTTOM Auxiliary CUT DOWN", "value": "double_bottom_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW"}, {"name": "Double with TOP Auxiliary CUT DOWN", "value": "double_top_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}]}, {"name": "Whitco MK2 - Lock Height (mm) (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_whitco_mk2_lh_centre_handle", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 367mm and less than door height - 333mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2' && bx_dbl_hinge_whitco_mk2_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 173, "question_number": 106, "quantity_formula": "1"}, {"name": "Whitco MK2 - Lock Type (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_whitco_mk2_lt_centre_handle", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2' && bx_dbl_hinge_whitco_mk2_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 174, "question_number": 107, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "3PT", "value": "3pt", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "3PT with TOP Auxiliary CUT DOWN", "value": "3pt_top_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW"}, {"name": "3PT with BOTTOM Auxiliary CUT DOWN", "value": "3pt_bottom_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW"}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "3PT with TOP & BOTTOM Auxiliary CUT DOWN", "value": "3pt_top_bottom_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}, {"name": "Double with BOTTOM Auxiliary CUT DOWN", "value": "double_bottom_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW"}, {"name": "Double with TOP Auxiliary CUT DOWN", "value": "double_top_aux_cut", "visibility_condition": "", "component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT"}]}, {"name": "<PERSON><PERSON><PERSON> Tasman ESCAPE - Lock Colour", "technical_name": "bx_dbl_hinge_whitco_tasman_escape_lock_colour", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_tasman_escape'\", \"logic\": \"and\"}]", "sequence": 175, "question_number": 108, "quantity_formula": "1", "options": [{"name": "Black", "value": "black", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged Lock W807017", "component_product_code": "WTESCP-BLK"}]}, {"name": "<PERSON><PERSON><PERSON> Tasman ESCAPE - Cylinder", "technical_name": "bx_dbl_hinge_whitco_tasman_escape_cylinder", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_tasman_escape'\", \"logic\": \"and\"}]", "sequence": 176, "question_number": 109, "quantity_formula": "1", "options": [{"name": "Whitco ESCAPE Cylinder w/ Turn Knob", "value": "whitco_escape_cylinder_turn_knob", "visibility_condition": "", "component_product": "<PERSON><PERSON><PERSON> Tasman Escape Cylinder With Turn Knob W845900", "component_product_code": "WTESCPCYL"}]}, {"name": "Whitco Tasman ESCAPE - Lock Height Location", "technical_name": "bx_dbl_hinge_whitco_tasman_escape_lock_height_location", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_tasman_escape'\", \"logic\": \"and\"}]", "sequence": 177, "question_number": 110, "quantity_formula": "1", "options": [{"name": "Top of Cut Out", "value": "top_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Cut Out", "value": "centre_of_cut_out", "visibility_condition": ""}, {"name": "Bottom of Cut Out", "value": "bottom_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Handle Lever", "value": "centre_of_handle_lever", "visibility_condition": ""}]}, {"name": "Whitco Tasman ESCAPE - Lock Height (mm) (Top of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 430mm and less than door height - 270mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_tasman_escape' && bx_dbl_hinge_whitco_tasman_escape_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 178, "question_number": 111, "quantity_formula": "1"}, {"name": "<PERSON><PERSON><PERSON> Tasman ESCAPE - Lock Type (Top of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_tasman_escape' && bx_dbl_hinge_whitco_tasman_escape_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 179, "question_number": 112, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT"}, {"name": "3PT", "value": "3pt", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT"}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT"}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT"}]}, {"name": "Whitco Tasman ESCAPE - Lock Height (mm) (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 351mm and less than door height - 349mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_tasman_escape' && bx_dbl_hinge_whitco_tasman_escape_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 180, "question_number": 113, "quantity_formula": "1"}, {"name": "Whitco Tasman ESCAPE - Lock Type (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_tasman_escape' && bx_dbl_hinge_whitco_tasman_escape_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 181, "question_number": 114, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT"}, {"name": "3PT", "value": "3pt", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT"}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT"}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT"}]}, {"name": "Whitco Tasman ESCAPE - Lock Height (mm) (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 272mm and less than door height - 428mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_tasman_escape' && bx_dbl_hinge_whitco_tasman_escape_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 182, "question_number": 115, "quantity_formula": "1"}, {"name": "<PERSON><PERSON><PERSON> Tasman ESCAPE - Lock Type (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_tasman_escape' && bx_dbl_hinge_whitco_tasman_escape_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 183, "question_number": 116, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT"}, {"name": "3PT", "value": "3pt", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT"}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT"}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT"}]}, {"name": "Whitco Tasman ESCAPE - Lock Height (mm) (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 370mm and less than door height - 330mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_tasman_escape' && bx_dbl_hinge_whitco_tasman_escape_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 184, "question_number": 117, "quantity_formula": "1"}, {"name": "Whitco Tasman ESCAPE - Lock Type (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_tasman_escape' && bx_dbl_hinge_whitco_tasman_escape_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 185, "question_number": 118, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT"}, {"name": "3PT", "value": "3pt", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT"}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT"}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": "", "component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT"}]}, {"name": "Austral Elegance XC CUT OUT ONLY - <PERSON><PERSON>er", "technical_name": "bx_dbl_hinge_austral_elegance_xc_co_cylinder", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc_cut_out_only'\", \"logic\": \"and\"}]", "sequence": 186, "question_number": 119, "quantity_formula": "1", "options": [{"name": "Austral 5 Pin DBL", "value": "austral_5_pin_dbl", "visibility_condition": "", "component_product": "Austral 5 Pin Double Cylinder", "component_product_code": "AU5PIN"}, {"name": "NO Cylinder", "value": "no_cylinder", "visibility_condition": ""}]}, {"name": "Austral Elegance XC CUT OUT ONLY - Lock Height Location", "technical_name": "bx_dbl_hinge_austral_elegance_xc_co_lock_height_location", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc_cut_out_only'\", \"logic\": \"and\"}]", "sequence": 187, "question_number": 120, "quantity_formula": "1", "options": [{"name": "Top of Cut Out", "value": "top_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Cut Out", "value": "centre_of_cut_out", "visibility_condition": ""}, {"name": "Bottom of Cut Out", "value": "bottom_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Handle Lever", "value": "centre_of_handle_lever", "visibility_condition": ""}]}, {"name": "Austral Elegance XC CUT OUT ONLY - Lock Height (mm) (Top of Cut Out)", "technical_name": "bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 430mm and less than door height - 270mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc_cut_out_only' && bx_dbl_hinge_austral_elegance_xc_co_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 188, "question_number": 121, "quantity_formula": "1"}, {"name": "Austral Elegance XC CUT OUT ONLY - Lock Type (Top of Cut Out)", "technical_name": "bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc_cut_out_only' && bx_dbl_hinge_austral_elegance_xc_co_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 189, "question_number": 122, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": ""}, {"name": "3PT", "value": "3pt", "visibility_condition": ""}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": ""}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": ""}]}, {"name": "Austral Elegance XC CUT OUT ONLY - Lock Height (mm) (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 351mm and less than door height - 349mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc_cut_out_only' && bx_dbl_hinge_austral_elegance_xc_co_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 190, "question_number": 123, "quantity_formula": "1"}, {"name": "Austral Elegance XC CUT OUT ONLY - Lock Type (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc_cut_out_only' && bx_dbl_hinge_austral_elegance_xc_co_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 191, "question_number": 124, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": ""}, {"name": "3PT", "value": "3pt", "visibility_condition": ""}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": ""}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": ""}]}, {"name": "Austral Elegance XC CUT OUT ONLY - Lock Height (mm) (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 272mm and less than door height - 428mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc_cut_out_only' && bx_dbl_hinge_austral_elegance_xc_co_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 192, "question_number": 125, "quantity_formula": "1"}, {"name": "Austral Elegance XC CUT OUT ONLY - Lock Type (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc_cut_out_only' && bx_dbl_hinge_austral_elegance_xc_co_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 193, "question_number": 126, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": ""}, {"name": "3PT", "value": "3pt", "visibility_condition": ""}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": ""}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": ""}]}, {"name": "Austral Elegance XC CUT OUT ONLY - Lock Height (mm) (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 383mm and less than door height - 317mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc_cut_out_only' && bx_dbl_hinge_austral_elegance_xc_co_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 194, "question_number": 127, "quantity_formula": "1"}, {"name": "Austral Elegance XC CUT OUT ONLY - Lock Type (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'austral_elegance_xc_cut_out_only' && bx_dbl_hinge_austral_elegance_xc_co_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 195, "question_number": 128, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": ""}, {"name": "3PT", "value": "3pt", "visibility_condition": ""}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": ""}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": ""}]}, {"name": "CommandeX Hinged CUT OUT ONLY - <PERSON><PERSON><PERSON>", "technical_name": "bx_dbl_hinge_commandex_hinged_co_cylinder", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged_cut_out_only'\", \"logic\": \"and\"}]", "sequence": 196, "question_number": 129, "quantity_formula": "1", "options": [{"name": "CommandeX 5 Pin", "value": "commandex_5_pin", "visibility_condition": "", "component_product": "Commandex 5 <PERSON><PERSON>er Keyed <PERSON>", "component_product_code": "CX5PIN"}, {"name": "Whitco 5 Pin", "value": "whitco_5_pin", "visibility_condition": "", "component_product": "Whitco 5 Pin <PERSON>", "component_product_code": "WT5PIN"}, {"name": "Whitco 5 Disc", "value": "whitco_5_disc", "visibility_condition": "", "component_product": "Whitco 5 Disc Cylinder", "component_product_code": "WT5DISC"}, {"name": "NO Cylinder", "value": "no_cylinder", "visibility_condition": ""}]}, {"name": "CommandeX Hinged CUT OUT ONLY - Lock Height Location", "technical_name": "bx_dbl_hinge_commandex_hinged_co_lock_height_location", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged_cut_out_only'\", \"logic\": \"and\"}]", "sequence": 197, "question_number": 130, "quantity_formula": "1", "options": [{"name": "Top of Cut Out", "value": "top_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Cut Out", "value": "centre_of_cut_out", "visibility_condition": ""}, {"name": "Bottom of Cut Out", "value": "bottom_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Handle Lever", "value": "centre_of_handle_lever", "visibility_condition": ""}]}, {"name": "CommandeX Hinged CUT OUT ONLY - Lock Height (mm) (Top of Cut Out)", "technical_name": "bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 430mm and less than door height - 270mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged_cut_out_only' && bx_dbl_hinge_commandex_hinged_co_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 198, "question_number": 131, "quantity_formula": "1"}, {"name": "CommandeX Hinged CUT OUT ONLY - Lock Type (Top of Cut Out)", "technical_name": "bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged_cut_out_only' && bx_dbl_hinge_commandex_hinged_co_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 199, "question_number": 132, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": ""}, {"name": "3PT", "value": "3pt", "visibility_condition": ""}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": ""}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": ""}]}, {"name": "CommandeX Hinged CUT OUT ONLY - Lock Height (mm) (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 351mm and less than door height - 349mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged_cut_out_only' && bx_dbl_hinge_commandex_hinged_co_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 200, "question_number": 133, "quantity_formula": "1"}, {"name": "CommandeX Hinged CUT OUT ONLY - Lock Type (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged_cut_out_only' && bx_dbl_hinge_commandex_hinged_co_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 201, "question_number": 134, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": ""}, {"name": "3PT", "value": "3pt", "visibility_condition": ""}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": ""}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": ""}]}, {"name": "CommandeX Hinged CUT OUT ONLY - Lock Height (mm) (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 272mm and less than door height - 428mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged_cut_out_only' && bx_dbl_hinge_commandex_hinged_co_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 202, "question_number": 135, "quantity_formula": "1"}, {"name": "CommandeX Hinged CUT OUT ONLY - Lock Type (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged_cut_out_only' && bx_dbl_hinge_commandex_hinged_co_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 203, "question_number": 136, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": ""}, {"name": "3PT", "value": "3pt", "visibility_condition": ""}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": ""}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": ""}]}, {"name": "CommandeX Hinged CUT OUT ONLY - Lock Height (mm) (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_commandex_hinged_co_lh_centre_handle", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 370mm and less than door height - 330mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged_cut_out_only' && bx_dbl_hinge_commandex_hinged_co_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 204, "question_number": 137, "quantity_formula": "1"}, {"name": "CommandeX Hinged CUT OUT ONLY - Lock Type (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_commandex_hinged_co_lt_centre_handle", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'commandex_hinged_cut_out_only' && bx_dbl_hinge_commandex_hinged_co_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 205, "question_number": 138, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": ""}, {"name": "3PT", "value": "3pt", "visibility_condition": ""}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": ""}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": ""}]}, {"name": "Lockwood 8654 CUT OUT ONLY - <PERSON><PERSON><PERSON>", "technical_name": "bx_dbl_hinge_lockwood_8654_co_cylinder", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654_cut_out_only'\", \"logic\": \"and\"}]", "sequence": 206, "question_number": 139, "quantity_formula": "1", "options": [{"name": "Whitco 5 Pin", "value": "whitco_5_pin", "visibility_condition": "", "component_product": "Whitco 5 Pin <PERSON>", "component_product_code": "WT5PIN"}, {"name": "Whitco 5 Disc", "value": "whitco_5_disc", "visibility_condition": "", "component_product": "Whitco 5 Disc Cylinder", "component_product_code": "WT5DISC"}, {"name": "NO Cylinder", "value": "no_cylinder", "visibility_condition": ""}]}, {"name": "Lockwood 8654 CUT OUT ONLY - Lock Height Location", "technical_name": "bx_dbl_hinge_lockwood_8654_co_lock_height_location", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654_cut_out_only'\", \"logic\": \"and\"}]", "sequence": 207, "question_number": 140, "quantity_formula": "1", "options": [{"name": "Top of Cut Out", "value": "top_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Cut Out", "value": "centre_of_cut_out", "visibility_condition": ""}, {"name": "Bottom of Cut Out", "value": "bottom_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Handle Lever", "value": "centre_of_handle_lever", "visibility_condition": ""}]}, {"name": "Lockwood 8654 CUT OUT ONLY - Lock Height (mm) (Top of Cut Out)", "technical_name": "bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 430mm and less than door height - 270mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654_cut_out_only' && bx_dbl_hinge_lockwood_8654_co_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 208, "question_number": 141, "quantity_formula": "1"}, {"name": "Lockwood 8654 CUT OUT ONLY - Lock Type (Top of Cut Out)", "technical_name": "bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654_cut_out_only' && bx_dbl_hinge_lockwood_8654_co_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 209, "question_number": 142, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": ""}, {"name": "3PT", "value": "3pt", "visibility_condition": ""}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": ""}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": ""}]}, {"name": "Lockwood 8654 CUT OUT ONLY - Lock Height (mm) (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 351mm and less than door height - 349mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654_cut_out_only' && bx_dbl_hinge_lockwood_8654_co_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 210, "question_number": 143, "quantity_formula": "1"}, {"name": "Lockwood 8654 CUT OUT ONLY - Lock Type (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654_cut_out_only' && bx_dbl_hinge_lockwood_8654_co_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 211, "question_number": 144, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": ""}, {"name": "3PT", "value": "3pt", "visibility_condition": ""}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": ""}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": ""}]}, {"name": "Lockwood 8654 CUT OUT ONLY - Lock Height (mm) (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 272mm and less than door height - 428mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654_cut_out_only' && bx_dbl_hinge_lockwood_8654_co_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 212, "question_number": 145, "quantity_formula": "1"}, {"name": "Lockwood 8654 CUT OUT ONLY - Lock Type (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654_cut_out_only' && bx_dbl_hinge_lockwood_8654_co_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 213, "question_number": 146, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": ""}, {"name": "3PT", "value": "3pt", "visibility_condition": ""}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": ""}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": ""}]}, {"name": "Lockwood 8654 CUT OUT ONLY - Lock Height (mm) (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_lockwood_8654_co_lh_centre_handle", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 370mm and less than door height - 330mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654_cut_out_only' && bx_dbl_hinge_lockwood_8654_co_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 214, "question_number": 147, "quantity_formula": "1"}, {"name": "Lockwood 8654 CUT OUT ONLY - Lock Type (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_lockwood_8654_co_lt_centre_handle", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'lockwood_8654_cut_out_only' && bx_dbl_hinge_lockwood_8654_co_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 215, "question_number": 148, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": ""}, {"name": "3PT", "value": "3pt", "visibility_condition": ""}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": ""}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": ""}]}, {"name": "Whitco MK2 CUT OUT ONLY - Cylinder", "technical_name": "bx_dbl_hinge_whitco_mk2_co_cylinder", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2_cut_out_only'\", \"logic\": \"and\"}]", "sequence": 216, "question_number": 149, "quantity_formula": "1", "options": [{"name": "Whitco 5 Pin", "value": "whitco_5_pin", "visibility_condition": "", "component_product": "Whitco 5 Pin <PERSON>", "component_product_code": "WT5PIN"}, {"name": "Whitco 5 Disc", "value": "whitco_5_disc", "visibility_condition": "", "component_product": "Whitco 5 Disc Cylinder", "component_product_code": "WT5DISC"}, {"name": "NO Cylinder", "value": "no_cylinder", "visibility_condition": ""}]}, {"name": "Whitco MK2 CUT OUT ONLY - Lock Height Location", "technical_name": "bx_dbl_hinge_whitco_mk2_co_lock_height_location", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2_cut_out_only'\", \"logic\": \"and\"}]", "sequence": 217, "question_number": 150, "quantity_formula": "1", "options": [{"name": "Top of Cut Out", "value": "top_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Cut Out", "value": "centre_of_cut_out", "visibility_condition": ""}, {"name": "Bottom of Cut Out", "value": "bottom_of_cut_out", "visibility_condition": ""}, {"name": "Centre of Handle Lever", "value": "centre_of_handle_lever", "visibility_condition": ""}]}, {"name": "Whitco MK2 CUT OUT ONLY - Lock Height (mm) (Top of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 430mm and less than door height - 270mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2_cut_out_only' && bx_dbl_hinge_whitco_mk2_co_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 218, "question_number": 151, "quantity_formula": "1"}, {"name": "Whitco MK2 CUT OUT ONLY - Lock Type (Top of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2_cut_out_only' && bx_dbl_hinge_whitco_mk2_co_lock_height_location == 'top_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 219, "question_number": 152, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": ""}, {"name": "3PT", "value": "3pt", "visibility_condition": ""}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": ""}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": ""}]}, {"name": "Whitco MK2 CUT OUT ONLY - Lock Height (mm) (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 351mm and less than door height - 349mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2_cut_out_only' && bx_dbl_hinge_whitco_mk2_co_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 220, "question_number": 153, "quantity_formula": "1"}, {"name": "Whitco MK2 CUT OUT ONLY - Lock Type (Centre of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2_cut_out_only' && bx_dbl_hinge_whitco_mk2_co_lock_height_location == 'centre_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 221, "question_number": 154, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": ""}, {"name": "3PT", "value": "3pt", "visibility_condition": ""}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": ""}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": ""}]}, {"name": "Whitco MK2 CUT OUT ONLY - Lock Height (mm) (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 272mm and less than door height - 428mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2_cut_out_only' && bx_dbl_hinge_whitco_mk2_co_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 222, "question_number": 155, "quantity_formula": "1"}, {"name": "Whitco MK2 CUT OUT ONLY - Lock Type (Bottom of Cut Out)", "technical_name": "bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2_cut_out_only' && bx_dbl_hinge_whitco_mk2_co_lock_height_location == 'bottom_of_cut_out'\", \"logic\": \"and\"}]", "sequence": 223, "question_number": 156, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": ""}, {"name": "3PT", "value": "3pt", "visibility_condition": ""}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": ""}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": ""}]}, {"name": "Whitco MK2 CUT OUT ONLY - Lock Height (mm) (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_whitco_mk2_co_lh_centre_handle", "field_type": "number", "required": true, "default_value": "", "help_text": "*Lock Height must be greater than 367mm and less than door height - 333mm", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2_cut_out_only' && bx_dbl_hinge_whitco_mk2_co_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 224, "question_number": 157, "quantity_formula": "1"}, {"name": "Whitco MK2 CUT OUT ONLY - Lock Type (Centre of Handle Lever)", "technical_name": "bx_dbl_hinge_whitco_mk2_co_lt_centre_handle", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_brand == 'whitco_mk2_cut_out_only' && bx_dbl_hinge_whitco_mk2_co_lock_height_location == 'centre_of_handle_lever'\", \"logic\": \"and\"}]", "sequence": 225, "question_number": 158, "quantity_formula": "1", "options": [{"name": "Single", "value": "single", "visibility_condition": ""}, {"name": "3PT", "value": "3pt", "visibility_condition": ""}, {"name": "Double with BOTTOM Auxiliary ONLY", "value": "double_bottom_aux_only", "visibility_condition": ""}, {"name": "Double with TOP Auxiliary ONLY", "value": "double_top_aux_only", "visibility_condition": ""}]}, {"name": "Lock or Cut Out Side - OUTSIDE VIEW (Double Door)", "technical_name": "bx_dbl_hinge_lock_cut_out_side_dd", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "", "sequence": 226, "question_number": 159, "quantity_formula": "1", "options": [{"name": "Left Hand Door, Right Hand Lock", "value": "left_hand_door_right_hand_lock", "visibility_condition": ""}, {"name": "Right Hand Door, Left Hand Lock", "value": "right_hand_door_left_hand_lock", "visibility_condition": ""}]}, {"name": "Non-Lock Door Striker Cut Outs", "technical_name": "bx_dbl_hinge_non_lock_door_striker_cutouts", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "", "sequence": 227, "question_number": 160, "quantity_formula": "1", "options": [{"name": "Yes", "value": "yes", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Flush Bolts & Patio Bolts (Non-Lock Door)", "technical_name": "bx_dbl_hinge_non_lock_door_bolts", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "", "sequence": 228, "question_number": 161, "quantity_formula": "1", "options": [{"name": "Top & Bottom Flush Bolts", "value": "top_bottom_flush_bolts", "visibility_condition": "", "component_product": "<PERSON><PERSON><PERSON>", "component_product_code": "WTFB-BLK"}, {"name": "Top Flush Bolt", "value": "top_flush_bolt", "visibility_condition": "", "component_product": "<PERSON><PERSON><PERSON>", "component_product_code": "WTFB-BLK"}, {"name": "Bottom Flush Bolt", "value": "bottom_flush_bolt", "visibility_condition": "", "component_product": "<PERSON><PERSON><PERSON>", "component_product_code": "WTFB-BLK"}, {"name": "Top & Bottom Flush Bolts CUT OUT", "value": "top_bottom_flush_bolts_cut_out", "visibility_condition": ""}, {"name": "Top Flush Bolt CUT OUT", "value": "top_flush_bolt_cut_out", "visibility_condition": ""}, {"name": "Bottom Flush Bolt CUT OUT", "value": "bottom_flush_bolt_cut_out", "visibility_condition": ""}, {"name": "1 Supply Only <PERSON><PERSON>", "value": "1_supply_patio_bolt", "visibility_condition": ""}, {"name": "2 Supply Only <PERSON><PERSON>", "value": "2_supply_patio_bolts", "visibility_condition": ""}, {"name": "Top Flush Bolt & 1 Supply Only Patio Bolt", "value": "top_flush_1_patio", "visibility_condition": "", "component_product": "<PERSON><PERSON><PERSON>", "component_product_code": "WTFB-BLK"}, {"name": "Bottom Flush Bolt & 1 Supply Only Patio Bolt", "value": "bottom_flush_1_patio", "visibility_condition": "", "component_product": "<PERSON><PERSON><PERSON>", "component_product_code": "WTFB-BLK"}, {"name": "Top Flush Bolt CUT OUT & 1 Supply Only Patio Bolt", "value": "top_flush_cut_1_patio", "visibility_condition": ""}, {"name": "Bottom Flush Bolt CUT OUT & 1 Supply Only <PERSON><PERSON>lt", "value": "bottom_flush_cut_1_patio", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "<PERSON><PERSON> (Non-Lock Door)", "technical_name": "bx_dbl_hinge_patio_bolt_colour_non_lock", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_non_lock_door_bolts == '1_supply_patio_bolt' || bx_dbl_hinge_non_lock_door_bolts == '2_supply_patio_bolts' || bx_dbl_hinge_non_lock_door_bolts == 'top_flush_1_patio' || bx_dbl_hinge_non_lock_door_bolts == 'bottom_flush_1_patio' || bx_dbl_hinge_non_lock_door_bolts == 'top_flush_cut_1_patio' || bx_dbl_hinge_non_lock_door_bolts == 'bottom_flush_cut_1_patio'\", \"logic\": \"and\"}]", "sequence": 229, "question_number": 162, "quantity_formula": "1", "options": [{"name": "Black", "value": "black", "visibility_condition": "", "component_product": "<PERSON><PERSON><PERSON>", "component_product_code": "WTPB-BLK"}, {"name": "White", "value": "white", "visibility_condition": "", "component_product": "<PERSON><PERSON><PERSON>", "component_product_code": "WTPB-PW"}]}, {"name": "Midrail (Conditional Case 1)", "technical_name": "bx_dbl_hinge_midrail_case1", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"(bx_dbl_hinge_largest_door_height_mm > 1310 && bx_dbl_hinge_largest_door_width_mm > 1310) || bx_dbl_hinge_largest_door_height_mm > 2510\", \"logic\": \"and\"}]", "sequence": 230, "question_number": 163, "quantity_formula": "1", "options": [{"name": "Yes", "value": "yes", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Midrail Height (mm) - TOP OF MIDRAIL (Conditional Case 1)", "technical_name": "bx_dbl_hinge_midrail_height_mm_case1", "field_type": "number", "required": true, "default_value": "", "help_text": "*Minimum height is Door Height - 1236mm & Maximum height is 1282mm.", "visibility_condition": "__JSON__[{\"condition\": \"((bx_dbl_hinge_largest_door_height_mm > 1310 and bx_dbl_hinge_largest_door_width_mm > 1310) or   bx_dbl_hinge_largest_door_height_mm > 2510) and bx_dbl_hinge_midrail_case1 == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_midrail_case1 == 'yes'\", \"logic\": \"and\"}]", "sequence": 231, "question_number": 164, "quantity_formula": "1"}, {"name": "Midrail Colour (Standard Frame, Conditional Case 1)", "technical_name": "bx_dbl_hinge_midrail_colour_std_case1", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"((bx_dbl_hinge_largest_door_height_mm > 1310 && bx_dbl_hinge_largest_door_width_mm > 1310) || bx_dbl_hinge_largest_door_height_mm > 2510) && bx_dbl_hinge_midrail_case1 == 'yes' && bx_dbl_hinge_frame_colour != 'western_red_cedar_mz306a4489v5' && bx_dbl_hinge_frame_colour != 'clear_anodise_15mm' && bx_dbl_hinge_frame_colour != 'light_bronze_anodise_15mm'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_midrail_case1 == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_frame_colour not in ['western_red_cedar_mz306a4489v5', 'clear_anodise_15mm', 'light_bronze_anodise_15mm']\", \"logic\": \"and\"}]", "sequence": 232, "question_number": 165, "quantity_formula": "1", "options": [{"name": "Black", "value": "black", "visibility_condition": "", "component_product": "Commandex Midrail", "component_product_code": "CXMR-BLK"}, {"name": "Monument Matt GL229A", "value": "Monument Matt GL229A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['monument_matt_gl229a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-MON"}, {"name": "Pearl White Gloss GA078A", "value": "Pearl White Gloss GA078A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['pearl_white_gloss_ga078a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-PW"}, {"name": "Primrose Gloss GD037A", "value": "Primrose Gloss GD037A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['primrose_gloss_gd037a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-PR"}, {"name": "Stone Beige Matt GD247A", "value": "Stone Beige Matt GD247A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['stone_beige_matt_gd247a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-STB"}, {"name": "Surfmist Matt GA236A", "value": "Surfmist Matt GA236A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['surfmist_matt_ga236a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-SFM"}, {"name": "Ultra Silver Gloss GY070A", "value": "Ultra Silver Gloss GY070A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['ultra_silver_gloss_gy070a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-US"}, {"name": "White Birch Gloss GA057A", "value": "White Birch Gloss GA057A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['white_birch_gloss_ga057a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-WB"}, {"name": "Woodland Grey Matt GL205A", "value": "Woodland Grey Matt GL205A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['woodland_grey_matt_gl205a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-WG"}, {"name": "Mill Finish", "value": "Mill Finish", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['mill_finish']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-MF"}, {"name": "Anodic Bronze Satin GY114A", "value": "Anodic Bronze Satin GY114A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['anodic_bronze_satin_gy114a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-ABZ"}, {"name": "Anodic <PERSON> Grey Matt GL213A", "value": "Anodic <PERSON> Grey Matt GL213A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['anodic_dark_grey_matt_gl213a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-ADG"}, {"name": "Anodic Natural Matt GY235A", "value": "Anodic Natural Matt GY235A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['anodic_natural_matt_gy235a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-AN"}, {"name": "Anodic Off White Matt GD227A", "value": "Anodic Off White Matt GD227A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['anodic_off_white_matt_gd227a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-AOW"}, {"name": "Anodic Silver Grey Matt GL237A", "value": "Anodic Silver Grey Matt GL237A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['anodic_silver_grey_matt_gl237a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-ASG"}, {"name": "APO Grey Satin GL107A", "value": "APO Grey Satin GL107A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['apo_grey_satin_gl107a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-APO"}, {"name": "Deep Ocean Matt GJ203A", "value": "Deep Ocean Matt GJ203A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['deep_ocean_matt_gj203a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-DPO"}, {"name": "Doeskin Satin GD188A", "value": "Doeskin Satin GD188A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['doeskin_satin_gd188a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-DOE"}, {"name": "<PERSON>ne Matt GL252A", "value": "<PERSON>ne Matt GL252A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['dune_matt_gl252a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-DUN"}, {"name": "Hamersley Brown Satin GM100A", "value": "Hamersley Brown Satin GM100A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['hamersley_brown_satin_gm100a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-HMB"}, {"name": "Hawthorn Green Gloss GK030A", "value": "Hawthorn Green Gloss GK030A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['hawthorn_green_gloss_gk030a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-HWG"}, {"name": "Ironstone Matt GL236A", "value": "Ironstone Matt GL236A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['ironstone_matt_gl236a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-IRS"}, {"name": "Jasper Satin GT114A", "value": "Jasper Satin GT114A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['jasper_satin_gt114a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-JSP"}, {"name": "Paperbark Satin GU114A", "value": "Paperbark Satin GU114A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['paperbark_satin_gu114a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-PBK"}, {"name": "Pottery Satin GM175A", "value": "Pottery Satin GM175A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['pottery_satin_gm175a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-POT"}, {"name": "Precious Silver Kinetic Pearl Satin 971-7043K", "value": "Precious Silver Kinetic Pearl Satin 971-7043K", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['precious_silver_kinetic_pearl_satin_971_7043k']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-PSK"}, {"name": "Shale Grey Satin GP184A", "value": "Shale Grey Satin GP184A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['shale_grey_satin_gp184a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-SHG"}, {"name": "Textura Black GN305A", "value": "Textura Black GN305A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['textura_black_gn305a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-TXB"}, {"name": "Charcoal Satin GL180A", "value": "Charcoal Satin GL180A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['charcoal_satin_gl180a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-CHL"}]}, {"name": "Midrail Colour (Special Frame, Conditional Case 1)", "technical_name": "bx_dbl_hinge_midrail_colour_special_case1", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"((bx_dbl_hinge_largest_door_height_mm > 1310 && bx_dbl_hinge_largest_door_width_mm > 1310) || bx_dbl_hinge_largest_door_height_mm > 2510) && bx_dbl_hinge_midrail_case1 == 'yes' && (bx_dbl_hinge_frame_colour == 'western_red_cedar_mz306a4489v5' || bx_dbl_hinge_frame_colour == 'clear_anodise_15mm' || bx_dbl_hinge_frame_colour == 'light_bronze_anodise_15mm')\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_midrail_case1 == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_frame_colour in ['western_red_cedar_mz306a4489v5', 'clear_anodise_15mm', 'light_bronze_anodise_15mm']\", \"logic\": \"and\"}]", "sequence": 233, "question_number": 166, "quantity_formula": "1", "options": [{"name": "Black", "value": "black", "visibility_condition": "", "component_product": "Commandex Midrail", "component_product_code": "CXMR-BLK"}]}, {"name": "Midrail (Conditional Case 2)", "technical_name": "bx_dbl_hinge_midrail_case2", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"(bx_dbl_hinge_largest_door_height_mm <= 2510 && bx_dbl_hinge_largest_door_width_mm <= 1310) || bx_dbl_hinge_largest_door_height_mm <= 1310\", \"logic\": \"and\"}]", "sequence": 234, "question_number": 167, "quantity_formula": "1", "options": [{"name": "Yes", "value": "yes", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Midrail Height (mm) - TOP OF MIDRAIL (Conditional Case 2)", "technical_name": "bx_dbl_hinge_midrail_height_mm_case2", "field_type": "number", "required": true, "default_value": "", "help_text": "*Minimum height is Door Height - 1236mm & Maximum height is 1282mm.", "visibility_condition": "__JSON__[{\"condition\": \"((bx_dbl_hingebx_largest_door_height_mm <= 2510 && bx_dbl_hinge_largest_door_width_mm <= 1310) || bx_dbl_hinge_largest_door_height_mm <= 1310) && bx_dbl_hinge_midrail_case2 == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_midrail_case2 == 'yes'\", \"logic\": \"and\"}]", "sequence": 235, "question_number": 168, "quantity_formula": "1"}, {"name": "Midrail Colour (Standard Frame, Conditional Case 2)", "technical_name": "bx_dbl_hinge_midrail_colour_std_case2", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"((bx_dbl_hinge_largest_door_height_mm <= 2510 && bx_dbl_hinge_largest_door_width_mm <= 1310) || bx_dbl_hinge_largest_door_height_mm <= 1310) && bx_dbl_hinge_midrail_case2 == 'yes' && bx_dbl_hinge_frame_colour != 'western_red_cedar_mz306a4489v5' && bx_dbl_hinge_frame_colour != 'clear_anodise_15mm' && bx_dbl_hinge_frame_colour != 'light_bronze_anodise_15mm'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_midrail_case2 == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_frame_colour not in ['western_red_cedar_mz306a4489v5', 'clear_anodise_15mm', 'light_bronze_anodise_15mm']\", \"logic\": \"and\"}]", "sequence": 236, "question_number": 169, "quantity_formula": "1", "options": [{"name": "Black", "value": "black", "visibility_condition": "", "component_product": "Commandex Midrail", "component_product_code": "CXMR-BLK"}, {"name": "Monument Matt GL229A", "value": "Monument Matt GL229A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['monument_matt_gl229a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-MON"}, {"name": "Pearl White Gloss GA078A", "value": "Pearl White Gloss GA078A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['pearl_white_gloss_ga078a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-PW"}, {"name": "Primrose Gloss GD037A", "value": "Primrose Gloss GD037A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['primrose_gloss_gd037a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-PR"}, {"name": "Stone Beige Matt GD247A", "value": "Stone Beige Matt GD247A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['stone_beige_matt_gd247a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-STB"}, {"name": "Surfmist Matt GA236A", "value": "Surfmist Matt GA236A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['surfmist_matt_ga236a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-SFM"}, {"name": "Ultra Silver Gloss GY070A", "value": "Ultra Silver Gloss GY070A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['ultra_silver_gloss_gy070a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-US"}, {"name": "White Birch Gloss GA057A", "value": "White Birch Gloss GA057A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['white_birch_gloss_ga057a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-WB"}, {"name": "Woodland Grey Matt GL205A", "value": "Woodland Grey Matt GL205A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['woodland_grey_matt_gl205a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-WG"}, {"name": "Mill Finish", "value": "Mill Finish", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['mill_finish']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-MF"}, {"name": "Anodic Bronze Satin GY114A", "value": "Anodic Bronze Satin GY114A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['anodic_bronze_satin_gy114a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-ABZ"}, {"name": "Anodic <PERSON> Grey Matt GL213A", "value": "Anodic <PERSON> Grey Matt GL213A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['anodic_dark_grey_matt_gl213a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-ADG"}, {"name": "Anodic Natural Matt GY235A", "value": "Anodic Natural Matt GY235A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['anodic_natural_matt_gy235a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-AN"}, {"name": "Anodic Off White Matt GD227A", "value": "Anodic Off White Matt GD227A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['anodic_off_white_matt_gd227a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-AOW"}, {"name": "Anodic Silver Grey Matt GL237A", "value": "Anodic Silver Grey Matt GL237A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['anodic_silver_grey_matt_gl237a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-ASG"}, {"name": "APO Grey Satin GL107A", "value": "APO Grey Satin GL107A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['apo_grey_satin_gl107a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-APO"}, {"name": "Deep Ocean Matt GJ203A", "value": "Deep Ocean Matt GJ203A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['deep_ocean_matt_gj203a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-DPO"}, {"name": "Doeskin Satin GD188A", "value": "Doeskin Satin GD188A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['doeskin_satin_gd188a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-DOE"}, {"name": "<PERSON>ne Matt GL252A", "value": "<PERSON>ne Matt GL252A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['dune_matt_gl252a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-DUN"}, {"name": "Hamersley Brown Satin GM100A", "value": "Hamersley Brown Satin GM100A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['hamersley_brown_satin_gm100a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-HMB"}, {"name": "Hawthorn Green Gloss GK030A", "value": "Hawthorn Green Gloss GK030A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['hawthorn_green_gloss_gk030a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-HWG"}, {"name": "Ironstone Matt GL236A", "value": "Ironstone Matt GL236A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['ironstone_matt_gl236a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-IRS"}, {"name": "Jasper Satin GT114A", "value": "Jasper Satin GT114A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['jasper_satin_gt114a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-JSP"}, {"name": "Paperbark Satin GU114A", "value": "Paperbark Satin GU114A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['paperbark_satin_gu114a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-PBK"}, {"name": "Pottery Satin GM175A", "value": "Pottery Satin GM175A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['pottery_satin_gm175a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-POT"}, {"name": "Precious Silver Kinetic Pearl Satin 971-7043K", "value": "Precious Silver Kinetic Pearl Satin 971-7043K", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['precious_silver_kinetic_pearl_satin_971_7043k']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-PSK"}, {"name": "Shale Grey Satin GP184A", "value": "Shale Grey Satin GP184A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['shale_grey_satin_gp184a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-SHG"}, {"name": "Textura Black GN305A", "value": "Textura Black GN305A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['textura_black_gn305a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-TXB"}, {"name": "Charcoal Satin GL180A", "value": "Charcoal Satin GL180A", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_frame_colour in ['charcoal_satin_gl180a']\", \"logic\": \"and\"}]", "component_product": "Commandex Midrail", "component_product_code": "CXMR-CHL"}]}, {"name": "Midrail Colour (Special Frame, Conditional Case 2)", "technical_name": "bx_dbl_hinge_midrail_colour_special_case2", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"((bx_dbl_hinge_largest_door_height_mm <= 2510 && bx_dbl_hinge_largest_door_width_mm <= 1310) || bx_dbl_hinge_largest_door_height_mm <= 1310) && bx_dbl_hinge_midrail_case2 == 'yes' && (bx_dbl_hinge_frame_colour == 'western_red_cedar_mz306a4489v5' || bx_dbl_hinge_frame_colour == 'clear_anodise_15mm' || bx_dbl_hinge_frame_colour == 'light_bronze_anodise_15mm')\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_midrail_case2 == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_frame_colour in ['western_red_cedar_mz306a4489v5', 'clear_anodise_15mm', 'light_bronze_anodise_15mm']\", \"logic\": \"and\"}]", "sequence": 237, "question_number": 170, "quantity_formula": "1", "options": [{"name": "Black", "value": "black", "visibility_condition": "", "component_product": "Commandex Midrail", "component_product_code": "CXMR-BLK"}]}, {"name": "Number of Security Hinges (Pick Up)", "technical_name": "bx_dbl_hinge_num_sec_hinges_pickup", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"job_info_delivery_pickup_status == 'pick_up'\", \"logic\": \"and\"}]", "sequence": 238, "question_number": 171, "quantity_formula": "1", "options": [{"name": "0", "value": "0", "visibility_condition": ""}, {"name": "2 per Door - Attached", "value": "2_per_door_attached", "visibility_condition": ""}, {"name": "2 per Door - Loose with holes drilled (No rivets supplied)", "value": "2_per_door_loose_drilled", "visibility_condition": ""}, {"name": "2 per Door - Loose with NO holes drilled (No rivets supplied)", "value": "2_per_door_loose_no_holes", "visibility_condition": ""}, {"name": "3 per Door - Attached", "value": "3_per_door_attached", "visibility_condition": ""}, {"name": "3 per Door - Loose with holes drilled (No rivets supplied)", "value": "3_per_door_loose_drilled", "visibility_condition": ""}, {"name": "3 per Door - Loose with NO holes drilled (No rivets supplied)", "value": "3_per_door_loose_no_holes", "visibility_condition": ""}, {"name": "4 per Door - Attached, <PERSON><PERSON> Spaced", "value": "4_per_door_attached_even", "visibility_condition": ""}, {"name": "4 per Door - Attached, Fourth Hinge 100mm below Top Hinge", "value": "4_per_door_attached_offset", "visibility_condition": ""}, {"name": "4 per Door - Loose with holes drilled, Evenly Spaced (No rivets supplied)", "value": "4_per_door_loose_drilled_even", "visibility_condition": ""}, {"name": "4 per Door - Loose with holes drilled, Fourth Hinge 100mm below Top Hinge (No rivets supplied)", "value": "4_per_door_loose_drilled_offset", "visibility_condition": ""}, {"name": "4 per Door - Loose with NO holes drilled (No rivets supplied)", "value": "4_per_door_loose_no_holes", "visibility_condition": ""}]}, {"name": "Number of Security Hinges (Deliver/Site)", "technical_name": "bx_dbl_hinge_num_sec_hinges_deliver", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"job_info_delivery_pickup_status == 'deliver' || job_info_delivery_pickup_status == 'deliver_to_site'\", \"logic\": \"and\"}]", "sequence": 239, "question_number": 172, "quantity_formula": "1", "options": [{"name": "0", "value": "0", "visibility_condition": ""}, {"name": "2 per Door - Loose with holes drilled (No rivets supplied)", "value": "2_per_door_loose_drilled", "visibility_condition": ""}, {"name": "2 per Door - Loose with NO holes drilled (No rivets supplied)", "value": "2_per_door_loose_no_holes", "visibility_condition": ""}, {"name": "3 per Door - Loose with holes drilled (No rivets supplied)", "value": "3_per_door_loose_drilled", "visibility_condition": ""}, {"name": "3 per Door - Loose with NO holes drilled (No rivets supplied)", "value": "3_per_door_loose_no_holes", "visibility_condition": ""}, {"name": "4 per Door - Loose with holes drilled, Evenly Spaced (No rivets supplied)", "value": "4_per_door_loose_drilled_even", "visibility_condition": ""}, {"name": "4 per Door - Loose with holes drilled, Fourth Hinge 100mm below Top Hinge (No rivets supplied)", "value": "4_per_door_loose_drilled_offset", "visibility_condition": ""}, {"name": "4 per Door - Loose with NO holes drilled (No rivets supplied)", "value": "4_per_door_loose_no_holes", "visibility_condition": ""}]}, {"name": "Specific Hinge Locations? (2 Hinges, Pick Up, Double Door)", "technical_name": "bx_dbl_hinge_spec_hinge_loc_2_pickup_dd", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"job_info_delivery_pickup_status == 'pick_up' && (bx_dbl_hinge_num_sec_hinges_pickup == '2_per_door_attached' || bx_dbl_hinge_num_sec_hinges_pickup == '2_per_door_loose_drilled')\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_pickup in ['2_per_door_attached', '2_per_door_loose_drilled']\", \"logic\": \"and\"}]", "sequence": 240, "question_number": 173, "quantity_formula": "1", "options": [{"name": "Yes", "value": "yes", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Top of Door to Top of Top Hinge (mm) (2 Hinges, Pick Up, Double Door)", "technical_name": "bx_dbl_hinge_top_hinge_pos_2_pickup_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"job_info_delivery_pickup_status == 'pick_up' && (bx_dbl_hinge_num_sec_hinges_pickup == '2_per_door_attached' || bx_dbl_hinge_num_sec_hinges_pickup == '2_per_door_loose_drilled') && bx_dbl_hinge_spec_hinge_loc_2_pickup_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_pickup in ['2_per_door_attached', '2_per_door_loose_drilled']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_2_pickup_dd == 'yes'\", \"logic\": \"and\"}]", "sequence": 241, "question_number": 174, "quantity_formula": "1"}, {"name": "Bottom of Door to Bottom of Bottom Hinge (mm) (2 Hinges, Pick Up, Double Door)", "technical_name": "bx_dbl_hinge_bottom_hinge_pos_2_pickup_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"job_info_delivery_pickup_status == 'pick_up' && (bx_dbl_hinge_num_sec_hinges_pickup == '2_per_door_attached' || bx_dbl_hinge_num_sec_hinges_pickup == '2_per_door_loose_drilled') && bx_dbl_hinge_spec_hinge_loc_2_pickup_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_pickup in ['2_per_door_attached', '2_per_door_loose_drilled']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_2_pickup_dd == 'yes'\", \"logic\": \"and\"}]", "sequence": 242, "question_number": 175, "quantity_formula": "1"}, {"name": "Specific Hinge Locations? (3 Hinges, Pick Up, Double Door)", "technical_name": "bx_dbl_hinge_spec_hinge_loc_3_pickup_dd", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"job_info_delivery_pickup_status == 'pick_up' && (bx_dbl_hinge_num_sec_hinges_pickup == '3_per_door_attached' || bx_dbl_hinge_num_sec_hinges_pickup == '3_per_door_loose_drilled')\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_pickup in ['3_per_door_attached', '3_per_door_loose_drilled']\", \"logic\": \"and\"}]", "sequence": 243, "question_number": 176, "quantity_formula": "1", "options": [{"name": "Yes", "value": "yes", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Top of Door to Top of Top Hinge (mm) (3 Hinges, Pick Up, Double Door)", "technical_name": "bx_dbl_hinge_top_hinge_pos_3_pickup_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"job_info_delivery_pickup_status == 'pick_up' && (bx_dbl_hinge_num_sec_hinges_pickup == '3_per_door_attached' || bx_dbl_hinge_num_sec_hinges_pickup == '3_per_door_loose_drilled') && bx_dbl_hinge_spec_hinge_loc_3_pickup_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_pickup in ['3_per_door_attached', '3_per_door_loose_drilled']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_3_pickup_dd == 'yes'\", \"logic\": \"and\"}]", "sequence": 244, "question_number": 177, "quantity_formula": "1"}, {"name": "Bottom of Door to Centre of Centre Hinge (mm) (3 Hinges, Pick Up, Double Door)", "technical_name": "bx_dbl_hinge_center_hinge_pos_3_pickup_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"job_info_delivery_pickup_status == 'pick_up' && (bx_dbl_hinge_num_sec_hinges_pickup == '3_per_door_attached' || bx_dbl_hinge_num_sec_hinges_pickup == '3_per_door_loose_drilled') && bx_dbl_hinge_spec_hinge_loc_3_pickup_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_pickup in ['3_per_door_attached', '3_per_door_loose_drilled']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_3_pickup_dd == 'yes'\", \"logic\": \"and\"}]", "sequence": 245, "question_number": 178, "quantity_formula": "1"}, {"name": "Bottom of Door to Bottom of Bottom Hinge (mm) (3 Hinges, Pick Up, Double Door)", "technical_name": "bx_dbl_hinge_bottom_hinge_pos_3_pickup_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"job_info_delivery_pickup_status == 'pick_up' && (bx_dbl_hinge_num_sec_hinges_pickup == '3_per_door_attached' || bx_dbl_hinge_num_sec_hinges_pickup == '3_per_door_loose_drilled') && bx_dbl_hinge_spec_hinge_loc_3_pickup_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_pickup in ['3_per_door_attached', '3_per_door_loose_drilled']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_3_pickup_dd == 'yes'\", \"logic\": \"and\"}]", "sequence": 246, "question_number": 179, "quantity_formula": "1"}, {"name": "Specific Hinge Locations? (4 Hinges, Pick Up, Double Door)", "technical_name": "bx_dbl_hinge_spec_hinge_loc_4_pickup_dd", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"job_info_delivery_pickup_status == 'pick_up' && (bx_dbl_hinge_num_sec_hinges_pickup == '4_per_door_attached_even' || bx_dbl_hinge_num_sec_hinges_pickup == '4_per_door_attached_offset' || bx_dbl_hinge_num_sec_hinges_pickup == '4_per_door_loose_drilled_even' || bx_dbl_hinge_num_sec_hinges_pickup == '4_per_door_loose_drilled_offset')\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_pickup in ['4_per_door_attached_even', ''4_per_door_attached_offset', ''4_per_door_loose_drilled_even', '4_per_door_loose_drilled_offset']\", \"logic\": \"and\"}]", "sequence": 247, "question_number": 180, "quantity_formula": "1", "options": [{"name": "Yes", "value": "yes", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Top of Door to Top of Top Hinge (mm) (4 Hinges, Pick Up, Double Door)", "technical_name": "bx_dbl_hinge_top_hinge_pos_4_pickup_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"job_info_delivery_pickup_status == 'pick_up' && (bx_dbl_hinge_num_sec_hinges_pickup == '4_per_door_attached_even' || bx_dbl_hinge_num_sec_hinges_pickup == '4_per_door_attached_offset' || bx_dbl_hinge_num_sec_hinges_pickup == '4_per_door_loose_drilled_even' || bx_dbl_hinge_num_sec_hinges_pickup == '4_per_door_loose_drilled_offset') && bx_dbl_hinge_spec_hinge_loc_4_pickup_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_pickup in ['4_per_door_attached_even', ''4_per_door_attached_offset', ''4_per_door_loose_drilled_even', '4_per_door_loose_drilled_offset']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_4_pickup_dd == 'yes'\", \"logic\": \"and\"}]", "sequence": 248, "question_number": 181, "quantity_formula": "1"}, {"name": "Top of Door to Top of Second Hinge (mm) (4 Hinges, Pick Up, Double Door)", "technical_name": "bx_dbl_hinge_second_hinge_pos_4_pickup_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"job_info_delivery_pickup_status == 'pick_up' && (bx_dbl_hinge_num_sec_hinges_pickup == '4_per_door_attached_even' || bx_dbl_hinge_num_sec_hinges_pickup == '4_per_door_attached_offset' || bx_dbl_hinge_num_sec_hinges_pickup == '4_per_door_loose_drilled_even' || bx_dbl_hinge_num_sec_hinges_pickup == '4_per_door_loose_drilled_offset') && bx_dbl_hinge_spec_hinge_loc_4_pickup_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_pickup in ['4_per_door_attached_even', ''4_per_door_attached_offset', ''4_per_door_loose_drilled_even', '4_per_door_loose_drilled_offset']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_4_pickup_dd == 'yes'\", \"logic\": \"and\"}]", "sequence": 249, "question_number": 182, "quantity_formula": "1"}, {"name": "Bottom of Door to Bottom of Third Hinge (mm) (4 Hinges, Pick Up, Double Door)", "technical_name": "bx_dbl_hinge_third_hinge_pos_4_pickup_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"job_info_delivery_pickup_status == 'pick_up' && (bx_dbl_hinge_num_sec_hinges_pickup == '4_per_door_attached_even' || bx_dbl_hinge_num_sec_hinges_pickup == '4_per_door_attached_offset' || xc_dbl_hinge_num_sec_hinges_pickup == '4_per_door_loose_drilled_even' || xc_dbl_hinge_num_sec_hinges_pickup == '4_per_door_loose_drilled_offset') && xc_dbl_hinge_spec_hinge_loc_4_pickup_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_pickup in ['4_per_door_attached_even', ''4_per_door_attached_offset', ''4_per_door_loose_drilled_even', '4_per_door_loose_drilled_offset']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_4_pickup_dd == 'yes'\", \"logic\": \"and\"}]", "sequence": 250, "question_number": 183, "quantity_formula": "1"}, {"name": "Bottom of Door to Bottom of Bottom Hinge (mm) (4 Hinges, Pick Up, Double Door)", "technical_name": "bx_dbl_hinge_bottom_hinge_pos_4_pickup_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"job_info_delivery_pickup_status == 'pick_up' && (bx_dbl_hinge_num_sec_hinges_pickup == '4_per_door_attached_even' || bx_dbl_hinge_num_sec_hinges_pickup == '4_per_door_attached_offset' || xc_dbl_hinge_num_sec_hinges_pickup == '4_per_door_loose_drilled_even' || xc_dbl_hinge_num_sec_hinges_pickup == '4_per_door_loose_drilled_offset') && xc_dbl_hinge_spec_hinge_loc_4_pickup_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_pickup in ['4_per_door_attached_even', ''4_per_door_attached_offset', ''4_per_door_loose_drilled_even', '4_per_door_loose_drilled_offset']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_4_pickup_dd == 'yes'\", \"logic\": \"and\"}]", "sequence": 251, "question_number": 184, "quantity_formula": "1"}, {"name": "Specific Hinge Locations? (2 Hinges, Deliver/Site, Double Door)", "technical_name": "bx_dbl_hinge_spec_hinge_loc_2_deliver_dd", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"(job_info_delivery_pickup_status == 'deliver' || job_info_delivery_pickup_status == 'deliver_to_site') && xc_dbl_hinge_num_sec_hinges_deliver == '2_per_door_loose_drilled'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_deliver in ['2_per_door_loose_drilled']\", \"logic\": \"and\"}]", "sequence": 252, "question_number": 185, "quantity_formula": "1", "options": [{"name": "Yes", "value": "yes", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Top of Door to Top of Top Hinge (mm) (2 Hinges, Deliver/Site, Double Door)", "technical_name": "bx_dbl_hinge_top_hinge_pos_2_deliver_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"(job_info_delivery_pickup_status == 'deliver' || job_info_delivery_pickup_status == 'deliver_to_site') && xc_dbl_hinge_num_sec_hinges_deliver == '2_per_door_loose_drilled' && xc_dbl_hinge_spec_hinge_loc_2_deliver_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_deliver in ['2_per_door_loose_drilled']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_2_deliver_dd == 'yes'\", \"logic\": \"and\"}]", "sequence": 253, "question_number": 186, "quantity_formula": "1"}, {"name": "Bottom of Door to Bottom of Bottom Hinge (mm) (2 Hinges, Deliver/Site, Double Door)", "technical_name": "bx_dbl_hinge_bottom_hinge_pos_2_deliver_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"(job_info_delivery_pickup_status == 'deliver' || job_info_delivery_pickup_status == 'deliver_to_site') && xc_dbl_hinge_num_sec_hinges_deliver == '2_per_door_loose_drilled' && xc_dbl_hinge_spec_hinge_loc_2_deliver_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_deliver in ['2_per_door_loose_drilled']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_2_deliver_dd == 'yes'\", \"logic\": \"and\"}]", "sequence": 254, "question_number": 187, "quantity_formula": "1"}, {"name": "Specific Hinge Locations? (3 Hinges, Deliver/Site, Double Door)", "technical_name": "bx_dbl_hinge_spec_hinge_loc_3_deliver_dd", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"(job_info_delivery_pickup_status == 'deliver' || job_info_delivery_pickup_status == 'deliver_to_site') && xc_dbl_hinge_num_sec_hinges_deliver == '3_per_door_loose_drilled'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_deliver in ['3_per_door_loose_drilled']\", \"logic\": \"and\"}]", "sequence": 255, "question_number": 188, "quantity_formula": "1", "options": [{"name": "Yes", "value": "yes", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Top of Door to Top of Top Hinge (mm) (3 Hinges, Deliver/Site, Double Door)", "technical_name": "bx_dbl_hinge_top_hinge_pos_3_deliver_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"(job_info_delivery_pickup_status == 'deliver' || job_info_delivery_pickup_status == 'deliver_to_site') && xc_dbl_hinge_num_sec_hinges_deliver == '3_per_door_loose_drilled' && xc_dbl_hinge_spec_hinge_loc_3_deliver_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_3_deliver_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_deliver in ['3_per_door_loose_drilled']\", \"logic\": \"and\"}]", "sequence": 256, "question_number": 189, "quantity_formula": "1"}, {"name": "Bottom of Door to Centre of Centre Hinge (mm) (3 Hinges, Deliver/Site, Double Door)", "technical_name": "bx_dbl_hinge_center_hinge_pos_3_deliver_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"(job_info_delivery_pickup_status == 'deliver' || job_info_delivery_pickup_status == 'deliver_to_site') && xc_dbl_hinge_num_sec_hinges_deliver == '3_per_door_loose_drilled' && xc_dbl_hinge_spec_hinge_loc_3_deliver_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_deliver in ['3_per_door_loose_drilled']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_3_deliver_dd == 'yes'\", \"logic\": \"and\"}]", "sequence": 257, "question_number": 190, "quantity_formula": "1"}, {"name": "Bottom of Door to Bottom of Bottom Hinge (mm) (3 Hinges, Deliver/Site, Double Door)", "technical_name": "bx_dbl_hinge_bottom_hinge_pos_3_deliver_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"(job_info_delivery_pickup_status == 'deliver' || job_info_delivery_pickup_status == 'deliver_to_site') && xc_dbl_hinge_num_sec_hinges_deliver == '3_per_door_loose_drilled' && xc_dbl_hinge_spec_hinge_loc_3_deliver_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_3_deliver_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_deliver in ['3_per_door_loose_drilled']\", \"logic\": \"and\"}]", "sequence": 258, "question_number": 191, "quantity_formula": "1"}, {"name": "Specific Hinge Locations? (4 Hinges, Deliver/Site, Double Door)", "technical_name": "bx_dbl_hinge_spec_hinge_loc_4_deliver_dd", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"(job_info_delivery_pickup_status == 'deliver' || job_info_delivery_pickup_status == 'deliver_to_site') && (xc_dbl_hinge_num_sec_hinges_deliver == '4_per_door_loose_drilled_even' || xc_dbl_hinge_num_sec_hinges_deliver == '4_per_door_loose_drilled_offset')\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_deliver in ['4_per_door_loose_drilled_even', '4_per_door_loose_drilled_offset']\", \"logic\": \"and\"}]", "sequence": 259, "question_number": 192, "quantity_formula": "1", "options": [{"name": "Yes", "value": "yes", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Top of Door to Top of Top Hinge (mm) (4 Hinges, Deliver/Site, Double Door)", "technical_name": "bx_dbl_hinge_top_hinge_pos_4_deliver_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"(job_info_delivery_pickup_status == 'deliver' || job_info_delivery_pickup_status == 'deliver_to_site') && (xc_dbl_hinge_num_sec_hinges_deliver == '4_per_door_loose_drilled_even' || xc_dbl_hinge_num_sec_hinges_deliver == '4_per_door_loose_drilled_offset') && xc_dbl_hinge_spec_hinge_loc_4_deliver_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_deliver in ['4_per_door_loose_drilled_even', '4_per_door_loose_drilled_offset']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_4_deliver_dd== 'yes'\", \"logic\": \"and\"}]", "sequence": 260, "question_number": 193, "quantity_formula": "1"}, {"name": "Top of Door to Top of Second Hinge (mm) (4 Hinges, Deliver/Site, Double Door)", "technical_name": "bx_dbl_hinge_second_hinge_pos_4_deliver_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"(job_info_delivery_pickup_status == 'deliver' || job_info_delivery_pickup_status == 'deliver_to_site') && (xc_dbl_hinge_num_sec_hinges_deliver == '4_per_door_loose_drilled_even' || xc_dbl_hinge_num_sec_hinges_deliver == '4_per_door_loose_drilled_offset') && xc_dbl_hinge_spec_hinge_loc_4_deliver_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_4_deliver_dd== 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_deliver in ['4_per_door_loose_drilled_even', '4_per_door_loose_drilled_offset']\", \"logic\": \"and\"}]", "sequence": 261, "question_number": 194, "quantity_formula": "1"}, {"name": "Bottom of Door to Bottom of Third Hinge (mm) (4 Hinges, Deliver/Site, Double Door)", "technical_name": "bx_dbl_hinge_third_hinge_pos_4_deliver_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"(job_info_delivery_pickup_status == 'deliver' || job_info_delivery_pickup_status == 'deliver_to_site') && (xc_dbl_hinge_num_sec_hinges_deliver == '4_per_door_loose_drilled_even' || xc_dbl_hinge_num_sec_hinges_deliver == '4_per_door_loose_drilled_offset') && xc_dbl_hinge_spec_hinge_loc_4_deliver_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_deliver in ['4_per_door_loose_drilled_even', '4_per_door_loose_drilled_offset']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_4_deliver_dd== 'yes'\", \"logic\": \"and\"}]", "sequence": 262, "question_number": 195, "quantity_formula": "1"}, {"name": "Bottom of Door to Bottom of Bottom Hinge (mm) (4 Hinges, Deliver/Site, Double Door)", "technical_name": "bx_dbl_hinge_bottom_hinge_pos_4_deliver_dd", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"(job_info_delivery_pickup_status == 'deliver' || job_info_delivery_pickup_status == 'deliver_to_site') && (xc_dbl_hinge_num_sec_hinges_deliver == '4_per_door_loose_drilled_even' || xc_dbl_hinge_num_sec_hinges_deliver == '4_per_door_loose_drilled_offset') && xc_dbl_hinge_spec_hinge_loc_4_deliver_dd == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_spec_hinge_loc_4_deliver_dd== 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_deliver in ['4_per_door_loose_drilled_even', '4_per_door_loose_drilled_offset']\", \"logic\": \"and\"}]", "sequence": 263, "question_number": 196, "quantity_formula": "1"}, {"name": "Security Hinge Type", "technical_name": "bx_dbl_hinge_sec_hinge_type", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"(job_info_delivery_pickup_status == 'pick_up' && bx_dbl_hinge_num_sec_hinges_pickup != '0') || ((job_info_delivery_pickup_status == 'deliver' || job_info_delivery_pickup_status == 'deliver_to_site') && bx_dbl_hinge_num_sec_hinges_deliver != '0')\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_pickup != '0'\", \"logic\": \"or\"}, {\"condition\": \"bx_dbl_hinge_num_sec_hinges_deliver != '0'\", \"logic\": \"or\"}]", "sequence": 264, "question_number": 197, "quantity_formula": "1", "options": [{"name": "Security Hinge", "value": "security_hinge", "visibility_condition": ""}, {"name": "Security Hinge with Safety Prong", "value": "security_hinge_safety_prong", "visibility_condition": ""}, {"name": "Security Hinge with Step", "value": "security_hinge_step", "visibility_condition": ""}]}, {"name": "Security Hinge Colour (Standard Hinge)", "technical_name": "bx_dbl_hinge_sec_hinge_colour_std", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_sec_hinge_type == 'security_hinge'\", \"logic\": \"and\"}]", "sequence": 265, "question_number": 198, "quantity_formula": "1", "options": [{"name": "Black", "value": "black", "visibility_condition": "", "component_product": "Whitco Security Hinges", "component_product_code": "WTSH-BLK"}, {"name": "<PERSON>", "value": "pearl_white", "visibility_condition": "", "component_product": "Whitco Security Hinges", "component_product_code": "WTSH-PW"}, {"name": "Silver Paint", "value": "silver_paint", "visibility_condition": "", "component_product": "Whitco Security Hinges", "component_product_code": "WTSH-SLV"}, {"name": "STAINLESS STEEL", "value": "stainless_steel", "visibility_condition": "", "component_product": "Whitco Security Hinges", "component_product_code": "WTSH-SS"}]}, {"name": "Security Hinge Colour (Prong/Step Hinge)", "technical_name": "bx_dbl_hinge_sec_hinge_colour_prong_step", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_sec_hinge_type == 'security_hinge_safety_prong' || bx_dbl_hinge_sec_hinge_type == 'security_hinge_step'\", \"logic\": \"and\"}]", "sequence": 266, "question_number": 199, "quantity_formula": "1", "options": [{"name": "Black", "value": "black", "visibility_condition": "", "component_product": "Whitco Security Hinges With Step", "component_product_code": "WTSHS-BLK"}]}, {"name": "Security Hinge Packers Quantity", "technical_name": "bx_dbl_hinge_sec_hinge_packers_qty", "field_type": "number", "required": true, "default_value": "0", "help_text": "", "visibility_condition": "", "sequence": 267, "question_number": 200, "quantity_formula": "1"}, {"name": "Closer", "technical_name": "bx_dbl_hinge_closer", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "", "sequence": 268, "question_number": 201, "quantity_formula": "1", "options": [{"name": "Austral Hinged Closer 18kg", "value": "austral_closer_18kg", "visibility_condition": ""}, {"name": "<PERSON><PERSON><PERSON> Hinged Closer 18kg", "value": "whitco_closer_18kg", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Closer Colour (Austral)", "technical_name": "bx_dbl_hinge_closer_colour_austral", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_closer == 'austral_closer_18kg'\", \"logic\": \"and\"}]", "sequence": 269, "question_number": 202, "quantity_formula": "1", "options": [{"name": "Black", "value": "black", "visibility_condition": "", "component_product": "Austral Hinged Closer", "component_product_code": "AUCL-BLK"}, {"name": "<PERSON>", "value": "pearl_white", "visibility_condition": "", "component_product": "Austral Hinged Closer", "component_product_code": "AUCL-PW"}]}, {"name": "Closer Colour (Whitco)", "technical_name": "bx_dbl_hinge_closer_colour_whitco", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_closer == 'whitco_closer_18kg'\", \"logic\": \"and\"}]", "sequence": 270, "question_number": 203, "quantity_formula": "1", "options": [{"name": "Black", "value": "black", "visibility_condition": "", "component_product": "<PERSON><PERSON><PERSON> Hinged Closer", "component_product_code": "WTCL-BLK"}, {"name": "<PERSON>", "value": "pearl_white", "visibility_condition": "", "component_product": "<PERSON><PERSON><PERSON> Hinged Closer", "component_product_code": "WTCL-PW"}, {"name": "Silver Paint", "value": "silver_paint", "visibility_condition": "", "component_product": "<PERSON><PERSON><PERSON> Hinged Closer", "component_product_code": "WTCL-SLV"}]}, {"name": "Pet Door", "technical_name": "bx_dbl_hinge_pet_door", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "", "sequence": 271, "question_number": 204, "quantity_formula": "1", "options": [{"name": "Small Pet Door", "value": "small_pet_door", "visibility_condition": ""}, {"name": "Medium Pet Door", "value": "medium_pet_door", "visibility_condition": ""}, {"name": "Large Pet Door", "value": "large_pet_door", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Pet Door Colour", "technical_name": "bx_dbl_hinge_pet_door_colour", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_pet_door != 'no'\", \"logic\": \"and\"}]", "sequence": 272, "question_number": 205, "quantity_formula": "1", "options": [{"name": "Small Black", "value": "black", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_pet_door in ['small_pet_door']\", \"logic\": \"and\"}]", "component_product": "Petway Small Pet Door", "component_product_code": "PDS-BLK"}, {"name": "Small White", "value": "white", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_pet_door in ['small_pet_door']\", \"logic\": \"and\"}]", "component_product": "Petway Small Pet Door", "component_product_code": "PDS-PW"}, {"name": "Medium Black", "value": "Medium Black", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_pet_door in ['medium_pet_door']\", \"logic\": \"and\"}]", "component_product": "Petway Medium Pet Door", "component_product_code": "PDM-BLK"}, {"name": "Medium White", "value": "Medium White", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_pet_door in ['medium_pet_door']\", \"logic\": \"and\"}]", "component_product": "Petway Medium Pet Door", "component_product_code": "PDM-PW"}, {"name": "Large Black", "value": "Large Black", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_pet_door in ['large_pet_door']\", \"logic\": \"and\"}]", "component_product": "Petway Large Pet Door", "component_product_code": "PDL-BLK"}, {"name": "Large White", "value": "Large White", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_pet_door in ['large_pet_door']\", \"logic\": \"and\"}]", "component_product": "Petway Large Pet Door", "component_product_code": "PDL-PW"}]}, {"name": "Pet Door Location", "technical_name": "bx_dbl_hinge_pet_door_location", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_pet_door != 'no'\", \"logic\": \"and\"}]", "sequence": 273, "question_number": 206, "quantity_formula": "1", "options": [{"name": "Lock Door - Lock Side", "value": "lock_door_lock_side", "visibility_condition": ""}, {"name": "Lock Door - Centre", "value": "lock_door_centre", "visibility_condition": ""}, {"name": "Lock Door - Hinge Side", "value": "lock_door_hinge_side", "visibility_condition": ""}, {"name": "Non-Lock Door - Lock Side", "value": "non_lock_door_lock_side", "visibility_condition": ""}, {"name": "Non-Lock Door - Centre", "value": "non_lock_door_centre", "visibility_condition": ""}, {"name": "Non-Lock Door - Hinge Side", "value": "non_lock_door_hinge_side", "visibility_condition": ""}]}, {"name": "Pet Door Flexible Flap", "technical_name": "bx_dbl_hinge_pet_door_flexible_flap", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_pet_door != 'no'\", \"logic\": \"and\"}]", "sequence": 274, "question_number": 207, "quantity_formula": "1", "options": [{"name": "Yes Small", "value": "yes", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_pet_door in ['small_pet_door']\", \"logic\": \"and\"}]", "component_product": "Petway Small Pet Door Flexible Flap", "component_product_code": "PDSFL-CLR"}, {"name": "Yes Medium", "value": "Yes Medium", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_pet_door in ['medium_pet_door']\", \"logic\": \"and\"}]", "component_product": "Petway Medium Pet Door Flexible Flap", "component_product_code": "PDMFL-CLR"}, {"name": "Yes Large", "value": "Yes Large", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_pet_door in ['large_pet_door']\", \"logic\": \"and\"}]", "component_product": "Petway Large Pet Door Flexible Flap", "component_product_code": "PDLFL-CLR"}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Pet Door Surround Infill", "technical_name": "bx_dbl_hinge_pet_door_surround_infill", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_pet_door != 'no'\", \"logic\": \"and\"}]", "sequence": 275, "question_number": 208, "quantity_formula": "1", "options": [{"name": "Yes Small", "value": "yes", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_pet_door in ['small_pet_door']\", \"logic\": \"and\"}]", "component_product": "Small Pet Door Surround Infill", "component_product_code": "PDSSI-BLK"}, {"name": "Yes Medium", "value": "Yes Medium", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_pet_door in ['medium_pet_door']\", \"logic\": \"and\"}]", "component_product": "Medium Pet Door Surround Infill", "component_product_code": "PDMSI-BLK"}, {"name": "Yes Large", "value": "Yes Large", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_pet_door in ['large_pet_door']\", \"logic\": \"and\"}]", "component_product": "Large Pet Door Surround Infill", "component_product_code": "PDLSI-BLK"}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Centre Striker Plate Packer", "technical_name": "bx_dbl_hinge_center_striker_packer", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "", "sequence": 276, "question_number": 209, "quantity_formula": "1", "options": [{"name": "Yes", "value": "yes", "visibility_condition": "", "component_product": "Whitco Mk2 / Commandex Centre Strike Packer", "component_product_code": "WMK2CSP"}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Auxiliary Striker Plate Packers", "technical_name": "bx_dbl_hinge_aux_striker_packers", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_lock_type_is_multipoint\", \"logic\": \"and\"}]", "sequence": 277, "question_number": 210, "quantity_formula": "1", "options": [{"name": "Yes - 2", "value": "yes_2", "visibility_condition": "", "component_product": "Sliding Striker Plate Packer 2mm", "component_product_code": "SSP2-BLK"}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Bugstrip Size", "technical_name": "bx_dbl_hinge_bugstrip_size", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "", "sequence": 278, "question_number": 211, "quantity_formula": "1", "options": [{"name": "16mm with 16mm Pile Seal", "value": "16mm_16mm_pile", "visibility_condition": ""}, {"name": "16mm with 28mm Pile Seal", "value": "16mm_28mm_pile", "visibility_condition": ""}, {"name": "25mm with 16mm Pile Seal", "value": "25mm_16mm_pile", "visibility_condition": ""}, {"name": "25mm with 28mm Pile Seal", "value": "25mm_28mm_pile", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Left Bugstrip Length (mm)", "technical_name": "bx_dbl_hinge_left_bugstrip_length_mm", "field_type": "number", "required": true, "default_value": "", "help_text": "Maximum Cut Length is 4300mm. Order Full Length in Extrusion Category.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_bugstrip_size != 'no'\", \"logic\": \"and\"}]", "sequence": 279, "question_number": 212, "quantity_formula": "1"}, {"name": "Right Bugstrip Length (mm)", "technical_name": "bx_dbl_hinge_right_bugstrip_length_mm", "field_type": "number", "required": true, "default_value": "", "help_text": "Maximum Cut Length is 4300mm. Order Full Length in Extrusion Category.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_bugstrip_size != 'no'\", \"logic\": \"and\"}]", "sequence": 280, "question_number": 213, "quantity_formula": "1"}, {"name": "Stop Bead Colour", "technical_name": "bx_dbl_hinge_stop_bead_colour", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_jamb_adaptor_not_required_overall\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_jamb_reveal_gt_20mm not in ['no_lt_20mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_screen_clears_handle not in ['no_lt_23mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_new_handle_clears_existing not in ['no_lt_56mm']\", \"logic\": \"and\"}]", "sequence": 281, "question_number": 214, "quantity_formula": "1", "options": [{"name": "Black", "value": "black", "visibility_condition": "", "component_product": "Stopbead 16 X 13mm", "component_product_code": "STPB16X13-BLK"}, {"name": "<PERSON>", "value": "brown", "visibility_condition": "", "component_product": "Stopbead 16 X 13mm", "component_product_code": "STPB16X13-BRN"}, {"name": "<PERSON><PERSON><PERSON>", "value": "primrose", "visibility_condition": "", "component_product": "Stopbead 16 X 13mm", "component_product_code": "STPB16X13-PR"}, {"name": "Stone Beige", "value": "stone_beige", "visibility_condition": "", "component_product": "Stopbead 16 X 13mm", "component_product_code": "STPB16X13-STB"}, {"name": "White", "value": "white", "visibility_condition": "", "component_product": "Stopbead 16 X 13mm", "component_product_code": "STPB16X13-PW"}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Stop Bead LEFT Length (mm)", "technical_name": "bx_dbl_hinge_stop_bead_left_length_mm", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_jamb_adaptor_not_required_overall && bx_dbl_hinge_stop_bead_colour != 'no'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_jamb_reveal_gt_20mm not in ['no_lt_20mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_screen_clears_handle not in ['no_lt_23mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_new_handle_clears_existing not in ['no_lt_56mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_stop_bead_colour != 'no'\", \"logic\": \"and\"}]", "sequence": 282, "question_number": 215, "quantity_formula": "1"}, {"name": "Stop Bead TOP Length (mm)", "technical_name": "bx_dbl_hinge_stop_bead_top_length_mm", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_jamb_adaptor_not_required_overall && bx_dbl_hinge_stop_bead_colour != 'no'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_stop_bead_colour != 'no'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_jamb_reveal_gt_20mm not in ['no_lt_20mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_screen_clears_handle not in ['no_lt_23mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_new_handle_clears_existing not in ['no_lt_56mm']\", \"logic\": \"and\"}]", "sequence": 283, "question_number": 216, "quantity_formula": "1"}, {"name": "Stop Bead RIGHT Length (mm)", "technical_name": "bx_dbl_hinge_stop_bead_right_length_mm", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_jamb_adaptor_not_required_overall && bx_dbl_hinge_stop_bead_colour != 'no'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_new_handle_clears_existing not in ['no_lt_56mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_screen_clears_handle not in ['no_lt_23mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_jamb_reveal_gt_20mm not in ['no_lt_20mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_stop_bead_colour != 'no'\", \"logic\": \"and\"}]", "sequence": 284, "question_number": 217, "quantity_formula": "1"}, {"name": "Adhesive Mohair", "technical_name": "bx_dbl_hinge_adhesive_mohair", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_jamb_adaptor_not_required_overall\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_jamb_reveal_gt_20mm not in ['no_lt_20mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_screen_clears_handle not in ['no_lt_23mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_new_handle_clears_existing not in ['no_lt_56mm']\", \"logic\": \"and\"}]", "sequence": 285, "question_number": 218, "quantity_formula": "1", "options": [{"name": "Yes", "value": "yes", "visibility_condition": "", "component_product": "Adhesive Mohair (Ultrafab Fin Clr-None Den-Vh Bk Op-3M Yrn Clr-Black)", "component_product_code": "AMOH-BLK"}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Adhesive Mohair Length (mm)", "technical_name": "bx_dbl_hinge_adhesive_mohair_length_mm", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_jamb_adaptor_not_required_overall && bx_dbl_hinge_adhesive_mohair == 'yes'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_new_handle_clears_existing not in ['no_lt_56mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_screen_clears_handle not in ['no_lt_23mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_jamb_reveal_gt_20mm not in ['no_lt_20mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_adhesive_mohair != 'no'\", \"logic\": \"and\"}]", "sequence": 286, "question_number": 219, "quantity_formula": "1"}, {"name": "Jamb Adaptor Type (Case 1: No Stop Bead/Mohair)", "technical_name": "bx_dbl_hinge_jamb_adaptor_type_case1", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_jamb_adaptor_required_pre_msr_chk_any == false && bx_dbl_hinge_stop_bead_colour == 'no' && bx_dbl_hinge_adhesive_mohair == 'no'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_jamb_reveal_gt_20mm not in ['no_lt_20mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_screen_clears_handle not in ['no_lt_23mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_new_handle_clears_existing not in ['no_lt_56mm']\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_adhesive_mohair == 'no'\", \"logic\": \"and\"}, {\"condition\": \"bx_dbl_hinge_stop_bead_colour == 'no'\", \"logic\": \"and\"}]", "sequence": 287, "question_number": 220, "quantity_formula": "1", "options": [{"name": "Long Leg Jamb Adaptor w/ <PERSON><PERSON><PERSON> (includes 2 corner stakes)", "value": "long_leg_adaptor", "visibility_condition": ""}, {"name": "Short Leg Jamb Adaptor w/ <PERSON><PERSON><PERSON> (includes 2 corner stakes)", "value": "short_leg_adaptor", "visibility_condition": ""}, {"name": "No", "value": "no", "visibility_condition": ""}]}, {"name": "Jamb Adaptor Type (Case 2: Short Leg Required)", "technical_name": "bx_dbl_hinge_jamb_adaptor_type_case2", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_jamb_reveal_gt_20mm == 'no_lt_20mm'\", \"logic\": \"and\"}]", "sequence": 288, "question_number": 221, "quantity_formula": "1", "options": [{"name": "Short Leg Jamb Adaptor w/ <PERSON><PERSON><PERSON> (includes 2 corner stakes)", "value": "short_leg_adaptor", "visibility_condition": ""}]}, {"name": "Jamb Adaptor Type (Case 3: General Jamb Adaptor Required)", "technical_name": "bx_dbl_hinge_jamb_adaptor_type_case3", "field_type": "selection", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_screen_clears_handle == 'no_lt_23mm'\", \"logic\": \"or\"}, {\"condition\": \"bx_dbl_hinge_new_handle_clears_existing == 'no_lt_56mm'\", \"logic\": \"or\"}]", "sequence": 289, "question_number": 222, "quantity_formula": "1", "options": [{"name": "Long Leg Jamb Adaptor w/ <PERSON><PERSON><PERSON> (includes 2 corner stakes)", "value": "long_leg_adaptor", "visibility_condition": ""}, {"name": "Short Leg Jamb Adaptor w/ <PERSON><PERSON><PERSON> (includes 2 corner stakes)", "value": "short_leg_adaptor", "visibility_condition": ""}]}, {"name": "Opening LEFT Height (mm) - SWING VIEW (Jamb Adaptor)", "technical_name": "bx_dbl_hinge_jamb_opening_left_height_mm", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_jamb_adaptor_type_case1 != 'no' || bx_dbl_hinge_jamb_adaptor_type_case2 == 'short_leg_adaptor' || bx_dbl_hinge_jamb_adaptor_type_case3 != null\", \"logic\": \"and\"}]", "sequence": 290, "question_number": 223, "quantity_formula": "1"}, {"name": "Height Addition (mm) (Jamb Adaptor)", "technical_name": "bx_dbl_hinge_jamb_height_addition_mm", "field_type": "number", "required": true, "default_value": "20", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_jamb_adaptor_type_case1 != 'no' || bx_dbl_hinge_jamb_adaptor_type_case2 == 'short_leg_adaptor' || bx_dbl_hinge_jamb_adaptor_type_case3 != null\", \"logic\": \"and\"}]", "sequence": 291, "question_number": 224, "quantity_formula": "1"}, {"name": "Jamb Adaptor LEFT Length (mm)", "technical_name": "bx_dbl_hinge_jamb_adaptor_left_length_mm", "field_type": "number", "required": true, "default_value": "", "help_text": "Maximum Cut Length is 5300mm. Order Full Length in Components Cut to Size Category.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_jamb_adaptor_type_case1 != 'no' || bx_dbl_hinge_jamb_adaptor_type_case2 == 'short_leg_adaptor' || bx_dbl_hinge_jamb_adaptor_type_case3 != null\", \"logic\": \"and\"}]", "sequence": 292, "question_number": 225, "quantity_formula": "1"}, {"name": "Opening TOP Width (mm) (Jamb Adaptor)", "technical_name": "bx_dbl_hinge_jamb_opening_top_width_mm", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_jamb_adaptor_type_case1 != 'no' || bx_dbl_hinge_jamb_adaptor_type_case2 == 'short_leg_adaptor' || bx_dbl_hinge_jamb_adaptor_type_case3 != null\", \"logic\": \"and\"}]", "sequence": 293, "question_number": 226, "quantity_formula": "1"}, {"name": "Width Addition (mm) (Jamb Adaptor)", "technical_name": "bx_dbl_hinge_jamb_width_addition_mm", "field_type": "number", "required": true, "default_value": "40", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_jamb_adaptor_type_case1 != 'no' || bx_dbl_hinge_jamb_adaptor_type_case2 == 'short_leg_adaptor' || bx_dbl_hinge_jamb_adaptor_type_case3 != null\", \"logic\": \"and\"}]", "sequence": 294, "question_number": 227, "quantity_formula": "1"}, {"name": "Jamb Adaptor TOP Length (mm)", "technical_name": "bx_dbl_hinge_jamb_adaptor_top_length_mm", "field_type": "number", "required": true, "default_value": "", "help_text": "Maximum Cut Length is 5300mm. Order Full Length in Components Cut to Size Category.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_jamb_adaptor_type_case1 != 'no' || bx_dbl_hinge_jamb_adaptor_type_case2 == 'short_leg_adaptor' || bx_dbl_hinge_jamb_adaptor_type_case3 != null\", \"logic\": \"and\"}]", "sequence": 295, "question_number": 228, "quantity_formula": "1"}, {"name": "Opening RIGHT Height (mm) - SWING VIEW (Jamb Adaptor)", "technical_name": "bx_dbl_hinge_jamb_opening_right_height_mm", "field_type": "number", "required": true, "default_value": "", "help_text": "", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_jamb_adaptor_type_case1 != 'no' || bx_dbl_hinge_jamb_adaptor_type_case2 == 'short_leg_adaptor' || bx_dbl_hinge_jamb_adaptor_type_case3 != null\", \"logic\": \"and\"}]", "sequence": 296, "question_number": 229, "quantity_formula": "1"}, {"name": "Jamb Adaptor RIGHT Length (mm)", "technical_name": "bx_dbl_hinge_jamb_adaptor_right_length_mm", "field_type": "number", "required": true, "default_value": "", "help_text": "Maximum Cut Length is 5300mm. Order Full Length in Components Cut to Size Category.", "visibility_condition": "__JSON__[{\"condition\": \"bx_dbl_hinge_jamb_adaptor_type_case1 != 'no' || bx_dbl_hinge_jamb_adaptor_type_case2 == 'short_leg_adaptor' || bx_dbl_hinge_jamb_adaptor_type_case3 != null\", \"logic\": \"and\"}]", "sequence": 297, "question_number": 230, "quantity_formula": "1"}]}], "component_mappings": [{"component_product": "Commandex Door Frame", "component_product_code": "CXDF-BLK", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "black_custom_matt_gn248a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-CA", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "clear_anodise_15mm"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-LBZ", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "light_bronze_anodise_15mm"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-MON", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "monument_matt_gl229a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PW", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "pearl_white_gloss_ga078a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PR", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "primrose_gloss_gd037a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-STB", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "stone_beige_matt_gd247a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-SFM", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "surfmist_matt_ga236a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-US", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "ultra_silver_gloss_gy070a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-WB", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "white_birch_gloss_ga057a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-WG", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "woodland_grey_matt_gl205a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-MF", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "mill_finish"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-ABZ", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "anodic_bronze_satin_gy114a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-ADG", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "anodic_dark_grey_matt_gl213a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-AN", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "anodic_natural_matt_gy235a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-AOW", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "anodic_off_white_matt_gd227a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-ASG", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "anodic_silver_grey_matt_gl237a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-APO", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "apo_grey_satin_gl107a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-CHL", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "charcoal_satin_gl180a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-DPO", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "deep_ocean_matt_gj203a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-DOE", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "doeskin_satin_gd188a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-DUN", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "dune_matt_gl252a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-HMB", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "hamersley_brown_satin_gm100a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-HWG", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "hawthorn_green_gloss_gk030a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-IRS", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "ironstone_matt_gl236a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-JSP", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "jasper_satin_gt114a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PBK", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "paperbark_satin_gu114a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-POT", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "pottery_satin_gm175a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PSK", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "precious_silver_kinetic_pearl_satin_971_7043k"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-SHG", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "shale_grey_satin_gp184a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-TXB", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "textura_black_gn305a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-WRC", "quantity_formula": "1", "field_name": "Frame Colour", "option_value": "western_red_cedar_mz306a4489v5"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "admiralty_gloss_6131g"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "almond_ivory_gloss_gd016a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "anodic_champagne_matt_gy276a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "anodic_clear_matt_gy221a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "asteroid_peral_matt_gy270a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "aubergine_satin_84736"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "azure_grey_satin_36603"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "barley_gloss_gd007a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "barrister_white_satin_84672"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "basalt_matt_gp208a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "berry_grey_gloss_7262g"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "black_onyx_gloss_gy042a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "black_satin_gn150a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "blue_ridge_satin_88480"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "bright_white_gloss_ga030a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "bronze_olive_matt_32348"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "bronze_pearl_satin_94686"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "bushland_matt_gk203a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "canola_cream_gloss_81796"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "champagne_kinetic_3059k"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "charcoal_metallic_gloss_gm019a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "charcoal_metallic_pearl_gloss_18796"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "charcoal_pearl_matt_gy237a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "citi_matt_gl211a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "citi_pearl_matt_88471"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "claret_satin_gg142a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "classic_cream_matt_gd245a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "claypot_satin_72165"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "copper_kinetic_7183k"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "cottage_green_matt_gk274a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "cove_matt_gd274a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "deep_brunswick_green_gloss_6134g"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "estate_mtt_89743"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "evening_haze_matt_gm235a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "french_champagne_matt_gx207c"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "gold_pearl_satin_96604"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "grey_nurse_gloss_50060"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "gully_matt_gm242a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "gunmetal_kinetic_8115k"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "headland_matt_gg219a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "hedge_matt_87336"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "heritage_green_gloss_gk044a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "horizon_blue_gloss_33344"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "hunter_red_satin_84209"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "ivory_coast_gloss_3061g"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "light_grey_gloss_7263g"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "loft_matt_8106m"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "magnolia_gloss_gd025a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "mangrove_matt_gk277a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "manor_red_matt_gg262a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "mass_vale_sands_satin_33019"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "metropolis_bronze_pearl_satin_59003"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "metropolis_silver_glow_pearl_gloss_84678"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "metropolis_storm_pearl_satin_88471"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "mother_of_pearl_gloss_84684"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "natural_pearl_matt_89119"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "natural_pearl_matt_97189119"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "nickel_pearl_matt_88360"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "night_sky_matt_gn231a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "nobel_silver_pearl_satin_gy119a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "notre_dame_gloss_gl040a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "ocean_mist_satin_6132s"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "off_white_matt_ga211a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "olde_pewter_satin_gl175a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "onyx_pearl_gloss_52052"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "oyster_grey_matt_gl258a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "pale_eucalypt_matt_gk236a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "palladium_silver_pearl_satin_gy184c"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "pandemonium_silver_flat_7004f"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "periwinkle_gloss_50276"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "pewter_pearl_satin_88202"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "platypus_kinetic_7214k"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "regency_grey_matt_50278"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "rivergum_beige_gloss_36991"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "rivergum_gloss_gd042a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "riversand_matt_36656"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "riversand_matt_gm204a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "roseberry_grey_gloss_gl015a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "sand_satin_51438"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "sandbank_matt_3003m"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "shoji_white_satin_ga160a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "smokey_glass_satin_78292"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "space_blue_gloss_mj008a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "st_elmos_fire_kinetic_7208k"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "steel_pearl_satin_57127"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "stone_grey_satin_87126"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-2", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "storm_front_matt_gl249a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "storm_grey_n42_gloss_gl060a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "stromboli_satin_gk148a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "teal_gloss_6133g"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "terrain_satin_gm143a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "textura_deep_ocean_gj301a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "textura_dune_gl352a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "textura_ironstone_gl336a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "textura_jasper_gm314a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "textura_monument_gl329a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "textura_paperbark_gd314a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "textura_primrose_gd331a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "textura_sable_bass_gn297a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "textura_silver_gy35la"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "textura_white_ga330a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-3", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "textura_woodland_grey_gl333a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "transformer_grey_gloss_gl031a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "wallaby_matt_gl210a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "wedgewood_satin_50279"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "white_satin_satin_ga124a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "wilderness_matt_gk289a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "windspray_matt_gl266a"}, {"component_product": "Commandex Door Frame", "component_product_code": "CXDF-PC-1", "quantity_formula": "1", "field_name": "Powder Coat Colour", "option_value": "wizard_gloss_50283"}, {"component_product": "Austral Elegance XC Hinged Lock", "component_product_code": "AUHL-BLK", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Colour", "option_value": "black"}, {"component_product": "Austral Elegance XC Hinged Lock", "component_product_code": "AUHL-PW", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Colour", "option_value": "pearl_white"}, {"component_product": "Austral Elegance XC Hinged Lock", "component_product_code": "AUHL-PR", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Colour", "option_value": "primrose"}, {"component_product": "Austral Elegance XC Hinged Lock", "component_product_code": "AUHL-SLV", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Colour", "option_value": "silver_paint"}, {"component_product": "Austral Elegance XC Hinged Lock", "component_product_code": "AUHL-WB", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Colour", "option_value": "white_birch"}, {"component_product": "Austral 5 Pin Double Cylinder", "component_product_code": "AU5PIN", "quantity_formula": "1", "field_name": "Austral Elegance XC - Cylinder", "option_value": "austral_5_pin_dbl"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Top of Cut Out)", "option_value": "single"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Top of Cut Out)", "option_value": "3pt"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Top of Cut Out)", "option_value": "3pt_top_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Top of Cut Out)", "option_value": "double_bottom_aux_only"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Top of Cut Out)", "option_value": "3pt_bottom_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Top of Cut Out)", "option_value": "double_top_aux_only"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Top of Cut Out)", "option_value": "3pt_top_bottom_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Top of Cut Out)", "option_value": "double_bottom_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Top of Cut Out)", "option_value": "double_top_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Cut Out)", "option_value": "single"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Cut Out)", "option_value": "3pt"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Cut Out)", "option_value": "3pt_top_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Cut Out)", "option_value": "double_bottom_aux_only"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Cut Out)", "option_value": "3pt_bottom_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Cut Out)", "option_value": "double_top_aux_only"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Cut Out)", "option_value": "3pt_top_bottom_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Cut Out)", "option_value": "double_bottom_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Cut Out)", "option_value": "double_top_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Bottom of Cut Out)", "option_value": "single"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Bottom of Cut Out)", "option_value": "3pt"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Bottom of Cut Out)", "option_value": "3pt_top_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Bottom of Cut Out)", "option_value": "double_bottom_aux_only"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Bottom of Cut Out)", "option_value": "3pt_bottom_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Bottom of Cut Out)", "option_value": "double_top_aux_only"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Bottom of Cut Out)", "option_value": "3pt_top_bottom_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Bottom of Cut Out)", "option_value": "double_bottom_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Bottom of Cut Out)", "option_value": "double_top_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Handle Lever)", "option_value": "single"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Handle Lever)", "option_value": "3pt"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Handle Lever)", "option_value": "3pt_top_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Handle Lever)", "option_value": "double_bottom_aux_only"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Handle Lever)", "option_value": "3pt_bottom_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Handle Lever)", "option_value": "double_top_aux_only"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Handle Lever)", "option_value": "3pt_top_bottom_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit Low", "component_product_code": "AUHL3PTLOW", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Handle Lever)", "option_value": "double_bottom_aux_cut"}, {"component_product": "Austral H14R Elegance Hinged 3Pt Kit", "component_product_code": "AUHL3PT", "quantity_formula": "1", "field_name": "Austral Elegance XC - Lock Type (Centre of Handle Lever)", "option_value": "double_top_aux_cut"}, {"component_product": "Commandex Hinged Lock", "component_product_code": "CXHL-BLK", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Colour", "option_value": "black"}, {"component_product": "Commandex Hinged Lock", "component_product_code": "CXHL-HMB", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Colour", "option_value": "hamersley_brown"}, {"component_product": "Commandex Hinged Lock", "component_product_code": "CXHL-PW", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Colour", "option_value": "pearl_white"}, {"component_product": "Commandex Hinged Lock", "component_product_code": "CXHL-PR", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Colour", "option_value": "primrose"}, {"component_product": "Commandex Hinged Lock", "component_product_code": "CXHL-SLV", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Colour", "option_value": "silver_paint"}, {"component_product": "Commandex 5 <PERSON><PERSON>er Keyed <PERSON>", "component_product_code": "CX5PIN", "quantity_formula": "1", "field_name": "CommandeX Hinged - <PERSON><PERSON><PERSON>", "option_value": "commandex_5_pin"}, {"component_product": "Whitco 5 Pin <PERSON>", "component_product_code": "WT5PIN", "quantity_formula": "1", "field_name": "CommandeX Hinged - <PERSON><PERSON><PERSON>", "option_value": "whitco_5_pin"}, {"component_product": "Whitco 5 Disc Cylinder", "component_product_code": "WT5DISC", "quantity_formula": "1", "field_name": "CommandeX Hinged - <PERSON><PERSON><PERSON>", "option_value": "whitco_5_disc"}, {"component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Type (Top of Cut Out)", "option_value": "single"}, {"component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Type (Top of Cut Out)", "option_value": "3pt"}, {"component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Type (Top of Cut Out)", "option_value": "double_bottom_aux_only"}, {"component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Type (Top of Cut Out)", "option_value": "double_top_aux_only"}, {"component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Type (Centre of Cut Out)", "option_value": "single"}, {"component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Type (Centre of Cut Out)", "option_value": "3pt"}, {"component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Type (Centre of Cut Out)", "option_value": "double_bottom_aux_only"}, {"component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Type (Centre of Cut Out)", "option_value": "double_top_aux_only"}, {"component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Type (Bottom of Cut Out)", "option_value": "single"}, {"component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Type (Bottom of Cut Out)", "option_value": "3pt"}, {"component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Type (Bottom of Cut Out)", "option_value": "double_bottom_aux_only"}, {"component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Type (Bottom of Cut Out)", "option_value": "double_top_aux_only"}, {"component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Type (Centre of Handle Lever)", "option_value": "single"}, {"component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Type (Centre of Handle Lever)", "option_value": "3pt"}, {"component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Type (Centre of Handle Lever)", "option_value": "double_bottom_aux_only"}, {"component_product": "Commandex Hinged 3Pt Kit", "component_product_code": "CXHL3PT", "quantity_formula": "1", "field_name": "CommandeX Hinged - Lock Type (Centre of Handle Lever)", "option_value": "double_top_aux_only"}, {"component_product": "Lockwood 8654 Hinged Lock", "component_product_code": "LW8654HL-BLK", "quantity_formula": "1", "field_name": "Lockwood 8654 - <PERSON> Colour", "option_value": "black"}, {"component_product": "Lockwood 8654 Hinged Lock", "component_product_code": "LW8654HL-HMB", "quantity_formula": "1", "field_name": "Lockwood 8654 - <PERSON> Colour", "option_value": "hamersley_brown"}, {"component_product": "Lockwood 8654 Hinged Lock", "component_product_code": "LW8654HL-PW", "quantity_formula": "1", "field_name": "Lockwood 8654 - <PERSON> Colour", "option_value": "pearl_white"}, {"component_product": "Lockwood 8654 Hinged Lock", "component_product_code": "LW8654HL-PR", "quantity_formula": "1", "field_name": "Lockwood 8654 - <PERSON> Colour", "option_value": "primrose"}, {"component_product": "Lockwood 8654 Hinged Lock", "component_product_code": "LW8654HL-SCH", "quantity_formula": "1", "field_name": "Lockwood 8654 - <PERSON> Colour", "option_value": "satin_chrome"}, {"component_product": "Lockwood 8654 Hinged Lock", "component_product_code": "LW8654HL-SLV", "quantity_formula": "1", "field_name": "Lockwood 8654 - <PERSON> Colour", "option_value": "silver_paint"}, {"component_product": "Lockwood 8654 Hinged Lock", "component_product_code": "LW8654HL-STB", "quantity_formula": "1", "field_name": "Lockwood 8654 - <PERSON> Colour", "option_value": "stone_beige"}, {"component_product": "Lockwood 8654 Hinged Lock", "component_product_code": "LW8654HL-WB", "quantity_formula": "1", "field_name": "Lockwood 8654 - <PERSON> Colour", "option_value": "white_birch"}, {"component_product": "Whitco 5 Pin <PERSON>", "component_product_code": "WT5PIN", "quantity_formula": "1", "field_name": "Lockwood 8654 - <PERSON><PERSON><PERSON>", "option_value": "whitco_5_pin"}, {"component_product": "Whitco 5 Disc Cylinder", "component_product_code": "WT5DISC", "quantity_formula": "1", "field_name": "Lockwood 8654 - <PERSON><PERSON><PERSON>", "option_value": "whitco_5_disc"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Top of Cut Out)", "option_value": "single"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Top of Cut Out)", "option_value": "3pt_hook_bolt"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Top of Cut Out)", "option_value": "3pt_projection_bolt"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Top of Cut Out)", "option_value": "double_bottom_aux_hook_bolt_only"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Top of Cut Out)", "option_value": "double_bottom_aux_projection_bolt_only"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Top of Cut Out)", "option_value": "double_top_aux_hook_bolt_only"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Top of Cut Out)", "option_value": "double_top_aux_projection_bolt_only"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Centre of Cut Out)", "option_value": "single"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Centre of Cut Out)", "option_value": "3pt_hook_bolt"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Centre of Cut Out)", "option_value": "3pt_projection_bolt"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Centre of Cut Out)", "option_value": "double_bottom_aux_hook_bolt_only"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Centre of Cut Out)", "option_value": "double_bottom_aux_projection_bolt_only"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Centre of Cut Out)", "option_value": "double_top_aux_hook_bolt_only"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Centre of Cut Out)", "option_value": "double_top_aux_projection_bolt_only"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Bottom of Cut Out)", "option_value": "single"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Bottom of Cut Out)", "option_value": "3pt_hook_bolt"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Bottom of Cut Out)", "option_value": "3pt_projection_bolt"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Bottom of Cut Out)", "option_value": "double_bottom_aux_hook_bolt_only"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Bottom of Cut Out)", "option_value": "double_bottom_aux_projection_bolt_only"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Bottom of Cut Out)", "option_value": "double_top_aux_hook_bolt_only"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Bottom of Cut Out)", "option_value": "double_top_aux_projection_bolt_only"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Centre of Handle Lever)", "option_value": "single"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Centre of Handle Lever)", "option_value": "3pt_hook_bolt"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Centre of Handle Lever)", "option_value": "3pt_projection_bolt"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Centre of Handle Lever)", "option_value": "double_bottom_aux_hook_bolt_only"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Centre of Handle Lever)", "option_value": "double_bottom_aux_projection_bolt_only"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit", "component_product_code": "LW8654H3PT", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Centre of Handle Lever)", "option_value": "double_top_aux_hook_bolt_only"}, {"component_product": "Lockwood 8654 Hinged 3Pt Kit Projection Bolt", "component_product_code": "LW8654H3PTPB", "quantity_formula": "1", "field_name": "Lockwood 8654 - Lock Type (Centre of Handle Lever)", "option_value": "double_top_aux_projection_bolt_only"}, {"component_product": "Whitco Mk2 Tasman Hinged Lock", "component_product_code": "WTMK2HL-BLK", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Colour", "option_value": "black"}, {"component_product": "Whitco Mk2 Tasman Hinged Lock", "component_product_code": "WTMK2HL-PW", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Colour", "option_value": "pearl_white"}, {"component_product": "Whitco Mk2 Tasman Hinged Lock", "component_product_code": "WTMK2HL-PR", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Colour", "option_value": "primrose"}, {"component_product": "Whitco 5 Pin <PERSON>", "component_product_code": "WT5PIN", "quantity_formula": "1", "field_name": "Whitco MK2 - <PERSON><PERSON>er", "option_value": "whitco_5_pin"}, {"component_product": "Whitco 5 Disc Cylinder", "component_product_code": "WT5DISC", "quantity_formula": "1", "field_name": "Whitco MK2 - <PERSON><PERSON>er", "option_value": "whitco_5_disc"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Top of Cut Out)", "option_value": "single"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Top of Cut Out)", "option_value": "3pt"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Top of Cut Out)", "option_value": "3pt_top_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Top of Cut Out)", "option_value": "double_bottom_aux_only"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Top of Cut Out)", "option_value": "3pt_bottom_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Top of Cut Out)", "option_value": "double_top_aux_only"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Top of Cut Out)", "option_value": "3pt_top_bottom_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Top of Cut Out)", "option_value": "double_bottom_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Top of Cut Out)", "option_value": "double_top_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Cut Out)", "option_value": "single"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Cut Out)", "option_value": "3pt"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Cut Out)", "option_value": "3pt_top_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Cut Out)", "option_value": "double_bottom_aux_only"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Cut Out)", "option_value": "3pt_bottom_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Cut Out)", "option_value": "double_top_aux_only"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Cut Out)", "option_value": "3pt_top_bottom_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Cut Out)", "option_value": "double_bottom_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Cut Out)", "option_value": "double_top_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Bottom of Cut Out)", "option_value": "single"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Bottom of Cut Out)", "option_value": "3pt"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Bottom of Cut Out)", "option_value": "3pt_top_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Bottom of Cut Out)", "option_value": "double_bottom_aux_only"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Bottom of Cut Out)", "option_value": "3pt_bottom_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Bottom of Cut Out)", "option_value": "double_top_aux_only"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Bottom of Cut Out)", "option_value": "3pt_top_bottom_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Bottom of Cut Out)", "option_value": "double_bottom_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Bottom of Cut Out)", "option_value": "double_top_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Handle Lever)", "option_value": "single"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Handle Lever)", "option_value": "3pt"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Handle Lever)", "option_value": "3pt_top_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Handle Lever)", "option_value": "double_bottom_aux_only"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Handle Lever)", "option_value": "3pt_bottom_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Handle Lever)", "option_value": "double_top_aux_only"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Handle Lever)", "option_value": "3pt_top_bottom_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit Low W893117", "component_product_code": "WTMK2HL3PTLOW", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Handle Lever)", "option_value": "double_bottom_aux_cut"}, {"component_product": "Whitco Mk2 Tasman Hinged 3Pt Kit W893217", "component_product_code": "WTMK2HL3PT", "quantity_formula": "1", "field_name": "Whitco MK2 - Lock Type (Centre of Handle Lever)", "option_value": "double_top_aux_cut"}, {"component_product": "Whitco Tasman Escape Hinged Lock W807017", "component_product_code": "WTESCP-BLK", "quantity_formula": "1", "field_name": "<PERSON><PERSON><PERSON> Tasman ESCAPE - Lock Colour", "option_value": "black"}, {"component_product": "<PERSON><PERSON><PERSON> Tasman Escape Cylinder With Turn Knob W845900", "component_product_code": "WTESCPCYL", "quantity_formula": "1", "field_name": "<PERSON><PERSON><PERSON> Tasman ESCAPE - Cylinder", "option_value": "whitco_escape_cylinder_turn_knob"}, {"component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT", "quantity_formula": "1", "field_name": "<PERSON><PERSON><PERSON> Tasman ESCAPE - Lock Type (Top of Cut Out)", "option_value": "single"}, {"component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT", "quantity_formula": "1", "field_name": "<PERSON><PERSON><PERSON> Tasman ESCAPE - Lock Type (Top of Cut Out)", "option_value": "3pt"}, {"component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT", "quantity_formula": "1", "field_name": "<PERSON><PERSON><PERSON> Tasman ESCAPE - Lock Type (Top of Cut Out)", "option_value": "double_bottom_aux_only"}, {"component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT", "quantity_formula": "1", "field_name": "<PERSON><PERSON><PERSON> Tasman ESCAPE - Lock Type (Top of Cut Out)", "option_value": "double_top_aux_only"}, {"component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT", "quantity_formula": "1", "field_name": "Whitco Tasman ESCAPE - Lock Type (Centre of Cut Out)", "option_value": "single"}, {"component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT", "quantity_formula": "1", "field_name": "Whitco Tasman ESCAPE - Lock Type (Centre of Cut Out)", "option_value": "3pt"}, {"component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT", "quantity_formula": "1", "field_name": "Whitco Tasman ESCAPE - Lock Type (Centre of Cut Out)", "option_value": "double_bottom_aux_only"}, {"component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT", "quantity_formula": "1", "field_name": "Whitco Tasman ESCAPE - Lock Type (Centre of Cut Out)", "option_value": "double_top_aux_only"}, {"component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT", "quantity_formula": "1", "field_name": "<PERSON><PERSON><PERSON> Tasman ESCAPE - Lock Type (Bottom of Cut Out)", "option_value": "single"}, {"component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT", "quantity_formula": "1", "field_name": "<PERSON><PERSON><PERSON> Tasman ESCAPE - Lock Type (Bottom of Cut Out)", "option_value": "3pt"}, {"component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT", "quantity_formula": "1", "field_name": "<PERSON><PERSON><PERSON> Tasman ESCAPE - Lock Type (Bottom of Cut Out)", "option_value": "double_bottom_aux_only"}, {"component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT", "quantity_formula": "1", "field_name": "<PERSON><PERSON><PERSON> Tasman ESCAPE - Lock Type (Bottom of Cut Out)", "option_value": "double_top_aux_only"}, {"component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT", "quantity_formula": "1", "field_name": "Whitco Tasman ESCAPE - Lock Type (Centre of Handle Lever)", "option_value": "single"}, {"component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT", "quantity_formula": "1", "field_name": "Whitco Tasman ESCAPE - Lock Type (Centre of Handle Lever)", "option_value": "3pt"}, {"component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT", "quantity_formula": "1", "field_name": "Whitco Tasman ESCAPE - Lock Type (Centre of Handle Lever)", "option_value": "double_bottom_aux_only"}, {"component_product": "Whitco Tasman Escape Hinged 3Pt Kit W807317", "component_product_code": "WTESCP3PT", "quantity_formula": "1", "field_name": "Whitco Tasman ESCAPE - Lock Type (Centre of Handle Lever)", "option_value": "double_top_aux_only"}, {"component_product": "Austral 5 Pin Double Cylinder", "component_product_code": "AU5PIN", "quantity_formula": "1", "field_name": "Austral Elegance XC CUT OUT ONLY - <PERSON><PERSON>er", "option_value": "austral_5_pin_dbl"}, {"component_product": "Commandex 5 <PERSON><PERSON>er Keyed <PERSON>", "component_product_code": "CX5PIN", "quantity_formula": "1", "field_name": "CommandeX Hinged CUT OUT ONLY - <PERSON><PERSON><PERSON>", "option_value": "commandex_5_pin"}, {"component_product": "Whitco 5 Pin <PERSON>", "component_product_code": "WT5PIN", "quantity_formula": "1", "field_name": "CommandeX Hinged CUT OUT ONLY - <PERSON><PERSON><PERSON>", "option_value": "whitco_5_pin"}, {"component_product": "Whitco 5 Disc Cylinder", "component_product_code": "WT5DISC", "quantity_formula": "1", "field_name": "CommandeX Hinged CUT OUT ONLY - <PERSON><PERSON><PERSON>", "option_value": "whitco_5_disc"}, {"component_product": "Whitco 5 Pin <PERSON>", "component_product_code": "WT5PIN", "quantity_formula": "1", "field_name": "Lockwood 8654 CUT OUT ONLY - <PERSON><PERSON><PERSON>", "option_value": "whitco_5_pin"}, {"component_product": "Whitco 5 Disc Cylinder", "component_product_code": "WT5DISC", "quantity_formula": "1", "field_name": "Lockwood 8654 CUT OUT ONLY - <PERSON><PERSON><PERSON>", "option_value": "whitco_5_disc"}, {"component_product": "Whitco 5 Pin <PERSON>", "component_product_code": "WT5PIN", "quantity_formula": "1", "field_name": "Whitco MK2 CUT OUT ONLY - Cylinder", "option_value": "whitco_5_pin"}, {"component_product": "Whitco 5 Disc Cylinder", "component_product_code": "WT5DISC", "quantity_formula": "1", "field_name": "Whitco MK2 CUT OUT ONLY - Cylinder", "option_value": "whitco_5_disc"}, {"component_product": "<PERSON><PERSON><PERSON>", "component_product_code": "WTFB-BLK", "quantity_formula": "2.0", "field_name": "Flush Bolts & Patio Bolts (Non-Lock Door)", "option_value": "top_bottom_flush_bolts"}, {"component_product": "<PERSON><PERSON><PERSON>", "component_product_code": "WTFB-BLK", "quantity_formula": "1", "field_name": "Flush Bolts & Patio Bolts (Non-Lock Door)", "option_value": "top_flush_bolt"}, {"component_product": "<PERSON><PERSON><PERSON>", "component_product_code": "WTFB-BLK", "quantity_formula": "1", "field_name": "Flush Bolts & Patio Bolts (Non-Lock Door)", "option_value": "bottom_flush_bolt"}, {"component_product": "<PERSON><PERSON><PERSON>", "component_product_code": "WTFB-BLK", "quantity_formula": "1", "field_name": "Flush Bolts & Patio Bolts (Non-Lock Door)", "option_value": "top_flush_1_patio"}, {"component_product": "<PERSON><PERSON><PERSON>", "component_product_code": "WTFB-BLK", "quantity_formula": "1", "field_name": "Flush Bolts & Patio Bolts (Non-Lock Door)", "option_value": "bottom_flush_1_patio"}, {"component_product": "<PERSON><PERSON><PERSON>", "component_product_code": "WTPB-BLK", "quantity_formula": "1", "field_name": "<PERSON><PERSON> (Non-Lock Door)", "option_value": "black"}, {"component_product": "<PERSON><PERSON><PERSON>", "component_product_code": "WTPB-PW", "quantity_formula": "1", "field_name": "<PERSON><PERSON> (Non-Lock Door)", "option_value": "white"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-BLK", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "black"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-MON", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Monument Matt GL229A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-PW", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Pearl White Gloss GA078A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-PR", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Primrose Gloss GD037A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-STB", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Stone Beige Matt GD247A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-SFM", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Surfmist Matt GA236A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-US", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Ultra Silver Gloss GY070A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-WB", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "White Birch Gloss GA057A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-WG", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Woodland Grey Matt GL205A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-MF", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Mill Finish"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-ABZ", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Anodic Bronze Satin GY114A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-ADG", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Anodic <PERSON> Grey Matt GL213A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-AN", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Anodic Natural Matt GY235A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-AOW", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Anodic Off White Matt GD227A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-ASG", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Anodic Silver Grey Matt GL237A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-APO", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "APO Grey Satin GL107A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-DPO", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Deep Ocean Matt GJ203A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-DOE", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Doeskin Satin GD188A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-DUN", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "<PERSON>ne Matt GL252A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-HMB", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Hamersley Brown Satin GM100A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-HWG", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Hawthorn Green Gloss GK030A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-IRS", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Ironstone Matt GL236A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-JSP", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Jasper Satin GT114A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-PBK", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Paperbark Satin GU114A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-POT", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Pottery Satin GM175A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-PSK", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Precious Silver Kinetic Pearl Satin 971-7043K"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-SHG", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Shale Grey Satin GP184A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-TXB", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Textura Black GN305A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-CHL", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 1)", "option_value": "Charcoal Satin GL180A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-BLK", "quantity_formula": "1", "field_name": "Midrail Colour (Special Frame, Conditional Case 1)", "option_value": "black"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-BLK", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "black"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-MON", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Monument Matt GL229A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-PW", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Pearl White Gloss GA078A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-PR", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Primrose Gloss GD037A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-STB", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Stone Beige Matt GD247A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-SFM", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Surfmist Matt GA236A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-US", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Ultra Silver Gloss GY070A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-WB", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "White Birch Gloss GA057A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-WG", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Woodland Grey Matt GL205A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-MF", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Mill Finish"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-ABZ", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Anodic Bronze Satin GY114A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-ADG", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Anodic <PERSON> Grey Matt GL213A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-AN", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Anodic Natural Matt GY235A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-AOW", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Anodic Off White Matt GD227A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-ASG", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Anodic Silver Grey Matt GL237A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-APO", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "APO Grey Satin GL107A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-DPO", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Deep Ocean Matt GJ203A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-DOE", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Doeskin Satin GD188A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-DUN", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "<PERSON>ne Matt GL252A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-HMB", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Hamersley Brown Satin GM100A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-HWG", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Hawthorn Green Gloss GK030A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-IRS", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Ironstone Matt GL236A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-JSP", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Jasper Satin GT114A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-PBK", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Paperbark Satin GU114A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-POT", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Pottery Satin GM175A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-PSK", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Precious Silver Kinetic Pearl Satin 971-7043K"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-SHG", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Shale Grey Satin GP184A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-TXB", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Textura Black GN305A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-CHL", "quantity_formula": "1", "field_name": "Midrail Colour (Standard Frame, Conditional Case 2)", "option_value": "Charcoal Satin GL180A"}, {"component_product": "Commandex Midrail", "component_product_code": "CXMR-BLK", "quantity_formula": "1", "field_name": "Midrail Colour (Special Frame, Conditional Case 2)", "option_value": "black"}, {"component_product": "Whitco Security Hinges", "component_product_code": "WTSH-BLK", "quantity_formula": "1", "field_name": "Security Hinge Colour (Standard Hinge)", "option_value": "black"}, {"component_product": "Whitco Security Hinges", "component_product_code": "WTSH-PW", "quantity_formula": "1", "field_name": "Security Hinge Colour (Standard Hinge)", "option_value": "pearl_white"}, {"component_product": "Whitco Security Hinges", "component_product_code": "WTSH-SLV", "quantity_formula": "1", "field_name": "Security Hinge Colour (Standard Hinge)", "option_value": "silver_paint"}, {"component_product": "Whitco Security Hinges", "component_product_code": "WTSH-SS", "quantity_formula": "1", "field_name": "Security Hinge Colour (Standard Hinge)", "option_value": "stainless_steel"}, {"component_product": "Whitco Security Hinges With Step", "component_product_code": "WTSHS-BLK", "quantity_formula": "1", "field_name": "Security Hinge Colour (Prong/Step Hinge)", "option_value": "black"}, {"component_product": "Austral Hinged Closer", "component_product_code": "AUCL-BLK", "quantity_formula": "1", "field_name": "Closer Colour (Austral)", "option_value": "black"}, {"component_product": "Austral Hinged Closer", "component_product_code": "AUCL-PW", "quantity_formula": "1", "field_name": "Closer Colour (Austral)", "option_value": "pearl_white"}, {"component_product": "<PERSON><PERSON><PERSON> Hinged Closer", "component_product_code": "WTCL-BLK", "quantity_formula": "1", "field_name": "Closer Colour (Whitco)", "option_value": "black"}, {"component_product": "<PERSON><PERSON><PERSON> Hinged Closer", "component_product_code": "WTCL-PW", "quantity_formula": "1", "field_name": "Closer Colour (Whitco)", "option_value": "pearl_white"}, {"component_product": "<PERSON><PERSON><PERSON> Hinged Closer", "component_product_code": "WTCL-SLV", "quantity_formula": "1", "field_name": "Closer Colour (Whitco)", "option_value": "silver_paint"}, {"component_product": "Petway Small Pet Door", "component_product_code": "PDS-BLK", "quantity_formula": "1", "field_name": "Pet Door Colour", "option_value": "black"}, {"component_product": "Petway Small Pet Door", "component_product_code": "PDS-PW", "quantity_formula": "1", "field_name": "Pet Door Colour", "option_value": "white"}, {"component_product": "Petway Medium Pet Door", "component_product_code": "PDM-BLK", "quantity_formula": "1", "field_name": "Pet Door Colour", "option_value": "Medium Black"}, {"component_product": "Petway Medium Pet Door", "component_product_code": "PDM-PW", "quantity_formula": "1", "field_name": "Pet Door Colour", "option_value": "Medium White"}, {"component_product": "Petway Large Pet Door", "component_product_code": "PDL-BLK", "quantity_formula": "1", "field_name": "Pet Door Colour", "option_value": "Large Black"}, {"component_product": "Petway Large Pet Door", "component_product_code": "PDL-PW", "quantity_formula": "1", "field_name": "Pet Door Colour", "option_value": "Large White"}, {"component_product": "Petway Small Pet Door Flexible Flap", "component_product_code": "PDSFL-CLR", "quantity_formula": "1", "field_name": "Pet Door Flexible Flap", "option_value": "yes"}, {"component_product": "Petway Medium Pet Door Flexible Flap", "component_product_code": "PDMFL-CLR", "quantity_formula": "1", "field_name": "Pet Door Flexible Flap", "option_value": "Yes Medium"}, {"component_product": "Petway Large Pet Door Flexible Flap", "component_product_code": "PDLFL-CLR", "quantity_formula": "1", "field_name": "Pet Door Flexible Flap", "option_value": "Yes Large"}, {"component_product": "Small Pet Door Surround Infill", "component_product_code": "PDSSI-BLK", "quantity_formula": "1", "field_name": "Pet Door Surround Infill", "option_value": "yes"}, {"component_product": "Medium Pet Door Surround Infill", "component_product_code": "PDMSI-BLK", "quantity_formula": "1", "field_name": "Pet Door Surround Infill", "option_value": "Yes Medium"}, {"component_product": "Large Pet Door Surround Infill", "component_product_code": "PDLSI-BLK", "quantity_formula": "1", "field_name": "Pet Door Surround Infill", "option_value": "Yes Large"}, {"component_product": "Whitco Mk2 / Commandex Centre Strike Packer", "component_product_code": "WMK2CSP", "quantity_formula": "1", "field_name": "Centre Striker Plate Packer", "option_value": "yes"}, {"component_product": "Sliding Striker Plate Packer 2mm", "component_product_code": "SSP2-BLK", "quantity_formula": "1", "field_name": "Auxiliary Striker Plate Packers", "option_value": "yes_2"}, {"component_product": "Stopbead 16 X 13mm", "component_product_code": "STPB16X13-BLK", "quantity_formula": "1", "field_name": "Stop Bead Colour", "option_value": "black"}, {"component_product": "Stopbead 16 X 13mm", "component_product_code": "STPB16X13-BRN", "quantity_formula": "1", "field_name": "Stop Bead Colour", "option_value": "brown"}, {"component_product": "Stopbead 16 X 13mm", "component_product_code": "STPB16X13-PR", "quantity_formula": "1", "field_name": "Stop Bead Colour", "option_value": "primrose"}, {"component_product": "Stopbead 16 X 13mm", "component_product_code": "STPB16X13-STB", "quantity_formula": "1", "field_name": "Stop Bead Colour", "option_value": "stone_beige"}, {"component_product": "Stopbead 16 X 13mm", "component_product_code": "STPB16X13-PW", "quantity_formula": "1", "field_name": "Stop Bead Colour", "option_value": "white"}, {"component_product": "Adhesive Mohair (Ultrafab Fin Clr-None Den-Vh Bk Op-3M Yrn Clr-Black)", "component_product_code": "AMOH-BLK", "quantity_formula": "1", "field_name": "Adhesive Mohair", "option_value": "yes"}]}