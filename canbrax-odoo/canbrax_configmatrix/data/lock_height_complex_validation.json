{"_CALCULATED_lock_height_complex_range_validation": {"description": "Boolean: Complex lock height validation with two alternative range conditions", "formula": "((_CALCULATED_lock_height >= (_CALCULATED_halfway_point + 79)) && (_CALCULATED_lock_height < 1250) && (_CALCULATED_lock_height > (_CALCULATED_smallest_door_height - 940))) || ((_CALCULATED_lock_height < (_CALCULATED_halfway_point + 79)) && (_CALCULATED_lock_height < 1100) && (_CALCULATED_lock_height > (_CALCULATED_smallest_door_height - 1090)))"}, "_CALCULATED_lock_height_condition_A": {"description": "Boolean: Lock Height >= (halfway + 79) AND < 1250 AND > (height - 940)", "formula": "(_CALCULATED_lock_height >= (_CALCULATED_halfway_point + 79)) && (_CALCULATED_lock_height < 1250) && (_CALCULATED_lock_height > (_CALCULATED_smallest_door_height - 940))"}, "_CALCULATED_lock_height_condition_B": {"description": "Boolean: Lock Height < (halfway + 79) AND < 1100 AND > (height - 1090)", "formula": "(_CALCULATED_lock_height < (_CALCULATED_halfway_point + 79)) && (_CALCULATED_lock_height < 1100) && (_CALCULATED_lock_height > (_CALCULATED_smallest_door_height - 1090))"}, "_CALCULATED_height_minus_1090": {"description": "Smallest Door Height - 1090mm", "formula": "_CALCULATED_smallest_door_height - 1090"}}