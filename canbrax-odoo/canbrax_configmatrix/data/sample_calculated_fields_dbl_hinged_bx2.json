{"_CALCULATED_smallest_door_height": {"description": "Minimum height of the door leaves.", "formula": "(_CALCULATED_height_calculation_method === 'manual') ? Math.min(_CALCULATED_manual_left_height, _CALCULATED_manual_right_height) : (_CALCULATED_height_calculation_method === 'even') ? _CALCULATED_even_each_door_height : (_CALCULATED_height_calculation_method === 'uneven') ? Math.min(_CALCULATED_uneven_left_height, _CALCULATED_uneven_right_height) : 0"}, "_CALCULATED_height_calculation_method": {"description": "Method used to calculate door heights: 'manual', 'even', 'uneven', or 'none'", "formula": "(bx_dbl_hinge_deduction_assistance === 'no') ? 'manual' : (bx_dbl_hinge_deduction_assistance === 'yes' && bx_dbl_hinge_door_split_type === 'even') ? 'even' : (bx_dbl_hinge_deduction_assistance === 'yes' && bx_dbl_hinge_door_split_type === 'uneven') ? 'uneven' : 'none'"}, "_CALCULATED_manual_left_height": {"description": "Manual left door height in mm", "formula": "parseFloat(bx_dbl_hinge_make_left_door_height_mm_manual) || 0"}, "_CALCULATED_manual_right_height": {"description": "Manual right door height in mm", "formula": "parseFloat(bx_dbl_hinge_make_right_door_height_mm_manual) || 0"}, "_CALCULATED_even_each_door_height": {"description": "Even split door height in mm", "formula": "parseFloat(bx_dbl_hinge_make_each_door_height_mm_even) || 0"}, "_CALCULATED_uneven_left_height": {"description": "Uneven left door height in mm", "formula": "parseFloat(bx_dbl_hinge_make_left_door_height_mm_uneven) || 0"}, "_CALCULATED_uneven_right_height": {"description": "Uneven right door height in mm", "formula": "parseFloat(bx_dbl_hinge_make_right_door_height_mm_uneven) || 0"}, "_CALCULATED_halfway_point": {"description": "Halfway point of smallest door height", "formula": "_CALCULATED_smallest_door_height / 2"}, "_CALCULATED_halfway_plus_32": {"description": "Halfway point plus 32mm", "formula": "_CALCULATED_halfway_point + 32"}, "_CALCULATED_halfway_plus_79": {"description": "Halfway point plus 79mm", "formula": "_CALCULATED_halfway_point + 79"}, "_CALCULATED_halfway_minus_79": {"description": "Halfway point minus 79mm", "formula": "_CALCULATED_halfway_point - 79"}, "_CALCULATED_height_minus_740": {"description": "Smallest door height minus 740mm (for lock visibility expressions)", "formula": "_CALCULATED_smallest_door_height - 740"}, "_CALCULATED_height_minus_940": {"description": "Smallest door height minus 940mm", "formula": "_CALCULATED_smallest_door_height - 940"}, "_CALCULATED_height_minus_987": {"description": "Smallest door height minus 987mm", "formula": "_CALCULATED_smallest_door_height - 987"}, "_CALCULATED_height_minus_1137": {"description": "Smallest door height minus 1137mm", "formula": "_CALCULATED_smallest_door_height - 1137"}, "_CALCULATED_height_minus_1169": {"description": "Smallest door height minus 1169mm", "formula": "_CALCULATED_smallest_door_height - 1169"}, "_CALCULATED_height_threshold_1050": {"description": "Boolean: Height is greater than or equal to 1050mm", "formula": "_CALCULATED_smallest_door_height >= 1050"}, "_CALCULATED_height_threshold_1200": {"description": "Boolean: Height is greater than or equal to 1200mm", "formula": "_CALCULATED_smallest_door_height >= 1200"}, "_CALCULATED_height_threshold_1500": {"description": "Boolean: Height is greater than or equal to 1500mm", "formula": "_CALCULATED_smallest_door_height >= 1500"}, "_CALCULATED_height_threshold_1800": {"description": "Boolean: Height is greater than or equal to 1800mm", "formula": "_CALCULATED_smallest_door_height >= 1800"}, "_CALCULATED_height_threshold_2100": {"description": "Boolean: Height is greater than or equal to 2100mm", "formula": "_CALCULATED_smallest_door_height >= 2100"}, "_CALCULATED_height_range_1050_1199": {"description": "Boolean: Height is between 1050-1199mm", "formula": "_CALCULATED_smallest_door_height >= 1050 && _CALCULATED_smallest_door_height <= 1199"}, "_CALCULATED_height_range_1200_1499": {"description": "Boolean: Height is between 1200-1499mm", "formula": "_CALCULATED_smallest_door_height >= 1200 && _CALCULATED_smallest_door_height <= 1499"}, "_CALCULATED_height_range_1500_1799": {"description": "Boolean: Height is between 1500-1799mm", "formula": "_CALCULATED_smallest_door_height >= 1500 && _CALCULATED_smallest_door_height <= 1799"}, "_CALCULATED_height_range_1800_2099": {"description": "Boolean: Height is between 1800-2099mm", "formula": "_CALCULATED_smallest_door_height >= 1800 && _CALCULATED_smallest_door_height <= 2099"}, "_CALCULATED_height_range_2100_plus": {"description": "Boolean: Height is 2100mm or greater", "formula": "_CALCULATED_smallest_door_height >= 2100"}, "_CALCULATED_lock_height_valid_range": {"description": "Boolean: Lock height is within valid range for door height", "formula": "(_CALCULATED_smallest_door_height > 0) && (bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out >= 430) && (bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out <= (_CALCULATED_smallest_door_height - 270))"}, "_CALCULATED_lock_condition_1053": {"description": "Boolean: Lock height >= 1053mm", "formula": "(parseFloat(bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out) || 0) >= 1053"}, "_CALCULATED_lock_condition_halfway_plus_32": {"description": "Boolean: Lock height < halfway point + 32mm", "formula": "(parseFloat(bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out) || 0) < _CALCULATED_halfway_plus_32"}, "_CALCULATED_lock_condition_height_minus_1137": {"description": "Boolean: Lock height <= height - 1137mm", "formula": "(parseFloat(bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out) || 0) <= _CALCULATED_height_minus_1137"}, "_CALCULATED_lock_regular_kit": {"description": "Boolean: Lock height qualifies for regular kit", "formula": "(parseFloat(bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out) || 0) >= _CALCULATED_halfway_plus_79"}, "_CALCULATED_lock_low_kit": {"description": "Boolean: Lock height qualifies for low kit", "formula": "(parseFloat(bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out) || 0) < _CALCULATED_halfway_plus_79"}, "_CALCULATED_unified_lock_condition": {"description": "Boolean: Unified lock condition result", "formula": "_CALCULATED_lock_condition_1053 && _CALCULATED_lock_condition_halfway_plus_32 && _CALCULATED_lock_condition_height_minus_1137"}, "_CALCULATED_is_even_split": {"description": "Boolean: Door split type is even", "formula": "bx_dbl_hinge_door_split_type === 'even'"}, "_CALCULATED_is_uneven_split": {"description": "Boolean: Door split type is uneven", "formula": "bx_dbl_hinge_door_split_type === 'uneven'"}, "_CALCULATED_is_manual_mode": {"description": "Boolean: Deduction assistance is disabled (manual mode)", "formula": "bx_dbl_hinge_deduction_assistance === 'no'"}, "_CALCULATED_deduction_assistance_enabled": {"description": "Boolean: Deduction assistance is enabled", "formula": "bx_dbl_hinge_deduction_assistance === 'yes'"}, "_CALCULATED_door_height_valid": {"description": "Boolean: Door height is valid (greater than 0)", "formula": "_CALCULATED_smallest_door_height > 0"}, "_CALCULATED_lock_height_entered": {"description": "Boolean: Lock height has been entered", "formula": "(parseFloat(bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out) || 0) > 0"}, "_CALCULATED_configuration_complete": {"description": "Boolean: Basic configuration is complete", "formula": "_CALCULATED_door_height_valid && _CALCULATED_lock_height_entered"}, "_CALCULATED_kit_type_recommendation": {"description": "String: Recommended kit type based on lock height", "formula": "_CALCULATED_lock_regular_kit ? 'regular' : (_CALCULATED_lock_low_kit ? 'low' : 'none')"}, "_CALCULATED_FINAL_VISIBILITY_CONDITION": {"description": "Boolean: Final visibility condition for complex components", "formula": "_CALCULATED_unified_lock_condition && _CALCULATED_door_height_valid && _CALCULATED_lock_height_valid_range"}}