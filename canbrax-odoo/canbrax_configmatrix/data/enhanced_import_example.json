{"name": "Enhanced Import Example", "code": "ENHANCED_EXAMPLE", "version": "1.0", "product_template": "example_product", "description": "Example showing enhanced import capabilities with use-case-specific configurations.", "sections": [{"name": "Basic Information", "sequence": 10, "description": "Basic product information with use-case-specific settings.", "fields": [{"name": "Product Quantity", "technical_name": "product_quantity", "field_type": "number", "sequence": 10, "help_text": "General help text for all use cases", "check_measure_help_text": "Check Measure: Enter the quantity to be measured", "sales_help_text": "Sales: Enter the quantity for quotation", "online_help_text": "Online: Select quantity for online ordering", "default_value": 1, "check_measure_default_value": "1", "sales_default_value": "5", "online_default_value": "1", "min_value": 1, "max_value": 100, "check_measure_min_value": "1", "check_measure_max_value": "50", "sales_min_value": "1", "sales_max_value": "100", "online_min_value": "1", "online_max_value": "20"}, {"name": "Product Type", "technical_name": "product_type", "field_type": "selection", "sequence": 20, "help_text": "Select the type of product", "check_measure_help_text": "Check Measure: Select type for measurement purposes", "sales_help_text": "Sales: Select type for pricing", "online_help_text": "Online: Choose your product type", "check_measure_visible": true, "sales_visible": true, "online_visible": true, "options": [{"name": "Standard", "value": "standard", "help_text": "Standard product configuration"}, {"name": "Premium", "value": "premium", "help_text": "Premium product with additional features"}, {"name": "Custom", "value": "custom", "help_text": "Custom configuration - additional charges may apply"}]}, {"name": "Special Instructions", "technical_name": "special_instructions", "field_type": "text", "sequence": 30, "visibility_condition": "product_type == 'custom'", "help_text": "Enter any special instructions for custom products", "check_measure_help_text": "Check Measure: Instructions for measurement team", "sales_help_text": "Sales: Instructions for sales team and pricing", "online_help_text": "Online: Special requirements or notes", "check_measure_visible": true, "sales_visible": true, "online_visible": false}, {"name": "Rush Order", "technical_name": "rush_order", "field_type": "boolean", "sequence": 40, "help_text": "Check if this is a rush order", "boolean_true_label": "Yes - Rush Order", "boolean_false_label": "No - Standard Delivery", "check_measure_default_value": "false", "sales_default_value": "false", "online_default_value": "false", "check_measure_visible": false, "sales_visible": true, "online_visible": true}]}, {"name": "Dimensions", "sequence": 20, "description": "Product dimensions with use-case-specific constraints.", "fields": [{"name": "Height (mm)", "technical_name": "height_mm", "field_type": "number", "sequence": 10, "help_text": "Product height in millimeters", "default_value": 1000, "check_measure_min_value": "100", "check_measure_max_value": "3000", "sales_min_value": "200", "sales_max_value": "2500", "online_min_value": "300", "online_max_value": "2000"}, {"name": "Width (mm)", "technical_name": "width_mm", "field_type": "number", "sequence": 20, "help_text": "Product width in millimeters", "default_value": 800, "check_measure_min_value": "100", "check_measure_max_value": "2500", "sales_min_value": "200", "sales_max_value": "2000", "online_min_value": "300", "online_max_value": "1500"}]}]}