{"_CALCULATED_manual_left_height": {"description": "Input: Manual height for the left door leaf.", "formula": "parseFloat(bx_dbl_hinge_make_left_door_height_mm_manual) || 0"}, "_CALCULATED_manual_right_height": {"description": "Input: Manual height for the right door leaf.", "formula": "parseFloat(bx_dbl_hinge_make_right_door_height_mm_manual) || 0"}, "_CALCULATED_height_calculation_method": {"description": "Input: Method used to determine door height (e.g., 'manual').", "formula": "bx_dbl_hinge_deduction_assistance === 'no' ? 'manual' : (bx_dbl_hinge_door_split_type === 'even' ? 'even' : 'uneven')"}, "_CALCULATED_door_split_type": {"description": "Input: Type of door split (e.g., 'even', 'uneven').", "formula": "bx_dbl_hinge_door_split_type"}, "_CALCULATED_deduction_assistance": {"description": "Input: Whether deduction assistance is used (e.g., 'yes', 'no').", "formula": "bx_dbl_hinge_deduction_assistance"}, "_CALCULATED_lock_height": {"description": "Input: The specified lock height.", "formula": "parseFloat(bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out) || 0"}, "_CALCULATED_smallest_door_height": {"description": "Minimum height of the door leaves.", "formula": "_CALCULATED_height_calculation_method === 'manual' ? Math.min(_CALCULATED_manual_left_height, _CALCULATED_manual_right_height) : (_CALCULATED_height_calculation_method === 'even' ? (parseFloat(bx_dbl_hinge_make_each_door_height_mm_even) || 0) : Math.min(parseFloat(bx_dbl_hinge_make_left_door_height_mm_uneven) || 0, parseFloat(bx_dbl_hinge_make_right_door_height_mm_uneven) || 0))"}, "_CALCULATED_largest_door_height": {"description": "Maximum height of the door leaves.", "formula": "_CALCULATED_height_calculation_method === 'manual' ? Math.max(_CALCULATED_manual_left_height, _CALCULATED_manual_right_height) : (_CALCULATED_height_calculation_method === 'even' ? (parseFloat(bx_dbl_hinge_make_each_door_height_mm_even) || 0) : Math.max(parseFloat(bx_dbl_hinge_make_left_door_height_mm_uneven) || 0, parseFloat(bx_dbl_hinge_make_right_door_height_mm_uneven) || 0))"}, "_CALCULATED_manual_formula": {"description": "String representation of the smallest door height calculation in manual mode.", "formula": "'Math.min(' + _CALCULATED_manual_left_height + ', ' + _CALCULATED_manual_right_height + ') = ' + _CALCULATED_smallest_door_height"}, "_CALCULATED_is_manual_mode": {"description": "Boolean: True if height calculation method is manual.", "formula": "_CALCULATED_height_calculation_method === 'manual'"}, "_CALCULATED_is_even_split": {"description": "Boolean: True if door split type is even.", "formula": "_CALCULATED_door_split_type === 'even'"}, "_CALCULATED_is_uneven_split": {"description": "Boolean: True if door split type is uneven.", "formula": "_CALCULATED_door_split_type === 'uneven'"}, "_CALCULATED_halfway_point": {"description": "Smallest Door Height / 2.", "formula": "_CALCULATED_smallest_door_height / 2"}, "_CALCULATED_halfway_plus_16": {"description": "Smallest Door Height / 2 + 16.", "formula": "_CALCULATED_halfway_point + 16"}, "_CALCULATED_halfway_plus_32": {"description": "Smallest Door Height / 2 + 32.", "formula": "_CALCULATED_halfway_point + 32"}, "_CALCULATED_halfway_plus_79": {"description": "Smallest Door Height / 2 + 79.", "formula": "_CALCULATED_halfway_point + 79"}, "_CALCULATED_halfway_minus_79": {"description": "Smallest Door Height / 2 - 79.", "formula": "_CALCULATED_halfway_point - 79"}, "_CALCULATED_height_minus_270": {"description": "Smallest Door Height - 270.", "formula": "_CALCULATED_smallest_door_height - 270"}, "_CALCULATED_height_minus_317": {"description": "Smallest Door Height - 317.", "formula": "_CALCULATED_smallest_door_height - 317"}, "_CALCULATED_height_minus_330": {"description": "Smallest Door Height - 330.", "formula": "_CALCULATED_smallest_door_height - 330"}, "_CALCULATED_height_minus_333": {"description": "Smallest Door Height - 333.", "formula": "_CALCULATED_smallest_door_height - 333"}, "_CALCULATED_height_minus_349": {"description": "Smallest Door Height - 349.", "formula": "_CALCULATED_smallest_door_height - 349"}, "_CALCULATED_height_minus_428": {"description": "Smallest Door Height - 428.", "formula": "_CALCULATED_smallest_door_height - 428"}, "_CALCULATED_height_minus_740": {"description": "Smallest Door Height - 740.", "formula": "_CALCULATED_smallest_door_height - 740"}, "_CALCULATED_height_minus_787": {"description": "Smallest Door Height - 787.", "formula": "_CALCULATED_smallest_door_height - 787"}, "_CALCULATED_height_minus_800": {"description": "Smallest Door Height - 800.", "formula": "_CALCULATED_smallest_door_height - 800"}, "_CALCULATED_height_minus_803": {"description": "Smallest Door Height - 803.", "formula": "_CALCULATED_smallest_door_height - 803"}, "_CALCULATED_height_minus_819": {"description": "Smallest Door Height - 819.", "formula": "_CALCULATED_smallest_door_height - 819"}, "_CALCULATED_height_minus_898": {"description": "Smallest Door Height - 898.", "formula": "_CALCULATED_smallest_door_height - 898"}, "_CALCULATED_height_minus_940": {"description": "Smallest Door Height - 940.", "formula": "_CALCULATED_smallest_door_height - 940"}, "_CALCULATED_height_minus_987": {"description": "Smallest Door Height - 987.", "formula": "_CALCULATED_smallest_door_height - 987"}, "_CALCULATED_height_minus_1000": {"description": "Smallest Door Height - 1000.", "formula": "_CALCULATED_smallest_door_height - 1000"}, "_CALCULATED_height_minus_1003": {"description": "Smallest Door Height - 1003.", "formula": "_CALCULATED_smallest_door_height - 1003"}, "_CALCULATED_height_minus_1019": {"description": "Smallest Door Height - 1019.", "formula": "_CALCULATED_smallest_door_height - 1019"}, "_CALCULATED_height_minus_1090": {"description": "Smallest Door Height - 1090.", "formula": "_CALCULATED_smallest_door_height - 1090"}, "_CALCULATED_height_minus_1098": {"description": "Smallest Door Height - 1098.", "formula": "_CALCULATED_smallest_door_height - 1098"}, "_CALCULATED_height_minus_1137": {"description": "Smallest Door Height - 1137.", "formula": "_CALCULATED_smallest_door_height - 1137"}, "_CALCULATED_height_minus_1153": {"description": "Smallest Door Height - 1153.", "formula": "_CALCULATED_smallest_door_height - 1153"}, "_CALCULATED_height_minus_1169": {"description": "Smallest Door Height - 1169.", "formula": "_CALCULATED_smallest_door_height - 1169"}, "_CALCULATED_height_minus_1248": {"description": "Smallest Door Height - 1248.", "formula": "_CALCULATED_smallest_door_height - 1248"}}