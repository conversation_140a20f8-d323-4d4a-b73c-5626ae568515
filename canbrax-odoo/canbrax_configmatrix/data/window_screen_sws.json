{"name": "SWS-WSCRN Window Screen Configuration", "code": "SWS_WSCRN", "version": "1.0", "product_template": "sws_wscrn_template", "description": "Configuration for SWS-WSCRN Window Screens.", "sections": [{"name": "General Information", "sequence": 10, "description": "Basic information about the window screen.", "fields": [{"name": "SWS-WSCRN Location", "technical_name": "sws_wscrn_location", "field_type": "selection", "sequence": 10, "options": [{"name": "Alfresco", "value": "alfresco"}, {"name": "Art Studio", "value": "art_studio"}, {"name": "Balcony", "value": "balcony"}, {"name": "Bathroom 1", "value": "bathroom_1"}, {"name": "Bathroom 2", "value": "bathroom_2"}, {"name": "Bathroom 3", "value": "bathroom_3"}, {"name": "Bathroom 4", "value": "bathroom_4"}, {"name": "Bathroom 5", "value": "bathroom_5"}, {"name": "BBQ", "value": "bbq"}, {"name": "Bed 1", "value": "bed_1"}, {"name": "Bed 2", "value": "bed_2"}, {"name": "Bed 3", "value": "bed_3"}, {"name": "Bed 4", "value": "bed_4"}, {"name": "Bed 5", "value": "bed_5"}, {"name": "Bed 6", "value": "bed_6"}, {"name": "Bed 7", "value": "bed_7"}, {"name": "Bed 8", "value": "bed_8"}, {"name": "Bed 9", "value": "bed_9"}, {"name": "Billiards Room", "value": "billiards_room"}, {"name": "Bonus Room", "value": "bonus_room"}, {"name": "Breakfast Nook", "value": "breakfast_nook"}, {"name": "<PERSON><PERSON>", "value": "butlers_pantry"}, {"name": "Cellar", "value": "cellar"}, {"name": "Clerestory", "value": "clerestory"}, {"name": "Closet", "value": "closet"}, {"name": "Conservatory", "value": "conservatory"}, {"name": "Corridor", "value": "corridor"}, {"name": "Courtyard", "value": "courtyard"}, {"name": "Craft Room", "value": "craft_room"}, {"name": "Den", "value": "den"}, {"name": "Dining Room", "value": "dining_room"}, {"name": "Dressing Room", "value": "dressing_room"}, {"name": "Ensuite Bed 1", "value": "ensuite_bed_1"}, {"name": "Ensuite Bed 2", "value": "ensuite_bed_2"}, {"name": "Ensuite Bed 3", "value": "ensuite_bed_3"}, {"name": "Ensuite Bed 4", "value": "ensuite_bed_4"}, {"name": "Ensuite Bed 5", "value": "ensuite_bed_5"}, {"name": "Ensuite Bed 6", "value": "ensuite_bed_6"}, {"name": "Ensuite Bed 7", "value": "ensuite_bed_7"}, {"name": "Ensuite Bed 8", "value": "ensuite_bed_8"}, {"name": "Ensuite Bed 9", "value": "ensuite_bed_9"}, {"name": "Ensuite Hers", "value": "ensuite_hers"}, {"name": "Ensuite His", "value": "ensuite_his"}, {"name": "Entry", "value": "entry"}, {"name": "Entryway", "value": "entryway"}, {"name": "Exercise Room", "value": "exercise_room"}, {"name": "Family Room", "value": "family_room"}, {"name": "Formal Dining Room", "value": "formal_dining_room"}, {"name": "<PERSON><PERSON><PERSON>", "value": "foyer"}, {"name": "Front Door", "value": "front_door"}, {"name": "Games Room", "value": "games_room"}, {"name": "Games Room Bathroom", "value": "games_room_bathroom"}, {"name": "Games Room Storage", "value": "games_room_storage"}, {"name": "Games Room Toilet", "value": "games_room_toilet"}, {"name": "Games Room WC", "value": "games_room_wc"}, {"name": "Games Room WIR", "value": "games_room_wir"}, {"name": "Garage", "value": "garage"}, {"name": "Granny Flat", "value": "granny_flat"}, {"name": "Granny Flat Bathroom", "value": "granny_flat_bathroom"}, {"name": "Granny Flat Bedroom", "value": "granny_flat_bedroom"}, {"name": "Granny Flat Ensuite", "value": "granny_flat_ensuite"}, {"name": "Granny Flat Kitchen", "value": "granny_flat_kitchen"}, {"name": "Granny Flat Laundry", "value": "granny_flat_laundry"}, {"name": "Granny Flat Living", "value": "granny_flat_living"}, {"name": "Granny Flat Lounge", "value": "granny_flat_lounge"}, {"name": "<PERSON> Flat Toilet", "value": "granny_flat_toilet"}, {"name": "Granny Flat WC", "value": "granny_flat_wc"}, {"name": "Granny Flat WIR", "value": "granny_flat_wir"}, {"name": "Guest Bed", "value": "guest_bed"}, {"name": "Guest <PERSON>", "value": "guest_bed_ensutie"}, {"name": "Guest <PERSON>", "value": "guest_bed_toilet"}, {"name": "Guest Bed WIR", "value": "guest_bed_wir"}, {"name": "Gym", "value": "gym"}, {"name": "Hallway", "value": "hallway"}, {"name": "Home Bar", "value": "home_bar"}, {"name": "Home Office", "value": "home_office"}, {"name": "Home Spa", "value": "home_spa"}, {"name": "Home Theatre", "value": "home_theatre"}, {"name": "Kids Bed", "value": "kids_bed"}, {"name": "Kids Living Room", "value": "kids_living_room"}, {"name": "Kids Lounge Room", "value": "kids_lounge_room"}, {"name": "Kids Rumpus Room", "value": "kids_rumpus_room"}, {"name": "Kitchen", "value": "kitchen"}, {"name": "<PERSON><PERSON><PERSON>", "value": "laundry"}, {"name": "Library", "value": "library"}, {"name": "Linen Room", "value": "linen_room"}, {"name": "Living Room", "value": "living_room"}, {"name": "Loft", "value": "loft"}, {"name": "Lounge Room", "value": "lounge_room"}, {"name": "Man Cave", "value": "man_cave"}, {"name": "Man Cave Bathroom", "value": "man_cave_bathroom"}, {"name": "Man Cave Storage", "value": "man_cave_storage"}, {"name": "Man <PERSON> Toilet", "value": "man_cave_toilet"}, {"name": "Man Cave WC", "value": "man_cave_wc"}, {"name": "Master Bed", "value": "master_bed"}, {"name": "Master Bed Ensuite", "value": "master_bed_ensuite"}, {"name": "Master <PERSON>", "value": "master_bed_toilet"}, {"name": "Master Bed WC", "value": "master_bed_wc"}, {"name": "Master Bed WIR", "value": "master_bed_wir"}, {"name": "Meals Room", "value": "meals_room"}, {"name": "Mechanical Room", "value": "mechanical_room"}, {"name": "Media Room", "value": "media_room"}, {"name": "Meditation Room", "value": "meditation_room"}, {"name": "Mezzanine", "value": "mezzanine"}, {"name": "<PERSON><PERSON><PERSON>", "value": "mudroom"}, {"name": "Music Room", "value": "music_room"}, {"name": "Nursery", "value": "nursery"}, {"name": "Observatory", "value": "observatory"}, {"name": "Office", "value": "office"}, {"name": "Outdoor Kitchen", "value": "outdoor_kitchen"}, {"name": "Pantry", "value": "pantry"}, {"name": "<PERSON><PERSON>", "value": "patio"}, {"name": "Pet Room", "value": "pet_room"}, {"name": "Playroom", "value": "playroom"}, {"name": "Pool", "value": "pool"}, {"name": "Pool House", "value": "pool_house"}, {"name": "Porch", "value": "porch"}, {"name": "Powder Room", "value": "powder_room"}, {"name": "Rumpus Room", "value": "rumpus_room"}, {"name": "Safe Room", "value": "safe_room"}, {"name": "Scullery", "value": "scullery"}, {"name": "<PERSON><PERSON>", "value": "servery"}, {"name": "She Shed", "value": "she_shed"}, {"name": "She Shed Bathroom", "value": "she_shed_bathroom"}, {"name": "She Shed Storage", "value": "she_shed_storage"}, {"name": "She Shed <PERSON>", "value": "she_shed_toilet"}, {"name": "She Shed WC", "value": "she_shed_wc"}, {"name": "Shed", "value": "shed"}, {"name": "Spare Room", "value": "spare_room"}, {"name": "Spare Room Ensuite", "value": "spare_room_ensuite"}, {"name": "Spare Room Toilet", "value": "spare_room_toilet"}, {"name": "Spare Room WC", "value": "spare_room_wc"}, {"name": "Spare Room WIR", "value": "spare_room_wir"}, {"name": "Stairs", "value": "stairs"}, {"name": "Storage Room", "value": "storage_room"}, {"name": "Study", "value": "study"}, {"name": "Study Nook", "value": "study_nook"}, {"name": "Sun Room", "value": "sun_room"}, {"name": "Toilet 1", "value": "toilet_1"}, {"name": "Toilet 2", "value": "toilet_2"}, {"name": "Toilet 3", "value": "toilet_3"}, {"name": "Toilet 4", "value": "toilet_4"}, {"name": "Toilet 5", "value": "toilet_5"}, {"name": "Toilet Bed 1", "value": "toilet_bed_1"}, {"name": "Toilet Bed 2", "value": "toilet_bed_2"}, {"name": "Toilet Bed 3", "value": "toilet_bed_3"}, {"name": "Toilet Bed 4", "value": "toilet_bed_4"}, {"name": "Toilet Bed 5", "value": "toilet_bed_5"}, {"name": "Toilet Bed 6", "value": "toilet_bed_6"}, {"name": "Toilet Bed 7", "value": "toilet_bed_7"}, {"name": "Toilet Bed 8", "value": "toilet_bed_8"}, {"name": "Toilet Bed 9", "value": "toilet_bed_9"}, {"name": "Utility Room", "value": "utility_room"}, {"name": "WC 1", "value": "wc_1"}, {"name": "WC 2", "value": "wc_2"}, {"name": "WC 3", "value": "wc_3"}, {"name": "WC 4", "value": "wc_4"}, {"name": "WC 5", "value": "wc_5"}, {"name": "WC Bed 1", "value": "wc_bed_1"}, {"name": "WC Bed 2", "value": "wc_bed_2"}, {"name": "WC Bed 3", "value": "wc_bed_3"}, {"name": "WC Bed 4", "value": "wc_bed_4"}, {"name": "WC Bed 5", "value": "wc_bed_5"}, {"name": "WC Bed 6", "value": "wc_bed_6"}, {"name": "WC Bed 7", "value": "wc_bed_7"}, {"name": "WC Bed 8", "value": "wc_bed_8"}, {"name": "WC Bed 9", "value": "wc_bed_9"}, {"name": "Wine Cellar", "value": "wine_cellar"}, {"name": "Wine Tasting Room", "value": "wine_tasting_room"}, {"name": "WIR Bed 1", "value": "wir_bed_1"}, {"name": "WIR Bed 2", "value": "wir_bed_2"}, {"name": "WIR Bed 3", "value": "wir_bed_3"}, {"name": "WIR Bed 4", "value": "wir_bed_4"}, {"name": "WIR Bed 5", "value": "wir_bed_5"}, {"name": "WIR Bed 6", "value": "wir_bed_6"}, {"name": "WIR Bed 7", "value": "wir_bed_7"}, {"name": "WIR Bed 8", "value": "wir_bed_8"}, {"name": "WIR Bed 9", "value": "wir_bed_9"}, {"name": "Work Shop", "value": "work_shop"}]}, {"name": "SWS-WSCRN Quantity", "technical_name": "sws_wscrn_quantity", "field_type": "number", "sequence": 11, "default_value": 1}]}, {"name": "Frame & Dimensions", "sequence": 20, "description": "Define frame color and screen dimensions.", "fields": [{"name": "SWS-WSCRN Frame Colour", "technical_name": "sws_wscrn_frame_colour", "field_type": "selection", "sequence": 10, "options": [{"name": "Black Custom Matt GN248A", "value": "black_custom_matt_gn248a"}, {"name": "Clear Anodise 15mm", "value": "clear_anodise_15mm", "help_text": "(NOTE: Mesh Can NOT be Tensioned)"}, {"name": "Light Bronze Anodise 15mm", "value": "light_bronze_anodise_15mm"}, {"name": "Monument Matt GL229A", "value": "monument_matt_gl229a"}, {"name": "Pearl White Gloss GA078A", "value": "pearl_white_gloss_ga078a"}, {"name": "Primrose Gloss GD037A", "value": "primrose_gloss_gd037a"}, {"name": "Stone Beige Matt GD247A", "value": "stone_beige_matt_gd247a"}, {"name": "Surfmist Matt GA236A", "value": "surfmist_matt_ga236a"}, {"name": "Ultra Silver Gloss GY070A", "value": "ultra_silver_gloss_gy070a"}, {"name": "White Birch Gloss GA057A", "value": "white_birch_gloss_ga057a"}, {"name": "Woodland Grey Matt GL205A", "value": "woodland_grey_matt_gl205a"}, {"name": "Mill Finish", "value": "mill_finish"}, {"name": "Anodic Bronze Satin GY114A", "value": "anodic_bronze_satin_gy114a"}, {"name": "Anodic <PERSON> Grey Matt GL213A", "value": "anodic_dark_grey_matt_gl213a"}, {"name": "Anodic Natural Matt GY235A", "value": "anodic_natural_matt_gy235a"}, {"name": "Anodic Off White Matt GD227A", "value": "anodic_off_white_matt_gd227a"}, {"name": "Anodic Silver Grey Matt GL237A", "value": "anodic_silver_grey_matt_gl237a"}, {"name": "APO Grey Satin GL107A", "value": "apo_grey_satin_gl107a"}, {"name": "Charcoal Satin GL180A", "value": "charcoal_satin_gl180a"}, {"name": "Deep Ocean Matt GJ203A", "value": "deep_ocean_matt_gj203a"}, {"name": "Doeskin Satin GD188A", "value": "doeskin_satin_gd188a"}, {"name": "<PERSON>ne Matt GL252A", "value": "dune_matt_gl252a"}, {"name": "Hamersley Brown Satin GM100A", "value": "hamersley_brown_satin_gm100a"}, {"name": "Hawthorn Green Gloss GK030A", "value": "hawthorn_green_gloss_gk030a"}, {"name": "Ironstone Matt GL236A", "value": "ironstone_matt_gl236a"}, {"name": "Jasper Satin GT114A", "value": "jasper_satin_gt114a"}, {"name": "Paperbark Satin GU114A", "value": "paperbark_satin_gu114a"}, {"name": "Pottery Satin GM175A", "value": "pottery_satin_gm175a"}, {"name": "Precious Silver Kinetic Pearl Satin 971-7043K", "value": "precious_silver_kinetic_pearl_satin_971_7043k"}, {"name": "Shale Grey Satin GP184A", "value": "shale_grey_satin_gp184a"}, {"name": "Textura Black GN305A", "value": "textura_black_gn305a"}, {"name": "Western Red Cedar MZ306A4489V5", "value": "western_red_cedar_mz306a4489v5"}, {"name": "Powder Coat", "value": "powder_coat"}]}, {"name": "Powder Coat Colour", "technical_name": "sws_wscrn_powder_coat_colour", "field_type": "selection", "sequence": 11, "visibility_condition": "sws_wscrn_frame_colour == 'powder_coat'", "help_text": "*Subject to Availability", "options": [{"name": "Admiralty Gloss 6131G", "value": "admiralty_gloss_6131g"}, {"name": "Almond Ivory Gloss GD016A", "value": "almond_ivory_gloss_gd016a"}, {"name": "Anodic Champagne Matt GY276A", "value": "anodic_champagne_matt_gy276a"}, {"name": "Anodic Clear Matt GY221A", "value": "anodic_clear_matt_gy221a"}, {"name": "Asteroid Peral Matt GY270A", "value": "asteroid_peral_matt_gy270a"}, {"name": "Aubergine Satin 84736", "value": "aubergine_satin_84736"}, {"name": "Azure Grey Satin 36603", "value": "azure_grey_satin_36603"}, {"name": "Barley Gloss GD007A", "value": "barley_gloss_gd007a"}, {"name": "Barrister White Satin 84672", "value": "barrister_white_satin_84672"}, {"name": "Basalt Matt GP208A", "value": "basalt_matt_gp208a"}, {"name": "Berry Grey Gloss 7262G", "value": "berry_grey_gloss_7262g"}, {"name": "Black Onyx Gloss GY042A", "value": "black_onyx_gloss_gy042a"}, {"name": "Black Satin GN150A", "value": "black_satin_gn150a"}, {"name": "Blue Ridge Satin 88480", "value": "blue_ridge_satin_88480"}, {"name": "Bright White Gloss GA030A", "value": "bright_white_gloss_ga030a"}, {"name": "Bronze <PERSON> 32348", "value": "bronze_olive_matt_32348"}, {"name": "Bronze Pearl Satin 94686", "value": "bronze_pearl_satin_94686"}, {"name": "Bushland Matt GK203A", "value": "bushland_matt_gk203a"}, {"name": "Canola Cream Gloss 81796", "value": "canola_cream_gloss_81796"}, {"name": "Champagne Kinetic 3059K", "value": "champagne_kinetic_3059k"}, {"name": "Charcoal Metallic Gloss GM019A", "value": "charcoal_metallic_gloss_gm019a"}, {"name": "Charcoal Metallic Pearl Gloss 18796", "value": "charcoal_metallic_pearl_gloss_18796"}, {"name": "Charcoal Pearl Matt GY237A", "value": "charcoal_pearl_matt_gy237a"}, {"name": "Citi Matt GL211A", "value": "citi_matt_gl211a"}, {"name": "Citi Pearl Matt 88471", "value": "citi_pearl_matt_88471"}, {"name": "Claret Satin GG142A", "value": "claret_satin_gg142a"}, {"name": "Classic Cream Matt GD245A", "value": "classic_cream_matt_gd245a"}, {"name": "Claypot Satin 72165", "value": "claypot_satin_72165"}, {"name": "Copper Kinetic 7183K", "value": "copper_kinetic_7183k"}, {"name": "Cottage Green Matt GK274A", "value": "cottage_green_matt_gk274a"}, {"name": "Cove Matt GD274A", "value": "cove_matt_gd274a"}, {"name": "Deep Brunswick Green Gloss 6134G", "value": "deep_brunswick_green_gloss_6134g"}, {"name": "Estate Mtt 89743", "value": "estate_mtt_89743"}, {"name": "Evening Haze Matt GM235A", "value": "evening_haze_matt_gm235a"}, {"name": "French Champagne Matt GX207C", "value": "french_champagne_matt_gx207c"}, {"name": "Gold Pearl Satin 96604", "value": "gold_pearl_satin_96604"}, {"name": "Grey Nurse Gloss 50060", "value": "grey_nurse_gloss_50060"}, {"name": "Gully Matt GM242A", "value": "gully_matt_gm242a"}, {"name": "Gunmetal Kinetic 8115K", "value": "gunmetal_kinetic_8115k"}, {"name": "Headland Matt GG219A", "value": "headland_matt_gg219a"}, {"name": "He<PERSON> 87336", "value": "hedge_matt_87336"}, {"name": "Heritage Green Gloss GK044A", "value": "heritage_green_gloss_gk044a"}, {"name": "Horizon Blue Gloss 33344", "value": "horizon_blue_gloss_33344"}, {"name": "Hunter Red Satin 84209", "value": "hunter_red_satin_84209"}, {"name": "Ivory Coast Gloss 3061G", "value": "ivory_coast_gloss_3061g"}, {"name": "Light Grey Gloss 7263G", "value": "light_grey_gloss_7263g"}, {"name": "Loft Matt 8106M", "value": "loft_matt_8106m"}, {"name": "Magnolia Gloss GD025A", "value": "magnolia_gloss_gd025a"}, {"name": "Mangrove Matt GK277A", "value": "mangrove_matt_gk277a"}, {"name": "Manor Red Matt GG262A", "value": "manor_red_matt_gg262a"}, {"name": "Moss Vale Sands Satin 33019", "value": "moss_vale_sands_satin_33019"}, {"name": "Metropolis Bronze Pearl Satin 59003", "value": "metropolis_bronze_pearl_satin_59003"}, {"name": "Metropolis Silver Glow Pearl Gloss 84678", "value": "metropolis_silver_glow_pearl_gloss_84678"}, {"name": "Metropolis Storm Pearl Satin 88471", "value": "metropolis_storm_pearl_satin_88471"}, {"name": "<PERSON> of Pearl Gloss 84684", "value": "mother_of_pearl_gloss_84684"}, {"name": "Natural Pearl Matt 89119", "value": "natural_pearl_matt_89119"}, {"name": "Natural Pearl Matt 97189119", "value": "natural_pearl_matt_97189119"}, {"name": "Nickel Pearl Matt 88360", "value": "nickel_pearl_matt_88360"}, {"name": "Night Sky Matt GN231A", "value": "night_sky_matt_gn231a"}, {"name": "Nobel Silver Pearl Satin GY119A", "value": "nobel_silver_pearl_satin_gy119a"}, {"name": "Notre Dame Gloss GL040A", "value": "notre_dame_gloss_gl040a"}, {"name": "Ocean Mist Satin 6132S", "value": "ocean_mist_satin_6132s"}, {"name": "Off White Matt GA211A", "value": "off_white_matt_ga211a"}, {"name": "Olde Pewter Satin GL175A", "value": "olde_pewter_satin_gl175a"}, {"name": "Onyx Pearl Gloss 52052", "value": "onyx_pearl_gloss_52052"}, {"name": "Oyster Grey Matt GL258A", "value": "oyster_grey_matt_gl258a"}, {"name": "Pale Eucalypt Matt GK236A", "value": "pale_eucalypt_matt_gk236a"}, {"name": "Palladium Silver Pearl Satin GY184C", "value": "palladium_silver_pearl_satin_gy184c"}, {"name": "Pandemonium Silver Flat 7004F", "value": "pandemonium_silver_flat_7004f"}, {"name": "Periwinkle Gloss 50276", "value": "periwinkle_gloss_50276"}, {"name": "Pewter Pearl Satin 88202", "value": "pewter_pearl_satin_88202"}, {"name": "Platypus Kinetic 7214K", "value": "platypus_kinetic_7214k"}, {"name": "Regency Grey Matt 50278", "value": "regency_grey_matt_50278"}, {"name": "Rivergum Beige Gloss 36991", "value": "rivergum_beige_gloss_36991"}, {"name": "Rivergum Gloss GD042A", "value": "rivergum_gloss_gd042a"}, {"name": "Riversand Matt 36656", "value": "riversand_matt_36656"}, {"name": "Riversand Matt GM204A", "value": "riversand_matt_gm204a"}, {"name": "Roseberry Grey Gloss GL015A", "value": "roseberry_grey_gloss_gl015a"}, {"name": "Sand Satin 51438", "value": "sand_satin_51438"}, {"name": "Sandbank Matt 3003M", "value": "sandbank_matt_3003m"}, {"name": "Shoji White Satin GA160A", "value": "shoji_white_satin_ga160a"}, {"name": "Smokey Glass Satin 78292", "value": "smokey_glass_satin_78292"}, {"name": "Space Blue Gloss MJ008A", "value": "space_blue_gloss_mj008a"}, {"name": "St Elmos Fire Kinetic 7208K", "value": "st_elmos_fire_kinetic_7208k"}, {"name": "Steel Pearl Satin 57127", "value": "steel_pearl_satin_57127"}, {"name": "Stone Grey Satin 87126", "value": "stone_grey_satin_87126"}, {"name": "Storm Front Matt GL249A", "value": "storm_front_matt_gl249a"}, {"name": "Storm Grey N42 Gloss GL060A", "value": "storm_grey_n42_gloss_gl060a"}, {"name": "Stromboli Satin GK148A", "value": "stromboli_satin_gk148a"}, {"name": "Teal Gloss 6133G", "value": "teal_gloss_6133g"}, {"name": "Terrain Satin GM143A", "value": "terrain_satin_gm143a"}, {"name": "Textura Deep Ocean GJ301A", "value": "textura_deep_ocean_gj301a"}, {"name": "Textura Dune GL352A", "value": "textura_dune_gl352a"}, {"name": "Textura Ironstone GL336A", "value": "textura_ironstone_gl336a"}, {"name": "Textura Jasper GM314A", "value": "textura_jasper_gm314a"}, {"name": "Textura Monument GL329A", "value": "textura_monument_gl329a"}, {"name": "Textura Paperbark GD314A", "value": "textura_paperbark_gd314a"}, {"name": "Textura Primrose GD331A", "value": "textura_primrose_gd331a"}, {"name": "Textura Sable Bass GN297A", "value": "textura_sable_bass_gn297a"}, {"name": "Textura Silver GY35LA", "value": "textura_silver_gy35la"}, {"name": "Textura White GA330A", "value": "textura_white_ga330a"}, {"name": "Textura Woodland Grey GL333A", "value": "textura_woodland_grey_gl333a"}, {"name": "Transformer Grey Gloss GL031A", "value": "transformer_grey_gloss_gl031a"}, {"name": "Wallaby Matt GL210A", "value": "wallaby_matt_gl210a"}, {"name": "Wedgewood Satin 50279", "value": "wedgewood_satin_50279"}, {"name": "White Satin GA124A", "value": "white_satin_ga124a"}, {"name": "Wilderness Matt GK289A", "value": "wilderness_matt_gk289a"}, {"name": "Windspray Matt GL266A", "value": "windspray_matt_gl266a"}, {"name": "Wizard Gloss 50283", "value": "wizard_gloss_50283"}, {"name": "Custom Powder Coat", "value": "custom_powder_coat"}]}, {"name": "Custom Powder Coat - Colour Name", "technical_name": "sws_wscrn_custom_powder_coat_name", "field_type": "text", "sequence": 12, "visibility_condition": "sws_wscrn_powder_coat_colour == 'custom_powder_coat'"}, {"name": "Custom Powder Coat - Colour Finish", "technical_name": "sws_wscrn_custom_powder_coat_finish", "field_type": "text", "sequence": 13, "visibility_condition": "sws_wscrn_powder_coat_colour == 'custom_powder_coat'"}, {"name": "Custom Powder Coat - Colour Code", "technical_name": "sws_wscrn_custom_powder_coat_code", "field_type": "text", "sequence": 14, "visibility_condition": "sws_wscrn_powder_coat_colour == 'custom_powder_coat'"}, {"name": "SWS-WSCRN Hand Built Window Screen?", "technical_name": "sws_wscrn_hand_built_window_screen", "field_type": "selection", "sequence": 15, "options": [{"name": "Yes (Mesh can NOT be Tensioned)", "value": "yes", "help_text": "*Mesh can NOT be Tensioned, Mitred Corners will not be perfect & Surcharge applies to this selection"}, {"name": "No (Square Corners, Opposing Sides Equal Dimensions)", "value": "no"}]}, {"name": "Type of Hand Built Window Screen", "technical_name": "sws_wscrn_hand_built_type", "field_type": "selection", "sequence": 16, "visibility_condition": "sws_wscrn_hand_built_window_screen == 'yes'", "options": [{"name": "Angled / Out of Square or Bow / Different Heights or Widths", "value": "angled_out_of_square_bow_different_heights_widths", "help_text": "*Open Diagram of Dimensions needed (Link to Open PDF attachment)"}, {"name": "Less Than or Greater Than 4 Corners (< 4 Corners <)", "value": "less_than_greater_than_4_corners"}]}, {"name": "SQUARE Window Screen MAKE Height (mm)", "technical_name": "sws_wscrn_square_make_height_mm", "field_type": "number", "sequence": 17, "default_value": 1, "visibility_condition": "sws_wscrn_hand_built_window_screen == 'yes' && sws_wscrn_hand_built_type == 'angled_out_of_square_bow_different_heights_widths'", "help_text": "*Min: 150mm - Max: 3620mm *Window Screen Height must be between 150 - 3620mm"}, {"name": "SQUARE Window Screen MAKE Width (mm)", "technical_name": "sws_wscrn_square_make_width_mm", "field_type": "number", "sequence": 18, "default_value": 1, "visibility_condition": "sws_wscrn_hand_built_window_screen == 'yes' && sws_wscrn_hand_built_type == 'angled_out_of_square_bow_different_heights_widths'", "help_text": "*Min: 150mm - Max: 3620mm if Window Screen Height <= 3295mm, 3295mm if Window Screen Height is >3295mm *Window Screen Width must be between 150 - 3620mm"}, {"name": "TOP LEFT TOP Out of Square Dimension (+/- mm)", "technical_name": "sws_wscrn_top_left_top_out_of_square_dimension_mm", "field_type": "number", "sequence": 19, "default_value": 0, "visibility_condition": "sws_wscrn_hand_built_window_screen == 'yes' && sws_wscrn_hand_built_type == 'angled_out_of_square_bow_different_heights_widths'", "help_text": "*Enter 0 for below dimensions if corner or side matches Square Window Screen *Min: -200mm & Window Screen Maintains Height >= 150mm to Max: 200mm *TOP LEFT TOP Out of Square Dimension must be between -200 to 200mm & Maintain a mimimum Height of 150mm. Please upload a dimensioned diagram under the < 4 Corners < selection if these criteria are not met."}, {"name": "TOP LEFT SIDE Out of Square Dimension (+/- mm)", "technical_name": "sws_wscrn_top_left_side_out_of_square_dimension_mm", "field_type": "number", "sequence": 20, "default_value": 0, "visibility_condition": "sws_wscrn_hand_built_window_screen == 'yes' && sws_wscrn_hand_built_type == 'angled_out_of_square_bow_different_heights_widths'", "help_text": "*Min: -200mm & Window Screen Maintains Width >= 150mm to Max: 200mm *TOP LEFT SIDE Out of Square Dimension must be between -200 to 200mm & Maintain a mimimum Width of 150mm. Please upload a dimensioned diagram under the < 4 Corners < selection if these criteria are not met."}, {"name": "MIDDLE LEFT SIDE Out of Square Dimension (+/- mm)", "technical_name": "sws_wscrn_middle_left_side_out_of_square_dimension_mm", "field_type": "number", "sequence": 21, "default_value": 0, "visibility_condition": "sws_wscrn_hand_built_window_screen == 'yes' && sws_wscrn_hand_built_type == 'angled_out_of_square_bow_different_heights_widths'", "help_text": "*Min: -200mm & Window Screen Maintains Width >= 150mm to Max: 200mm *MIDDLE LEFT SIDE Out of Square Dimension must be between -200 to 200mm & Maintain a mimimum Width of 150mm. Please upload a dimensioned diagram under the < 4 Corners < selection if these criteria are not met."}, {"name": "BOTTOM LEFT SIDE Out of Square Dimension (+/- mm)", "technical_name": "sws_wscrn_bottom_left_side_out_of_square_dimension_mm", "field_type": "number", "sequence": 22, "default_value": 0, "visibility_condition": "sws_wscrn_hand_built_window_screen == 'yes' && sws_wscrn_hand_built_type == 'angled_out_of_square_bow_different_heights_widths'", "help_text": "*Min: -200mm & Window Screen Maintains Width >= 150mm to Max: 200mm *BOTTOM LEFT SIDE Out of Square Dimension must be between -200 to 200mm & Maintain a mimimum Width of 150mm. Please upload a dimensioned diagram under the < 4 Corners < selection if these criteria are not met."}, {"name": "BOTTOM LEFT BOTTOM Out of Square Dimension (+/- mm)", "technical_name": "sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm", "field_type": "number", "sequence": 23, "default_value": 0, "visibility_condition": "sws_wscrn_hand_built_window_screen == 'yes' && sws_wscrn_hand_built_type == 'angled_out_of_square_bow_different_heights_widths'", "help_text": "*Min: -200mm & Window Screen Maintains Height >= 150mm to Max: 200mm *BOTTOM LEFT BOTTOM Out of Square Dimension must be between -200 to 200mm & Maintain a mimimum Height of 150mm. Please upload a dimensioned diagram under the < 4 Corners < selection if these criteria are not met."}, {"name": "TOP RIGHT TOP Out of Square Dimension (+/- mm)", "technical_name": "sws_wscrn_top_right_top_out_of_square_dimension_mm", "field_type": "number", "sequence": 24, "default_value": 0, "visibility_condition": "sws_wscrn_hand_built_window_screen == 'yes' && sws_wscrn_hand_built_type == 'angled_out_of_square_bow_different_heights_widths'", "help_text": "*Min: -200mm & Window Screen Maintains Height >= 150mm to Max: 200mm *TOP RIGHT TOP Out of Square Dimension must be between -200 to 200mm & Maintain a mimimum Height of 150mm. Please upload a dimensioned diagram under the < 4 Corners < selection if these criteria are not met."}, {"name": "TOP RIGHT SIDE Out of Square Dimension (+/- mm)", "technical_name": "sws_wscrn_top_right_side_out_of_square_dimension_mm", "field_type": "number", "sequence": 25, "default_value": 0, "visibility_condition": "sws_wscrn_hand_built_window_screen == 'yes' && sws_wscrn_hand_built_type == 'angled_out_of_square_bow_different_heights_widths'", "help_text": "*Min: -200mm & Window Screen Maintains Width >= 150mm to Max: 200mm *TOP RIGHT SIDE Out of Square Dimension must be between -200 to 200mm & Maintain a mimimum Width of 150mm. Please upload a dimensioned diagram under the < 4 Corners < selection if these criteria are not met."}, {"name": "MIDDLE RIGHT SIDE Out of Square Dimension (+/- mm)", "technical_name": "sws_wscrn_middle_right_side_out_of_square_dimension_mm", "field_type": "number", "sequence": 26, "default_value": 0, "visibility_condition": "sws_wscrn_hand_built_window_screen == 'yes' && sws_wscrn_hand_built_type == 'angled_out_of_square_bow_different_heights_widths'", "help_text": "*Min: -200mm & Window Screen Maintains Width >= 150mm to Max: 200mm *MIDDLE RIGHT SIDE Out of Square Dimension must be between -200 to 200mm & Maintain a mimimum Width of 150mm. Please upload a dimensioned diagram under the < 4 Corners < selection if these criteria are not met."}, {"name": "BOTTOM RIGHT SIDE Out of Square Dimension (+/- mm)", "technical_name": "sws_wscrn_bottom_right_side_out_of_square_dimension_mm", "field_type": "number", "sequence": 27, "default_value": 0, "visibility_condition": "sws_wscrn_hand_built_window_screen == 'yes' && sws_wscrn_hand_built_type == 'angled_out_of_square_bow_different_heights_widths'", "help_text": "*Min: -200mm & Window Screen Maintains Width >= 150mm to Max: 200mm *BOTTOM RIGHT SIDE Out of Square Dimension must be between -200 to 200mm & Maintain a mimimum Width of 150mm. Please upload a dimensioned diagram under the < 4 Corners < selection if these criteria are not met."}, {"name": "BOTTOM RIGHT BOTTOM Out of Square Dimension (+/- mm)", "technical_name": "sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm", "field_type": "number", "sequence": 28, "default_value": 0, "visibility_condition": "sws_wscrn_hand_built_window_screen == 'yes' && sws_wscrn_hand_built_type == 'angled_out_of_square_bow_different_heights_widths'", "help_text": "*Min: -200mm & Window Screen Maintains Height >= 150mm to Max: 200mm *BOTTOM RIGHT BOTTOM Out of Square Dimension must be between -200 to 200mm & Maintain a mimimum Height of 150mm. Please upload a dimensioned diagram under the < 4 Corners < selection if these criteria are not met."}, {"name": "MAKE Window Screen Height (mm)", "technical_name": "sws_wscrn_make_window_screen_height_mm", "field_type": "number", "sequence": 29, "default_value": 1, "visibility_condition": "sws_wscrn_hand_built_window_screen == 'no'", "help_text": "*Min: 150mm - Max: 4020mm *Window Screen Height must be between 150 - 4020mm"}, {"name": "MAKE Window Screen Width (mm)", "technical_name": "sws_wscrn_make_window_screen_width_mm", "field_type": "number", "sequence": 30, "default_value": 1, "visibility_condition": "sws_wscrn_hand_built_window_screen == 'no'", "help_text": "*Min: 150mm - Max: 4020mm if Window Screen Height <= 3695mm, 3695mm if Window Screen Height is > 3695mm *Window Screen Width must be between 150 - 4020mm"}]}, {"name": "Structural Components", "sequence": 30, "description": "Configure bracing and frame size details.", "fields": [{"name": "SWS-WSCRN Frame Size", "technical_name": "sws_wscrn_frame_size", "field_type": "selection", "sequence": 10, "help_text": "Largest Height & Largest Width in this question is only relevant when the option \"Yes\" is chosen in Q4. Otherwise just use Height & Width of a Square Screen. Largest Height & Largest Width are determined by using the SQUARE measurements and adding on all the Out of Square Dimensions to find the OUTER SQUARE Dimensions", "options": [{"name": "11 mm", "value": "11_mm"}, {"name": "9 mm", "value": "9_mm"}, {"name": "Door Frame Upgrade (Fees Apply)", "value": "door_frame_upgrade_fees_apply"}]}, {"name": "SWS-WSCRN X-Brace or Mullion Quantity", "technical_name": "sws_wscrn_x_brace_mullion_quantity", "field_type": "selection", "sequence": 11, "options": [{"name": "0", "value": "0"}, {"name": "1 X-Brace (Horizontal)", "value": "1_x_brace_horizontal"}, {"name": "1 Mullion (Vertical)", "value": "1_mullion_vertical"}, {"name": "2 X-Braces (Horizontal)", "value": "2_x_braces_horizontal"}, {"name": "2 Mullions (Vertical)", "value": "2_mullions_vertical"}, {"name": "3 X-Braces (Horizontal)", "value": "3_x_braces_horizontal"}, {"name": "3 Mullions (Vertical)", "value": "3_mullions_vertical"}, {"name": "4 X-Braces (Horizontal)", "value": "4_x_braces_horizontal"}, {"name": "4 Mullions (Vertical)", "value": "4_mullions_vertical"}]}, {"name": "X-Brace Location (mm)", "technical_name": "sws_wscrn_x_brace_location_1_mm", "field_type": "number", "sequence": 12, "default_value": 1, "visibility_condition": "sws_wscrn_x_brace_mullion_quantity == '1_x_brace_horizontal' || sws_wscrn_x_brace_mullion_quantity == '2_x_braces_horizontal' || sws_wscrn_x_brace_mullion_quantity == '3_x_braces_horizontal' || sws_wscrn_x_brace_mullion_quantity == '4_x_braces_horizontal'", "help_text": "* Default is Centre of Height. Custom Locations are from Bottom of Screen to Centre of X-Brace. *Min: 150mm - Max: Left Window Screen Height - 150mm. *X-Brace Location must be greater than 150mm and less than Height - 150mm."}, {"name": "X-Brace Location 2 (mm)", "technical_name": "sws_wscrn_x_brace_location_2_mm", "field_type": "number", "sequence": 13, "default_value": 1, "visibility_condition": "sws_wscrn_x_brace_mullion_quantity == '2_x_braces_horizontal' || sws_wscrn_x_brace_mullion_quantity == '3_x_braces_horizontal' || sws_wscrn_x_brace_mullion_quantity == '4_x_braces_horizontal'", "help_text": "* Default is 2/3 of Height. Custom Locations are from Bottom of Screen to Centre of X-Braces *Min: X-Brace Location 1 + 150mm - Max: Left Window Screen Height - 150mm *X-Brace Location must be greater than 150mm from Location 1 and less than Height - 150mm"}, {"name": "X-Brace Location 3 (mm)", "technical_name": "sws_wscrn_x_brace_location_3_mm", "field_type": "number", "sequence": 14, "default_value": 1, "visibility_condition": "sws_wscrn_x_brace_mullion_quantity == '3_x_braces_horizontal' || sws_wscrn_x_brace_mullion_quantity == '4_x_braces_horizontal'", "help_text": "* Default is 3/4 of Height. Custom Locations are from Bottom of Screen to Centre of X-Braces *Min: X-Brace Location 2 + 150mm - Max: Left Window Screen Height - 150mm *X-Brace Location must be greater than 150mm from Location 2 and less than Height - 150mm"}, {"name": "X-Brace Location 4 (mm)", "technical_name": "sws_wscrn_x_brace_location_4_mm", "field_type": "number", "sequence": 15, "default_value": 1, "visibility_condition": "sws_wscrn_x_brace_mullion_quantity == '4_x_braces_horizontal'", "help_text": "* Default is 4/5 of Height. Custom Locations are from Bottom of Screen to Centre of X-Braces *Min: X-Brace Location 3 + 150mm - Max: Left Window Screen Height - 150mm *X-Brace Location must be greater than 150mm from Location 3 and less than Height - 150mm"}, {"name": "X-Brace Colour", "technical_name": "sws_wscrn_x_brace_colour", "field_type": "selection", "sequence": 16, "visibility_condition": "sws_wscrn_x_brace_mullion_quantity == '1_x_brace_horizontal' || sws_wscrn_x_brace_mullion_quantity == '2_x_braces_horizontal' || sws_wscrn_x_brace_mullion_quantity == '3_x_braces_horizontal' || sws_wscrn_x_brace_mullion_quantity == '4_x_braces_horizontal'", "options": [{"name": "Black", "value": "black"}, {"name": "Match Window Screen Frame (Powder Coat Fee)", "value": "match_window_screen_frame_powder_coat_fee"}]}, {"name": "Mullion Location (mm)", "technical_name": "sws_wscrn_mullion_location_1_mm", "field_type": "number", "sequence": 17, "default_value": 1, "visibility_condition": "sws_wscrn_x_brace_mullion_quantity == '1_mullion_vertical' || sws_wscrn_x_brace_mullion_quantity == '2_mullions_vertical' || sws_wscrn_x_brace_mullion_quantity == '3_mullions_vertical' || sws_wscrn_x_brace_mullion_quantity == '4_mullions_vertical'", "help_text": "* Default is Centre of Width. Custom Locations are from Left of Screen to Centre of Mullion, OUTSIDE VIEW *Min: 150mm - Max: Bottom Window Width - 150mm *Mullion Location must be greater than 150mm and less than Width - 150mm"}, {"name": "Mullion Location 2 (mm)", "technical_name": "sws_wscrn_mullion_location_2_mm", "field_type": "number", "sequence": 18, "default_value": 1, "visibility_condition": "sws_wscrn_x_brace_mullion_quantity == '2_mullions_vertical' || sws_wscrn_x_brace_mullion_quantity == '3_mullions_vertical' || sws_wscrn_x_brace_mullion_quantity == '4_mullions_vertical'", "help_text": "* Default is 2/3 of Width. Custom Locations are from Left of Screen to Centre of Mullions, OUTSIDE VIEW *Min: Mullion Location 1 + 150mm - Max: Bottom Window Screen Width - 150mm *Mullion Location must be greater than 150mm from Location 1 and less than Width - 150mm"}, {"name": "Mullion Location 3 (mm)", "technical_name": "sws_wscrn_mullion_location_3_mm", "field_type": "number", "sequence": 19, "default_value": 1, "visibility_condition": "sws_wscrn_x_brace_mullion_quantity == '3_mullions_vertical' || sws_wscrn_x_brace_mullion_quantity == '4_mullions_vertical'", "help_text": "* Default is 3/4 of Width. Custom Locations are from Left of Screen to Centre of Mullions, OUTSIDE VIEW *Min: Mullion Location 2 + 150mm - Max: Bottom Window Screen Width - 150mm *Mullion Location must be greater than 150mm from Location 2 and less than Width - 150mm"}, {"name": "Mullion Location 4 (mm)", "technical_name": "sws_wscrn_mullion_location_4_mm", "field_type": "number", "sequence": 20, "default_value": 1, "visibility_condition": "sws_wscrn_x_brace_mullion_quantity == '4_mullions_vertical'", "help_text": "* Default is 4/5 of Width. Custom Locations are from Left of Screen to Centre of Mullions, OUTSIDE VIEW *Min: Mullion Location 3 + 150mm - Max: Bottom Window Screen Width - 150mm *Mullion Location must be greater than 150mm from Location 3 and less than Width - 150mm"}, {"name": "Mullion Colour", "technical_name": "sws_wscrn_mullion_colour", "field_type": "selection", "sequence": 21, "visibility_condition": "sws_wscrn_x_brace_mullion_quantity == '1_mullion_vertical' || sws_wscrn_x_brace_mullion_quantity == '2_mullions_vertical' || sws_wscrn_x_brace_mullion_quantity == '3_mullions_vertical' || sws_wscrn_x_brace_mullion_quantity == '4_mullions_vertical'", "options": [{"name": "Black", "value": "black"}, {"name": "Match Window Screen Frame (Powder Coat Fee)", "value": "match_window_screen_frame_powder_coat_fee"}]}]}, {"name": "Installed Accessories", "sequence": 40, "description": "Select installed accessories.", "fields": [{"name": "SWS-WSCRN Hopper Hatch or Small Pet Door Quantity", "technical_name": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity", "field_type": "selection", "sequence": 10, "default_value": 0, "help_text": "*Please note there will be a decrease in mesh tension around the hopper hatch or small pet door", "options": [{"name": "0", "value": "0"}, {"name": "1 Small Pet Door", "value": "1_small_pet_door"}, {"name": "1 Hopper Hatch", "value": "1_hopper_hatch"}, {"name": "2 <PERSON>", "value": "2_hopper_hatches"}, {"name": "3 <PERSON>", "value": "3_hopper_hatches"}, {"name": "4 <PERSON>", "value": "4_hopper_hatches"}, {"name": "5 <PERSON>", "value": "5_hopper_hatches"}, {"name": "6 <PERSON>", "value": "6_hopper_hatches"}]}, {"name": "Small Pet Door Colour", "technical_name": "sws_wscrn_small_pet_door_colour", "field_type": "selection", "sequence": 11, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '1_small_pet_door'", "options": [{"name": "Black", "value": "black"}, {"name": "White", "value": "white"}]}, {"name": "Small Pet Door Height Location", "technical_name": "sws_wscrn_small_pet_door_height_location", "field_type": "selection", "sequence": 12, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '1_small_pet_door'", "options": [{"name": "Bottom of Window Screen", "value": "bottom_of_window_screen"}, {"name": "Centre of Useable Opening at Centre of Window Screen Height", "value": "centre_of_useable_opening_at_centre_of_window_screen_height"}, {"name": "Top of Window Screen", "value": "top_of_window_screen"}, {"name": "Custom Height Location to Centre of Useable Opening", "value": "custom_height_location_to_centre_of_useable_opening"}]}, {"name": "Small Pet Door Custom Height Location (mm)", "technical_name": "sws_wscrn_small_pet_door_custom_height_location_mm", "field_type": "number", "sequence": 13, "default_value": 1, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '1_small_pet_door' && sws_wscrn_small_pet_door_height_location == 'custom_height_location_to_centre_of_useable_opening'", "help_text": "* Custom Height Locations are from Bottom of Screen to Centre of Useable Opening. *Min: 200 mm - Max: Left Window Screen Height - 210mm. Small Pet Door Height Location must be greater than 200mm and less than Height - 210mm"}, {"name": "Small Pet Door Width Location", "technical_name": "sws_wscrn_small_pet_door_width_location", "field_type": "selection", "sequence": 14, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '1_small_pet_door'", "options": [{"name": "Left Side of Window Screen, OUTSIDE VIEW", "value": "left_side_of_window_screen_outside_view"}, {"name": "Centre of Window Screen Width", "value": "centre_of_window_screen_width"}, {"name": "Right Side of Window Screen, OUTSIDE VIEW", "value": "right_side_of_window_screen_outside_view"}, {"name": "Custom Width Location to Centre of Opening", "value": "custom_width_location_to_centre_of_opening"}]}, {"name": "Small Pet Door Custom Width Location (mm)", "technical_name": "sws_wscrn_small_pet_door_custom_width_location_mm", "field_type": "number", "sequence": 15, "default_value": 1, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '1_small_pet_door' && sws_wscrn_small_pet_door_width_location == 'custom_width_location_to_centre_of_opening'", "help_text": "* Custom Width Locations are from Left of Screen to Centre of Useable Opening, OUTSIDE VIEW. *Min: 180 mm - Max: Bottom Window Screen Width - 180mm. Small Pet Door Width Location must be greater than 180mm and less than Width - 180mm. *All Small Pet Door Centre Locations (Selection & Custom) must be a minium of 195mm from X-Brace Locations above them, a minimum of 185mm from X-Brace Locations below them, a minimum of 166mm from Mullion Locations Left or Right of them. *Small Pet Door Location clashes with X-Brace or Mullion Location. Please change Small Pet Door Location."}, {"name": "Pet Door Flexible Flap", "technical_name": "sws_wscrn_pet_door_flexible_flap", "field_type": "selection", "sequence": 16, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '1_small_pet_door'", "options": [{"name": "Yes", "value": "yes"}, {"name": "No", "value": "no"}]}, {"name": "Small Pet Door Surround Infill", "technical_name": "sws_wscrn_small_pet_door_surround_infill", "field_type": "selection", "sequence": 17, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '1_small_pet_door'", "options": [{"name": "Yes", "value": "yes"}, {"name": "No", "value": "no"}]}, {"name": "<PERSON>", "technical_name": "sws_wscrn_hopper_hatch_colour", "field_type": "selection", "sequence": 18, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '1_hopper_hatch'", "options": [{"name": "Black", "value": "black"}]}, {"name": "Hopper Hatch Height Location", "technical_name": "sws_wscrn_hopper_hatch_height_location", "field_type": "selection", "sequence": 19, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '1_hopper_hatch'", "options": [{"name": "Bottom of Window Screen", "value": "bottom_of_window_screen"}, {"name": "Centre of Useable Opening at Centre of Window Screen Height", "value": "centre_of_useable_opening_at_centre_of_window_screen_height"}, {"name": "Top of Window Screen", "value": "top_of_window_screen"}, {"name": "Custom Height Location to Centre of Useable Opening", "value": "custom_height_location_to_centre_of_useable_opening"}]}, {"name": "Hopper Hatch Custom Height Location (mm)", "technical_name": "sws_wscrn_hopper_hatch_custom_height_location_mm", "field_type": "number", "sequence": 20, "default_value": 1, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '1_hopper_hatch' && sws_wscrn_hopper_hatch_height_location == 'custom_height_location_to_centre_of_useable_opening'", "help_text": "* Custom Height Locations are from Bottom of Screen to Centre of Useable Opening. *Min: 133mm - Max: Left Window Screen Height - 145mm *Hopper Hatch Height Location must be greater than 133mm and less than Height - 145mm"}, {"name": "Hopper Hatch Width Location", "technical_name": "sws_wscrn_hopper_hatch_width_location", "field_type": "selection", "sequence": 21, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '1_hopper_hatch'", "options": [{"name": "Left Side of Window Screen, OUTSIDE VIEW", "value": "left_side_of_window_screen_outside_view"}, {"name": "Centre of Window Screen Width", "value": "centre_of_window_screen_width"}, {"name": "Right Side of Window Screen, OUTSIDE VIEW", "value": "right_side_of_window_screen_outside_view"}, {"name": "Custom Width Location to Centre of Opening", "value": "custom_width_location_to_centre_of_opening"}]}, {"name": "Hopper Hatch Custom Width Location (mm)", "technical_name": "sws_wscrn_hopper_hatch_custom_width_location_mm", "field_type": "number", "sequence": 22, "default_value": 1, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '1_hopper_hatch' && sws_wscrn_hopper_hatch_width_location == 'custom_width_location_to_centre_of_opening'", "help_text": "* Custom Width Locations are from Left of Screen to Centre of Opening, OUTSIDE VIEW. *Min: 138mm - Max: Bottom Window Screen Width - 138mm. *Hopper Hatch Width Location must be greater than 138mm and less than Width - 138mm. *All Hopper Hatch Centre Locations (Selection & Custom) must be a minium of 130mm from X-Brace Locations above them, a minimum of 117mm from X-Brace Locations below them, a minimum of 123mm from Mullion Locations Left or Right of them. *Hopper Hatch Location clashes with X-Brace or Mullion Location. Please change Hopper Hatch Location."}, {"name": "Hopper Hatch Surround Infill", "technical_name": "sws_wscrn_hopper_hatch_surround_infill", "field_type": "selection", "sequence": 23, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '1_hopper_hatch'", "options": [{"name": "Yes", "value": "yes"}, {"name": "No", "value": "no"}]}, {"name": "<PERSON>", "technical_name": "sws_wscrn_hopper_hatches_colour", "field_type": "selection", "sequence": 24, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '2_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '3_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches'", "options": [{"name": "Black", "value": "black"}]}, {"name": "Hopper Hatch Height Location 1", "technical_name": "sws_wscrn_hopper_hatch_height_location_1", "field_type": "selection", "sequence": 25, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '2_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '3_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches'", "options": [{"name": "Bottom of Window Screen", "value": "bottom_of_window_screen"}, {"name": "Centre of Useable Opening at Centre of Window Screen Height", "value": "centre_of_useable_opening_at_centre_of_window_screen_height"}, {"name": "Top of Window Screen", "value": "top_of_window_screen"}, {"name": "Custom Height Location to Centre of Useable Opening", "value": "custom_height_location_to_centre_of_useable_opening"}]}, {"name": "Hopper Hatch Custom Height Location 1 (mm)", "technical_name": "sws_wscrn_hopper_hatch_custom_height_location_1_mm", "field_type": "number", "sequence": 26, "default_value": 1, "visibility_condition": "sws_wscrn_hopper_hatch_height_location_1 == 'custom_height_location_to_centre_of_useable_opening' && (sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '2_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '3_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches')", "help_text": "* Custom Height Locations are from Bottom of Screen to Centre of Useable Opening. *Min: 133mm - Max: Left Window Screen Height - 145mm *Hopper Hatch Height Location must be greater than 133mm and less than Height - 145mm"}, {"name": "Hopper Hatch Width Location 1", "technical_name": "sws_wscrn_hopper_hatch_width_location_1", "field_type": "selection", "sequence": 27, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '2_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '3_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches'", "options": [{"name": "Left Side of Window Screen, OUTSIDE VIEW", "value": "left_side_of_window_screen_outside_view"}, {"name": "Centre of Window Screen Width", "value": "centre_of_window_screen_width"}, {"name": "Right Side of Window Screen, OUTSIDE VIEW", "value": "right_side_of_window_screen_outside_view"}, {"name": "Custom Width Location to Centre of Opening", "value": "custom_width_location_to_centre_of_opening"}]}, {"name": "Hopper Hatch Custom Width Location 1 (mm)", "technical_name": "sws_wscrn_hopper_hatch_custom_width_location_1_mm", "field_type": "number", "sequence": 28, "default_value": 1, "visibility_condition": "sws_wscrn_hopper_hatch_width_location_1 == 'custom_width_location_to_centre_of_opening' && (sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '2_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '3_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches')", "help_text": "* Custom Width Locations are from Left of Screen to Centre of Opening, OUTSIDE VIEW. *Min: 138mm - Max: Bottom Window Screen Width - 138mm. *Hopper Hatch Width Location must be greater than 138mm and less than Width - 138mm. *All Hopper Hatch Centre Locations (Selection & Custom) must be a minium of 130mm from X-Brace Locations above them, a minimum of 117mm from X-Brace Locations below them, a minimum of 123mm from Mullion Locations Left or Right of them. *Hopper Hatch Location clashes with X-Brace or Mullion Location. Please change Hopper Hatch Location."}, {"name": "Hopper Hatch Surround Infill 1", "technical_name": "sws_wscrn_hopper_hatch_surround_infill_1", "field_type": "selection", "sequence": 29, "visibility_condition": "(sws_wscrn_hopper_hatch_height_location_1 == 'centre_of_useable_opening_at_centre_of_window_screen_height' || sws_wscrn_hopper_hatch_height_location_1 == 'custom_height_location_to_centre_of_useable_opening') && (sws_wscrn_hopper_hatch_width_location_1 == 'centre_of_window_screen_width' || sws_wscrn_hopper_hatch_width_location_1 == 'custom_width_location_to_centre_of_opening') && (sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '2_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '3_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches')", "options": [{"name": "Yes", "value": "yes"}]}, {"name": "Hopper Hatch Height Location 2", "technical_name": "sws_wscrn_hopper_hatch_height_location_2", "field_type": "selection", "sequence": 30, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '2_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '3_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches'", "options": [{"name": "Bottom of Window Screen", "value": "bottom_of_window_screen"}, {"name": "Centre of Useable Opening at Centre of Window Screen Height", "value": "centre_of_useable_opening_at_centre_of_window_screen_height"}, {"name": "Top of Window Screen", "value": "top_of_window_screen"}, {"name": "Custom Height Location to Centre of Useable Opening", "value": "custom_height_location_to_centre_of_useable_opening"}]}, {"name": "Hopper Hatch Custom Height Location 2 (mm)", "technical_name": "sws_wscrn_hopper_hatch_custom_height_location_2_mm", "field_type": "number", "sequence": 31, "default_value": 1, "visibility_condition": "sws_wscrn_hopper_hatch_height_location_2 == 'custom_height_location_to_centre_of_useable_opening' && (sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '2_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '3_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches')", "help_text": "* Custom Height Locations are from Bottom of Screen to Centre of Useable Opening. *Min: 133mm - Max: Left Window Screen Height - 145mm *Hopper Hatch Height Location must be greater than 133mm and less than Height - 145mm"}, {"name": "Hopper Hatch Width Location 2", "technical_name": "sws_wscrn_hopper_hatch_width_location_2", "field_type": "selection", "sequence": 32, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '2_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '3_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches'", "options": [{"name": "Left Side of Window Screen, OUTSIDE VIEW", "value": "left_side_of_window_screen_outside_view"}, {"name": "Centre of Window Screen Width", "value": "centre_of_window_screen_width"}, {"name": "Right Side of Window Screen, OUTSIDE VIEW", "value": "right_side_of_window_screen_outside_view"}, {"name": "Custom Width Location to Centre of Opening", "value": "custom_width_location_to_centre_of_opening"}]}, {"name": "Hopper Hatch Custom Width Location 2 (mm)", "technical_name": "sws_wscrn_hopper_hatch_custom_width_location_2_mm", "field_type": "number", "sequence": 33, "default_value": 1, "visibility_condition": "sws_wscrn_hopper_hatch_width_location_2 == 'custom_width_location_to_centre_of_opening' && (sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '2_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '3_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches')", "help_text": "* Custom Width Locations are from Left of Screen to Centre of Opening, OUTSIDE VIEW. *Min: 138mm - Max: Bottom Window Screen Width - 138mm. *Hopper Hatch Width Location must be greater than 138mm and less than Width - 138mm. *All Hopper Hatch Centre Locations (Selection & Custom) must be a minium of 200mm apart in Height or Width from all other Centre Locations. *All Hopper Hatch Centre Locations (Selection & Custom) must be a minium of 130mm from X-Brace Locations above them, a minimum of 117mm from X-Brace Locations below them, a minimum of 123mm from Mullion Locations Left or Right of them. *Hopper Hatch Location clashes with X-Brace or Mullion Location. Please change Hopper Hatch Location."}, {"name": "Hopper Hatch Surround Infill 2", "technical_name": "sws_wscrn_hopper_hatch_surround_infill_2", "field_type": "selection", "sequence": 34, "visibility_condition": "(sws_wscrn_hopper_hatch_height_location_2 == 'centre_of_useable_opening_at_centre_of_window_screen_height' || sws_wscrn_hopper_hatch_height_location_2 == 'custom_height_location_to_centre_of_useable_opening') && (sws_wscrn_hopper_hatch_width_location_2 == 'centre_of_window_screen_width' || sws_wscrn_hopper_hatch_width_location_2 == 'custom_width_location_to_centre_of_opening') && (sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '2_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '3_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches')", "options": [{"name": "Yes", "value": "yes"}]}, {"name": "Hopper Hatch Height Location 3", "technical_name": "sws_wscrn_hopper_hatch_height_location_3", "field_type": "selection", "sequence": 35, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '3_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches'", "options": [{"name": "Bottom of Window Screen", "value": "bottom_of_window_screen"}, {"name": "Centre of Useable Opening at Centre of Window Screen Height", "value": "centre_of_useable_opening_at_centre_of_window_screen_height"}, {"name": "Top of Window Screen", "value": "top_of_window_screen"}, {"name": "Custom Height Location to Centre of Useable Opening", "value": "custom_height_location_to_centre_of_useable_opening"}]}, {"name": "Hopper Hatch Custom Height Location 3 (mm)", "technical_name": "sws_wscrn_hopper_hatch_custom_height_location_3_mm", "field_type": "number", "sequence": 36, "default_value": 1, "visibility_condition": "sws_wscrn_hopper_hatch_height_location_3 == 'custom_height_location_to_centre_of_useable_opening' && (sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '3_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches')", "help_text": "* Custom Height Locations are from Bottom of Screen to Centre of Useable Opening. *Min: 133mm - Max: Left Window Screen Height - 145mm *Hopper Hatch Height Location must be greater than 133mm and less than Height - 145mm"}, {"name": "Hopper Hatch Width Location 3", "technical_name": "sws_wscrn_hopper_hatch_width_location_3", "field_type": "selection", "sequence": 37, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '3_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches'", "options": [{"name": "Left Side of Window Screen, OUTSIDE VIEW", "value": "left_side_of_window_screen_outside_view"}, {"name": "Centre of Window Screen Width", "value": "centre_of_window_screen_width"}, {"name": "Right Side of Window Screen, OUTSIDE VIEW", "value": "right_side_of_window_screen_outside_view"}, {"name": "Custom Width Location to Centre of Opening", "value": "custom_width_location_to_centre_of_opening"}]}, {"name": "Hopper Hatch Custom Width Location 3 (mm)", "technical_name": "sws_wscrn_hopper_hatch_custom_width_location_3_mm", "field_type": "number", "sequence": 38, "default_value": 1, "visibility_condition": "sws_wscrn_hopper_hatch_width_location_3 == 'custom_width_location_to_centre_of_opening' && (sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '3_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches')", "help_text": "* Custom Width Locations are from Left of Screen to Centre of Opening, OUTSIDE VIEW. *Min: 138mm - Max: Bottom Window Screen Width - 138mm. *Hopper Hatch Width Location must be greater than 138mm and less than Width - 138mm. *All Hopper Hatch Centre Locations (Selection & Custom) must be a minium of 200mm apart in Height or Width from all other Centre Locations. *All Hopper Hatch Centre Locations (Selection & Custom) must be a minium of 130mm from X-Brace Locations above them, a minimum of 117mm from X-Brace Locations below them, a minimum of 123mm from Mullion Locations Left or Right of them. *Hopper Hatch Location clashes with X-Brace or Mullion Location. Please change Hopper Hatch Location."}, {"name": "Hopper Hatch Surround Infill 3", "technical_name": "sws_wscrn_hopper_hatch_surround_infill_3", "field_type": "selection", "sequence": 39, "visibility_condition": "(sws_wscrn_hopper_hatch_height_location_3 == 'centre_of_useable_opening_at_centre_of_window_screen_height' || sws_wscrn_hopper_hatch_height_location_3 == 'custom_height_location_to_centre_of_useable_opening') && (sws_wscrn_hopper_hatch_width_location_3 == 'centre_of_window_screen_width' || sws_wscrn_hopper_hatch_width_location_3 == 'custom_width_location_to_centre_of_opening') && (sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '3_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches')", "options": [{"name": "Yes", "value": "yes"}]}, {"name": "Hopper Hatch Height Location 4", "technical_name": "sws_wscrn_hopper_hatch_height_location_4", "field_type": "selection", "sequence": 40, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches'", "options": [{"name": "Bottom of Window Screen", "value": "bottom_of_window_screen"}, {"name": "Centre of Useable Opening at Centre of Window Screen Height", "value": "centre_of_useable_opening_at_centre_of_window_screen_height"}, {"name": "Top of Window Screen", "value": "top_of_window_screen"}, {"name": "Custom Height Location to Centre of Useable Opening", "value": "custom_height_location_to_centre_of_useable_opening"}]}, {"name": "Hopper Hatch Custom Height Location 4 (mm)", "technical_name": "sws_wscrn_hopper_hatch_custom_height_location_4_mm", "field_type": "number", "sequence": 41, "default_value": 1, "visibility_condition": "sws_wscrn_hopper_hatch_height_location_4 == 'custom_height_location_to_centre_of_useable_opening' && (sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches')", "help_text": "* Custom Height Locations are from Bottom of Screen to Centre of Useable Opening. *Min: 133mm - Max: Left Window Screen Height - 145mm *Hopper Hatch Height Location must be greater than 133mm and less than Height - 145mm"}, {"name": "Hopper Hatch Width Location 4", "technical_name": "sws_wscrn_hopper_hatch_width_location_4", "field_type": "selection", "sequence": 42, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches'", "options": [{"name": "Left Side of Window Screen, OUTSIDE VIEW", "value": "left_side_of_window_screen_outside_view"}, {"name": "Centre of Window Screen Width", "value": "centre_of_window_screen_width"}, {"name": "Right Side of Window Screen, OUTSIDE VIEW", "value": "right_side_of_window_screen_outside_view"}, {"name": "Custom Width Location to Centre of Opening", "value": "custom_width_location_to_centre_of_opening"}]}, {"name": "Hopper Hatch Custom Width Location 4 (mm)", "technical_name": "sws_wscrn_hopper_hatch_custom_width_location_4_mm", "field_type": "number", "sequence": 43, "default_value": 1, "visibility_condition": "sws_wscrn_hopper_hatch_width_location_4 == 'custom_width_location_to_centre_of_opening' && (sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches')", "help_text": "* Custom Width Locations are from Left of Screen to Centre of Opening, OUTSIDE VIEW. *Min: 138mm - Max: Bottom Window Screen Width - 138mm. *Hopper Hatch Width Location must be greater than 138mm and less than Width - 138mm. *All Hopper Hatch Centre Locations (Selection & Custom) must be a minium of 200mm apart in Height or Width from all other Centre Locations. *All Hopper Hatch Centre Locations (Selection & Custom) must be a minium of 130mm from X-Brace Locations above them, a minimum of 117mm from X-Brace Locations below them, a minimum of 123mm from Mullion Locations Left or Right of them. *Hopper Hatch Location clashes with X-Brace or Mullion Location. Please change Hopper Hatch Location."}, {"name": "Hopper Hatch Surround Infill 4", "technical_name": "sws_wscrn_hopper_hatch_surround_infill_4", "field_type": "selection", "sequence": 44, "visibility_condition": "(sws_wscrn_hopper_hatch_height_location_4 == 'centre_of_useable_opening_at_centre_of_window_screen_height' || sws_wscrn_hopper_hatch_height_location_4 == 'custom_height_location_to_centre_of_useable_opening') && (sws_wscrn_hopper_hatch_width_location_4 == 'centre_of_window_screen_width' || sws_wscrn_hopper_hatch_width_location_4 == 'custom_width_location_to_centre_of_opening') && (sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '4_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches')", "options": [{"name": "Yes", "value": "yes"}]}, {"name": "Hopper Hatch Height Location 5", "technical_name": "sws_wscrn_hopper_hatch_height_location_5", "field_type": "selection", "sequence": 45, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches'", "options": [{"name": "Bottom of Window Screen", "value": "bottom_of_window_screen"}, {"name": "Centre of Useable Opening at Centre of Window Screen Height", "value": "centre_of_useable_opening_at_centre_of_window_screen_height"}, {"name": "Top of Window Screen", "value": "top_of_window_screen"}, {"name": "Custom Height Location to Centre of Useable Opening", "value": "custom_height_location_to_centre_of_useable_opening"}]}, {"name": "Hopper Hatch Custom Height Location 5 (mm)", "technical_name": "sws_wscrn_hopper_hatch_custom_height_location_5_mm", "field_type": "number", "sequence": 46, "default_value": 1, "visibility_condition": "sws_wscrn_hopper_hatch_height_location_5 == 'custom_height_location_to_centre_of_useable_opening' && (sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches')", "help_text": "* Custom Height Locations are from Bottom of Screen to Centre of Useable Opening. *Min: 133mm - Max: Left Window Screen Height - 145mm *Hopper Hatch Height Location must be greater than 133mm and less than Height - 145mm"}, {"name": "Hopper Hatch Width Location 5", "technical_name": "sws_wscrn_hopper_hatch_width_location_5", "field_type": "selection", "sequence": 47, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches'", "options": [{"name": "Left Side of Window Screen, OUTSIDE VIEW", "value": "left_side_of_window_screen_outside_view"}, {"name": "Centre of Window Screen Width", "value": "centre_of_window_screen_width"}, {"name": "Right Side of Window Screen, OUTSIDE VIEW", "value": "right_side_of_window_screen_outside_view"}, {"name": "Custom Width Location to Centre of Opening", "value": "custom_width_location_to_centre_of_opening"}]}, {"name": "Hopper Hatch Custom Width Location 5 (mm)", "technical_name": "sws_wscrn_hopper_hatch_custom_width_location_5_mm", "field_type": "number", "sequence": 48, "default_value": 1, "visibility_condition": "sws_wscrn_hopper_hatch_width_location_5 == 'custom_width_location_to_centre_of_opening' && (sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches')", "help_text": "* Custom Width Locations are from Left of Screen to Centre of Opening, OUTSIDE VIEW. *Min: 138mm - Max: Bottom Window Screen Width - 138mm. *Hopper Hatch Width Location must be greater than 138mm and less than Width - 138mm. *All Hopper Hatch Centre Locations (Selection & Custom) must be a minium of 200mm apart in Height or Width from all other Centre Locations. *All Hopper Hatch Centre Locations (Selection & Custom) must be a minium of 130mm from X-Brace Locations above them, a minimum of 117mm from X-Brace Locations below them, a minimum of 123mm from Mullion Locations Left or Right of them. *Hopper Hatch Location clashes with X-Brace or Mullion Location. Please change Hopper Hatch Location."}, {"name": "Hopper Hatch Surround Infill 5", "technical_name": "sws_wscrn_hopper_hatch_surround_infill_5", "field_type": "selection", "sequence": 49, "visibility_condition": "(sws_wscrn_hopper_hatch_height_location_5 == 'centre_of_useable_opening_at_centre_of_window_screen_height' || sws_wscrn_hopper_hatch_height_location_5 == 'custom_height_location_to_centre_of_useable_opening') && (sws_wscrn_hopper_hatch_width_location_5 == 'centre_of_window_screen_width' || sws_wscrn_hopper_hatch_width_location_5 == 'custom_width_location_to_centre_of_opening') && (sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '5_hopper_hatches' || sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches')", "options": [{"name": "Yes", "value": "yes"}]}, {"name": "Hopper Hatch Height Location 6", "technical_name": "sws_wscrn_hopper_hatch_height_location_6", "field_type": "selection", "sequence": 50, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches'", "options": [{"name": "Bottom of Window Screen", "value": "bottom_of_window_screen"}, {"name": "Centre of Useable Opening at Centre of Window Screen Height", "value": "centre_of_useable_opening_at_centre_of_window_screen_height"}, {"name": "Top of Window Screen", "value": "top_of_window_screen"}, {"name": "Custom Height Location to Centre of Useable Opening", "value": "custom_height_location_to_centre_of_useable_opening"}]}, {"name": "Hopper Hatch Custom Height Location 6 (mm)", "technical_name": "sws_wscrn_hopper_hatch_custom_height_location_6_mm", "field_type": "number", "sequence": 51, "default_value": 1, "visibility_condition": "sws_wscrn_hopper_hatch_height_location_6 == 'custom_height_location_to_centre_of_useable_opening' && sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches'", "help_text": "* Custom Height Locations are from Bottom of Screen to Centre of Useable Opening. *Min: 133mm - Max: Left Window Screen Height - 145mm *Hopper Hatch Height Location must be greater than 133mm and less than Height - 145mm"}, {"name": "<PERSON> Hatch Width Location 6", "technical_name": "sws_wscrn_hopper_hatch_width_location_6", "field_type": "selection", "sequence": 52, "visibility_condition": "sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches'", "options": [{"name": "Left Side of Window Screen, OUTSIDE VIEW", "value": "left_side_of_window_screen_outside_view"}, {"name": "Centre of Window Screen Width", "value": "centre_of_window_screen_width"}, {"name": "Right Side of Window Screen, OUTSIDE VIEW", "value": "right_side_of_window_screen_outside_view"}, {"name": "Custom Width Location to Centre of Opening", "value": "custom_width_location_to_centre_of_opening"}]}, {"name": "Hopper Hatch Custom Width Location 6 (mm)", "technical_name": "sws_wscrn_hopper_hatch_custom_width_location_6_mm", "field_type": "number", "sequence": 53, "default_value": 1, "visibility_condition": "sws_wscrn_hopper_hatch_width_location_6 == 'custom_width_location_to_centre_of_opening' && sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches'", "help_text": "* Custom Width Locations are from Left of Screen to Centre of Opening, OUTSIDE VIEW. *Min: 138mm - Max: Bottom Window Screen Width - 138mm. *Hopper Hatch Width Location must be greater than 138mm and less than Width - 138mm. *All Hopper Hatch Centre Locations (Selection & Custom) must be a minium of 200mm apart in Height or Width from all other Centre Locations. *All Hopper Hatch Centre Locations (Selection & Custom) must be a minium of 130mm from X-Brace Locations above them, a minimum of 117mm from X-Brace Locations below them, a minimum of 123mm from Mullion Locations Left or Right of them. *Hopper Hatch Location clashes with X-Brace or Mullion Location. Please change Hopper Hatch Location."}, {"name": "<PERSON> Hatch Surround Infill 6", "technical_name": "sws_wscrn_hopper_hatch_surround_infill_6", "field_type": "selection", "sequence": 54, "visibility_condition": "(sws_wscrn_hopper_hatch_height_location_6 == 'centre_of_useable_opening_at_centre_of_window_screen_height' || sws_wscrn_hopper_hatch_height_location_6 == 'custom_height_location_to_centre_of_useable_opening') && (sws_wscrn_hopper_hatch_width_location_6 == 'centre_of_window_screen_width' || sws_wscrn_hopper_hatch_width_location_6 == 'custom_width_location_to_centre_of_opening') && sws_wscrn_hopper_hatch_or_small_pet_door_quantity == '6_hopper_hatches'", "options": [{"name": "Yes", "value": "yes"}]}, {"name": "SWS-WSCRN Nylon Guide Buttons Installed to Bottom for Water Drainage", "technical_name": "sws_wscrn_nylon_guide_buttons_installed_bottom_water_drainage", "field_type": "selection", "sequence": 55, "options": [{"name": "2 - Small (1.5mm)", "value": "2_small_1_5mm"}, {"name": "2 - Medium (2.5mm)", "value": "2_medium_2_5mm"}, {"name": "2 - Large (3.8mm)", "value": "2_large_3_8mm"}, {"name": "No", "value": "no"}]}]}, {"name": "Supply Only Accessories", "sequence": 50, "description": "Select additional accessories to be supplied.", "fields": [{"name": "SWS-WSCRN Fitting Accessories (Supply Only) 1", "technical_name": "sws_wscrn_fitting_accessories_supply_only_1", "field_type": "selection", "sequence": 10, "options": [{"name": "Box - SHS Square Corners 19x1.6mm", "value": "box_shs_square_corners_19x1_6mm"}, {"name": "Box - SHS Square Corners 25x1.6mm", "value": "box_shs_square_corners_25x1_6mm"}, {"name": "Box - SHS Square Corners 50x1.6mm", "value": "box_shs_square_corners_50x1_6mm"}, {"name": "Box - RHS Square Corners 50x25x1.6mm", "value": "box_rhs_square_corners_50x25x1_6mm"}, {"name": "Bugstrip - 16mm with 16mm Pile Seal", "value": "bugstrip_16mm_16mm_pile_seal"}, {"name": "Bugstrip - 16mm with 28mm Pile Seal", "value": "bugstrip_16mm_28mm_pile_seal"}, {"name": "Bugstrip - 25mm with 16mm Pile Seal", "value": "bugstrip_25mm_16mm_pile_seal"}, {"name": "Bugstrip - 25mm with 28mm Pile Seal", "value": "bugstrip_25mm_28mm_pile_seal"}, {"name": "Fire Attenuation Metallic Label 118 x 50 x 1mm - Red", "value": "fire_attenuation_metallic_label_red"}, {"name": "Flyscreen Track - Single Top", "value": "flyscreen_track_single_top"}, {"name": "Flyscreen Track - Single Bottom", "value": "flyscreen_track_single_bottom"}, {"name": "Flyscreen Track - Face Fix Single Top", "value": "flyscreen_track_face_fix_single_top"}, {"name": "Flyscreen Track - Face Fix Single Bottom", "value": "flyscreen_track_face_fix_single_bottom"}, {"name": "Flyscreen Track - Double Top", "value": "flyscreen_track_double_top"}, {"name": "Flyscreen Track - Double Bottom", "value": "flyscreen_track_double_bottom"}, {"name": "Flyscreen Track - Face Fix Double Top", "value": "flyscreen_track_face_fix_double_top"}, {"name": "Flyscreen Track - Face Fix Double Bottom", "value": "flyscreen_track_face_fix_double_bottom"}, {"name": "Interlock - Flat", "value": "interlock_flat"}, {"name": "Interlock - Shallow Offset", "value": "interlock_shallow_offset"}, {"name": "Interlock - Deep Offset", "value": "interlock_deep_offset"}, {"name": "Louvre Build Out (including corner angles)", "value": "louvre_build_out_including_corner_angles"}, {"name": "Trim Angle 12x12mm", "value": "trim_angle_12x12mm"}, {"name": "Trim Angle 20x12mm", "value": "trim_angle_20x12mm"}, {"name": "Trim Angle 20x20mm", "value": "trim_angle_20x20mm"}, {"name": "Trim Angle 25x12mm", "value": "trim_angle_25x12mm"}, {"name": "Trim Angle 25x20mm", "value": "trim_angle_25x20mm"}, {"name": "Trim Angle 25x25mm", "value": "trim_angle_25x25mm"}, {"name": "Trim Angle 32x20mm", "value": "trim_angle_32x20mm"}, {"name": "Trim Angle 40x12mm", "value": "trim_angle_40x12mm"}, {"name": "Trim Angle 40x20mm", "value": "trim_angle_40x20mm"}, {"name": "Trim Angle 50x25mm", "value": "trim_angle_50x25mm"}, {"name": "Window Screen Retainer Lug - Flat", "value": "window_screen_retainer_lug_flat"}, {"name": "Window Screen Retainer Lug - Shallow Offset", "value": "window_screen_retainer_lug_shallow_offset"}, {"name": "Window Screen Retainer Lug - Deep Offset", "value": "window_screen_retainer_lug_deep_offset"}, {"name": "No", "value": "no"}]}, {"name": "Box - SHS Square Corners 19x1.6mm Cut Type", "technical_name": "sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type", "field_type": "selection", "sequence": 11, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_shs_square_corners_19x1_6mm'", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_long_point_to_long_point"}, {"name": "Straight Cut One End, Mitre Cut One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_one_end"}]}, {"name": "Box - SHS Square Corners 19x1.6mm Length 1 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_19x1_6mm_length_1_mm", "field_type": "number", "sequence": 12, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_shs_square_corners_19x1_6mm' && (sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type == 'straight_cut' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 19x1.6mm Length 2 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_19x1_6mm_length_2_mm", "field_type": "number", "sequence": 13, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_shs_square_corners_19x1_6mm' && (sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type == 'straight_cut' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 19x1.6mm Length 3 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_19x1_6mm_length_3_mm", "field_type": "number", "sequence": 14, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_shs_square_corners_19x1_6mm' && (sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type == 'straight_cut' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 19x1.6mm Length 4 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_19x1_6mm_length_4_mm", "field_type": "number", "sequence": 15, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_shs_square_corners_19x1_6mm' && (sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type == 'straight_cut' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 25x1.6mm Cut Type", "technical_name": "sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type", "field_type": "selection", "sequence": 16, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_shs_square_corners_25x1_6mm'", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_long_point_to_long_point"}, {"name": "Straight Cut One End, Mitre Cut One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_one_end"}]}, {"name": "Box - SHS Square Corners 25x1.6mm Length 1 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_25x1_6mm_length_1_mm", "field_type": "number", "sequence": 17, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_shs_square_corners_25x1_6mm' && (sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type == 'straight_cut' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 25x1.6mm Length 2 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_25x1_6mm_length_2_mm", "field_type": "number", "sequence": 18, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_shs_square_corners_25x1_6mm' && (sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type == 'straight_cut' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 25x1.6mm Length 3 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_25x1_6mm_length_3_mm", "field_type": "number", "sequence": 19, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_shs_square_corners_25x1_6mm' && (sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type == 'straight_cut' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 25x1.6mm Length 4 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_25x1_6mm_length_4_mm", "field_type": "number", "sequence": 20, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_shs_square_corners_25x1_6mm' && (sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type == 'straight_cut' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 50x1.6mm Cut Type", "technical_name": "sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type", "field_type": "selection", "sequence": 21, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_shs_square_corners_50x1_6mm'", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_long_point_to_long_point"}, {"name": "Straight Cut One End, Mitre Cut One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_one_end"}]}, {"name": "Box - SHS Square Corners 50x1.6mm Length 1 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_50x1_6mm_length_1_mm", "field_type": "number", "sequence": 22, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_shs_square_corners_50x1_6mm' && (sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type == 'straight_cut' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 50x1.6mm Length 2 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_50x1_6mm_length_2_mm", "field_type": "number", "sequence": 23, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_shs_square_corners_50x1_6mm' && (sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type == 'straight_cut' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 50x1.6mm Length 3 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_50x1_6mm_length_3_mm", "field_type": "number", "sequence": 24, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_shs_square_corners_50x1_6mm' && (sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type == 'straight_cut' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 50x1.6mm Length 4 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_50x1_6mm_length_4_mm", "field_type": "number", "sequence": 25, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_shs_square_corners_50x1_6mm' && (sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type == 'straight_cut' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - RHS Square Corners 50x25x1.6mm Cut Type", "technical_name": "sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type", "field_type": "selection", "sequence": 26, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_rhs_square_corners_50x25x1_6mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face.", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_cut_small_face"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_cut_large_face"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face"}]}, {"name": "Box - RHS Square Corners 50x25x1.6mm Length 1 (mm)", "technical_name": "sws_wscrn_box_rhs_square_corners_50x25x1_6mm_length_1_mm", "field_type": "number", "sequence": 27, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_rhs_square_corners_50x25x1_6mm' && (sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type == 'straight_cut' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type == 'mitre_cut_large_face_bevel_cut_small_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type == 'mitre_cut_small_face_bevel_cut_large_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type == 'straight_cut_one_end_mitre_cut_large_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type == 'straight_cut_one_end_mitre_cut_small_face')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - RHS Square Corners 50x25x1.6mm Length 2 (mm)", "technical_name": "sws_wscrn_box_rhs_square_corners_50x25x1_6mm_length_2_mm", "field_type": "number", "sequence": 28, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_rhs_square_corners_50x25x1_6mm' && (sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type == 'straight_cut' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type == 'mitre_cut_large_face_bevel_cut_small_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type == 'mitre_cut_small_face_bevel_cut_large_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type == 'straight_cut_one_end_mitre_cut_large_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type == 'straight_cut_one_end_mitre_cut_small_face')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - RHS Square Corners 50x25x1.6mm Length 3 (mm)", "technical_name": "sws_wscrn_box_rhs_square_corners_50x25x1_6mm_length_3_mm", "field_type": "number", "sequence": 29, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_rhs_square_corners_50x25x1_6mm' && (sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type == 'straight_cut' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type == 'mitre_cut_large_face_bevel_cut_small_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type == 'mitre_cut_small_face_bevel_cut_large_face')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - RHS Square Corners 50x25x1.6mm Length 4 (mm)", "technical_name": "sws_wscrn_box_rhs_square_corners_50x25x1_6mm_length_4_mm", "field_type": "number", "sequence": 30, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'box_rhs_square_corners_50x25x1_6mm' && (sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type == 'straight_cut' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type == 'mitre_cut_large_face_bevel_cut_small_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type == 'mitre_cut_small_face_bevel_cut_large_face')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Bugstrip - 16mm with 16mm Pile Seal Length 1 (mm)", "technical_name": "sws_wscrn_bugstrip_16mm_16mm_pile_seal_length_1_mm", "field_type": "number", "sequence": 31, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'bugstrip_16mm_16mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_1 == 'bugstrip_16mm_28mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_1 == 'bugstrip_25mm_16mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_1 == 'bugstrip_25mm_28mm_pile_seal'", "help_text": "*Max: 4300mm. Maximum Cut Length is 4300mm. Order Full Length in Extrusion Category."}, {"name": "Bugstrip - 16mm with 16mm Pile Seal Length 2 (mm)", "technical_name": "sws_wscrn_bugstrip_16mm_16mm_pile_seal_length_2_mm", "field_type": "number", "sequence": 32, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'bugstrip_16mm_16mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_1 == 'bugstrip_16mm_28mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_1 == 'bugstrip_25mm_16mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_1 == 'bugstrip_25mm_28mm_pile_seal'", "help_text": "*Max: 4300mm. Maximum Cut Length is 4300mm. Order Full Length in Extrusion Category."}, {"name": "Fire Attenuation Metallic Label 118 x 50 x 1mm - Red Quantity", "technical_name": "sws_wscrn_fire_attenuation_metallic_label_red_quantity", "field_type": "number", "sequence": 33, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'fire_attenuation_metallic_label_red'"}, {"name": "Flyscreen Track - Single Top Length 1 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_top_length_1_mm", "field_type": "number", "sequence": 34, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_single_top' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_single_bottom' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_face_fix_single_top' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_face_fix_single_bottom' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_double_top' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_double_bottom' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_face_fix_double_top' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_face_fix_double_bottom'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Flyscreen Track - Single Top Length 2 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_top_length_2_mm", "field_type": "number", "sequence": 35, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_single_top' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_face_fix_single_top' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_double_top' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_face_fix_double_top'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Flyscreen Track - Single Top Length 3 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_top_length_3_mm", "field_type": "number", "sequence": 36, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_single_top' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_face_fix_single_top' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_double_top' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_face_fix_double_top'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Flyscreen Track - Single Bottom Length 1 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_bottom_length_1_mm", "field_type": "number", "sequence": 37, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_single_bottom' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_face_fix_single_bottom' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_double_bottom' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_face_fix_double_bottom'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Flyscreen Track - Single Bottom Length 2 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_bottom_length_2_mm", "field_type": "number", "sequence": 38, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_single_bottom' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_face_fix_single_bottom' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_double_bottom' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_face_fix_double_bottom'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Flyscreen Track - Single Bottom Length 3 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_bottom_length_3_mm", "field_type": "number", "sequence": 39, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_single_bottom' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_face_fix_single_bottom' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_double_bottom' || sws_wscrn_fitting_accessories_supply_only_1 == 'flyscreen_track_face_fix_double_bottom'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Interlock - Flat Length 1 (mm)", "technical_name": "sws_wscrn_interlock_flat_length_1_mm", "field_type": "number", "sequence": 40, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'interlock_flat' || sws_wscrn_fitting_accessories_supply_only_1 == 'interlock_shallow_offset' || sws_wscrn_fitting_accessories_supply_only_1 == 'interlock_deep_offset'", "help_text": "*Max: 4750mm. Maximum Cut Length is 4750mm. Order Full Length in Extrusion Category."}, {"name": "Interlock - Flat Length 2 (mm)", "technical_name": "sws_wscrn_interlock_flat_length_2_mm", "field_type": "number", "sequence": 41, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'interlock_flat' || sws_wscrn_fitting_accessories_supply_only_1 == 'interlock_shallow_offset' || sws_wscrn_fitting_accessories_supply_only_1 == 'interlock_deep_offset'", "help_text": "*Max: 4750mm. Maximum Cut Length is 4750mm. Order Full Length in Extrusion Category."}, {"name": "Louvre Build Out (including corner angles) LEFT Height (outside view) (mm)", "technical_name": "sws_wscrn_louvre_build_out_left_height_mm", "field_type": "number", "sequence": 42, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'louvre_build_out_including_corner_angles'", "help_text": "*Max: 5400mm. Maximum Cut Length is 5400mm. Order Full Length in Extrusion Category."}, {"name": "Louvre Build Out (including corner angles) TOP Width (mm)", "technical_name": "sws_wscrn_louvre_build_out_top_width_mm", "field_type": "number", "sequence": 43, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'louvre_build_out_including_corner_angles'", "help_text": "*Max: 5400mm. Maximum Cut Length is 5400mm. Order Full Length in Extrusion Category."}, {"name": "Louvre Build Out (including corner angles) RIGHT Height (outside view) (mm)", "technical_name": "sws_wscrn_louvre_build_out_right_height_mm", "field_type": "number", "sequence": 44, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'louvre_build_out_including_corner_angles'", "help_text": "*Max: 5400mm. Maximum Cut Length is 5400mm. Order Full Length in Extrusion Category."}, {"name": "Louvre Build Out (including corner angles) BOTTOM Width (mm)", "technical_name": "sws_wscrn_louvre_build_out_bottom_width_mm", "field_type": "number", "sequence": 45, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'louvre_build_out_including_corner_angles'", "help_text": "*Max: 5400mm. Maximum Cut Length is 5400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 12x12mm Cut Type", "technical_name": "sws_wscrn_trim_angle_12x12mm_cut_type", "field_type": "selection", "sequence": 46, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_12x12mm'", "help_text": "*Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_internal_fit"}, {"name": "Mitre Cut for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_external_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for Internal Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_internal_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for External Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_external_fit"}]}, {"name": "Trim Angle 12x12mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_12x12mm_length_1_mm", "field_type": "number", "sequence": 47, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_12x12mm' && (sws_wscrn_trim_angle_12x12mm_cut_type == 'straight_cut' || sws_wscrn_trim_angle_12x12mm_cut_type == 'mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type == 'mitre_cut_external_fit' || sws_wscrn_trim_angle_12x12mm_cut_type == 'straight_cut_one_end_mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type == 'straight_cut_one_end_mitre_cut_external_fit')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 12x12mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_12x12mm_length_2_mm", "field_type": "number", "sequence": 48, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_12x12mm' && (sws_wscrn_trim_angle_12x12mm_cut_type == 'straight_cut' || sws_wscrn_trim_angle_12x12mm_cut_type == 'mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type == 'mitre_cut_external_fit' || sws_wscrn_trim_angle_12x12mm_cut_type == 'straight_cut_one_end_mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type == 'straight_cut_one_end_mitre_cut_external_fit')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 12x12mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_12x12mm_length_3_mm", "field_type": "number", "sequence": 49, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_12x12mm' && (sws_wscrn_trim_angle_12x12mm_cut_type == 'straight_cut' || sws_wscrn_trim_angle_12x12mm_cut_type == 'mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type == 'mitre_cut_external_fit')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 12x12mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_12x12mm_length_4_mm", "field_type": "number", "sequence": 50, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_12x12mm' && (sws_wscrn_trim_angle_12x12mm_cut_type == 'straight_cut' || sws_wscrn_trim_angle_12x12mm_cut_type == 'mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type == 'mitre_cut_external_fit')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x12mm Cut Type", "technical_name": "sws_wscrn_trim_angle_20x12mm_cut_type", "field_type": "selection", "sequence": 51, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_20x12mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 20x12mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_20x12mm_length_1_mm", "field_type": "number", "sequence": 52, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_20x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x12mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_20x12mm_length_2_mm", "field_type": "number", "sequence": 53, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_20x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x12mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_20x12mm_length_3_mm", "field_type": "number", "sequence": 54, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_20x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x12mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_20x12mm_length_4_mm", "field_type": "number", "sequence": 55, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_20x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x20mm Cut Type", "technical_name": "sws_wscrn_trim_angle_20x20mm_cut_type", "field_type": "selection", "sequence": 56, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_20x20mm'", "help_text": "*Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_internal_fit"}, {"name": "Mitre Cut for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_external_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for Internal Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_internal_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for External Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_external_fit"}]}, {"name": "Trim Angle 20x20mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_20x20mm_length_1_mm", "field_type": "number", "sequence": 57, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_20x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x20mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_20x20mm_length_2_mm", "field_type": "number", "sequence": 58, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_20x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x20mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_20x20mm_length_3_mm", "field_type": "number", "sequence": 59, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_20x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x20mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_20x20mm_length_4_mm", "field_type": "number", "sequence": 60, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_20x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x12mm Cut Type", "technical_name": "sws_wscrn_trim_angle_25x12mm_cut_type", "field_type": "selection", "sequence": 61, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_25x12mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 25x12mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_25x12mm_length_1_mm", "field_type": "number", "sequence": 62, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_25x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x12mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_25x12mm_length_2_mm", "field_type": "number", "sequence": 63, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_25x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x12mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_25x12mm_length_3_mm", "field_type": "number", "sequence": 64, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_25x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x12mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_25x12mm_length_4_mm", "field_type": "number", "sequence": 65, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_25x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x20mm Cut Type", "technical_name": "sws_wscrn_trim_angle_25x20mm_cut_type", "field_type": "selection", "sequence": 66, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_25x20mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 25x20mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_25x20mm_length_1_mm", "field_type": "number", "sequence": 67, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_25x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x20mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_25x20mm_length_2_mm", "field_type": "number", "sequence": 68, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_25x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x20mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_25x20mm_length_3_mm", "field_type": "number", "sequence": 69, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_25x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x20mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_25x20mm_length_4_mm", "field_type": "number", "sequence": 70, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_25x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x25mm Cut Type", "technical_name": "sws_wscrn_trim_angle_25x25mm_cut_type", "field_type": "selection", "sequence": 71, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_25x25mm'", "help_text": "*Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_internal_fit"}, {"name": "Mitre Cut for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_external_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for Internal Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_internal_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for External Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_external_fit"}]}, {"name": "Trim Angle 25x25mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_25x25mm_length_1_mm", "field_type": "number", "sequence": 72, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_25x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x25mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_25x25mm_length_2_mm", "field_type": "number", "sequence": 73, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_25x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x25mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_25x25mm_length_3_mm", "field_type": "number", "sequence": 74, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_25x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x25mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_25x25mm_length_4_mm", "field_type": "number", "sequence": 75, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_25x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 32x20mm Cut Type", "technical_name": "sws_wscrn_trim_angle_32x20mm_cut_type", "field_type": "selection", "sequence": 76, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_32x20mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 32x20mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_32x20mm_length_1_mm", "field_type": "number", "sequence": 77, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_32x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 32x20mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_32x20mm_length_2_mm", "field_type": "number", "sequence": 78, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_32x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 32x20mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_32x20mm_length_3_mm", "field_type": "number", "sequence": 79, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_32x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 32x20mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_32x20mm_length_4_mm", "field_type": "number", "sequence": 80, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_32x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x12mm Cut Type", "technical_name": "sws_wscrn_trim_angle_40x12mm_cut_type", "field_type": "selection", "sequence": 81, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_40x12mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 40x12mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_40x12mm_length_1_mm", "field_type": "number", "sequence": 82, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_40x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x12mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_40x12mm_length_2_mm", "field_type": "number", "sequence": 83, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_40x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x12mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_40x12mm_length_3_mm", "field_type": "number", "sequence": 84, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_40x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x12mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_40x12mm_length_4_mm", "field_type": "number", "sequence": 85, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_40x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x20mm Cut Type", "technical_name": "sws_wscrn_trim_angle_40x20mm_cut_type", "field_type": "selection", "sequence": 86, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_40x20mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 40x20mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_40x20mm_length_1_mm", "field_type": "number", "sequence": 87, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_40x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x20mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_40x20mm_length_2_mm", "field_type": "number", "sequence": 88, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_40x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x20mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_40x20mm_length_3_mm", "field_type": "number", "sequence": 89, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_40x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x20mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_40x20mm_length_4_mm", "field_type": "number", "sequence": 90, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_40x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 50x25mm Cut Type", "technical_name": "sws_wscrn_trim_angle_50x25mm_cut_type", "field_type": "selection", "sequence": 91, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_50x25mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 50x25mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_50x25mm_length_1_mm", "field_type": "number", "sequence": 92, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_50x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 50x25mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_50x25mm_length_2_mm", "field_type": "number", "sequence": 93, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_50x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 50x25mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_50x25mm_length_3_mm", "field_type": "number", "sequence": 94, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_50x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 50x25mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_50x25mm_length_4_mm", "field_type": "number", "sequence": 95, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'trim_angle_50x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Window Screen Retainer Lug - Flat Colour", "technical_name": "sws_wscrn_window_screen_retainer_lug_flat_colour", "field_type": "selection", "sequence": 96, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'window_screen_retainer_lug_flat'", "options": [{"name": "Black", "value": "black"}, {"name": "Match Window Screen Frame", "value": "match_window_screen_frame"}]}, {"name": "Window Screen Retainer Lug - Shallow Offset Colour", "technical_name": "sws_wscrn_window_screen_retainer_lug_shallow_offset_colour", "field_type": "selection", "sequence": 97, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'window_screen_retainer_lug_shallow_offset'", "options": [{"name": "Black", "value": "black"}, {"name": "Match Window Screen Frame", "value": "match_window_screen_frame"}]}, {"name": "Window Screen Retainer Lug - Deep Offset Colour", "technical_name": "sws_wscrn_window_screen_retainer_lug_deep_offset_colour", "field_type": "selection", "sequence": 98, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'window_screen_retainer_lug_deep_offset'", "options": [{"name": "Black", "value": "black"}, {"name": "Match Window Screen Frame", "value": "match_window_screen_frame"}]}, {"name": "Window Screen Retainer Lug Quantity", "technical_name": "sws_wscrn_window_screen_retainer_lug_quantity", "field_type": "number", "sequence": 99, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_1 == 'window_screen_retainer_lug_flat' || sws_wscrn_fitting_accessories_supply_only_1 == 'window_screen_retainer_lug_shallow_offset' || sws_wscrn_fitting_accessories_supply_only_1 == 'window_screen_retainer_lug_deep_offset'"}, {"name": "SWS-WSCRN Fitting Accessories (Supply Only) 2", "technical_name": "sws_wscrn_fitting_accessories_supply_only_2", "field_type": "selection", "sequence": 100, "options": [{"name": "Box - SHS Square Corners 19x1.6mm", "value": "box_shs_square_corners_19x1_6mm"}, {"name": "Box - SHS Square Corners 25x1.6mm", "value": "box_shs_square_corners_25x1_6mm"}, {"name": "Box - SHS Square Corners 50x1.6mm", "value": "box_shs_square_corners_50x1_6mm"}, {"name": "Box - RHS Square Corners 50x25x1.6mm", "value": "box_rhs_square_corners_50x25x1_6mm"}, {"name": "Bugstrip - 16mm with 16mm Pile Seal", "value": "bugstrip_16mm_16mm_pile_seal"}, {"name": "Bugstrip - 16mm with 28mm Pile Seal", "value": "bugstrip_16mm_28mm_pile_seal"}, {"name": "Bugstrip - 25mm with 16mm Pile Seal", "value": "bugstrip_25mm_16mm_pile_seal"}, {"name": "Bugstrip - 25mm with 28mm Pile Seal", "value": "bugstrip_25mm_28mm_pile_seal"}, {"name": "Fire Attenuation Metallic Label 118 x 50 x 1mm - Red", "value": "fire_attenuation_metallic_label_red"}, {"name": "Flyscreen Track - Single Top", "value": "flyscreen_track_single_top"}, {"name": "Flyscreen Track - Single Bottom", "value": "flyscreen_track_single_bottom"}, {"name": "Flyscreen Track - Face Fix Single Top", "value": "flyscreen_track_face_fix_single_top"}, {"name": "Flyscreen Track - Face Fix Single Bottom", "value": "flyscreen_track_face_fix_single_bottom"}, {"name": "Flyscreen Track - Double Top", "value": "flyscreen_track_double_top"}, {"name": "Flyscreen Track - Double Bottom", "value": "flyscreen_track_double_bottom"}, {"name": "Flyscreen Track - Face Fix Double Top", "value": "flyscreen_track_face_fix_double_top"}, {"name": "Flyscreen Track - Face Fix Double Bottom", "value": "flyscreen_track_face_fix_double_bottom"}, {"name": "Interlock - Flat", "value": "interlock_flat"}, {"name": "Interlock - Shallow Offset", "value": "interlock_shallow_offset"}, {"name": "Interlock - Deep Offset", "value": "interlock_deep_offset"}, {"name": "Louvre Build Out (including corner angles)", "value": "louvre_build_out_including_corner_angles"}, {"name": "Trim Angle 12x12mm", "value": "trim_angle_12x12mm"}, {"name": "Trim Angle 20x12mm", "value": "trim_angle_20x12mm"}, {"name": "Trim Angle 20x20mm", "value": "trim_angle_20x20mm"}, {"name": "Trim Angle 25x12mm", "value": "trim_angle_25x12mm"}, {"name": "Trim Angle 25x20mm", "value": "trim_angle_25x20mm"}, {"name": "Trim Angle 25x25mm", "value": "trim_angle_25x25mm"}, {"name": "Trim Angle 32x20mm", "value": "trim_angle_32x20mm"}, {"name": "Trim Angle 40x12mm", "value": "trim_angle_40x12mm"}, {"name": "Trim Angle 40x20mm", "value": "trim_angle_40x20mm"}, {"name": "Trim Angle 50x25mm", "value": "trim_angle_50x25mm"}, {"name": "Window Screen Retainer Lug - Flat", "value": "window_screen_retainer_lug_flat"}, {"name": "Window Screen Retainer Lug - Shallow Offset", "value": "window_screen_retainer_lug_shallow_offset"}, {"name": "Window Screen Retainer Lug - Deep Offset", "value": "window_screen_retainer_lug_deep_offset"}, {"name": "No", "value": "no"}]}, {"name": "Box - SHS Square Corners 19x1.6mm Cut Type", "technical_name": "sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_2", "field_type": "selection", "sequence": 101, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_shs_square_corners_19x1_6mm'", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_long_point_to_long_point"}, {"name": "Straight Cut One End, Mitre Cut One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_one_end"}]}, {"name": "Box - SHS Square Corners 19x1.6mm Length 1 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_19x1_6mm_length_1_mm_2", "field_type": "number", "sequence": 102, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_shs_square_corners_19x1_6mm' && (sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_2 == 'straight_cut' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_2 == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_2 == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 19x1.6mm Length 2 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_19x1_6mm_length_2_mm_2", "field_type": "number", "sequence": 103, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_shs_square_corners_19x1_6mm' && (sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_2 == 'straight_cut' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_2 == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_2 == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 19x1.6mm Length 3 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_19x1_6mm_length_3_mm_2", "field_type": "number", "sequence": 104, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_shs_square_corners_19x1_6mm' && (sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_2 == 'straight_cut' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_2 == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 19x1.6mm Length 4 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_19x1_6mm_length_4_mm_2", "field_type": "number", "sequence": 105, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_shs_square_corners_19x1_6mm' && (sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_2 == 'straight_cut' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_2 == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 25x1.6mm Cut Type", "technical_name": "sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_2", "field_type": "selection", "sequence": 106, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_shs_square_corners_25x1_6mm'", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_long_point_to_long_point"}, {"name": "Straight Cut One End, Mitre Cut One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_one_end"}]}, {"name": "Box - SHS Square Corners 25x1.6mm Length 1 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_25x1_6mm_length_1_mm_2", "field_type": "number", "sequence": 107, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_shs_square_corners_25x1_6mm' && (sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_2 == 'straight_cut' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_2 == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_2 == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 25x1.6mm Length 2 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_25x1_6mm_length_2_mm_2", "field_type": "number", "sequence": 108, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_shs_square_corners_25x1_6mm' && (sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_2 == 'straight_cut' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_2 == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_2 == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 25x1.6mm Length 3 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_25x1_6mm_length_3_mm_2", "field_type": "number", "sequence": 109, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_shs_square_corners_25x1_6mm' && (sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_2 == 'straight_cut' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_2 == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 25x1.6mm Length 4 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_25x1_6mm_length_4_mm_2", "field_type": "number", "sequence": 110, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_shs_square_corners_25x1_6mm' && (sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_2 == 'straight_cut' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_2 == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 50x1.6mm Cut Type", "technical_name": "sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_2", "field_type": "selection", "sequence": 111, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_shs_square_corners_50x1_6mm'", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_long_point_to_long_point"}, {"name": "Straight Cut One End, Mitre Cut One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_one_end"}]}, {"name": "Box - SHS Square Corners 50x1.6mm Length 1 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_50x1_6mm_length_1_mm_2", "field_type": "number", "sequence": 112, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_shs_square_corners_50x1_6mm' && (sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_2 == 'straight_cut' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_2 == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_2 == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 50x1.6mm Length 2 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_50x1_6mm_length_2_mm_2", "field_type": "number", "sequence": 113, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_shs_square_corners_50x1_6mm' && (sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_2 == 'straight_cut' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_2 == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_2 == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 50x1.6mm Length 3 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_50x1_6mm_length_3_mm_2", "field_type": "number", "sequence": 114, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_shs_square_corners_50x1_6mm' && (sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_2 == 'straight_cut' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_2 == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 50x1.6mm Length 4 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_50x1_6mm_length_4_mm_2", "field_type": "number", "sequence": 115, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_shs_square_corners_50x1_6mm' && (sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_2 == 'straight_cut' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_2 == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - RHS Square Corners 50x25x1.6mm Cut Type", "technical_name": "sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2", "field_type": "selection", "sequence": 116, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_rhs_square_corners_50x25x1_6mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face.", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_face"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_face"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face"}]}, {"name": "Box - RHS Square Corners 50x25x1.6mm Length 1 (mm)", "technical_name": "sws_wscrn_box_rhs_square_corners_50x25x1_6mm_length_1_mm_2", "field_type": "number", "sequence": 117, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_rhs_square_corners_50x25x1_6mm' && (sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2 == 'straight_cut' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2 == 'mitre_cut_large_face_bevel_small_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2 == 'mitre_cut_small_face_bevel_large_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2 == 'straight_cut_one_end_mitre_cut_large_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2 == 'straight_cut_one_end_mitre_cut_small_face')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - RHS Square Corners 50x25x1.6mm Length 2 (mm)", "technical_name": "sws_wscrn_box_rhs_square_corners_50x25x1_6mm_length_2_mm_2", "field_type": "number", "sequence": 118, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_rhs_square_corners_50x25x1_6mm' && (sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2 == 'straight_cut' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2 == 'mitre_cut_large_face_bevel_small_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2 == 'mitre_cut_small_face_bevel_large_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2 == 'straight_cut_one_end_mitre_cut_large_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2 == 'straight_cut_one_end_mitre_cut_small_face')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - RHS Square Corners 50x25x1.6mm Length 3 (mm)", "technical_name": "sws_wscrn_box_rhs_square_corners_50x25x1_6mm_length_3_mm_2", "field_type": "number", "sequence": 119, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_rhs_square_corners_50x25x1_6mm' && (sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2 == 'straight_cut' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2 == 'mitre_cut_large_face_bevel_small_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2 == 'mitre_cut_small_face_bevel_large_face')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - RHS Square Corners 50x25x1.6mm Length 4 (mm)", "technical_name": "sws_wscrn_box_rhs_square_corners_50x25x1_6mm_length_4_mm_2", "field_type": "number", "sequence": 120, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'box_rhs_square_corners_50x25x1_6mm' && (sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2 == 'straight_cut' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2 == 'mitre_cut_large_face_bevel_small_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_2 == 'mitre_cut_small_face_bevel_large_face')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Bugstrip - 16mm with 16mm Pile Seal Length 1 (mm)", "technical_name": "sws_wscrn_bugstrip_16mm_16mm_pile_seal_length_1_mm_2", "field_type": "number", "sequence": 121, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'bugstrip_16mm_16mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_2 == 'bugstrip_16mm_28mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_2 == 'bugstrip_25mm_16mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_2 == 'bugstrip_25mm_28mm_pile_seal'", "help_text": "*Max: 4300mm. Maximum Cut Length is 4300mm. Order Full Length in Extrusion Category."}, {"name": "Bugstrip - 16mm with 16mm Pile Seal Length 2 (mm)", "technical_name": "sws_wscrn_bugstrip_16mm_16mm_pile_seal_length_2_mm_2", "field_type": "number", "sequence": 122, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'bugstrip_16mm_16mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_2 == 'bugstrip_16mm_28mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_2 == 'bugstrip_25mm_16mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_2 == 'bugstrip_25mm_28mm_pile_seal'", "help_text": "*Max: 4300mm. Maximum Cut Length is 4300mm. Order Full Length in Extrusion Category."}, {"name": "Fire Attenuation Metallic Label 118 x 50 x 1mm - Red Quantity", "technical_name": "sws_wscrn_fire_attenuation_metallic_label_red_quantity_2", "field_type": "number", "sequence": 123, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'fire_attenuation_metallic_label_red'"}, {"name": "Flyscreen Track - Single Top Length 1 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_top_length_1_mm_2", "field_type": "number", "sequence": 124, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_single_top' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_single_bottom' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_face_fix_single_top' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_face_fix_single_bottom' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_double_top' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_double_bottom' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_face_fix_double_top' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_face_fix_double_bottom'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Flyscreen Track - Single Top Length 2 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_top_length_2_mm_2", "field_type": "number", "sequence": 125, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_single_top' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_face_fix_single_top' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_double_top' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_face_fix_double_top'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Flyscreen Track - Single Top Length 3 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_top_length_3_mm_2", "field_type": "number", "sequence": 126, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_single_top' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_face_fix_single_top' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_double_top' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_face_fix_double_top'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Flyscreen Track - Single Bottom Length 1 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_bottom_length_1_mm_2", "field_type": "number", "sequence": 127, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_single_bottom' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_face_fix_single_bottom' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_double_bottom' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_face_fix_double_bottom'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Flyscreen Track - Single Bottom Length 2 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_bottom_length_2_mm_2", "field_type": "number", "sequence": 128, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_single_bottom' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_face_fix_single_bottom' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_double_bottom' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_face_fix_double_bottom'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Flyscreen Track - Single Bottom Length 3 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_bottom_length_3_mm_2", "field_type": "number", "sequence": 129, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_single_bottom' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_face_fix_single_bottom' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_double_bottom' || sws_wscrn_fitting_accessories_supply_only_2 == 'flyscreen_track_face_fix_double_bottom'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Interlock - Flat Length 1 (mm)", "technical_name": "sws_wscrn_interlock_flat_length_1_mm_2", "field_type": "number", "sequence": 130, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'interlock_flat' || sws_wscrn_fitting_accessories_supply_only_2 == 'interlock_shallow_offset' || sws_wscrn_fitting_accessories_supply_only_2 == 'interlock_deep_offset'", "help_text": "*Max: 4750mm. Maximum Cut Length is 4750mm. Order Full Length in Extrusion Category."}, {"name": "Interlock - Flat Length 2 (mm)", "technical_name": "sws_wscrn_interlock_flat_length_2_mm_2", "field_type": "number", "sequence": 131, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'interlock_flat' || sws_wscrn_fitting_accessories_supply_only_2 == 'interlock_shallow_offset' || sws_wscrn_fitting_accessories_supply_only_2 == 'interlock_deep_offset'", "help_text": "*Max: 4750mm. Maximum Cut Length is 4750mm. Order Full Length in Extrusion Category."}, {"name": "Louvre Build Out (including corner angles) LEFT Height (outside view) (mm)", "technical_name": "sws_wscrn_louvre_build_out_left_height_mm_2", "field_type": "number", "sequence": 132, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'louvre_build_out_including_corner_angles'", "help_text": "*Max: 5400mm. Maximum Cut Length is 5400mm. Order Full Length in Extrusion Category."}, {"name": "Louvre Build Out (including corner angles) TOP Width (mm)", "technical_name": "sws_wscrn_louvre_build_out_top_width_mm_2", "field_type": "number", "sequence": 133, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'louvre_build_out_including_corner_angles'", "help_text": "*Max: 5400mm. Maximum Cut Length is 5400mm. Order Full Length in Extrusion Category."}, {"name": "Louvre Build Out (including corner angles) RIGHT Height (outside view) (mm)", "technical_name": "sws_wscrn_louvre_build_out_right_height_mm_2", "field_type": "number", "sequence": 134, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'louvre_build_out_including_corner_angles'", "help_text": "*Max: 5400mm. Maximum Cut Length is 5400mm. Order Full Length in Extrusion Category."}, {"name": "Louvre Build Out (including corner angles) BOTTOM Width (mm)", "technical_name": "sws_wscrn_louvre_build_out_bottom_width_mm_2", "field_type": "number", "sequence": 135, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'louvre_build_out_including_corner_angles'", "help_text": "*Max: 5400mm. Maximum Cut Length is 5400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 12x12mm Cut Type", "technical_name": "sws_wscrn_trim_angle_12x12mm_cut_type_2", "field_type": "selection", "sequence": 136, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_12x12mm'", "help_text": "*Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_internal_fit"}, {"name": "Mitre Cut for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_external_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for Internal Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_internal_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for External Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_external_fit"}]}, {"name": "Trim Angle 12x12mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_12x12mm_length_1_mm_2", "field_type": "number", "sequence": 137, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_12x12mm' && (sws_wscrn_trim_angle_12x12mm_cut_type_2 == 'straight_cut' || sws_wscrn_trim_angle_12x12mm_cut_type_2 == 'mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type_2 == 'mitre_cut_external_fit' || sws_wscrn_trim_angle_12x12mm_cut_type_2 == 'straight_cut_one_end_mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type_2 == 'straight_cut_one_end_mitre_cut_external_fit')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 12x12mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_12x12mm_length_2_mm_2", "field_type": "number", "sequence": 138, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_12x12mm' && (sws_wscrn_trim_angle_12x12mm_cut_type_2 == 'straight_cut' || sws_wscrn_trim_angle_12x12mm_cut_type_2 == 'mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type_2 == 'mitre_cut_external_fit' || sws_wscrn_trim_angle_12x12mm_cut_type_2 == 'straight_cut_one_end_mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type_2 == 'straight_cut_one_end_mitre_cut_external_fit')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 12x12mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_12x12mm_length_3_mm_2", "field_type": "number", "sequence": 139, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_12x12mm' && (sws_wscrn_trim_angle_12x12mm_cut_type_2 == 'straight_cut' || sws_wscrn_trim_angle_12x12mm_cut_type_2 == 'mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type_2 == 'mitre_cut_external_fit')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 12x12mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_12x12mm_length_4_mm_2", "field_type": "number", "sequence": 140, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_12x12mm' && (sws_wscrn_trim_angle_12x12mm_cut_type_2 == 'straight_cut' || sws_wscrn_trim_angle_12x12mm_cut_type_2 == 'mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type_2 == 'mitre_cut_external_fit')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x12mm Cut Type", "technical_name": "sws_wscrn_trim_angle_20x12mm_cut_type_2", "field_type": "selection", "sequence": 141, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_20x12mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 20x12mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_20x12mm_length_1_mm_2", "field_type": "number", "sequence": 142, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_20x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x12mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_20x12mm_length_2_mm_2", "field_type": "number", "sequence": 143, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_20x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x12mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_20x12mm_length_3_mm_2", "field_type": "number", "sequence": 144, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_20x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x12mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_20x12mm_length_4_mm_2", "field_type": "number", "sequence": 145, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_20x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x20mm Cut Type", "technical_name": "sws_wscrn_trim_angle_20x20mm_cut_type_2", "field_type": "selection", "sequence": 146, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_20x20mm'", "help_text": "*Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_internal_fit"}, {"name": "Mitre Cut for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_external_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for Internal Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_internal_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for External Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_external_fit"}]}, {"name": "Trim Angle 20x20mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_20x20mm_length_1_mm_2", "field_type": "number", "sequence": 147, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_20x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x20mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_20x20mm_length_2_mm_2", "field_type": "number", "sequence": 148, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_20x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x20mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_20x20mm_length_3_mm_2", "field_type": "number", "sequence": 149, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_20x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x20mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_20x20mm_length_4_mm_2", "field_type": "number", "sequence": 150, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_20x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x12mm Cut Type", "technical_name": "sws_wscrn_trim_angle_25x12mm_cut_type_2", "field_type": "selection", "sequence": 151, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_25x12mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 25x12mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_25x12mm_length_1_mm_2", "field_type": "number", "sequence": 152, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_25x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x12mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_25x12mm_length_2_mm_2", "field_type": "number", "sequence": 153, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_25x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x12mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_25x12mm_length_3_mm_2", "field_type": "number", "sequence": 154, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_25x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x12mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_25x12mm_length_4_mm_2", "field_type": "number", "sequence": 155, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_25x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x20mm Cut Type", "technical_name": "sws_wscrn_trim_angle_25x20mm_cut_type_2", "field_type": "selection", "sequence": 156, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_25x20mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 25x20mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_25x20mm_length_1_mm_2", "field_type": "number", "sequence": 157, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_25x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x20mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_25x20mm_length_2_mm_2", "field_type": "number", "sequence": 158, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_25x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x20mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_25x20mm_length_3_mm_2", "field_type": "number", "sequence": 159, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_25x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x20mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_25x20mm_length_4_mm_2", "field_type": "number", "sequence": 160, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_25x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x25mm Cut Type", "technical_name": "sws_wscrn_trim_angle_25x25mm_cut_type_2", "field_type": "selection", "sequence": 161, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_25x25mm'", "help_text": "*Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_internal_fit"}, {"name": "Mitre Cut for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_external_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for Internal Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_internal_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for External Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_external_fit"}]}, {"name": "Trim Angle 25x25mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_25x25mm_length_1_mm_2", "field_type": "number", "sequence": 162, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_25x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x25mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_25x25mm_length_2_mm_2", "field_type": "number", "sequence": 163, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_25x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x25mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_25x25mm_length_3_mm_2", "field_type": "number", "sequence": 164, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_25x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x25mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_25x25mm_length_4_mm_2", "field_type": "number", "sequence": 165, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_25x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 32x20mm Cut Type", "technical_name": "sws_wscrn_trim_angle_32x20mm_cut_type_2", "field_type": "selection", "sequence": 166, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_32x20mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 32x20mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_32x20mm_length_1_mm_2", "field_type": "number", "sequence": 167, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_32x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 32x20mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_32x20mm_length_2_mm_2", "field_type": "number", "sequence": 168, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_32x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 32x20mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_32x20mm_length_3_mm_2", "field_type": "number", "sequence": 169, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_32x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 32x20mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_32x20mm_length_4_mm_2", "field_type": "number", "sequence": 170, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_32x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x12mm Cut Type", "technical_name": "sws_wscrn_trim_angle_40x12mm_cut_type_2", "field_type": "selection", "sequence": 171, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_40x12mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 40x12mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_40x12mm_length_1_mm_2", "field_type": "number", "sequence": 172, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_40x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x12mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_40x12mm_length_2_mm_2", "field_type": "number", "sequence": 173, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_40x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x12mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_40x12mm_length_3_mm_2", "field_type": "number", "sequence": 174, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_40x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x12mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_40x12mm_length_4_mm_2", "field_type": "number", "sequence": 175, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_40x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x20mm Cut Type", "technical_name": "sws_wscrn_trim_angle_40x20mm_cut_type_2", "field_type": "selection", "sequence": 176, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_40x20mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 40x20mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_40x20mm_length_1_mm_2", "field_type": "number", "sequence": 177, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_40x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x20mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_40x20mm_length_2_mm_2", "field_type": "number", "sequence": 178, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_40x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x20mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_40x20mm_length_3_mm_2", "field_type": "number", "sequence": 179, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_40x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x20mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_40x20mm_length_4_mm_2", "field_type": "number", "sequence": 180, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_40x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 50x25mm Cut Type", "technical_name": "sws_wscrn_trim_angle_50x25mm_cut_type_2", "field_type": "selection", "sequence": 181, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_50x25mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 50x25mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_50x25mm_length_1_mm_2", "field_type": "number", "sequence": 182, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_50x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 50x25mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_50x25mm_length_2_mm_2", "field_type": "number", "sequence": 183, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_50x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 50x25mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_50x25mm_length_3_mm_2", "field_type": "number", "sequence": 184, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_50x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 50x25mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_50x25mm_length_4_mm_2", "field_type": "number", "sequence": 185, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'trim_angle_50x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Window Screen Retainer Lug - Flat Colour", "technical_name": "sws_wscrn_window_screen_retainer_lug_flat_colour_2", "field_type": "selection", "sequence": 186, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'window_screen_retainer_lug_flat'", "options": [{"name": "Black", "value": "black"}, {"name": "Match Window Screen Frame", "value": "match_window_screen_frame"}]}, {"name": "Window Screen Retainer Lug - Shallow Offset Colour", "technical_name": "sws_wscrn_window_screen_retainer_lug_shallow_offset_colour_2", "field_type": "selection", "sequence": 187, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'window_screen_retainer_lug_shallow_offset'", "options": [{"name": "Black", "value": "black"}, {"name": "Match Window Screen Frame", "value": "match_window_screen_frame"}]}, {"name": "Window Screen Retainer Lug - Deep Offset Colour", "technical_name": "sws_wscrn_window_screen_retainer_lug_deep_offset_colour_2", "field_type": "selection", "sequence": 188, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'window_screen_retainer_lug_deep_offset'", "options": [{"name": "Black", "value": "black"}, {"name": "Match Window Screen Frame", "value": "match_window_screen_frame"}]}, {"name": "Window Screen Retainer Lug Quantity", "technical_name": "sws_wscrn_window_screen_retainer_lug_quantity_2", "field_type": "number", "sequence": 189, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_2 == 'window_screen_retainer_lug_flat' || sws_wscrn_fitting_accessories_supply_only_2 == 'window_screen_retainer_lug_shallow_offset' || sws_wscrn_fitting_accessories_supply_only_2 == 'window_screen_retainer_lug_deep_offset'"}, {"name": "SWS-WSCRN Fitting Accessories (Supply Only) 3", "technical_name": "sws_wscrn_fitting_accessories_supply_only_3", "field_type": "selection", "sequence": 190, "options": [{"name": "Box - SHS Square Corners 19x1.6mm", "value": "box_shs_square_corners_19x1_6mm"}, {"name": "Box - SHS Square Corners 25x1.6mm", "value": "box_shs_square_corners_25x1_6mm"}, {"name": "Box - SHS Square Corners 50x1.6mm", "value": "box_shs_square_corners_50x1_6mm"}, {"name": "Box - RHS Square Corners 50x25x1.6mm", "value": "box_rhs_square_corners_50x25x1_6mm"}, {"name": "Bugstrip - 16mm with 16mm Pile Seal", "value": "bugstrip_16mm_16mm_pile_seal"}, {"name": "Bugstrip - 16mm with 28mm Pile Seal", "value": "bugstrip_16mm_28mm_pile_seal"}, {"name": "Bugstrip - 25mm with 16mm Pile Seal", "value": "bugstrip_25mm_16mm_pile_seal"}, {"name": "Bugstrip - 25mm with 28mm Pile Seal", "value": "bugstrip_25mm_28mm_pile_seal"}, {"name": "Fire Attenuation Metallic Label 118 x 50 x 1mm - Red", "value": "fire_attenuation_metallic_label_red"}, {"name": "Flyscreen Track - Single Top", "value": "flyscreen_track_single_top"}, {"name": "Flyscreen Track - Single Bottom", "value": "flyscreen_track_single_bottom"}, {"name": "Flyscreen Track - Face Fix Single Top", "value": "flyscreen_track_face_fix_single_top"}, {"name": "Flyscreen Track - Face Fix Single Bottom", "value": "flyscreen_track_face_fix_single_bottom"}, {"name": "Flyscreen Track - Double Top", "value": "flyscreen_track_double_top"}, {"name": "Flyscreen Track - Double Bottom", "value": "flyscreen_track_double_bottom"}, {"name": "Flyscreen Track - Face Fix Double Top", "value": "flyscreen_track_face_fix_double_top"}, {"name": "Flyscreen Track - Face Fix Double Bottom", "value": "flyscreen_track_face_fix_double_bottom"}, {"name": "Interlock - Flat", "value": "interlock_flat"}, {"name": "Interlock - Shallow Offset", "value": "interlock_shallow_offset"}, {"name": "Interlock - Deep Offset", "value": "interlock_deep_offset"}, {"name": "Louvre Build Out (including corner angles)", "value": "louvre_build_out_including_corner_angles"}, {"name": "Trim Angle 12x12mm", "value": "trim_angle_12x12mm"}, {"name": "Trim Angle 20x12mm", "value": "trim_angle_20x12mm"}, {"name": "Trim Angle 20x20mm", "value": "trim_angle_20x20mm"}, {"name": "Trim Angle 25x12mm", "value": "trim_angle_25x12mm"}, {"name": "Trim Angle 25x20mm", "value": "trim_angle_25x20mm"}, {"name": "Trim Angle 25x25mm", "value": "trim_angle_25x25mm"}, {"name": "Trim Angle 32x20mm", "value": "trim_angle_32x20mm"}, {"name": "Trim Angle 40x12mm", "value": "trim_angle_40x12mm"}, {"name": "Trim Angle 40x20mm", "value": "trim_angle_40x20mm"}, {"name": "Trim Angle 50x25mm", "value": "trim_angle_50x25mm"}, {"name": "Window Screen Retainer Lug - Flat", "value": "window_screen_retainer_lug_flat"}, {"name": "Window Screen Retainer Lug - Shallow Offset", "value": "window_screen_retainer_lug_shallow_offset"}, {"name": "Window Screen Retainer Lug - Deep Offset", "value": "window_screen_retainer_lug_deep_offset"}, {"name": "No", "value": "no"}]}, {"name": "Box - SHS Square Corners 19x1.6mm Cut Type", "technical_name": "sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_3", "field_type": "selection", "sequence": 191, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_shs_square_corners_19x1_6mm'", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_long_point_to_long_point"}, {"name": "Straight Cut One End, Mitre Cut One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_one_end"}]}, {"name": "Box - SHS Square Corners 19x1.6mm Length 1 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_19x1_6mm_length_1_mm_3", "field_type": "number", "sequence": 192, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_shs_square_corners_19x1_6mm' && (sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_3 == 'straight_cut' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_3 == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_3 == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 19x1.6mm Length 2 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_19x1_6mm_length_2_mm_3", "field_type": "number", "sequence": 193, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_shs_square_corners_19x1_6mm' && (sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_3 == 'straight_cut' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_3 == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_3 == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 19x1.6mm Length 3 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_19x1_6mm_length_3_mm_3", "field_type": "number", "sequence": 194, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_shs_square_corners_19x1_6mm' && (sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_3 == 'straight_cut' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_3 == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 19x1.6mm Length 4 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_19x1_6mm_length_4_mm_3", "field_type": "number", "sequence": 195, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_shs_square_corners_19x1_6mm' && (sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_3 == 'straight_cut' || sws_wscrn_box_shs_square_corners_19x1_6mm_cut_type_3 == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 25x1.6mm Cut Type", "technical_name": "sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_3", "field_type": "selection", "sequence": 196, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_shs_square_corners_25x1_6mm'", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_long_point_to_long_point"}, {"name": "Straight Cut One End, Mitre Cut One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_one_end"}]}, {"name": "Box - SHS Square Corners 25x1.6mm Length 1 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_25x1_6mm_length_1_mm_3", "field_type": "number", "sequence": 197, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_shs_square_corners_25x1_6mm' && (sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_3 == 'straight_cut' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_3 == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_3 == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 25x1.6mm Length 2 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_25x1_6mm_length_2_mm_3", "field_type": "number", "sequence": 198, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_shs_square_corners_25x1_6mm' && (sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_3 == 'straight_cut' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_3 == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_3 == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 25x1.6mm Length 3 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_25x1_6mm_length_3_mm_3", "field_type": "number", "sequence": 199, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_shs_square_corners_25x1_6mm' && (sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_3 == 'straight_cut' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_3 == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 25x1.6mm Length 4 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_25x1_6mm_length_4_mm_3", "field_type": "number", "sequence": 200, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_shs_square_corners_25x1_6mm' && (sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_3 == 'straight_cut' || sws_wscrn_box_shs_square_corners_25x1_6mm_cut_type_3 == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 50x1.6mm Cut Type", "technical_name": "sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_3", "field_type": "selection", "sequence": 201, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_shs_square_corners_50x1_6mm'", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_long_point_to_long_point"}, {"name": "Straight Cut One End, Mitre Cut One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_one_end"}]}, {"name": "Box - SHS Square Corners 50x1.6mm Length 1 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_50x1_6mm_length_1_mm_3", "field_type": "number", "sequence": 202, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_shs_square_corners_50x1_6mm' && (sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_3 == 'straight_cut' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_3 == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_3 == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 50x1.6mm Length 2 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_50x1_6mm_length_2_mm_3", "field_type": "number", "sequence": 203, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_shs_square_corners_50x1_6mm' && (sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_3 == 'straight_cut' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_3 == 'mitre_cut_long_point_to_long_point' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_3 == 'straight_cut_one_end_mitre_cut_one_end')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 50x1.6mm Length 3 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_50x1_6mm_length_3_mm_3", "field_type": "number", "sequence": 204, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_shs_square_corners_50x1_6mm' && (sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_3 == 'straight_cut' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_3 == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - SHS Square Corners 50x1.6mm Length 4 (mm)", "technical_name": "sws_wscrn_box_shs_square_corners_50x1_6mm_length_4_mm_3", "field_type": "number", "sequence": 205, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_shs_square_corners_50x1_6mm' && (sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_3 == 'straight_cut' || sws_wscrn_box_shs_square_corners_50x1_6mm_cut_type_3 == 'mitre_cut_long_point_to_long_point')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - RHS Square Corners 50x25x1.6mm Cut Type", "technical_name": "sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3", "field_type": "selection", "sequence": 206, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_rhs_square_corners_50x25x1_6mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face.", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_face"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_face"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face"}]}, {"name": "Box - RHS Square Corners 50x25x1.6mm Length 1 (mm)", "technical_name": "sws_wscrn_box_rhs_square_corners_50x25x1_6mm_length_1_mm_3", "field_type": "number", "sequence": 207, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_rhs_square_corners_50x25x1_6mm' && (sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3 == 'straight_cut' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3 == 'mitre_cut_large_face_bevel_small_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3 == 'mitre_cut_small_face_bevel_large_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3 == 'straight_cut_one_end_mitre_cut_large_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3 == 'straight_cut_one_end_mitre_cut_small_face')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - RHS Square Corners 50x25x1.6mm Length 2 (mm)", "technical_name": "sws_wscrn_box_rhs_square_corners_50x25x1_6mm_length_2_mm_3", "field_type": "number", "sequence": 208, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_rhs_square_corners_50x25x1_6mm' && (sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3 == 'straight_cut' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3 == 'mitre_cut_large_face_bevel_small_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3 == 'mitre_cut_small_face_bevel_large_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3 == 'straight_cut_one_end_mitre_cut_large_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3 == 'straight_cut_one_end_mitre_cut_small_face')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - RHS Square Corners 50x25x1.6mm Length 3 (mm)", "technical_name": "sws_wscrn_box_rhs_square_corners_50x25x1_6mm_length_3_mm_3", "field_type": "number", "sequence": 209, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_rhs_square_corners_50x25x1_6mm' && (sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3 == 'straight_cut' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3 == 'mitre_cut_large_face_bevel_small_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3 == 'mitre_cut_small_face_bevel_large_face')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Box - RHS Square Corners 50x25x1.6mm Length 4 (mm)", "technical_name": "sws_wscrn_box_rhs_square_corners_50x25x1_6mm_length_4_mm_3", "field_type": "number", "sequence": 210, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'box_rhs_square_corners_50x25x1_6mm' && (sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3 == 'straight_cut' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3 == 'mitre_cut_large_face_bevel_small_face' || sws_wscrn_box_rhs_square_corners_50x25x1_6mm_cut_type_3 == 'mitre_cut_small_face_bevel_large_face')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Bugstrip - 16mm with 16mm Pile Seal Length 1 (mm)", "technical_name": "sws_wscrn_bugstrip_16mm_16mm_pile_seal_length_1_mm_3", "field_type": "number", "sequence": 211, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'bugstrip_16mm_16mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_3 == 'bugstrip_16mm_28mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_3 == 'bugstrip_25mm_16mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_3 == 'bugstrip_25mm_28mm_pile_seal'", "help_text": "*Max: 4300mm. Maximum Cut Length is 4300mm. Order Full Length in Extrusion Category."}, {"name": "Bugstrip - 16mm with 16mm Pile Seal Length 2 (mm)", "technical_name": "sws_wscrn_bugstrip_16mm_16mm_pile_seal_length_2_mm_3", "field_type": "number", "sequence": 212, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'bugstrip_16mm_16mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_3 == 'bugstrip_16mm_28mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_3 == 'bugstrip_25mm_16mm_pile_seal' || sws_wscrn_fitting_accessories_supply_only_3 == 'bugstrip_25mm_28mm_pile_seal'", "help_text": "*Max: 4300mm. Maximum Cut Length is 4300mm. Order Full Length in Extrusion Category."}, {"name": "Fire Attenuation Metallic Label 118 x 50 x 1mm - Red Quantity", "technical_name": "sws_wscrn_fire_attenuation_metallic_label_red_quantity_3", "field_type": "number", "sequence": 213, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'fire_attenuation_metallic_label_red'"}, {"name": "Flyscreen Track - Single Top Length 1 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_top_length_1_mm_3", "field_type": "number", "sequence": 214, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_single_top' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_single_bottom' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_face_fix_single_top' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_face_fix_single_bottom' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_double_top' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_double_bottom' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_face_fix_double_top' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_face_fix_double_bottom'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Flyscreen Track - Single Top Length 2 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_top_length_2_mm_3", "field_type": "number", "sequence": 215, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_single_top' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_face_fix_single_top' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_double_top' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_face_fix_double_top'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Flyscreen Track - Single Top Length 3 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_top_length_3_mm_3", "field_type": "number", "sequence": 216, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_single_top' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_face_fix_single_top' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_double_top' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_face_fix_double_top'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Flyscreen Track - Single Bottom Length 1 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_bottom_length_1_mm_3", "field_type": "number", "sequence": 217, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_single_bottom' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_face_fix_single_bottom' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_double_bottom' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_face_fix_double_bottom'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Flyscreen Track - Single Bottom Length 2 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_bottom_length_2_mm_3", "field_type": "number", "sequence": 218, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_single_bottom' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_face_fix_single_bottom' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_double_bottom' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_face_fix_double_bottom'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Flyscreen Track - Single Bottom Length 3 (mm)", "technical_name": "sws_wscrn_flyscreen_track_single_bottom_length_3_mm_3", "field_type": "number", "sequence": 219, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_single_bottom' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_face_fix_single_bottom' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_double_bottom' || sws_wscrn_fitting_accessories_supply_only_3 == 'flyscreen_track_face_fix_double_bottom'", "help_text": "*Max: 4900mm. Maximum Cut Length is 4900mm. Order Full Length in Extrusion Category."}, {"name": "Interlock - Flat Length 1 (mm)", "technical_name": "sws_wscrn_interlock_flat_length_1_mm_3", "field_type": "number", "sequence": 220, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'interlock_flat' || sws_wscrn_fitting_accessories_supply_only_3 == 'interlock_shallow_offset' || sws_wscrn_fitting_accessories_supply_only_3 == 'interlock_deep_offset'", "help_text": "*Max: 4750mm. Maximum Cut Length is 4750mm. Order Full Length in Extrusion Category."}, {"name": "Interlock - Flat Length 2 (mm)", "technical_name": "sws_wscrn_interlock_flat_length_2_mm_3", "field_type": "number", "sequence": 221, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'interlock_flat' || sws_wscrn_fitting_accessories_supply_only_3 == 'interlock_shallow_offset' || sws_wscrn_fitting_accessories_supply_only_3 == 'interlock_deep_offset'", "help_text": "*Max: 4750mm. Maximum Cut Length is 4750mm. Order Full Length in Extrusion Category."}, {"name": "Louvre Build Out (including corner angles) LEFT Height (outside view) (mm)", "technical_name": "sws_wscrn_louvre_build_out_left_height_mm_3", "field_type": "number", "sequence": 222, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'louvre_build_out_including_corner_angles'", "help_text": "*Max: 5400mm. Maximum Cut Length is 5400mm. Order Full Length in Extrusion Category."}, {"name": "Louvre Build Out (including corner angles) TOP Width (mm)", "technical_name": "sws_wscrn_louvre_build_out_top_width_mm_3", "field_type": "number", "sequence": 223, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'louvre_build_out_including_corner_angles'", "help_text": "*Max: 5400mm. Maximum Cut Length is 5400mm. Order Full Length in Extrusion Category."}, {"name": "Louvre Build Out (including corner angles) RIGHT Height (outside view) (mm)", "technical_name": "sws_wscrn_louvre_build_out_right_height_mm_3", "field_type": "number", "sequence": 224, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'louvre_build_out_including_corner_angles'", "help_text": "*Max: 5400mm. Maximum Cut Length is 5400mm. Order Full Length in Extrusion Category."}, {"name": "Louvre Build Out (including corner angles) BOTTOM Width (mm)", "technical_name": "sws_wscrn_louvre_build_out_bottom_width_mm_3", "field_type": "number", "sequence": 225, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'louvre_build_out_including_corner_angles'", "help_text": "*Max: 5400mm. Maximum Cut Length is 5400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 12x12mm Cut Type", "technical_name": "sws_wscrn_trim_angle_12x12mm_cut_type_3", "field_type": "selection", "sequence": 226, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_12x12mm'", "help_text": "*Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_internal_fit"}, {"name": "Mitre Cut for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_external_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for Internal Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_internal_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for External Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_external_fit"}]}, {"name": "Trim Angle 12x12mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_12x12mm_length_1_mm_3", "field_type": "number", "sequence": 227, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_12x12mm' && (sws_wscrn_trim_angle_12x12mm_cut_type_3 == 'straight_cut' || sws_wscrn_trim_angle_12x12mm_cut_type_3 == 'mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type_3 == 'mitre_cut_external_fit' || sws_wscrn_trim_angle_12x12mm_cut_type_3 == 'straight_cut_one_end_mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type_3 == 'straight_cut_one_end_mitre_cut_external_fit')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 12x12mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_12x12mm_length_2_mm_3", "field_type": "number", "sequence": 228, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_12x12mm' && (sws_wscrn_trim_angle_12x12mm_cut_type_3 == 'straight_cut' || sws_wscrn_trim_angle_12x12mm_cut_type_3 == 'mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type_3 == 'mitre_cut_external_fit' || sws_wscrn_trim_angle_12x12mm_cut_type_3 == 'straight_cut_one_end_mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type_3 == 'straight_cut_one_end_mitre_cut_external_fit')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 12x12mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_12x12mm_length_3_mm_3", "field_type": "number", "sequence": 229, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_12x12mm' && (sws_wscrn_trim_angle_12x12mm_cut_type_3 == 'straight_cut' || sws_wscrn_trim_angle_12x12mm_cut_type_3 == 'mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type_3 == 'mitre_cut_external_fit')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 12x12mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_12x12mm_length_4_mm_3", "field_type": "number", "sequence": 230, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_12x12mm' && (sws_wscrn_trim_angle_12x12mm_cut_type_3 == 'straight_cut' || sws_wscrn_trim_angle_12x12mm_cut_type_3 == 'mitre_cut_internal_fit' || sws_wscrn_trim_angle_12x12mm_cut_type_3 == 'mitre_cut_external_fit')", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x12mm Cut Type", "technical_name": "sws_wscrn_trim_angle_20x12mm_cut_type_3", "field_type": "selection", "sequence": 231, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_20x12mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 20x12mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_20x12mm_length_1_mm_3", "field_type": "number", "sequence": 232, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_20x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x12mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_20x12mm_length_2_mm_3", "field_type": "number", "sequence": 233, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_20x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x12mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_20x12mm_length_3_mm_3", "field_type": "number", "sequence": 234, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_20x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x12mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_20x12mm_length_4_mm_3", "field_type": "number", "sequence": 235, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_20x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x20mm Cut Type", "technical_name": "sws_wscrn_trim_angle_20x20mm_cut_type_3", "field_type": "selection", "sequence": 236, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_20x20mm'", "help_text": "*Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_internal_fit"}, {"name": "Mitre Cut for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_external_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for Internal Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_internal_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for External Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_external_fit"}]}, {"name": "Trim Angle 20x20mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_20x20mm_length_1_mm_3", "field_type": "number", "sequence": 237, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_20x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x20mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_20x20mm_length_2_mm_3", "field_type": "number", "sequence": 238, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_20x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x20mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_20x20mm_length_3_mm_3", "field_type": "number", "sequence": 239, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_20x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 20x20mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_20x20mm_length_4_mm_3", "field_type": "number", "sequence": 240, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_20x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x12mm Cut Type", "technical_name": "sws_wscrn_trim_angle_25x12mm_cut_type_3", "field_type": "selection", "sequence": 241, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_25x12mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 25x12mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_25x12mm_length_1_mm_3", "field_type": "number", "sequence": 242, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_25x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x12mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_25x12mm_length_2_mm_3", "field_type": "number", "sequence": 243, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_25x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x12mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_25x12mm_length_3_mm_3", "field_type": "number", "sequence": 244, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_25x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x12mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_25x12mm_length_4_mm_3", "field_type": "number", "sequence": 245, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_25x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x20mm Cut Type", "technical_name": "sws_wscrn_trim_angle_25x20mm_cut_type_3", "field_type": "selection", "sequence": 246, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_25x20mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 25x20mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_25x20mm_length_1_mm_3", "field_type": "number", "sequence": 247, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_25x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x20mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_25x20mm_length_2_mm_3", "field_type": "number", "sequence": 248, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_25x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x20mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_25x20mm_length_3_mm_3", "field_type": "number", "sequence": 249, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_25x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x20mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_25x20mm_length_4_mm_3", "field_type": "number", "sequence": 250, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_25x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x25mm Cut Type", "technical_name": "sws_wscrn_trim_angle_25x25mm_cut_type_3", "field_type": "selection", "sequence": 251, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_25x25mm'", "help_text": "*Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_internal_fit"}, {"name": "Mitre Cut for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_external_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for Internal Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_internal_fit"}, {"name": "Straight Cut One End, <PERSON><PERSON> <PERSON> for External Fit One End (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_external_fit"}]}, {"name": "Trim Angle 25x25mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_25x25mm_length_1_mm_3", "field_type": "number", "sequence": 252, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_25x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x25mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_25x25mm_length_2_mm_3", "field_type": "number", "sequence": 253, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_25x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x25mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_25x25mm_length_3_mm_3", "field_type": "number", "sequence": 254, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_25x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 25x25mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_25x25mm_length_4_mm_3", "field_type": "number", "sequence": 255, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_25x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 32x20mm Cut Type", "technical_name": "sws_wscrn_trim_angle_32x20mm_cut_type_3", "field_type": "selection", "sequence": 256, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_32x20mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 32x20mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_32x20mm_length_1_mm_3", "field_type": "number", "sequence": 257, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_32x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 32x20mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_32x20mm_length_2_mm_3", "field_type": "number", "sequence": 258, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_32x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 32x20mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_32x20mm_length_3_mm_3", "field_type": "number", "sequence": 259, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_32x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 32x20mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_32x20mm_length_4_mm_3", "field_type": "number", "sequence": 260, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_32x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x12mm Cut Type", "technical_name": "sws_wscrn_trim_angle_40x12mm_cut_type_3", "field_type": "selection", "sequence": 261, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_40x12mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 40x12mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_40x12mm_length_1_mm_3", "field_type": "number", "sequence": 262, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_40x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x12mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_40x12mm_length_2_mm_3", "field_type": "number", "sequence": 263, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_40x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x12mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_40x12mm_length_3_mm_3", "field_type": "number", "sequence": 264, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_40x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x12mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_40x12mm_length_4_mm_3", "field_type": "number", "sequence": 265, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_40x12mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x20mm Cut Type", "technical_name": "sws_wscrn_trim_angle_40x20mm_cut_type_3", "field_type": "selection", "sequence": 266, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_40x20mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 40x20mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_40x20mm_length_1_mm_3", "field_type": "number", "sequence": 267, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_40x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x20mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_40x20mm_length_2_mm_3", "field_type": "number", "sequence": 268, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_40x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x20mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_40x20mm_length_3_mm_3", "field_type": "number", "sequence": 269, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_40x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 40x20mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_40x20mm_length_4_mm_3", "field_type": "number", "sequence": 270, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_40x20mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 50x25mm Cut Type", "technical_name": "sws_wscrn_trim_angle_50x25mm_cut_type_3", "field_type": "selection", "sequence": 271, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_50x25mm'", "help_text": "*A mitre cut is an angled cut across the face of the extrusion. A bevel cut is an angled cut through the thickness of the extrusion face. *Open Diagram showing difference between Internal Fit Angle & External Fit Angle (Link to Open PDF attachment)", "options": [{"name": "Straight Cut", "value": "straight_cut"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_internal"}, {"name": "Mitre Cut Across Large Face, Bevel Cut Through Small Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_large_face_bevel_small_external"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for Internal Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_internal"}, {"name": "Mitre Cut Across Small Face, Bevel Cut Through Large Face for External Fit (Length is from long point of mitre to long point of mitre)", "value": "mitre_cut_small_face_bevel_large_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Large Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_large_face_external"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for Internal Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_internal"}, {"name": "Straight Cut One End, Mitre Cut One End Across Small Face for External Fit (Length is to long point of mitre)", "value": "straight_cut_one_end_mitre_cut_small_face_external"}]}, {"name": "Trim Angle 50x25mm Length 1 (mm)", "technical_name": "sws_wscrn_trim_angle_50x25mm_length_1_mm_3", "field_type": "number", "sequence": 272, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_50x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 50x25mm Length 2 (mm)", "technical_name": "sws_wscrn_trim_angle_50x25mm_length_2_mm_3", "field_type": "number", "sequence": 273, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_50x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 50x25mm Length 3 (mm)", "technical_name": "sws_wscrn_trim_angle_50x25mm_length_3_mm_3", "field_type": "number", "sequence": 274, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_50x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Trim Angle 50x25mm Length 4 (mm)", "technical_name": "sws_wscrn_trim_angle_50x25mm_length_4_mm_3", "field_type": "number", "sequence": 275, "default_value": 0, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'trim_angle_50x25mm'", "help_text": "*Max: 6400mm. Maximum Cut Length is 6400mm. Order Full Length in Extrusion Category."}, {"name": "Window Screen Retainer Lug - Flat Colour", "technical_name": "sws_wscrn_window_screen_retainer_lug_flat_colour_3", "field_type": "selection", "sequence": 276, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'window_screen_retainer_lug_flat'", "options": [{"name": "Black", "value": "black"}, {"name": "Match Window Screen Frame", "value": "match_window_screen_frame"}]}, {"name": "Window Screen Retainer Lug - Shallow Offset Colour", "technical_name": "sws_wscrn_window_screen_retainer_lug_shallow_offset_colour_3", "field_type": "selection", "sequence": 277, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'window_screen_retainer_lug_shallow_offset'", "options": [{"name": "Black", "value": "black"}, {"name": "Match Window Screen Frame", "value": "match_window_screen_frame"}]}, {"name": "Window Screen Retainer Lug - Deep Offset Colour", "technical_name": "sws_wscrn_window_screen_retainer_lug_deep_offset_colour_3", "field_type": "selection", "sequence": 278, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'window_screen_retainer_lug_deep_offset'", "options": [{"name": "Black", "value": "black"}, {"name": "Match Window Screen Frame", "value": "match_window_screen_frame"}]}, {"name": "Window Screen Retainer Lug Quantity", "technical_name": "sws_wscrn_window_screen_retainer_lug_quantity_3", "field_type": "number", "sequence": 279, "default_value": 1, "visibility_condition": "sws_wscrn_fitting_accessories_supply_only_3 == 'window_screen_retainer_lug_flat' || sws_wscrn_fitting_accessories_supply_only_3 == 'window_screen_retainer_lug_shallow_offset' || sws_wscrn_fitting_accessories_supply_only_3 == 'window_screen_retainer_lug_deep_offset'"}]}]}