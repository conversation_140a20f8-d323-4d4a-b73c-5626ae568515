#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for parseFloat and other JavaScript to Python conversions
"""

def test_parseFloat_conversion():
    """Test parseFloat conversion and evaluation"""
    
    # Test context with parseFloat function
    context = {
        'parseFloat': lambda x: float(x) if x else 0,
        'max': max,
        'min': min,
        'bx_dbl_hinge_make_each_door_height_mm_even': '200',
        'bx_dbl_hinge_make_left_door_height_mm_uneven': '180',
        'bx_dbl_hinge_make_right_door_height_mm_uneven': '160',
        '_CALCULATED_height_calculation_method': 'even',
        '_CALCULATED_manual_left_height': 100,
        '_CALCULATED_manual_right_height': 150
    }
    
    # Test cases
    test_cases = [
        "parseFloat(bx_dbl_hinge_make_each_door_height_mm_even) or 0",
        "max(parseFloat(bx_dbl_hinge_make_left_door_height_mm_uneven) or 0, parseFloat(bx_dbl_hinge_make_right_door_height_mm_uneven) or 0)",
        "max(_CALCULATED_manual_left_height, _CALCULATED_manual_right_height)",
        "(parseFloat(bx_dbl_hinge_make_each_door_height_mm_even) or 0) if (_CALCULATED_height_calculation_method == 'even') else max(parseFloat(bx_dbl_hinge_make_left_door_height_mm_uneven) or 0, parseFloat(bx_dbl_hinge_make_right_door_height_mm_uneven) or 0)"
    ]
    
    print("🧪 Testing parseFloat and expression evaluation")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case}")
        try:
            result = eval(test_case, context)
            print(f"✅ Result: {result}")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    # Test the full converted formula
    print(f"\n{'='*60}")
    print("Testing full converted formula:")
    print("=" * 60)
    
    full_formula = "(max(_CALCULATED_manual_left_height, _CALCULATED_manual_right_height) if (_CALCULATED_height_calculation_method == 'manual') else ((parseFloat(bx_dbl_hinge_make_each_door_height_mm_even)  or  0) if (_CALCULATED_height_calculation_method == 'even') else max(parseFloat(bx_dbl_hinge_make_left_door_height_mm_uneven)  or  0, parseFloat(bx_dbl_hinge_make_right_door_height_mm_uneven)  or  0)))"
    
    print(f"Formula: {full_formula}")
    
    # Test with different calculation methods
    methods = ['manual', 'even', 'uneven']
    
    for method in methods:
        context['_CALCULATED_height_calculation_method'] = method
        try:
            result = eval(full_formula, context)
            print(f"✅ {method} method: {result}")
        except Exception as e:
            print(f"❌ {method} method error: {e}")

def test_operator_conversions():
    """Test JavaScript to Python operator conversions"""
    
    print(f"\n{'='*60}")
    print("Testing operator conversions:")
    print("=" * 60)
    
    conversions = [
        ("===", "=="),
        ("!==", "!="),
        ("&&", " and "),
        ("||", " or "),
        ("Math.max(", "max("),
        ("Math.min(", "min(")
    ]
    
    for js_op, py_op in conversions:
        print(f"Converting '{js_op}' to '{py_op}'")
    
    # Test a complete conversion
    js_expr = "a === b && c !== d || Math.max(e, f)"
    py_expr = js_expr.replace('===', '==').replace('!==', '!=').replace('&&', ' and ').replace('||', ' or ').replace('Math.max(', 'max(')
    
    print(f"\nJavaScript: {js_expr}")
    print(f"Python: {py_expr}")

if __name__ == "__main__":
    test_parseFloat_conversion()
    test_operator_conversions() 