#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Debug script for ternary operator parsing logic
"""

def find_matching_colon(expr, start_pos):
    """Find the colon that matches the question mark at start_pos"""
    print(f"🔍 Finding matching colon for question mark at position {start_pos}")
    print(f"   Expression: {expr}")
    
    paren_depth = 0
    for i in range(start_pos + 1, len(expr)):
        char = expr[i]
        print(f"   Position {i}: '{char}' (paren_depth: {paren_depth})")
        
        if char == '(':
            paren_depth += 1
        elif char == ')':
            paren_depth -= 1
        elif char == ':' and paren_depth == 0:
            print(f"   ✅ Found matching colon at position {i}")
            return i
    
    print(f"   ❌ No matching colon found")
    return -1

def find_innermost_ternary(expr):
    """Find the innermost ternary operator"""
    print(f"🔍 Finding innermost ternary in: {expr}")
    
    # Find the rightmost question mark
    rightmost_question = expr.rfind('?')
    if rightmost_question == -1:
        print("   ❌ No question mark found")
        return None
    
    print(f"   Rightmost question mark at position {rightmost_question}")
    
    # Find the colon that matches this question mark
    matching_colon = find_matching_colon(expr, rightmost_question)
    if matching_colon == -1:
        print("   ❌ No matching colon found")
        return None
    
    # Extract the parts
    condition = expr[:rightmost_question].strip()
    true_part = expr[rightmost_question + 1:matching_colon].strip()
    false_part = expr[matching_colon + 1:].strip()
    
    print(f"   ✅ Found ternary:")
    print(f"      Condition: {condition}")
    print(f"      True part: {true_part}")
    print(f"      False part: {false_part}")
    
    return {
        'condition': condition,
        'true_part': true_part,
        'false_part': false_part,
        'start': 0,
        'end': len(expr)
    }

def find_rightmost_ternary(expr):
    """Find the rightmost ternary operator"""
    print(f"🔍 Finding rightmost ternary in: {expr}")
    
    paren_depth = 0
    question_pos = -1
    colon_pos = -1
    
    for i, char in enumerate(expr):
        if char == '(':
            paren_depth += 1
        elif char == ')':
            paren_depth -= 1
        elif char == '?' and paren_depth == 0:
            question_pos = i
            print(f"   Found question mark at position {i}")
        elif char == ':' and paren_depth == 0 and question_pos != -1:
            colon_pos = i
            print(f"   Found matching colon at position {i}")
            break
    
    if question_pos != -1 and colon_pos != -1:
        condition = expr[:question_pos].strip()
        true_part = expr[question_pos + 1:colon_pos].strip()
        false_part = expr[colon_pos + 1:].strip()
        
        print(f"   ✅ Found ternary:")
        print(f"      Condition: {condition}")
        print(f"      True part: {true_part}")
        print(f"      False part: {false_part}")
        
        return {
            'condition': condition,
            'true_part': true_part,
            'false_part': false_part,
            'start': 0,
            'end': len(expr)
        }
    
    print("   ❌ No ternary found")
    return None

def parse_ternary_operators(expr):
    """Parse and convert ternary operators"""
    print(f"🔄 Parsing ternary operators in: {expr}")
    
    # Find the rightmost ternary
    ternary = find_rightmost_ternary(expr)
    if not ternary:
        print("   No ternary found, returning original expression")
        return expr
    
    # Convert to Python if-else
    python_expr = f"({ternary['true_part']} if ({ternary['condition']}) else {ternary['false_part']})"
    print(f"   ✅ Converted to: {python_expr}")
    
    return python_expr

def test_simple_ternary():
    """Test with a simple ternary expression"""
    print("=" * 60)
    print("🧪 Testing simple ternary: a ? b : c")
    print("=" * 60)
    
    expr = "a ? b : c"
    result = parse_ternary_operators(expr)
    print(f"Result: {result}")
    print()

def test_nested_ternary():
    """Test with a nested ternary expression"""
    print("=" * 60)
    print("🧪 Testing nested ternary: a ? b : (c ? d : e)")
    print("=" * 60)
    
    expr = "a ? b : (c ? d : e)"
    result = parse_ternary_operators(expr)
    print(f"Result: {result}")
    print()

def test_complex_ternary():
    """Test with the complex ternary from the original problem"""
    print("=" * 60)
    print("🧪 Testing complex ternary from original problem")
    print("=" * 60)
    
    expr = "_CALCULATED_height_calculation_method == 'manual' ? max(_CALCULATED_manual_left_height, _CALCULATED_manual_right_height) : (_CALCULATED_height_calculation_method == 'even' ? (parseFloat(bx_dbl_hinge_make_each_door_height_mm_even)  or  0) : max(parseFloat(bx_dbl_hinge_make_left_door_height_mm_uneven)  or  0, parseFloat(bx_dbl_hinge_make_right_door_height_mm_uneven)  or  0))"
    
    result = parse_ternary_operators(expr)
    print(f"Result: {result}")
    print()

if __name__ == "__main__":
    test_simple_ternary()
    test_nested_ternary()
    test_complex_ternary() 