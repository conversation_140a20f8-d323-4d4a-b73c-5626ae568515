# Odoo Module Consolidation Rule
## Task: Consolidate multiple Odoo modules into a single module
**CRITICAL: Follow this rule SYSTEMATICALLY and COMPLETELY. Do NOT do incremental or reactive consolidation.**
When consolidating multiple Odoo modules into a single module, follow these steps systematically:
### 1. Pre-Consolidation Analysis
- **Identify source modules**: List all modules to be consolidated (e.g., `vida_shipping_app`, `vidawood`)
- **Analyze dependencies**: Check `__manifest__.py` files for external dependencies and internal references
- **Map functionality**: Identify core models, extended models, views, wizards, security groups, and data files
- **Check for conflicts**: Look for overlapping model names, field names, or action names between modules
- **Create complete inventory**: List EVERY file in each source module before starting consolidation
### 2. Create Target Module Structure
- **Create new module directory**: `prefix_consolidated_name` (e.g., `modula_delivery_courier`)
- **Set up basic structure**:
  ```
  prefix_consolidated_name/
  ├── __init__.py
  ├── __manifest__.py
  ├── models/
  ├── views/
  ├── wizard/
  ├── security/
  ├── data/
  └── static/
  ```
- **Create inventory checklist**: Create a checklist of ALL files that need to be moved
### 3. Complete File Inventory and Migration
**CRITICAL: Do NOT start moving files until you have a complete inventory.**
- **Create complete file inventory**:
  ```
  Source Module 1 (e.g., vida_shipping_app):
  ├── models/
  │   ├── sale_order.py
  │   ├── stock_picking.py
  │   ├── stock_quant.py
  │   ├── stock_lot.py
  │   └── [ALL OTHER .py files]
  ├── views/
  │   ├── stock_quant_views.xml
  │   ├── sales_order_views.xml
  │   └── [ALL OTHER .xml files]
  ├── wizard/
  ├── security/
  ├── data/
  └── static/
  Source Module 2 (e.g., vidawood):
  ├── models/
  ├── views/
  ├── wizard/
  ├── security/
  ├── data/
  └── static/
  ```
- **Move ALL files systematically**:
  - Move core models first (e.g., `stock.shipment`, `stock.container`)
  - Move ALL extended models (e.g., `sale_order.py`, `stock_lot.py`, `stock_quant.py`)
  - Move ALL view files
  - Move ALL wizard files
  - Move ALL security files
  - Move ALL data files
  - Update ALL `__init__.py` imports
  - Update ALL references to use new module name
### 4. Systematic Verification Process
**CRITICAL: After moving files, verify COMPLETENESS before proceeding.**
- **File presence verification**:
  - [ ] ALL source model files exist in target module
  - [ ] ALL source view files exist in target module
  - [ ] ALL source wizard files exist in target module
  - [ ] ALL source security files exist in target module
  - [ ] ALL source data files exist in target module
- **Content completeness verification**:
  - [ ] Each model file has ALL fields from source
  - [ ] Each model file has ALL methods from source
  - [ ] Each view file has ALL view definitions from source
  - [ ] File sizes are comparable between source and target
- **Dependency verification**:
  - [ ] All related field dependencies are satisfied
  - [ ] All model references exist
  - [ ] All import statements are updated
  - [ ] All manifest entries are updated
### 5. Views and UI Components
- **Consolidate view files**: Merge view definitions from all source modules
- **Update XML references**: Change all action and menu references to new module name
- **Maintain view hierarchy**: Preserve menu structures and navigation
- **Check for conflicts**: Resolve any duplicate view IDs or conflicting definitions
### 6. Wizards and Popups
- **Move wizard models**: Copy all wizard classes and their logic
- **Update wizard views**: Ensure wizard views reference correct models
- **Preserve wizard actions**: Maintain all wizard launch actions and buttons
### 7. Security and Access Rights
- **Consolidate security groups**: Merge security group definitions
- **Update access rights**: Combine `ir.model.access.csv` files
- **Maintain permissions**: Ensure all necessary access rights are preserved
- **Update group references**: Change all security group references to new module
- **Fix model references**: Update all model references to use new module name (e.g., `modula_delivery_courier.model_stock_shipment` not `stock.model_stock_shipment`)
- **Verify external IDs**: Ensure all model external IDs are correctly referenced
### 8. Data Files and Records
- **Merge data files**: Combine all XML data files
- **Update record references**: Change all external IDs to use new module name
- **Preserve default data**: Maintain any default records or configurations
- **Check dependencies**: Ensure data records reference correct models and fields
- **Fix model references in data files**: Update all model references to use new module name (e.g., `modula_delivery_courier.model_stock_shipment` not `stock.model_stock_shipment`)
- **Verify cron job model references**: Check that all cron jobs reference correct models
- **Check action and menu references**: Ensure all actions and menus reference correct models
### 9. Manifest File Configuration
- **Update dependencies**: Remove dependencies on source modules, add external dependencies
- **Configure assets**: Update any static asset paths
- **Set module metadata**: Update name, description, category, version
- **Remove internal references**: Clean up any references to old module names
### 10. Import and Reference Updates
- **Update all imports**: Change import statements to use new module name
- **Fix action references**: Update all action calls and references
- **Update menu references**: Change parent menu references
- **Fix model references**: Update any hardcoded model references
### 11. Model File Verification and Completeness Check
- **Systematic model audit**: Check that ALL model files from source modules are moved
- **Compare model lists**: Ensure no model files are missing from the consolidation
- **Field verification**: Check each model for missing fields from source modules
- **Compare field lists**: Ensure all custom fields are present in consolidated module
- **Verify computed fields**: Check that computed fields and their dependencies are intact
- **Test field relationships**: Ensure related fields and foreign keys work correctly
- **Check model inheritance**: Ensure all model extensions are properly moved
### 12. Odoo 18 Compliance
- **Update XML structure**: Use `<list>` instead of `<tree>`, proper `<app>`/`<block>`/`<setting>` tags
- **Fix XPath expressions**: Update to target `//form` directly instead of `//div[hasclass('settings')]`
- **Update translation calls**: Use `self.env._()` instead of `_()` for translations
- **Follow Odoo 18 patterns**: Use modern structure and best practices
### 13. JavaScript and Static Assets
- **Evaluate JS files**: Check if JavaScript files are needed for the consolidated functionality
- **Move required assets**: Only move JS files that are actually used by the consolidated module
- **Update asset references**: Change any asset paths to use new module name
### 14. Testing and Validation
- **Check for missing functionality**: Verify all features from source modules are present
- **Test model relationships**: Ensure foreign keys and related fields work
- **Validate business logic**: Test computed fields and method overrides
- **Check view rendering**: Ensure all views display correctly
- **Test wizard functionality**: Verify all wizards work as expected
### 15. Common Issues to Watch For
- **Missing model files**: Entire model files can be overlooked (e.g., `sale_order.py`, `stock_picking.py`, `stock_lot.py`)
- **Missing view files**: Entire view files can be overlooked (e.g., `stock_quant_views.xml`, `sales_order_views.xml`)
- **Partial model migrations**: Files exist but are missing fields/methods (e.g., `stock_quant.py` missing `x_container`)
- **Partial view migrations**: View files exist but are missing view definitions
- **Missing fields**: Double-check that all custom fields are migrated
- **Missing methods**: Ensure all custom methods and overrides are preserved
- **Missing views**: Ensure all view definitions are preserved
- **Broken references**: Look for any remaining references to old module names
- **Incomplete method overrides**: Ensure all custom methods are preserved
- **Security gaps**: Verify all access rights are properly configured
- **Security file model references**: Incorrect model references in `ir.model.access.csv` (e.g., `stock.model_stock_shipment` should be `modula_delivery_courier.model_stock_shipment`)
- **Data file model references**: Incorrect model references in data files (e.g., `ir_cron.xml`, `system_parameters.xml`)
- **View conflicts**: Check for duplicate view definitions or conflicting IDs
- **Missing imports**: Check that all model imports are added to `__init__.py` files
- **Missing manifest entries**: Check that all view files are included in `__manifest__.py` data list
- **Incomplete file copying**: Files copied but content truncated or missing
- **Related field dependencies**: Models referenced in related fields are missing (e.g., `related="lot_id.cost"` but `stock.lot` model missing)
### 16. Files to Always Check
- `__manifest__.py` - dependencies and configuration
- `__init__.py` - import statements
- `models/*.py` - all model definitions and extensions
- `views/*.xml` - all view definitions
- `wizard/*.py` and `wizard/*.xml` - wizard models and views
- `security/*.csv` - access rights
- `data/*.xml` - data records and configurations
- `static/src/js/*.js` - JavaScript assets (if applicable)
### 17. Critical Verification Steps
- **List all model files**: Create a complete list of all `.py` files in source modules' `models/` directories
- **List all view files**: Create a complete list of all `.xml` files in source modules' `views/` directories
- **Cross-reference with target**: Ensure every source model file has a corresponding file in the consolidated module
- **Cross-reference view files**: Ensure every source view file has a corresponding file in the consolidated module
- **Check for missing models**: Use `grep_search` to find any model references that don't have corresponding files
- **Check for missing views**: Use `grep_search` to find any view references that don't have corresponding files
- **Verify model inheritance**: Ensure all `_inherit` and `_name` declarations are properly moved
- **Test model imports**: Verify that all models can be imported without errors
- **Systematic file comparison**: For each model file, compare the complete content between source and target
- **Systematic view comparison**: For each view file, compare the complete content between source and target
### 18. Field and Method Completeness Verification
- **Field count comparison**: Count fields in source vs target files using `grep -c "fields\."`
- **Method count comparison**: Count methods in source vs target files using `grep -c "def "`
- **Missing field detection**: Use `grep_search` to find specific field names that should exist
- **Missing method detection**: Use `grep_search` to find specific method names that should exist
- **Many2many field verification**: Check relation table names and column definitions
- **Computed field verification**: Verify compute methods exist and are correct
- **Related field verification**: Check related model and field references
- **Domain field verification**: Verify domain method implementations
- **Button method verification**: Verify all button action methods exist
- **Onchange method verification**: Check all @api.onchange decorators
- **Override method verification**: Check super() calls in overridden methods
### 19. Common Field Oversights to Check
- **`imported_container_ids`**: Many2many field for imported containers
- **`attachment_ids`**: Many2many field for attachments
- **`note_sale_order`**: Related field for sale order notes
- **`can_create_backorder`**: Computed boolean field
- **`not_create_bo`**: Boolean field for backorder control
- **`invoice`**: Char field for invoice information
- **`no_of_packs`**: Char field for pack information
- **`po_id`**: Many2one field for purchase orders
- **`delivery_user_id`**: Many2one field for delivery users
- **`signature`**: Image field for signatures
- **`sign_datetime`**: Computed datetime field
- **`signed_name`**: Char field for signed names
### 20. Common Method Oversights to Check
- **`reset_signature()`**: Method to reset signature
- **`_compute_po_id()`**: Compute method for PO ID
- **`_def_check_can_create_bo()`**: Compute method for backorder creation
- **`itms_create_bo()`**: Method to create backorders
- **`clear_operations()`**: Method to clear operations
- **`get_lots_from_po()`**: Method to get lots from PO
- **`_compute_move_without_package()`**: Compute method for moves
- **`_attach_sign()`**: Method to attach signatures
- **`action_confirm()`**: Override method for confirmation
- **`action_assign()`**: Override method for assignment
- **`button_validate()`**: Override method for validation with container logic
- **Field-by-field verification**: Ensure every field definition is present in the consolidated module
- **Method-by-method verification**: Ensure every method is present in the consolidated module
- **View-by-view verification**: Ensure every view definition is present in the consolidated module
- **Check for partial migrations**: Look for files that exist but are missing content
- **Check manifest data list**: Ensure all view files are included in the `data` list
- **Check related field dependencies**: Ensure all models referenced in related fields exist
- **Verify field relationships**: Check that all related fields have their target models available
### 18. Verification Commands
- **Search for old module names**: `grep_search` for old module references
- **Check for missing imports**: Look for import errors or missing dependencies
- **Validate XML structure**: Ensure all XML files are well-formed
- **Test module installation**: Verify the consolidated module installs without errors
- **List source model files**: `ls source_module/models/*.py` to get complete list
- **List source view files**: `ls source_module/views/*.xml` to get complete list
- **Cross-check model files**: Compare source and target model directories
- **Cross-check view files**: Compare source and target view directories
- **Compare file contents**: `diff source_module/models/file.py target_module/models/file.py`
- **Compare view contents**: `diff source_module/views/file.xml target_module/views/file.xml`
- **Search for specific fields**: `grep_search` for field names to ensure they exist in target
- **Search for specific methods**: `grep_search` for method names to ensure they exist in target
- **Search for specific views**: `grep_search` for view IDs to ensure they exist in target
- **Search for related fields**: `grep_search` for `related=` to find all related field dependencies
- **Check file sizes**: Ensure target files are not significantly smaller than source files
- **Check manifest data**: Verify all view files are listed in `__manifest__.py` data section
- **Check related field targets**: Verify all models referenced in related fields exist
- **Check security file references**: `grep_search` for `model_id:id` in CSV files to verify correct module references
- **Check data file references**: `grep_search` for `model_id` ref= in XML files to verify correct module references
- **Verify external IDs**: Check that all model references use correct module names
### 19. Post-Consolidation Cleanup
- **Remove source modules**: Once consolidation is complete and tested
- **Update any remaining references**: Check other modules that might reference the old modules
- **Document changes**: Update any documentation or README files
- **Version control**: Commit the consolidation as a single logical change
## **CRITICAL SUCCESS FACTORS**
### **DO NOT DO:**
- ❌ Incremental consolidation (fixing issues as they come up)
- ❌ Reactive consolidation (responding to problems)
- ❌ Selective file movement (moving only some files)
- ❌ Skipping verification steps
- ❌ Assuming files are complete without checking
### **DO:**
- ✅ Create complete inventory BEFORE starting
- ✅ Move ALL files systematically
- ✅ Verify completeness at each step
- ✅ Use systematic verification tools (`diff`, `grep_search`)
- ✅ Follow the checklist completely
- ✅ Test everything before considering it done
This rule ensures **thorough and systematic module consolidation** while maintaining all functionality and following Odoo 18 best practices. **The key is to be systematic and complete, not reactive and incremental.**
### 20. Pre-Release Verification Checklist
**CRITICAL: Before considering consolidation complete, verify EVERYTHING systematically.**
□ **Complete File Inventory Check**
- [ ] Created complete inventory of ALL files in source modules
- [ ] ALL source model files exist in target module
- [ ] ALL source view files exist in target module  
- [ ] ALL source wizard files exist in target module
- [ ] ALL source data files exist in target module
- [ ] ALL source security files exist in target module
□ **Complete Content Verification Check**
- [ ] Each model file has ALL fields from source (compare line by line)
- [ ] Each model file has ALL methods from source (compare line by line)
- [ ] Each model file has ALL imports and dependencies
- [ ] Each view file has ALL view definitions from source (compare line by line)
- [ ] Each view file has ALL field references from source
- [ ] File sizes are comparable between source and target
- [ ] No files are truncated or incomplete
□ **Complete Field Verification Check**
- [ ] All custom fields are present (use `grep_search` for field names)
- [ ] All computed fields have their compute methods
- [ ] All related fields have proper references
- [ ] All field attributes (store, index, etc.) are preserved
- [ ] All fields referenced in views are present in models
- [ ] All models referenced in related fields exist in the consolidated module
- [ ] No field dependencies are broken
□ **Complete Method Verification Check**
- [ ] All custom methods are present (use `grep_search` for method names)
- [ ] All method overrides are complete
- [ ] All decorators (@api.depends, @api.onchange, etc.) are preserved
- [ ] All business logic is intact
- [ ] No methods are missing or incomplete
□ **Complete Reference Check**
- [ ] No references to old module names remain
- [ ] All action references updated to new module
- [ ] All menu references updated to new module
- [ ] All model references updated to new module
- [ ] All view references updated to new module
- [ ] All security file model references updated to new module
- [ ] All data file model references updated to new module
- [ ] All cron job model references updated to new module
- [ ] All external IDs use correct module names
□ **Complete Import and Manifest Check**
- [ ] All model imports added to `__init__.py` files
- [ ] All dependencies updated in `__manifest__.py`
- [ ] All view files included in `__manifest__.py` data list
- [ ] No import errors when loading module
- [ ] All security files included in manifest
□ **Final Systematic Verification**
- [ ] Used `diff` command to compare source and target files
- [ ] Used `grep_search` to verify all field names exist
- [ ] Used `grep_search` to verify all method names exist
- [ ] Used `grep_search` to verify all view IDs exist
- [ ] Used `grep_search` to find any remaining old module references
- [ ] Tested module installation without errors
**CRITICAL: Only mark consolidation as complete when ALL checks pass. Do NOT skip any verification steps.** 