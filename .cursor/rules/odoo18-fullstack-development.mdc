---
description: Comprehensive guidelines for Odoo 18 fullstack development involving both internal backend and portal frontend components
globs: **/*.py,**/*.xml,**/*.js,**/*.scss
alwaysApply: true
---
# Odoo 18 Fullstack Development Guidelines
When developing Odoo 18 modules that involve both internal backend and portal frontend components, follow these comprehensive guidelines based on successful implementation experience.
## Requirements Analysis Framework
### Business Process Mapping
ALWAYS start with business process mapping:
- Identify all user roles involved
- Map current vs. desired workflows
- Document state transitions and triggers
- Identify integration points with existing systems
- Define success criteria and acceptance tests
### Technical Requirements Gathering
Gather technical requirements systematically:
- Data model changes needed
- UI/UX requirements for both backend and portal
- Security and access control requirements
- Performance requirements and constraints
- Integration requirements with existing modules
- Mobile/responsive design requirements
### Odoo 18 Compatibility Assessment
Check Odoo 18 compatibility before implementation:
- Review deprecated features and APIs
- Check XML view structure changes (tree → list)
- Verify translation performance optimizations
- Assess portal framework changes
- Review security model updates
## Architecture Design Principles
### Separation of Concerns
Use clear separation between models, controllers, and views:
```python
# GOOD: Clear separation
models/
├── stock_picking.py      # Business logic and data model
├── res_partner.py        # Partner extensions
└── custom_wizard.py      # Transient models for wizards
controllers/
├── portal.py            # Portal-specific routes
└── main.py              # Internal routes
views/
├── stock_picking_views.xml    # Backend views
├── portal_templates.xml       # Portal templates
└── wizard_views.xml          # Wizard interfaces
```
### Inheritance Patterns
Use inheritance for extending existing functionality:
```python
# GOOD: Use inheritance for extending existing functionality
class StockPicking(models.Model):
    _inherit = "stock.picking"
    # Add new fields
    delivery_user_id = fields.Many2one(...)
    state = fields.Selection(selection_add=[...])
    # Extend existing methods
    def button_validate(self):
        result = super().button_validate()
        # Add custom logic
        return result
```
### State Management Strategy
Use computed fields for state management:
```python
# GOOD: Use computed fields for state management
@api.depends('freight_carrier_id', 'state', 'signature')
def _compute_delivery_state(self):
    for picking in self:
        if picking.state == 'done':
            picking.delivery_state = 'delivered'
        elif picking.freight_carrier_id and picking.state in ['ready', 'assigned']:
            picking.delivery_state = 'ready_for_delivery'
```
## Backend Development Standards
### Model Development
Follow Odoo 18 standards:
```python
# GOOD: Follow Odoo 18 standards
class StockPicking(models.Model):
    _inherit = "stock.picking"
    # Use descriptive field names
    delivery_user_id = fields.Many2one(
        "res.users", 
        compute="_compute_delivery_user_id", 
        string="Delivery Driver", 
        store=True,
    )
    # Use selection_add for extending existing selections
    state = fields.Selection(
        selection_add=[("out_for_delivery", "Out for delivery")],
        ondelete={"out_for_delivery": "set draft"},
    )
    # Use computed fields with proper dependencies
    @api.depends("freight_carrier_id")
    def _compute_delivery_user_id(self):
        for picking in self:
            picking.delivery_user_id = picking.freight_carrier_id.user_id
```
### Method Implementation
Use proper error handling and validation:
```python
# GOOD: Use proper error handling and validation
def action_warehouse_validate(self):
    """Warehouse validation - sets delivery to out_for_delivery for freight carriers"""
    for picking in self:
        if not picking.freight_carrier_id:
            raise UserError(self.env._("No freight carrier assigned to this delivery"))
        if picking.state not in ['ready', 'assigned']:
            raise UserError(self.env._("Delivery must be ready or assigned before validation"))
        # Set appropriate state based on carrier type
        if picking.freight_carrier_id:
            picking.state = "out_for_delivery"
            picking.message_post(
                body=self.env._("Delivery set to 'Out for Delivery' by warehouse staff. Assigned to vendor: %s")
                % picking.freight_carrier_id.name
            )
        else:
            picking.state = "done"
```
### Translation Performance
Use optimized translations for Odoo 18:
```python
# GOOD: Use optimized translations for Odoo 18
message = self.env._("Delivery validated successfully")
# AVOID: Standard translation function
message = _("Delivery validated successfully")
```
## Portal Development Patterns
### Portal Controller Structure
Extend CustomerPortal properly:
```python
# GOOD: Extend CustomerPortal properly
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
class DeliveryCustomerPortal(CustomerPortal):
    def _prepare_home_portal_values(self, counters):
        """Extend portal home with custom counters"""
        values = super()._prepare_home_portal_values(counters)
        if 'vendor_delivery_count' in counters:
            values['vendor_delivery_count'] = self._get_vendor_delivery_count()
        return values
    def _get_vendor_delivery_count(self):
        """Get delivery count for current vendor"""
        return request.env['stock.picking'].search_count([
            ('freight_carrier_id', '=', request.env.user.partner_id.id)
        ])
```
### Route Organization
Organize routes by functionality:
```python
# GOOD: Organize routes by functionality
@http.route(['/my/vendor-deliveries'], type='http', auth="user", website=True)
def portal_my_vendor_deliveries(self, **kw):
    """List view - paginated delivery listing"""
@http.route(['/my/vendor-deliveries/<int:delivery_id>'], type='http', auth="user", website=True)
def portal_my_vendor_delivery(self, delivery_id, **kw):
    """Detail view - individual delivery page"""
@http.route(['/my/vendor-deliveries/<int:delivery_id>/validate'], type='http', auth="user", methods=['POST'], website=True)
def portal_my_vendor_delivery_validate(self, delivery_id, **post):
    """Action route - delivery validation"""
```
### Access Control
Implement proper access control:
```python
# GOOD: Implement proper access control
def _check_portal_access(self):
    """Check if current user has portal access"""
    local_freight_type = request.env.ref('modula_contact_extended.res_partner_type_local_freight', raise_if_not_found=False)
    if not local_freight_type or request.env.user.partner_id.customer_type_id != local_freight_type:
        return False
    return True
@http.route(['/my/vendor-deliveries'], type='http', auth="user", website=True)
def portal_my_vendor_deliveries(self, **kw):
    """Vendor delivery list page"""
    if not self._check_portal_access():
        return request.redirect('/my')
```
## Frontend-Backend Integration
### OWL Component Development
Use modern OWL patterns with useState:
```javascript
// GOOD: Use modern OWL patterns with useState
/** @odoo-module **/
import { Component, onMounted, useRef, useState } from "@odoo/owl";
export class PortalSignatureDialog extends Component {
    setup() {
        this.rootRef = useRef("root");
        this.csrfToken = odoo.csrf_token;
        // Use useState for reactive state management
        this.state = useState({
            error: false,
            success: false,
            loading: false,
        });
        this.localFiles = useState([]);
        this.signature = useState({
            name: '',
            getSignatureImage: () => "",
            resetSignature: () => { },
            isSignatureEmpty: true,
        });
    }
    // Implement proper file management
    addLocalFile(file) {
        const fileId = Date.now() + Math.random();
        this.localFiles.push({
            id: fileId,
            file: file,
            name: file.name,
            size: file.size,
            type: file.type,
        });
    }
}
```
### Template Development
Use Odoo 18 template structure:
```xml
<!-- GOOD: Use Odoo 18 template structure -->
<template id="portal_my_vendor_delivery" name="Vendor Delivery Detail">
    <t t-call="portal.portal_layout">
        <t t-set="breadcrumbs_searchbar" t-value="True"/>
        <t t-call="portal.portal_searchbar">
            <t t-set="title">Delivery Details</t>
        </t>
        <div class="container-fluid">
            <!-- Use Bootstrap classes for responsive design -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5>Delivery Information</h5>
                        </div>
                        <div class="card-body">
                            <!-- Content here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>
</template>
```
### Asset Management
Proper asset organization:
```xml
<!-- GOOD: Proper asset organization -->
<template id="assets_frontend" inherit_id="web.assets_frontend">
    <xpath expr="." position="inside">
        <link rel="stylesheet" type="text/scss" href="/modula_delivery_courier/static/src/scss/portal_badges.scss"/>
        <script type="text/javascript" src="/modula_delivery_courier/static/src/js/portal_signature.js"/>
    </xpath>
</template>
```
## Security Implementation
### Record Rules
Implement proper record rules:
```xml
<!-- GOOD: Implement proper record rules -->
<record id="vendor_delivery_rule_portal" model="ir.rule">
    <field name="name">Vendor Delivery: portal users can only access their assigned deliveries</field>
    <field name="model_id" ref="stock.model_stock_picking"/>
    <field name="domain_force">[
        ('freight_carrier_id', '=', user.partner_id.id)
    ]</field>
    <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
</record>
```
### Access Rights
Define proper access rights:
```xml
<!-- GOOD: Define proper access rights -->
<record id="vendor_delivery_access_portal" model="ir.model.access">
    <field name="name">vendor.delivery.portal</field>
    <field name="model_id" ref="stock.model_stock_picking"/>
    <field name="group_id" ref="base.group_portal"/>
    <field name="perm_read" eval="True"/>
    <field name="perm_write" eval="True"/>
    <field name="perm_create" eval="False"/>
    <field name="perm_unlink" eval="False"/>
</record>
```
### CSRF Protection
Always use CSRF protection for POST routes:
```python
# GOOD: Always use CSRF protection for POST routes
@http.route(['/my/vendor-deliveries/save-signature'], type='http', auth="user", methods=['POST'], website=True, csrf=True)
def portal_save_signature(self, **post):
    """Save signature with CSRF protection"""
```
## Common Pitfalls and Solutions
### Odoo 18 Compatibility Issues
```markdown
PROBLEM: Using deprecated <tree> tags
SOLUTION: Use <list> tags instead
PROBLEM: Using attrs attributes
SOLUTION: Use direct attributes (invisible="condition")
PROBLEM: Using _() for translations
SOLUTION: Use self.env._() for better performance
```
### Portal Security Issues
```markdown
PROBLEM: Missing CSRF protection
SOLUTION: Always add csrf=True to POST routes
PROBLEM: Insufficient access control
SOLUTION: Implement proper record rules and access rights
PROBLEM: Missing user validation
SOLUTION: Always validate user permissions in portal routes
```
### State Management Issues
```markdown
PROBLEM: Overriding _compute_state incorrectly
SOLUTION: Use selection_add and proper state logic
PROBLEM: Not handling state transitions properly
SOLUTION: Implement proper state validation and transitions
```
## Implementation Checklist
### Pre-Development
- [ ] Complete requirements analysis
- [ ] Design architecture and data model
- [ ] Plan security implementation
- [ ] Set up development environment
### Development
- [ ] Implement backend models and business logic
- [ ] Create portal controllers and routes
- [ ] Develop frontend templates and components
- [ ] Implement security controls
- [ ] Add comprehensive error handling
### Testing
- [ ] Write unit tests for all components
- [ ] Perform integration testing
- [ ] Test security controls
- [ ] Validate Odoo 18 compatibility
- [ ] Test portal functionality
### Documentation
- [ ] Document code with proper docstrings
- [ ] Create user documentation
- [ ] Write technical documentation
- [ ] Document API reference
- [ ] Create installation and configuration guides
### Deployment
- [ ] Perform code review
- [ ] Test in staging environment
- [ ] Validate performance
- [ ] Deploy to production
- [ ] Monitor for issues
## Performance Optimization
### Database Optimization
Use proper indexing and queries:
```python
# GOOD: Use proper indexing and queries
@api.depends("freight_carrier_id")
def _compute_delivery_user_id(self):
    # Use proper domain for efficient queries
    for picking in self:
        if picking.freight_carrier_id:
            picking.delivery_user_id = picking.freight_carrier_id.user_id
```
### Frontend Performance
Optimize frontend performance:
```javascript
// GOOD: Optimize frontend performance
export class PortalSignatureDialog extends Component {
    setup() {
        // Use useState for efficient state management
        this.localFiles = useState([]);
        // Debounce expensive operations
        this.debouncedUpload = this.debounce(this.uploadFiles, 300);
    }
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}
```
### Caching Strategy
Use proper caching for computed fields:
```python
# GOOD: Use proper caching for computed fields
@api.depends("freight_carrier_id")
def _compute_delivery_user_id(self):
    # Store computed field for better performance
    for picking in self:
        picking.delivery_user_id = picking.freight_carrier_id.user_id
```
## Code Review Process
ALWAYS review code for:
- Odoo 18 compliance
- Security best practices
- Performance optimization opportunities
- Code maintainability
- Documentation completeness
## Continuous Improvement
### Performance Monitoring
Monitor performance metrics:
- Database query performance
- Portal response times
- Memory usage
- User experience metrics
### User Feedback Integration
Collect and integrate user feedback:
- Portal usability improvements
- Feature requests
- Bug reports
- Performance issues
Remember: Follow these guidelines consistently to ensure high-quality, maintainable, and secure Odoo 18 fullstack development.
description:
globs:
alwaysApply: false
---
