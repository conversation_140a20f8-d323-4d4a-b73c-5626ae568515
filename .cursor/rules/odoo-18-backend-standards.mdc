---
description: Odoo 18 Backend Development Standards for Models, Controllers, and Business Logic
globs: **/*.py
alwaysApply: false
---
# Odoo 18 Backend Development Standards
## Context
This rule enforces Odoo 18 backend development standards for the ConfigMatrix system, ensuring code quality, performance, and maintainability. Based on proven AI-assisted development patterns and real-world implementations.
## Input
- Python model files (.py)
- Controller files
- Wizard files
- Business logic implementations
## Output
- Compliant Odoo 18 code following best practices
- Proper model inheritance patterns
- Optimized database queries
- Secure controller implementations
## Core Standards
### 1. Model Definition Standards
#### Template Extension Pattern
```python
# ✅ CORRECT - Use proper inheritance
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Configuration Template'
    _order = 'name'
    # Use computed fields for performance
    @api.depends('section_ids', 'section_ids.field_ids')
    def _compute_field_count(self):
        for template in self:
            template.field_count = sum(len(section.field_ids) for section in template.section_ids)
    # Use selection_add for extending existing selections
    state = fields.Selection(selection_add=[
        ('archived', 'Archived')
    ], ondelete={'archived': 'set draft'})
    # Use related fields for efficient queries
    product_name = fields.Char(related='product_template_id.name', store=True)
    # Use constraints for data integrity
    _sql_constraints = [
        ('unique_code', 'unique(code)', 'Template code must be unique!')
    ]
```
#### Field Definition Standards
```python
# ✅ CORRECT - Proper field definitions
class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    # Basic fields with proper types
    name = fields.Char("Field Name", required=True)
    technical_name = fields.Char("Technical Name", required=True)
    field_type = fields.Selection([
        ('char', 'Text'),
        ('integer', 'Integer'),
        ('float', 'Float'),
        ('boolean', 'Boolean'),
        ('selection', 'Selection'),
        ('many2one', 'Many2One'),
        ('many2many', 'Many2Many'),
        ('one2many', 'One2Many'),
    ], required=True, default='char')
    # Use computed fields with proper dependencies
    @api.depends('sequence', 'section_id.sequence')
    def _compute_question_number(self):
        for field in self:
            if field.section_id:
                field.question_number = field.section_id.sequence * 100 + field.sequence
    # Use onchange for immediate UI updates
    @api.onchange('field_type')
    def _onchange_field_type(self):
        if self.field_type == 'selection':
            self.is_searchable_dropdown = True
        else:
            self.is_searchable_dropdown = False
```
### 2. Odoo 18 Critical Breaking Changes
#### User Group Access Control
```python
# ✅ CORRECT - Odoo 18 user group checking
# Use self.env.user.has_group() instead of user_has_groups()
def action_validate(self):
    """Validate template - only managers can validate"""
    self.ensure_one()
    # ✅ CORRECT - Odoo 18 pattern
    if not self.env.user.has_group('base.group_system'):
        raise AccessError(self.env._("Only managers can validate templates"))
    # ❌ WRONG - Odoo 17 deprecated pattern
    # if self.user_has_groups('base.group_system'):  # AttributeError in Odoo 18
    self.state = 'testing'
    return True
```
#### Access Control Methods
```python
# ✅ CORRECT - Odoo 18 unified access control
def my_method(self):
    # Use check_access() instead of separate methods
    self.check_access('read')  # or 'write', 'create', 'unlink'
    # For filtering records with access
    accessible_records = self._filter_access('read')
    return accessible_records
# ❌ WRONG - Odoo 17 deprecated methods
# self.check_access_rights('read')  # Use check_access('read')
# self.check_access_rule('write')   # Use check_access('write')
```
#### Translation Performance Optimization
```python
# ✅ CORRECT - Odoo 18 optimized translations
message = self.env._("Template validated successfully")
error_msg = self.env._("Only managers can validate templates")
# ❌ AVOID - Standard translation function (less optimal)
message = _("Template validated successfully")
```
### 3. Method Implementation Standards
#### Action Methods
```python
# ✅ CORRECT - Proper action method implementation
def action_open_matrix_editor(self):
    """Open the visual matrix editor for this matrix"""
    self.ensure_one()
    return {
        'name': self.env._('Visual Matrix Editor - %s') % self.name,
        'type': 'ir.actions.act_window',
        'res_model': 'config.matrix.visual.editor',
        'view_mode': 'form',
        'target': 'new',
        'context': {
            'default_matrix_id': self.id,
            'default_matrix_type': 'labor',
        },
    }
```
#### Business Logic Methods
```python
# ✅ CORRECT - Business logic with proper error handling
def action_warehouse_validate(self):
    """Warehouse validation - sets delivery to out_for_delivery for freight carriers"""
    for picking in self:
        if not picking.freight_carrier_id:
            raise UserError(self.env._("No freight carrier assigned to this delivery"))
        if picking.state not in ['ready', 'assigned']:
            raise UserError(self.env._("Delivery must be ready or assigned before validation"))
        # Set appropriate state based on carrier type
        if picking.freight_carrier_id:
            picking.state = "out_for_delivery"
            picking.message_post(
                body=self.env._("Delivery set to 'Out for Delivery' by warehouse staff. Assigned to vendor: %s")
                % picking.freight_carrier_id.name
            )
        else:
            picking.state = "done"
```
### 4. Proven Solutions Patterns
#### Field Visibility Control
```python
# ✅ CORRECT - Override _get_view method for dynamic field visibility
@api.model
def _get_view(self, view_id, view_type, **options):
    arch, view = super()._get_view(view_id, view_type, **options)
    # Hide sensitive fields from certain user groups
    if not self.env.user.has_group('base.group_system'):
        if arch.xpath("//field[@name='internal_notes']"):
            arch.xpath("//field[@name='internal_notes']")[0].set("invisible", "1")
    return arch, view
# ❌ AVOID - Direct field definition with groups
# internal_notes = fields.Text("Internal Notes", groups="base.group_system")
```
#### Multi-Company Record Rules
```python
# ✅ CORRECT - Multi-company record rules
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    company_id = fields.Many2one('res.company', string='Company', 
                                default=lambda self: self.env.company)
    @api.model
    def _get_company_domain(self):
        return [('company_id', 'in', [False, self.env.company.id])]
    @api.model
    def _get_company_rule(self):
        return [('company_id', 'in', [False, self.env.company.id])]
```
#### Optimized Computed Fields
```python
# ✅ CORRECT - Optimized computed fields with proper dependencies
@api.depends('section_ids', 'section_ids.field_ids', 'section_ids.active')
def _compute_field_count(self):
    """Compute field count with performance optimization"""
    for template in self:
        # Use sum() for better performance
        template.field_count = sum(
            len(section.field_ids.filtered(lambda f: f.active))
            for section in template.section_ids
            if section.active
        )
```
### 5. Controller Standards
#### RESTful API Design
```python
# ✅ CORRECT - RESTful controller implementation
class ConfigMatrixController(http.Controller):
    @http.route('/config_matrix/api/template/<int:template_id>', 
                type='json', auth='user', methods=['GET'])
    def get_template(self, template_id, **kw):
        """Get template data with caching"""
        try:
            template = request.env['config.matrix.template'].browse(template_id)
            if not template.exists():
                return {'error': 'Template not found'}, 404
            return {
                'id': template.id,
                'name': template.name,
                'sections': template.section_ids.read(['name', 'sequence']),
                'fields': template.field_ids.read(['name', 'field_type', 'technical_name'])
            }
        except Exception as e:
            return {'error': str(e)}, 500
```
### 6. Performance Optimization Standards
#### Computed Fields
```python
# ✅ CORRECT - Optimized computed fields
@api.depends('freight_carrier_id')
def _compute_delivery_user_id(self):
    """Compute delivery user based on freight carrier"""
    for picking in self:
        picking.delivery_user_id = picking.freight_carrier_id.user_id
```
#### Database Queries
```python
# ✅ CORRECT - Efficient database queries
def _get_related_records(self):
    """Get related records efficiently"""
    return self.env['related.model'].search([
        ('parent_id', 'in', self.ids)
    ]).mapped('child_ids')
```
### 7. Security Standards
#### Access Control
```python
# ✅ CORRECT - Proper access control
def _check_portal_access(self):
    """Check if current user has portal access"""
    local_freight_type = request.env.ref(
        'modula_contact_extended.res_partner_type_local_freight', 
        raise_if_not_found=False
    )
    if not local_freight_type or request.env.user.partner_id.customer_type_id != local_freight_type:
        return False
    return True
```
#### CSRF Protection
```python
# ✅ CORRECT - CSRF protection for POST routes
@http.route(['/my/vendor-deliveries/save-signature'], 
            type='http', auth="user", methods=['POST'], website=True, csrf=True)
def portal_save_signature(self, **post):
    """Save signature with CSRF protection"""
    # Implementation here
```
### 8. Error Handling Standards
#### Validation Errors
```python
# ✅ CORRECT - Proper validation and error handling
@api.constrains('technical_name')
def _check_technical_name(self):
    """Validate technical name format"""
    for field in self:
        if not field.technical_name.isidentifier():
            raise ValidationError(self.env._("Technical name must be a valid Python identifier"))
@api.constrains('field_type', 'selection_options')
def _check_selection_options(self):
    """Validate selection options for selection fields"""
    for field in self:
        if field.field_type == 'selection' and not field.selection_options:
            raise ValidationError(self.env._("Selection options are required for selection fields"))
```
### 9. AI Development Process Integration
#### Requirements Analysis
```python
# ✅ CORRECT - Clear requirements documentation in docstrings
class ConfigMatrixTemplate(models.Model):
    """
    Configuration Template Model
    Business Requirements:
    - Support multiple use cases (check measure, sales, online)
    - Enable dynamic field visibility
    - Support pricing and labor matrices
    - Provide portal access for customers
    Technical Requirements:
    - Odoo 18 compatibility
    - Performance optimization for large datasets
    - Security for sensitive pricing data
    - Integration with existing sales and manufacturing modules
    """
    _name = 'config.matrix.template'
    _description = 'Configuration Template'
```
#### Implementation Phases
```python
# ✅ CORRECT - Phase-based implementation with clear milestones
def action_validate(self):
    """
    Phase 1: Basic validation
    Phase 2: Business rule validation  
    Phase 3: Integration validation
    Phase 4: Performance optimization
    """
    self.ensure_one()
    # Phase 1: Basic validation
    if not self.name or not self.code:
        raise ValidationError(self.env._("Name and code are required"))
    # Phase 2: Business rule validation
    if self.state != 'draft':
        raise UserError(self.env._("Only draft templates can be validated"))
    # Phase 3: Integration validation
    if not self.section_ids:
        raise UserError(self.env._("At least one section is required"))
    # Phase 4: Performance optimization
    self.state = 'testing'
    self.message_post(body=self.env._("Template validated successfully"))
    return True
```
### 10. Common Pitfalls to Avoid
#### Dictionary Syntax Errors
```python
# ❌ WRONG - Missing closing brace
matrix = self.create({
    'name': name,
    'product_template_id': product_template_id,
    'matrix_type': 'price',
    'last_imported': fields.Datetime.now(),
return matrix
# ✅ CORRECT - Proper closing brace
matrix = self.create({
    'name': name,
    'product_template_id': product_template_id,
    'matrix_type': 'price',
    'last_imported': fields.Datetime.now(),
})
return matrix
```
#### Import Issues
```python
# ✅ CORRECT - Proper imports
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError, AccessError
```
#### Odoo 18 Migration Issues
```python
# ❌ WRONG - Odoo 17 deprecated patterns
if self.user_has_groups('base.group_system'):  # AttributeError in Odoo 18
    pass
# ✅ CORRECT - Odoo 18 patterns
if self.env.user.has_group('base.group_system'):
    pass
```
## Examples
### Complete Model Example
```python
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError, AccessError
class ConfigMatrixTemplate(models.Model):
    """
    Configuration Template Model
    Business Requirements:
    - Support multiple use cases (check measure, sales, online)
    - Enable dynamic field visibility
    - Support pricing and labor matrices
    - Provide portal access for customers
    """
    _name = 'config.matrix.template'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Configuration Template'
    _order = 'name'
    # Basic fields
    name = fields.Char("Template Name", required=True)
    product_template_id = fields.Many2one('product.template', "Product Template", required=True)
    description = fields.Text("Description")
    code = fields.Char("Template Code", required=True)
    version = fields.Char("Version", default="1.0")
    # Administrative fields
    active = fields.Boolean("Active", default=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('testing', 'Testing'),
        ('active', 'Active'),
        ('archived', 'Archived')
    ], default='draft', string="Status")
    # Computed fields
    field_count = fields.Integer("Field Count", compute='_compute_field_count', store=True)
    # Constraints
    _sql_constraints = [
        ('unique_code', 'unique(code)', 'Template code must be unique!')
    ]
    # Computed methods
    @api.depends('section_ids', 'section_ids.field_ids')
    def _compute_field_count(self):
        for template in self:
            template.field_count = sum(len(section.field_ids) for section in template.section_ids)
    # Action methods
    def action_open_matrix_editor(self):
        """Open the visual matrix editor"""
        self.ensure_one()
        return {
            'name': self.env._('Visual Matrix Editor - %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.visual.editor',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_matrix_id': self.id,
            },
        }
    # Validation methods
    @api.constrains('code')
    def _check_code_format(self):
        for template in self:
            if not template.code.isalnum():
                raise ValidationError(self.env._("Template code must contain only alphanumeric characters"))
    def action_validate(self):
        """Validate template - only managers"""
        self.ensure_one()
        if not self.env.user.has_group('base.group_system'):
            raise AccessError(self.env._("Only managers can validate templates"))
        if self.state != 'draft':
            raise UserError(self.env._("Only draft templates can be validated"))
        self.state = 'testing'
        self.message_post(body=self.env._("Template validated successfully"))
        return True
