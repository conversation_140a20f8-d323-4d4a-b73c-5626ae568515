---
description: Odoo 18 Security Standards for Access Control, Data Protection, and Secure Development
globs: **/*.py,**/*.xml
alwaysApply: false
---
# Odoo 18 Security Standards
## Context
This rule enforces Odoo 18 security standards for the ConfigMatrix system, ensuring proper access control, data protection, and secure development practices.
## Input
- Python model files (.py)
- XML view files (.xml)
- Controller files
- Security configuration files
- Access rights definitions
## Output
- Secure Odoo 18 implementations
- Proper access control mechanisms
- Data protection measures
- Compliance with security best practices
## Core Standards
### 1. Access Rights Standards
#### Model Access Rights
```xml
<!-- ✅ CORRECT - Proper access rights definition -->
<record id="config_matrix_template_access_user" model="ir.model.access">
    <field name="name">config.matrix.template.user</field>
    <field name="model_id" ref="config_matrix.model_config_matrix_template"/>
    <field name="group_id" ref="base.group_user"/>
    <field name="perm_read" eval="True"/>
    <field name="perm_write" eval="True"/>
    <field name="perm_create" eval="True"/>
    <field name="perm_unlink" eval="False"/>
</record>
<record id="config_matrix_template_access_manager" model="ir.model.access">
    <field name="name">config.matrix.template.manager</field>
    <field name="model_id" ref="config_matrix.model_config_matrix_template"/>
    <field name="group_id" ref="base.group_system"/>
    <field name="perm_read" eval="True"/>
    <field name="perm_write" eval="True"/>
    <field name="perm_create" eval="True"/>
    <field name="perm_unlink" eval="True"/>
</record>
<record id="config_matrix_template_access_portal" model="ir.model.access">
    <field name="name">config.matrix.template.portal</field>
    <field name="model_id" ref="config_matrix.model_config_matrix_template"/>
    <field name="group_id" ref="base.group_portal"/>
    <field name="perm_read" eval="True"/>
    <field name="perm_write" eval="False"/>
    <field name="perm_create" eval="False"/>
    <field name="perm_unlink" eval="False"/>
</record>
```
#### Record Rules
```xml
<!-- ✅ CORRECT - Record rules for data access control -->
<record id="config_matrix_template_rule_user" model="ir.rule">
    <field name="name">ConfigMatrix Template: users can only access active templates</field>
    <field name="model_id" ref="config_matrix.model_config_matrix_template"/>
    <field name="domain_force">[('active', '=', True)]</field>
    <field name="groups" eval="[(4, ref('base.group_user'))]"/>
</record>
<record id="config_matrix_template_rule_portal" model="ir.rule">
    <field name="name">ConfigMatrix Template: portal users can only access public templates</field>
    <field name="model_id" ref="config_matrix.model_config_matrix_template"/>
    <field name="domain_force">[
        ('active', '=', True),
        ('state', '=', 'active'),
        ('enable_online', '=', True)
    ]</field>
    <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
</record>
<record id="config_matrix_template_rule_manager" model="ir.rule">
    <field name="name">ConfigMatrix Template: managers can access all templates</field>
    <field name="model_id" ref="config_matrix.model_config_matrix_template"/>
    <field name="domain_force">[(1, '=', 1)]</field>
    <field name="groups" eval="[(4, ref('base.group_system'))]"/>
</record>
```
### 2. Field Security Standards
#### Field-Level Security
```python
# ✅ CORRECT - Field-level security implementation
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    # Public fields (visible to all users)
    name = fields.Char("Template Name", required=True)
    description = fields.Text("Description")
    state = fields.Selection([
        ('draft', 'Draft'),
        ('testing', 'Testing'),
        ('active', 'Active'),
        ('archived', 'Archived')
    ], default='draft', string="Status")
    # Internal fields (only internal users)
    internal_notes = fields.Text("Internal Notes", groups="base.group_user")
    admin_notes = fields.Text("Administrative Notes", groups="base.group_system")
    # Sensitive fields (only managers)
    pricing_data = fields.Text("Pricing Data", groups="base.group_system")
    cost_structure = fields.Text("Cost Structure", groups="base.group_system")
    # Computed fields with security
    @api.depends('section_ids', 'section_ids.field_ids')
    def _compute_field_count(self):
        """Compute field count with proper access control"""
        for template in self:
            if self.env.user.has_group('base.group_system'):
                # Managers can see all fields
                template.field_count = sum(len(section.field_ids) for section in template.section_ids)
            elif self.env.user.has_group('base.group_user'):
                # Internal users can see active fields
                template.field_count = sum(
                    len(section.field_ids.filtered(lambda f: f.active))
                    for section in template.section_ids
                )
            else:
                # Portal users can only see public fields
                template.field_count = sum(
                    len(section.field_ids.filtered(lambda f: f.active and f.is_public))
                    for section in template.section_ids
                )
```
### 3. Method Security Standards
#### Method Access Control
```python
# ✅ CORRECT - Method-level security
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    def action_validate(self):
        """Validate template - only managers can validate"""
        self.ensure_one()
        # Check access rights
        if not self.env.user.has_group('base.group_system'):
            raise AccessError(_("Only managers can validate templates"))
        # Check business rules
        if self.state != 'draft':
            raise UserError(_("Only draft templates can be validated"))
        # Perform validation
        self.state = 'testing'
        self.message_post(body=_("Template validated by %s") % self.env.user.name)
        return True
    def action_activate(self):
        """Activate template - only managers can activate"""
        self.ensure_one()
        # Check access rights
        if not self.env.user.has_group('base.group_system'):
            raise AccessError(_("Only managers can activate templates"))
        # Check business rules
        if self.state != 'testing':
            raise UserError(_("Only tested templates can be activated"))
        # Perform activation
        self.state = 'active'
        self.message_post(body=_("Template activated by %s") % self.env.user.name)
        return True
    def action_archive(self):
        """Archive template - only managers can archive"""
        self.ensure_one()
        # Check access rights
        if not self.env.user.has_group('base.group_system'):
            raise AccessError(_("Only managers can archive templates"))
        # Perform archiving
        self.active = False
        self.state = 'archived'
        self.message_post(body=_("Template archived by %s") % self.env.user.name)
        return True
```
### 4. Controller Security Standards
#### Portal Access Control
```python
# ✅ CORRECT - Portal access control
class ConfigMatrixCustomerPortal(CustomerPortal):
    """Portal controller with proper security"""
    def _check_portal_access(self):
        """Check if current user has portal access"""
        try:
            # Check if user is portal user
            if not request.env.user.has_group('base.group_portal'):
                return False
            # Check specific access rights
            local_freight_type = request.env.ref(
                'modula_contact_extended.res_partner_type_local_freight', 
                raise_if_not_found=False
            )
            if not local_freight_type or request.env.user.partner_id.customer_type_id != local_freight_type:
                return False
            return True
        except Exception:
            return False
    @http.route(['/my/config-matrices'], type='http', auth="user", website=True)
    def portal_my_config_matrices(self, **kw):
        """List view with access control"""
        if not self._check_portal_access():
            return request.redirect('/my')
        try:
            # Use proper domain for portal users
            domain = [
                ('active', '=', True),
                ('state', '=', 'active'),
                ('enable_online', '=', True)
            ]
            templates = request.env['config.matrix.template'].search(domain)
            values = {
                'templates': templates,
            }
            return request.render("config_matrix.portal_my_config_matrices", values)
        except Exception as e:
            return request.render("portal.portal_my_home", {'error': str(e)})
```
#### CSRF Protection
```python
# ✅ CORRECT - CSRF protection for POST routes
@http.route(['/my/config-matrices/<int:template_id>/save'], 
            type='http', auth="user", methods=['POST'], website=True, csrf=True)
def portal_save_configuration(self, template_id, **post):
    """Save configuration with CSRF protection"""
    if not self._check_portal_access():
        return request.redirect('/my')
    try:
        template = request.env['config.matrix.template'].browse(template_id)
        if not template.exists():
            return request.redirect('/my/config-matrices')
        # Validate template access
        if not template.enable_online or template.state != 'active':
            return request.redirect('/my/config-matrices')
        # Process and save configuration
        # Implementation here
        return request.redirect(f'/my/config-matrices/{template_id}?success=1')
    except Exception as e:
        return request.render("portal.portal_my_home", {'error': str(e)})
```
### 5. Data Validation Standards
#### Input Validation
```python
# ✅ CORRECT - Input validation with security
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    @api.constrains('code')
    def _check_code_security(self):
        """Validate code format and security"""
        for template in self:
            # Check for SQL injection patterns
            if any(char in template.code for char in [';', '--', '/*', '*/', 'union', 'select']):
                raise ValidationError(_("Invalid characters in template code"))
            # Check for XSS patterns
            if any(pattern in template.code.lower() for pattern in ['<script', 'javascript:', 'onload=']):
                raise ValidationError(_("Invalid code format"))
    @api.constrains('name')
    def _check_name_security(self):
        """Validate name security"""
        for template in self:
            # Check for script injection
            if '<script' in template.name.lower():
                raise ValidationError(_("Invalid characters in template name"))
    @api.constrains('description')
    def _check_description_security(self):
        """Validate description security"""
        for template in self:
            # Check for HTML injection
            if template.description and ('<script' in template.description.lower() or 
                                       'javascript:' in template.description.lower()):
                raise ValidationError(_("Invalid content in description"))
```
### 6. API Security Standards
#### API Access Control
```python
# ✅ CORRECT - API security implementation
class ConfigMatrixController(http.Controller):
    """Controller with proper API security"""
    def _validate_api_access(self, template_id):
        """Validate API access for template"""
        try:
            template = request.env['config.matrix.template'].browse(template_id)
            if not template.exists():
                return False, "Template not found"
            # Check user access rights
            if request.env.user.has_group('base.group_system'):
                return True, template
            if request.env.user.has_group('base.group_user'):
                if template.active:
                    return True, template
                else:
                    return False, "Template not accessible"
            if request.env.user.has_group('base.group_portal'):
                if (template.active and template.state == 'active' and 
                    template.enable_online):
                    return True, template
                else:
                    return False, "Template not accessible for portal users"
            return False, "Access denied"
        except Exception as e:
            return False, str(e)
    @http.route('/config_matrix/api/template/<int:template_id>', 
                type='json', auth='user', methods=['GET'])
    def get_template(self, template_id, **kw):
        """Get template with security validation"""
        try:
            has_access, result = self._validate_api_access(template_id)
            if not has_access:
                return {'error': result}, 403
            template = result
            template_data = template.read([
                'name', 'description', 'state', 'version'
            ])[0]
            return {'success': True, 'data': template_data}
        except Exception as e:
            return {'error': str(e)}, 500
```
### 7. File Upload Security
#### Secure File Handling
```python
# ✅ CORRECT - Secure file upload handling
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    attachment_ids = fields.Many2many('ir.attachment', string="Attachments")
    def _validate_file_upload(self, file_data):
        """Validate file upload security"""
        # Check file size
        max_size = 10 * 1024 * 1024  # 10MB
        if len(file_data) > max_size:
            raise ValidationError(_("File size exceeds maximum allowed size"))
        # Check file type
        allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf']
        if file_data.get('mimetype') not in allowed_types:
            raise ValidationError(_("File type not allowed"))
        # Check for malicious content
        content = file_data.get('content', '')
        if any(pattern in content.lower() for pattern in ['<script', 'javascript:', 'vbscript:']):
            raise ValidationError(_("File contains invalid content"))
    @http.route('/config_matrix/upload_attachment', 
                type='http', auth='user', methods=['POST'], csrf=True)
    def upload_attachment(self, **post):
        """Secure file upload endpoint"""
        try:
            if not request.env.user.has_group('base.group_user'):
                return {'error': 'Access denied'}, 403
            # Validate file
            file_data = request.httprequest.files.get('file')
            if not file_data:
                return {'error': 'No file provided'}, 400
            self._validate_file_upload(file_data)
            # Create attachment
            attachment = request.env['ir.attachment'].create({
                'name': file_data.filename,
                'datas': file_data.read(),
                'mimetype': file_data.content_type,
                'res_model': 'config.matrix.template',
                'res_id': int(post.get('template_id')),
            })
            return {'success': True, 'attachment_id': attachment.id}
        except Exception as e:
            return {'error': str(e)}, 500
```
### 8. Audit Trail Standards
#### Security Logging
```python
# ✅ CORRECT - Security audit trail
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    def _log_security_event(self, action, details=None):
        """Log security events for audit trail"""
        message = f"Security event: {action}"
        if details:
            message += f" - {details}"
        self.message_post(
            body=message,
            subject=f"Security Event: {action}",
            message_type='notification'
        )
    def action_validate(self):
        """Validate template with security logging"""
        self.ensure_one()
        # Check access rights
        if not self.env.user.has_group('base.group_system'):
            self._log_security_event("Unauthorized validation attempt", 
                                   f"User: {self.env.user.name}")
            raise AccessError(_("Only managers can validate templates"))
        # Perform validation
        self.state = 'testing'
        # Log successful action
        self._log_security_event("Template validated", 
                               f"User: {self.env.user.name}, State: {self.state}")
        return True
```
## Examples
### Complete Security Implementation
```python
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError, AccessError
import logging
_logger = logging.getLogger(__name__)
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Configuration Template'
    # Basic fields
    name = fields.Char("Template Name", required=True)
    code = fields.Char("Template Code", required=True)
    description = fields.Text("Description")
    state = fields.Selection([
        ('draft', 'Draft'),
        ('testing', 'Testing'),
        ('active', 'Active'),
        ('archived', 'Archived')
    ], default='draft', string="Status")
    # Security-sensitive fields
    internal_notes = fields.Text("Internal Notes", groups="base.group_user")
    admin_notes = fields.Text("Administrative Notes", groups="base.group_system")
    pricing_data = fields.Text("Pricing Data", groups="base.group_system")
    # Public access fields
    enable_online = fields.Boolean("Enable Online Sales", default=True)
    active = fields.Boolean("Active", default=True)
    # Constraints
    _sql_constraints = [
        ('unique_code', 'unique(code)', 'Template code must be unique!')
    ]
    # Security validations
    @api.constrains('code')
    def _check_code_security(self):
        """Validate code security"""
        for template in self:
            if any(char in template.code for char in [';', '--', '/*', '*/']):
                raise ValidationError(_("Invalid characters in template code"))
    @api.constrains('name')
    def _check_name_security(self):
        """Validate name security"""
        for template in self:
            if '<script' in template.name.lower():
                raise ValidationError(_("Invalid characters in template name"))
    # Secure methods
    def action_validate(self):
        """Validate template - only managers"""
        self.ensure_one()
        if not self.env.user.has_group('base.group_system'):
            self._log_security_event("Unauthorized validation attempt")
            raise AccessError(_("Only managers can validate templates"))
        if self.state != 'draft':
            raise UserError(_("Only draft templates can be validated"))
        self.state = 'testing'
        self._log_security_event("Template validated")
        return True
    def action_activate(self):
        """Activate template - only managers"""
        self.ensure_one()
        if not self.env.user.has_group('base.group_system'):
            self._log_security_event("Unauthorized activation attempt")
            raise AccessError(_("Only managers can activate templates"))
        if self.state != 'testing':
            raise UserError(_("Only tested templates can be activated"))
        self.state = 'active'
        self._log_security_event("Template activated")
        return True
    def _log_security_event(self, action, details=None):
        """Log security events"""
        message = f"Security event: {action}"
        if details:
            message += f" - {details}"
        self.message_post(
            body=message,
            subject=f"Security Event: {action}",
            message_type='notification'
        )
```