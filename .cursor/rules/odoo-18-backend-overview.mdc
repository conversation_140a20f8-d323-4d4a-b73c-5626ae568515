---
description: Odoo 18 Backend Development Overview and Quick Reference Guide
globs: **/*.py,**/*.xml
alwaysApply: false
---
# Odoo 18 Backend Development Overview
## Context
This rule provides a comprehensive overview of Odoo 18 backend development standards for the ConfigMatrix system, serving as a quick reference for all development activities. Based on proven AI-assisted development patterns and real-world implementations.
## Input
- All backend development files
- Python models, controllers, and business logic
- XML views and security configurations
- API endpoints and portal implementations
## Output
- Consistent Odoo 18 compliant code
- Secure and performant implementations
- Maintainable and scalable architecture
## AI Development Process Integration
### Phase 1: Requirements Analysis
```python
# ✅ CORRECT - Clear requirements documentation in docstrings
class ConfigMatrixTemplate(models.Model):
    """
    Configuration Template Model
    Business Requirements:
    - Support multiple use cases (check measure, sales, online)
    - Enable dynamic field visibility
    - Support pricing and labor matrices
    - Provide portal access for customers
    Technical Requirements:
    - Odoo 18 compatibility
    - Performance optimization for large datasets
    - Security for sensitive pricing data
    - Integration with existing sales and manufacturing modules
    """
    _name = 'config.matrix.template'
    _description = 'Configuration Template'
```
### Phase 2: Implementation Planning
```python
# ✅ CORRECT - Phase-based implementation with clear milestones
def action_validate(self):
    """
    Phase 1: Basic validation
    Phase 2: Business rule validation  
    Phase 3: Integration validation
    Phase 4: Performance optimization
    """
    self.ensure_one()
    # Phase 1: Basic validation
    if not self.name or not self.code:
        raise ValidationError(self.env._("Name and code are required"))
    # Phase 2: Business rule validation
    if self.state != 'draft':
        raise UserError(self.env._("Only draft templates can be validated"))
    # Phase 3: Integration validation
    if not self.section_ids:
        raise UserError(self.env._("At least one section is required"))
    # Phase 4: Performance optimization
    self.state = 'testing'
    self.message_post(body=self.env._("Template validated successfully"))
    return True
```
## Quick Reference Standards
### 1. Model Development Checklist
#### ✅ Required Elements
```python
# ✅ CORRECT - Complete model structure
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError, AccessError
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Configuration Template'
    _order = 'name'
    # Basic fields
    name = fields.Char("Template Name", required=True)
    code = fields.Char("Template Code", required=True)
    active = fields.Boolean("Active", default=True)
    # Computed fields
    field_count = fields.Integer("Field Count", compute='_compute_field_count', store=True)
    # Constraints
    _sql_constraints = [
        ('unique_code', 'unique(code)', 'Template code must be unique!')
    ]
    # Computed methods
    @api.depends('section_ids', 'section_ids.field_ids')
    def _compute_field_count(self):
        for template in self:
            template.field_count = sum(len(section.field_ids) for section in template.section_ids)
    # Action methods
    def action_validate(self):
        self.ensure_one()
        if not self.env.user.has_group('base.group_system'):
            raise AccessError(self.env._("Only managers can validate templates"))
        self.state = 'testing'
        return True
    # Validation methods
    @api.constrains('code')
    def _check_code_format(self):
        for template in self:
            if not template.code.isalnum():
                raise ValidationError(self.env._("Template code must contain only alphanumeric characters"))
```
### 2. XML View Checklist
#### ✅ Required Elements
```xml
<!-- ✅ CORRECT - Complete view structure -->
<record id="config_matrix_template_form" model="ir.ui.view">
    <field name="name">config.matrix.template.form</field>
    <field name="model">config.matrix.template</field>
    <field name="arch" type="xml">
        <form string="Configuration Template">
            <header>
                <field name="state" widget="statusbar" 
                       statusbar_visible="draft,testing,active"/>
            </header>
            <sheet>
                <div class="oe_title">
                    <h1>
                        <field name="name" placeholder="Template Name"/>
                    </h1>
                </div>
                <group>
                    <group>
                        <field name="code"/>
                        <field name="description"/>
                    </group>
                    <group>
                        <field name="active" invisible="1"/>
                        <field name="field_count" readonly="1"/>
                    </group>
                </group>
            </sheet>
            <chatter/>
        </form>
    </field>
</record>
```
### 3. Controller Checklist
#### ✅ Required Elements
```python
# ✅ CORRECT - Complete controller structure
from odoo import http
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
import logging
_logger = logging.getLogger(__name__)
class ConfigMatrixController(http.Controller):
    """Main controller for ConfigMatrix API endpoints"""
    def _format_success_response(self, data=None, message=None):
        """Format successful response"""
        response = {'success': True}
        if data:
            response['data'] = data
        if message:
            response['message'] = message
        return response
    @http.route('/config_matrix/api/template/<int:template_id>', 
                type='json', auth='user', methods=['GET'])
    def get_template(self, template_id, **kw):
        """Get template with consistent response format"""
        try:
            template = request.env['config.matrix.template'].browse(template_id)
            if not template.exists():
                return self._format_error_response('Template not found', 404)
            template_data = template.read(['name', 'code', 'state', 'description'])[0]
            return self._format_success_response(template_data)
        except Exception as e:
            _logger.error(f"Error getting template {template_id}: {str(e)}")
            return self._format_error_response(str(e), 500)
```
### 4. Security Checklist
#### ✅ Required Elements
```xml
<!-- ✅ CORRECT - Complete security configuration -->
<record id="config_matrix_template_access_user" model="ir.model.access">
    <field name="name">config.matrix.template.user</field>
    <field name="model_id" ref="config_matrix.model_config_matrix_template"/>
    <field name="group_id" ref="base.group_user"/>
    <field name="perm_read" eval="True"/>
    <field name="perm_write" eval="True"/>
    <field name="perm_create" eval="True"/>
    <field name="perm_unlink" eval="False"/>
</record>
<record id="config_matrix_template_rule_user" model="ir.rule">
    <field name="name">ConfigMatrix Template: users can only access active templates</field>
    <field name="model_id" ref="config_matrix.model_config_matrix_template"/>
    <field name="domain_force">[('active', '=', True)]</field>
    <field name="groups" eval="[(4, ref('base.group_user'))]"/>
</record>
```
## Odoo 18 Critical Changes
### 1. User Group Access Control
```python
# ✅ CORRECT - Odoo 18 user group checking
if not self.env.user.has_group('base.group_system'):
    raise AccessError(self.env._("Only managers can validate templates"))
# ❌ WRONG - Odoo 17 deprecated pattern
# if self.user_has_groups('base.group_system'):  # AttributeError in Odoo 18
```
### 2. Translation Performance Optimization
```python
# ✅ CORRECT - Odoo 18 optimized translations
message = self.env._("Template validated successfully")
# ❌ AVOID - Standard translation function (less optimal)
message = _("Template validated successfully")
```
### 3. XML View Changes
```xml
<!-- ✅ CORRECT - Odoo 18 list view -->
<list string="Configuration Templates">
    <field name="name"/>
    <field name="code"/>
</list>
<!-- ❌ DEPRECATED - Odoo 17 tree view -->
<!-- <tree string="Configuration Templates"> -->
```
### 4. Path Field for Better URLs
```xml
<!-- ✅ CORRECT - Odoo 18 path field -->
<record id="action_config_matrix_template" model="ir.actions.act_window">
    <field name="name">Configuration Templates</field>
    <field name="res_model">config.matrix.template</field>
    <field name="path">config-templates</field>  <!-- ✅ NEW: Better URL -->
    <field name="view_mode">list,form</field>
</record>
```
### 5. Simplified Chatter
```xml
<!-- ✅ CORRECT - Odoo 18 simplified chatter -->
<form string="Configuration Template">
    <sheet>
        <!-- Content -->
    </sheet>
    <chatter/>  <!-- ✅ NEW: Simple chatter tag -->
</form>
```
## Proven Solutions Patterns
### 1. Field Visibility Control
```python
# ✅ CORRECT - Override _get_view method for dynamic field visibility
@api.model
def _get_view(self, view_id, view_type, **options):
    arch, view = super()._get_view(view_id, view_type, **options)
    # Hide sensitive fields from certain user groups
    if not self.env.user.has_group('base.group_system'):
        if arch.xpath("//field[@name='internal_notes']"):
            arch.xpath("//field[@name='internal_notes']")[0].set("invisible", "1")
    return arch, view
```
### 2. Multi-Company Record Rules
```python
# ✅ CORRECT - Multi-company record rules
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    company_id = fields.Many2one('res.company', string='Company', 
                                default=lambda self: self.env.company)
    @api.model
    def _get_company_domain(self):
        return [('company_id', 'in', [False, self.env.company.id])]
```
### 3. Optimized Computed Fields
```python
# ✅ CORRECT - Optimized computed fields with proper dependencies
@api.depends('section_ids', 'section_ids.field_ids', 'section_ids.active')
def _compute_field_count(self):
    """Compute field count with performance optimization"""
    for template in self:
        # Use sum() for better performance
        template.field_count = sum(
            len(section.field_ids.filtered(lambda f: f.active))
            for section in template.section_ids
            if section.active
        )
```
## Portal Development Patterns
### 1. Portal User Management
```python
# ✅ CORRECT - Portal user identification strategy
def _check_portal_access(self):
    """Check if current user has portal access"""
    try:
        # Check if user is portal user
        if not request.env.user.has_group('base.group_portal'):
            return False
        # Use existing partner categorization instead of custom boolean fields
        local_freight_type = request.env.ref(
            'modula_contact_extended.res_partner_type_local_freight', 
            raise_if_not_found=False
        )
        if not local_freight_type or request.env.user.partner_id.customer_type_id != local_freight_type:
            return False
        return True
    except Exception:
        return False
```
### 2. Portal Controller Structure
```python
# ✅ CORRECT - Portal controller structure
class ConfigMatrixCustomerPortal(CustomerPortal):
    """Portal controller for customer-facing ConfigMatrix features"""
    def _prepare_home_portal_values(self, counters):
        """Extend portal home with custom counters"""
        values = super()._prepare_home_portal_values(counters)
        if 'config_matrix_count' in counters:
            values['config_matrix_count'] = self._get_config_matrix_count()
        return values
    def _get_config_matrix_count(self):
        """Get configuration count for current user"""
        return request.env['config.matrix.template'].search_count([
            ('active', '=', True)
        ])
```
## Performance Optimization
### 1. Database Queries
```python
# ✅ CORRECT - Efficient database queries
def _get_related_records(self):
    """Get related records efficiently"""
    return self.env['related.model'].search([
        ('parent_id', 'in', self.ids)
    ]).mapped('child_ids')
```
### 2. Computed Fields
```python
# ✅ CORRECT - Optimized computed fields
@api.depends('freight_carrier_id')
def _compute_delivery_user_id(self):
    """Compute delivery user based on freight carrier"""
    for picking in self:
        picking.delivery_user_id = picking.freight_carrier_id.user_id
```
## Error Handling
### 1. Validation Errors
```python
# ✅ CORRECT - Proper validation and error handling
@api.constrains('technical_name')
def _check_technical_name(self):
    """Validate technical name format"""
    for field in self:
        if not field.technical_name.isidentifier():
            raise ValidationError(self.env._("Technical name must be a valid Python identifier"))
```
### 2. Business Logic Errors
```python
# ✅ CORRECT - Business logic with proper error handling
def action_warehouse_validate(self):
    """Warehouse validation - sets delivery to out_for_delivery for freight carriers"""
    for picking in self:
        if not picking.freight_carrier_id:
            raise UserError(self.env._("No freight carrier assigned to this delivery"))
        if picking.state not in ['ready', 'assigned']:
            raise UserError(self.env._("Delivery must be ready or assigned before validation"))
```
## Common Pitfalls to Avoid
### 1. Dictionary Syntax Errors
```python
# ❌ WRONG - Missing closing brace
matrix = self.create({
    'name': name,
    'product_template_id': product_template_id,
    'matrix_type': 'price',
    'last_imported': fields.Datetime.now(),
return matrix
# ✅ CORRECT - Proper closing brace
matrix = self.create({
    'name': name,
    'product_template_id': product_template_id,
    'matrix_type': 'price',
    'last_imported': fields.Datetime.now(),
})
return matrix
```
### 2. Odoo 18 Migration Issues
```python
# ❌ WRONG - Odoo 17 deprecated patterns
if self.user_has_groups('base.group_system'):  # AttributeError in Odoo 18
    pass
# ✅ CORRECT - Odoo 18 patterns
if self.env.user.has_group('base.group_system'):
    pass
```
### 3. Import Issues
```python
# ✅ CORRECT - Proper imports
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError, AccessError
```
## Development Workflow
### 1. Pre-Development Checklist
- [ ] Define clear business requirements
- [ ] Establish technical context
- [ ] Prepare reference materials
- [ ] Plan implementation phases
### 2. Development Checklist
- [ ] Create module structure
- [ ] Implement security setup
- [ ] Develop core models
- [ ] Create XML views
- [ ] Implement controllers
- [ ] Add portal functionality
### 3. Testing Checklist
- [ ] Unit tests for all components
- [ ] Integration testing
- [ ] Security testing
- [ ] Performance testing
- [ ] Portal functionality testing
### 4. Documentation Checklist
- [ ] Code documentation with docstrings
- [ ] User documentation
- [ ] Technical documentation
- [ ] API reference
- [ ] Installation guides
