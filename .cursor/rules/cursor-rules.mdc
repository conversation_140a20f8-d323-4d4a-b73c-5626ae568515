---
description: How to add new cursor rules to the project
globs: 
alwaysApply: false
---
# Cursor Rules Location
How to add new cursor rules to the project

## Available Rules
- `module-docs-requirement.mdc` - Requires reading all documents in module's docs folder for AI to understand the purpose, architecture, and technical structure of module

1. Always place rule files in PROJECT_ROOT/.cursor/rules/:
    ```
    .cursor/rules/
    ├── your-rule-name.mdc
    ├── another-rule.mdc
    └── ...
    ```
  Nested rules automatically attach when files in their directory are referenced.
  Example
    ```
    .cursor/rules/        # Project-wide rules
    ├── backend/
    ├── ├──  server/
    ├── ├── ├── .cursor/rules/    # Backend-specific rules
    ├── frontend/
    ├── ├── ├──.cursor/rules/      # Frontend-specific rules
    ```
2. Best practices for Cursor rules:
Good rules are focused, actionable, and scoped.
   * Keep rules under 500 lines
   * Split large rules into multiple, composable rules
   * Provide concrete examples or referenced files
   * Avoid vague guidance. Write rules like clear internal docs
   * Reuse rules when repeating prompts in chat
3. Follow the naming convention:
    - Use kebab-case for filenames
    - Always use .mdc extension
    - Make names descriptive of the rule's purpose
4. Directory structure:
    ```
    PROJECT_ROOT/
    ├── .cursor/
    │   └── rules/
    │       ├── your-rule-name.mdc
    │       └── ...
    └── ...
    ```
5. Never place rule files:
    - In the project root
    - In subdirectories outside .cursor/rules
    - In any other location
6. Cursor rules have the following structure:
````
---
description: Short description of the rule's purpose
globs: optional/path/pattern/**/* 
alwaysApply: false
---
# Rule Title
Main content explaining the rule with markdown formatting.
1. Step-by-step instructions
2. Code examples
3. Guidelines
Example:
```typescript
// Good example
function goodExample() {
  // Implementation following guidelines
}
// Bad example
function badExample() {
  // Implementation not following guidelines
}
```
````
