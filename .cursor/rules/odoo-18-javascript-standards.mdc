---
description: Odoo 18 JavaScript Development Standards for Components, Widgets, and Frontend Integration
globs: **/*.js,**/*.xml
alwaysApply: false
---
# Odoo 18 JavaScript Development Standards
## Context
This rule enforces Odoo 18 JavaScript development standards for the ConfigMatrix system, ensuring proper component architecture, widget development, and frontend integration. Based on proven OWL component patterns and real-world implementations.
## Input
- JavaScript component files (.js)
- XML template files (.xml)
- Widget extensions
- Form controllers
- Dialog components
## Output
- Compliant Odoo 18 JavaScript components
- Proper OWL component patterns
- Optimized widget implementations
- Maintainable frontend code
## Core Standards
### 1. Component Architecture Standards
#### Basic Component Structure
```javascript
// ✅ CORRECT - Basic OWL component structure
/** @odoo-module **/
import { Component, onMounted, useRef, useState } from "@odoo/owl";
export class ConfigMatrixComponent extends Component {
    setup() {
        super.setup();
        // Use useState for reactive state management
        this.state = useState({
            loading: false,
            error: null,
            data: null,
        });
        // Use useRef for DOM references
        this.rootRef = useRef("root");
        // Lifecycle hooks
        onMounted(() => {
            this.initializeComponent();
        });
    }
    async initializeComponent() {
        this.state.loading = true;
        try {
            // Component initialization logic
            this.state.data = await this.loadData();
        } catch (error) {
            this.state.error = error.message;
        } finally {
            this.state.loading = false;
        }
    }
    async loadData() {
        // Data loading logic
        return await this.orm.call("config.matrix.template", "read", []);
    }
}
ConfigMatrixComponent.template = "config_matrix.ConfigMatrixComponent";
```
#### Template-JavaScript Relationship
```javascript
// ✅ CORRECT - Template name must match exactly
MyComponent.template = "module_name.ComponentName";
```
```xml
<!-- ✅ CORRECT - XML template with matching name -->
<t t-name="module_name.ComponentName">
    <div class="component-container">
        <!-- Component content -->
    </div>
</t>
```
### 2. File Organization Standards
#### Directory Structure
```
static/src/
├── components/
│   ├── config_matrix_editor.js
│   └── config_matrix_editor.xml
├── views/
│   ├── form_view.js
│   └── form_view.xml
├── fields/
│   ├── custom_field.js
│   └── custom_field.xml
└── widgets/
    ├── matrix_widget.js
    └── matrix_widget.xml
```
### 3. Widget Development Standards
#### Field Extensions
```javascript
// ✅ CORRECT - Field extension using patch
/** @odoo-module **/
import { FloatField } from "@web/views/fields/float/float_field";
import { patch } from "@web/core/utils/patch";
import { useService } from "@web/core/utils/hooks";
patch(FloatField.prototype, {
    setup() {
        super.setup();
        this.orm = useService("orm");
        this.notification = useService("notification");
    },
    async onFocusOut() {
        // Custom logic here
        if (this.customCondition) {
            // Handle custom behavior
            await this.validateCustomLogic();
        } else {
            return super.onFocusOut();
        }
    },
    async validateCustomLogic() {
        try {
            const result = await this.orm.call(
                "config.matrix.template", 
                "validate_field", 
                [this.props.value]
            );
            if (!result.valid) {
                this.notification.add(result.message, { type: "danger" });
            }
        } catch (error) {
            this.notification.add("Validation failed", { type: "danger" });
        }
    }
});
```
#### Form Controller Extensions
```javascript
// ✅ CORRECT - Form controller extension
/** @odoo-module **/
import { FormController } from "@web/views/form/form_controller";
import { useService } from "@web/core/utils/hooks";
import { onWillStart, useSubEnv } from "@odoo/owl";
export class ConfigMatrixFormController extends FormController {
    setup() {
        super.setup();
        // Services
        this.actionService = useService('action');
        this.notification = useService('notification');
        this.orm = useService('orm');
        // Lifecycle hooks
        onWillStart(async () => {
            await this.loadInitialData();
        });
        // Environment setup
        useSubEnv({
            configMatrixData: this.configMatrixData,
        });
    }
    async loadInitialData() {
        try {
            this.configMatrixData = await this.orm.call(
                "config.matrix.template", 
                "get_configuration_data", 
                []
            );
        } catch (error) {
            this.notification.add("Failed to load configuration data", { type: "danger" });
        }
    }
    async onSaveRecord() {
        const result = await super.onSaveRecord();
        if (result) {
            this.notification.add("Configuration saved successfully", { type: "success" });
        }
        return result;
    }
}
```
### 4. Dialog Component Standards
#### Dialog Implementation
```javascript
// ✅ CORRECT - Dialog component implementation
/** @odoo-module **/
import { Dialog } from "@web/core/dialog/dialog";
import { useService } from "@web/core/utils/hooks";
export class ConfigMatrixDialog extends Dialog {
    setup() {
        super.setup();
        this.orm = useService("orm");
        this.notification = useService("notification");
        this.state = useState({
            loading: false,
            data: this.props.data || {},
        });
    }
    async onConfirm() {
        this.state.loading = true;
        try {
            const result = await this.orm.call(
                "config.matrix.template", 
                "save_configuration", 
                [this.state.data]
            );
            this.notification.add("Configuration saved", { type: "success" });
            this.close(result);
        } catch (error) {
            this.notification.add("Failed to save configuration", { type: "danger" });
        } finally {
            this.state.loading = false;
        }
    }
    onCancel() {
        this.close();
    }
}
ConfigMatrixDialog.template = "config_matrix.ConfigMatrixDialog";
```
#### Dialog Template
```xml
<!-- ✅ CORRECT - Dialog template structure -->
<templates xml:space="preserve">
    <t t-name="config_matrix.ConfigMatrixDialog">
        <Dialog title="props.title" size="props.size or 'lg'">
            <div class="modal-body">
                <div class="row" t-if="props.message">
                    <div class="col-12">
                        <p t-esc="props.message"/>
                    </div>
                </div>
                <!-- Custom content -->
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <label>Configuration Name</label>
                            <input type="text" 
                                   class="form-control" 
                                   t-model="state.data.name"
                                   placeholder="Enter configuration name"/>
                        </div>
                    </div>
                </div>
            </div>
            <t t-set-slot="footer">
                <button class="btn btn-primary" 
                        t-on-click="onConfirm"
                        t-att-disabled="state.loading">
                    <i class="fa fa-check"/> Confirm
                </button>
                <button class="btn btn-secondary" 
                        t-on-click="onCancel"
                        t-att-disabled="state.loading">
                    <i class="fa fa-times"/> Cancel
                </button>
            </t>
        </Dialog>
    </t>
</templates>
```
### 5. Service Integration Standards
#### Service Usage
```javascript
// ✅ CORRECT - Service integration
import { useService } from "@web/core/utils/hooks";
export class MyComponent extends Component {
    setup() {
        super.setup();
        // Core services
        this.orm = useService("orm");
        this.notification = useService("notification");
        this.dialog = useService("dialog");
        this.actionService = useService("action");
        this.rpc = useService("rpc");
        // Custom services
        this.configMatrixService = useService("configMatrixService");
    }
    async performAction() {
        try {
            const result = await this.orm.call(
                "config.matrix.template", 
                "action_method", 
                [this.props.recordId]
            );
            this.notification.add("Action completed successfully", { type: "success" });
            return result;
        } catch (error) {
            this.notification.add("Action failed", { type: "danger" });
            throw error;
        }
    }
}
```
### 6. State Management Standards
#### useState Pattern
```javascript
// ✅ CORRECT - useState for reactive state
export class StatefulComponent extends Component {
    setup() {
        super.setup();
        // Use useState for reactive state management
        this.state = useState({
            loading: false,
            error: null,
            data: [],
            selectedItems: new Set(),
            formData: {
                name: "",
                description: "",
                active: true,
            },
        });
    }
    updateFormData(field, value) {
        this.state.formData[field] = value;
    }
    toggleSelection(itemId) {
        if (this.state.selectedItems.has(itemId)) {
            this.state.selectedItems.delete(itemId);
        } else {
            this.state.selectedItems.add(itemId);
        }
    }
    async loadData() {
        this.state.loading = true;
        this.state.error = null;
        try {
            this.state.data = await this.orm.call(
                "config.matrix.template", 
                "search_read", 
                [[["active", "=", true]]]
            );
        } catch (error) {
            this.state.error = error.message;
        } finally {
            this.state.loading = false;
        }
    }
}
```
### 7. Event Handling Standards
#### Event Handler Implementation
```javascript
// ✅ CORRECT - Event handling patterns
export class EventHandlingComponent extends Component {
    setup() {
        super.setup();
        this.orm = useService("orm");
        this.notification = useService("notification");
    }
    // Click event handlers
    async onButtonClick() {
        try {
            await this.performAction();
        } catch (error) {
            this.notification.add("Action failed", { type: "danger" });
        }
    }
    // Input event handlers
    onInputChange(ev) {
        const value = ev.target.value;
        this.updateField(ev.target.name, value);
    }
    // Form submission
    async onSubmit(ev) {
        ev.preventDefault();
        if (this.validateForm()) {
            await this.saveData();
        }
    }
    // Custom event handlers
    onItemSelect(itemId) {
        this.state.selectedItem = itemId;
        this.trigger("item-selected", { itemId });
    }
    // Validation
    validateForm() {
        const errors = [];
        if (!this.state.formData.name) {
            errors.push("Name is required");
        }
        if (errors.length > 0) {
            this.notification.add(errors.join(", "), { type: "danger" });
            return false;
        }
        return true;
    }
}
```
### 8. Template Development Standards
#### XML Template Structure
```xml
<!-- ✅ CORRECT - XML template structure -->
<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="config_matrix.ConfigMatrixEditor">
        <div class="config-matrix-editor">
            <!-- Header -->
            <div class="editor-header">
                <h3 t-esc="props.title"/>
                <div class="header-actions">
                    <button class="btn btn-primary" 
                            t-on-click="onSave"
                            t-att-disabled="state.loading">
                        <i class="fa fa-save"/> Save
                    </button>
                    <button class="btn btn-secondary" 
                            t-on-click="onCancel">
                        <i class="fa fa-times"/> Cancel
                    </button>
                </div>
            </div>
            <!-- Content -->
            <div class="editor-content">
                <!-- Loading state -->
                <div t-if="state.loading" class="loading-overlay">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                </div>
                <!-- Error state -->
                <div t-if="state.error" class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"/> 
                    <span t-esc="state.error"/>
                </div>
                <!-- Main content -->
                <div t-if="!state.loading and !state.error" class="matrix-content">
                    <!-- Form fields -->
                    <div class="form-group">
                        <label>Configuration Name</label>
                        <input type="text" 
                               class="form-control" 
                               t-model="state.formData.name"
                               placeholder="Enter configuration name"/>
                    </div>
                    <!-- Matrix grid -->
                    <div class="matrix-grid">
                        <t t-foreach="state.data" t-as="item" t-key="item.id">
                            <div class="matrix-item" 
                                 t-on-click="() => this.onItemClick(item.id)"
                                 t-att-class="{'selected': state.selectedItems.has(item.id)}">
                                <span t-esc="item.name"/>
                            </div>
                        </t>
                    </div>
                </div>
            </div>
        </div>
    </t>
</templates>
```
### 9. Performance Optimization Standards
#### Efficient Rendering
```javascript
// ✅ CORRECT - Performance optimization
export class OptimizedComponent extends Component {
    setup() {
        super.setup();
        // Debounce expensive operations
        this.debouncedSearch = this.debounce(this.performSearch, 300);
        // Memoize expensive computations
        this.memoizedData = this.memoize(this.computeData, this);
    }
    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    // Memoize function
    memoize(func, context) {
        const cache = new Map();
        return function (...args) {
            const key = JSON.stringify(args);
            if (cache.has(key)) {
                return cache.get(key);
            }
            const result = func.apply(context, args);
            cache.set(key, result);
            return result;
        };
    }
    // Optimized search
    async performSearch(query) {
        if (query.length < 2) return;
        this.state.loading = true;
        try {
            const results = await this.orm.call(
                "config.matrix.template", 
                "search_read", 
                [[["name", "ilike", query]]]
            );
            this.state.searchResults = results;
        } catch (error) {
            this.notification.add("Search failed", { type: "danger" });
        } finally {
            this.state.loading = false;
        }
    }
}
```
### 10. Error Handling Standards
#### Comprehensive Error Handling
```javascript
// ✅ CORRECT - Error handling patterns
export class ErrorHandlingComponent extends Component {
    setup() {
        super.setup();
        this.notification = useService("notification");
        this.orm = useService("orm");
    }
    async performAction() {
        try {
            this.state.loading = true;
            this.state.error = null;
            const result = await this.orm.call(
                "config.matrix.template", 
                "action_method", 
                [this.props.recordId]
            );
            this.notification.add("Action completed successfully", { type: "success" });
            return result;
        } catch (error) {
            // Handle different types of errors
            if (error.name === "ValidationError") {
                this.state.error = "Validation failed: " + error.message;
                this.notification.add(this.state.error, { type: "warning" });
            } else if (error.name === "AccessError") {
                this.state.error = "Access denied: " + error.message;
                this.notification.add(this.state.error, { type: "danger" });
            } else {
                this.state.error = "An unexpected error occurred: " + error.message;
                this.notification.add(this.state.error, { type: "danger" });
            }
            // Log error for debugging
            console.error("Action failed:", error);
            throw error;
        } finally {
            this.state.loading = false;
        }
    }
}
```
## Examples
### Complete Component Example
```javascript
/** @odoo-module **/
import { Component, onMounted, useRef, useState } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";
export class ConfigMatrixEditor extends Component {
    setup() {
        super.setup();
        // Services
        this.orm = useService("orm");
        this.notification = useService("notification");
        this.dialog = useService("dialog");
        // State management
        this.state = useState({
            loading: false,
            error: null,
            data: [],
            selectedItems: new Set(),
            formData: {
                name: "",
                description: "",
                active: true,
            },
        });
        // Refs
        this.rootRef = useRef("root");
        // Lifecycle
        onMounted(() => {
            this.loadData();
        });
    }
    async loadData() {
        this.state.loading = true;
        this.state.error = null;
        try {
            this.state.data = await this.orm.call(
                "config.matrix.template", 
                "search_read", 
                [[["active", "=", true]]]
            );
        } catch (error) {
            this.state.error = error.message;
            this.notification.add("Failed to load data", { type: "danger" });
        } finally {
            this.state.loading = false;
        }
    }
    async onSave() {
        if (!this.validateForm()) return;
        this.state.loading = true;
        try {
            const result = await this.orm.call(
                "config.matrix.template", 
                "create", 
                [this.state.formData]
            );
            this.notification.add("Configuration saved successfully", { type: "success" });
            this.props.onSave(result);
        } catch (error) {
            this.notification.add("Failed to save configuration", { type: "danger" });
        } finally {
            this.state.loading = false;
        }
    }
    onCancel() {
        this.props.onCancel();
    }
    validateForm() {
        const errors = [];
        if (!this.state.formData.name) {
            errors.push("Name is required");
        }
        if (errors.length > 0) {
            this.notification.add(errors.join(", "), { type: "danger" });
            return false;
        }
        return true;
    }
    updateFormData(field, value) {
        this.state.formData[field] = value;
    }
    toggleSelection(itemId) {
        if (this.state.selectedItems.has(itemId)) {
            this.state.selectedItems.delete(itemId);
        } else {
            this.state.selectedItems.add(itemId);
        }
    }
}
ConfigMatrixEditor.template = "config_matrix.ConfigMatrixEditor";
```