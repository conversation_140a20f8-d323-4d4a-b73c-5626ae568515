---
description: Odoo 18 XML View Standards for Forms, Lists, Search Views, and View Inheritance
globs: **/*.xml
alwaysApply: false
---
# Odoo 18 XML View Standards
## Context
This rule enforces Odoo 18 XML view standards for the ConfigMatrix system, ensuring proper view structure, syntax, and compatibility with Odoo 18 changes. Based on proven development patterns and real-world implementations.
## Input
- XML view files (.xml)
- Form views
- List views (formerly tree views)
- Search views
- Kanban views
- Graph views
## Output
- Compliant Odoo 18 XML views
- Proper view inheritance
- Optimized view performance
- Accessible and maintainable view definitions
## Core Standards
### 1. Odoo 18 Critical Changes
#### Path Field for Better URLs
```xml
<!-- ✅ CORRECT - Odoo 18 path field for better URLs -->
<record id="action_config_matrix_template" model="ir.actions.act_window">
    <field name="name">Configuration Templates</field>
    <field name="res_model">config.matrix.template</field>
    <field name="path">config-templates</field>  <!-- ✅ NEW: Better URL -->
    <field name="view_mode">list,form</field>
    <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
            Create your first configuration template!
        </p>
    </field>
</record>
<!-- ❌ AVOID - Missing path field -->
<record id="action_config_matrix_template" model="ir.actions.act_window">
    <field name="name">Configuration Templates</field>
    <field name="res_model">config.matrix.template</field>
    <field name="view_mode">list,form</field>
</record>
```
#### Simplified Chatter Implementation
```xml
<!-- ✅ CORRECT - Odoo 18 simplified chatter -->
<form string="Configuration Template">
    <sheet>
        <div class="oe_title">
            <h1>
                <field name="name" placeholder="Template Name"/>
            </h1>
        </div>
        <group>
            <field name="code"/>
            <field name="description"/>
        </group>
    </sheet>
    <chatter/>  <!-- ✅ NEW: Simple chatter tag -->
</form>
<!-- ❌ DEPRECATED - Odoo 17 complex chatter structure -->
<form string="Configuration Template">
    <sheet>
        <!-- Content -->
    </sheet>
    <!-- ❌ OLD: Complex chatter structure -->
    <div class="oe_chatter">
        <field name="message_follower_ids" widget="mail_followers"/>
        <field name="activity_ids" widget="mail_activity"/>
        <field name="message_ids" widget="mail_thread"/>
    </div>
</form>
```
### 2. View Structure Standards
#### Form View Structure
```xml
<!-- ✅ CORRECT - Odoo 18 form view structure -->
<record id="config_matrix_template_form" model="ir.ui.view">
    <field name="name">config.matrix.template.form</field>
    <field name="model">config.matrix.template</field>
    <field name="arch" type="xml">
        <form string="Configuration Template">
            <header>
                <field name="state" widget="statusbar" 
                       statusbar_visible="draft,testing,active"/>
            </header>
            <sheet>
                <div class="oe_title">
                    <h1>
                        <field name="name" placeholder="Template Name"/>
                    </h1>
                </div>
                <group>
                    <group>
                        <field name="product_template_id"/>
                        <field name="code"/>
                        <field name="version"/>
                    </group>
                    <group>
                        <field name="active"/>
                        <field name="field_count"/>
                    </group>
                </group>
                <notebook>
                    <page string="Sections" name="sections">
                        <field name="section_ids">
                            <list editable="bottom">
                                <field name="sequence" widget="handle"/>
                                <field name="name"/>
                                <field name="description"/>
                                <field name="field_count"/>
                            </list>
                        </field>
                    </page>
                </notebook>
            </sheet>
            <chatter/>
        </form>
    </field>
</record>
```
#### List View Structure (formerly Tree Views)
```xml
<!-- ✅ CORRECT - Odoo 18 list view structure -->
<record id="config_matrix_template_list" model="ir.ui.view">
    <field name="name">config.matrix.template.list</field>
    <field name="model">config.matrix.template</field>
    <field name="arch" type="xml">
        <list string="Configuration Templates" 
              default_order="name asc"
              editable="bottom">
            <field name="name"/>
            <field name="product_template_id"/>
            <field name="code"/>
            <field name="state"/>
            <field name="field_count"/>
            <field name="active"/>
        </list>
    </field>
</record>
```
### 3. Field Definition Standards
#### Basic Field Definitions
```xml
<!-- ✅ CORRECT - Proper field definitions -->
<field name="name" string="Template Name" required="1"/>
<field name="description" placeholder="Enter description..."/>
<field name="active" invisible="1"/>
<field name="sequence" widget="handle"/>
<field name="amount" widget="monetary" options="{'currency_field': 'currency_id'}"/>
```
#### Relational Field Definitions
```xml
<!-- ✅ CORRECT - Relational field definitions -->
<field name="product_template_id" 
       domain="[('type', '=', 'product')]"
       context="{'default_type': 'product'}"
       options="{'no_create': True}"/>
<field name="section_ids" 
       context="{'default_matrix_id': active_id}"
       options="{'no_create': False}">
    <list editable="bottom">
        <field name="sequence" widget="handle"/>
        <field name="name"/>
        <field name="description"/>
    </list>
</field>
```
### 4. Visibility and Conditional Display
#### Direct Invisible Attribute (Odoo 18)
```xml
<!-- ✅ CORRECT - Odoo 18 direct invisible attribute -->
<field name="field_name" invisible="other_field == False"/>
<field name="conditional_field" invisible="state != 'active'"/>
<group invisible="matrix_type != 'price'">
    <field name="price_field"/>
</group>
<!-- ❌ DEPRECATED - Odoo 17 and earlier attrs syntax -->
<field name="field_name" attrs="{'invisible': [('other_field', '=', False)]}"/>
```
#### Conditional Readonly
```xml
<!-- ✅ CORRECT - Conditional readonly -->
<field name="name" readonly="state == 'archived'"/>
<field name="code" readonly="state in ('active', 'archived')"/>
```
### 5. Widget Usage Standards
#### Common Widgets
```xml
<!-- ✅ CORRECT - Proper widget usage -->
<field name="description" widget="html" options="{'style-inline': true}"/>
<field name="date_field" widget="date"/>
<field name="datetime_field" widget="datetime"/>
<field name="amount" widget="monetary" options="{'currency_field': 'currency_id'}"/>
<field name="percentage" widget="percentage"/>
<field name="image" widget="image" options="{'preview_image': 'image_128'}"/>
<field name="binary_field" widget="binary" filename="filename_field"/>
```
#### Custom Widgets
```xml
<!-- ✅ CORRECT - Custom widget usage -->
<field name="matrix_data" widget="config_matrix_editor"/>
<field name="svg_preview" widget="svg_renderer"/>
<field name="signature" widget="signature_pad"/>
```
### 6. View Inheritance Standards
#### Proper View Inheritance
```xml
<!-- ✅ CORRECT - View inheritance -->
<record id="config_matrix_template_form_inherit" model="ir.ui.view">
    <field name="name">config.matrix.template.form.inherit</field>
    <field name="model">config.matrix.template</field>
    <field name="inherit_id" ref="config_matrix.config_matrix_template_form"/>
    <field name="arch" type="xml">
        <!-- Add field after name -->
        <xpath expr="//field[@name='name']" position="after">
            <field name="custom_field"/>
        </xpath>
        <!-- Add new page to notebook -->
        <xpath expr="//notebook" position="inside">
            <page string="Custom Page" name="custom_page">
                <field name="custom_one2many_field">
                    <list>
                        <field name="name"/>
                        <field name="value"/>
                    </list>
                </field>
            </page>
        </xpath>
        <!-- Replace field -->
        <xpath expr="//field[@name='old_field']" position="replace">
            <field name="new_field"/>
        </xpath>
    </field>
</record>
```
#### XPath Best Practices
```xml
<!-- ✅ CORRECT - XPath with proper targeting -->
<!-- Target existing fields that are guaranteed to exist -->
<xpath expr="//field[@name='partner_shipping_id']" position="after">
    <field name="custom_field"/>
</xpath>
<!-- ❌ AVOID - Targeting fields that might not exist -->
<xpath expr="//field[@name='carrier_id']" position="after">
    <field name="custom_field"/>
</xpath>
```
### 7. Search View Standards
#### Search View Structure
```xml
<!-- ✅ CORRECT - Search view structure -->
<record id="config_matrix_template_search" model="ir.ui.view">
    <field name="name">config.matrix.template.search</field>
    <field name="model">config.matrix.template</field>
    <field name="arch" type="xml">
        <search string="Search Configuration Templates">
            <field name="name"/>
            <field name="code"/>
            <field name="product_template_id"/>
            <filter string="Active" name="active" domain="[('active', '=', True)]"/>
            <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
            <filter string="Active" name="active_state" domain="[('state', '=', 'active')]"/>
            <group expand="0" string="Group By">
                <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                <filter string="Product Template" name="group_product" context="{'group_by': 'product_template_id'}"/>
            </group>
        </search>
    </field>
</record>
```
### 8. Action Definition Standards
#### Window Actions with Path Field
```xml
<!-- ✅ CORRECT - Window action definition with path field -->
<record id="action_config_matrix_template" model="ir.actions.act_window">
    <field name="name">Configuration Templates</field>
    <field name="res_model">config.matrix.template</field>
    <field name="path">config-templates</field>  <!-- ✅ NEW: Better URL -->
    <field name="view_mode">list,form</field>
    <field name="context">{'search_default_active': 1}</field>
    <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
            Create your first configuration template!
        </p>
        <p>
            Configuration templates define the structure and fields for configurable products.
        </p>
    </field>
</record>
```
#### Server Actions
```xml
<!-- ✅ CORRECT - Server action definition -->
<record id="action_validate_templates" model="ir.actions.server">
    <field name="name">Validate Templates</field>
    <field name="model_id" ref="config_matrix.model_config_matrix_template"/>
    <field name="state">code</field>
    <field name="code">
        for record in records:
            record.action_validate()
    </field>
</record>
```
### 9. Menu Definition Standards
#### Menu Structure
```xml
<!-- ✅ CORRECT - Menu structure -->
<menuitem id="menu_config_matrix_root" 
          name="ConfigMatrix" 
          sequence="10"/>
<menuitem id="menu_config_matrix_templates" 
          name="Templates" 
          parent="menu_config_matrix_root" 
          sequence="10"/>
<menuitem id="menu_config_matrix_template" 
          name="Configuration Templates" 
          parent="menu_config_matrix_templates" 
          action="action_config_matrix_template" 
          sequence="10"/>
```
### 10. Common Pitfalls to Avoid
#### Deprecated Syntax
```xml
<!-- ❌ DEPRECATED - Odoo 17 and earlier -->
<tree>  <!-- Use <list> instead -->
    <field name="name"/>
</tree>
<!-- ✅ CORRECT - Odoo 18 -->
<list>
    <field name="name"/>
</list>
```
#### Incorrect XPath Expressions
```xml
<!-- ❌ WRONG - Incorrect XPath -->
<xpath expr="//field[@name='name']" position="after">
    <field name="new_field"/>
</xpath>
<!-- ✅ CORRECT - Proper XPath -->
<xpath expr="//field[@name='name']" position="after">
    <field name="new_field"/>
</xpath>
```
#### Missing Path Field
```xml
<!-- ❌ AVOID - Missing path field for better URLs -->
<record id="action_config_matrix_template" model="ir.actions.act_window">
    <field name="name">Configuration Templates</field>
    <field name="res_model">config.matrix.template</field>
    <field name="view_mode">list,form</field>
</record>
<!-- ✅ CORRECT - Include path field -->
<record id="action_config_matrix_template" model="ir.actions.act_window">
    <field name="name">Configuration Templates</field>
    <field name="res_model">config.matrix.template</field>
    <field name="path">config-templates</field>
    <field name="view_mode">list,form</field>
</record>
```
### 11. Performance Optimization
#### Efficient Field Loading
```xml
<!-- ✅ CORRECT - Efficient field loading -->
<field name="related_field" readonly="1"/>
<field name="computed_field" readonly="1"/>
<field name="many2one_field" options="{'no_create': True, 'no_open': True}"/>
```
#### Optimized List Views
```xml
<!-- ✅ CORRECT - Optimized list view -->
<list string="Records" 
      default_order="name asc"
      limit="80"
      editable="bottom">
    <field name="name"/>
    <field name="state"/>
    <field name="date"/>
</list>
```
### 12. Path Field Best Practices
#### Path Field Guidelines
```xml
<!-- ✅ CORRECT - Path field best practices -->
<field name="path">config-templates</field>  <!-- Use kebab-case -->
<field name="path">vendor-bills</field>      <!-- Be descriptive -->
<field name="path">work-entries</field>      <!-- Keep it short -->
<field name="path">iap-accounts</field>      <!-- Use hyphens -->
<!-- ❌ AVOID - Bad path field practices -->
<field name="path">config_templates</field>  <!-- No underscores -->
<field name="path">configtemplates</field>   <!-- No hyphens -->
<field name="path">very-long-descriptive-path-name</field>  <!-- Too long -->
```
## Examples
### Complete View Example
```xml
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View -->
    <record id="config_matrix_template_form" model="ir.ui.view">
        <field name="name">config.matrix.template.form</field>
        <field name="model">config.matrix.template</field>
        <field name="arch" type="xml">
            <form string="Configuration Template">
                <header>
                    <field name="state" widget="statusbar" 
                           statusbar_visible="draft,testing,active"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Template Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="product_template_id" 
                                   domain="[('type', '=', 'product')]"
                                   options="{'no_create': True}"/>
                            <field name="code"/>
                            <field name="version"/>
                        </group>
                        <group>
                            <field name="active" invisible="1"/>
                            <field name="field_count" readonly="1"/>
                            <field name="description"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Sections" name="sections">
                            <field name="section_ids" 
                                   context="{'default_matrix_id': active_id}">
                                <list editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="description"/>
                                    <field name="field_count" readonly="1"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>
    <!-- List View -->
    <record id="config_matrix_template_list" model="ir.ui.view">
        <field name="name">config.matrix.template.list</field>
        <field name="model">config.matrix.template</field>
        <field name="arch" type="xml">
            <list string="Configuration Templates" 
                  default_order="name asc"
                  editable="bottom">
                <field name="name"/>
                <field name="product_template_id"/>
                <field name="code"/>
                <field name="state"/>
                <field name="field_count"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>
    <!-- Search View -->
    <record id="config_matrix_template_search" model="ir.ui.view">
        <field name="name">config.matrix.template.search</field>
        <field name="model">config.matrix.template</field>
        <field name="arch" type="xml">
            <search string="Search Configuration Templates">
                <field name="name"/>
                <field name="code"/>
                <field name="product_template_id"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>
    <!-- Action with Path Field -->
    <record id="action_config_matrix_template" model="ir.actions.act_window">
        <field name="name">Configuration Templates</field>
        <field name="res_model">config.matrix.template</field>
        <field name="path">config-templates</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first configuration template!
            </p>
        </field>
    </record>
</odoo>
```