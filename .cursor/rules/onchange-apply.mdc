---
description: create method with @api.onchange
globs: 
alwaysApply: false
---
# Odoo 18: Best Practice for @api.onchange with Interdependent Fields
## Rule: Separate @api.onchange Methods for Interdependent Fields
**Context:**  
When two fields in an Odoo model (e.g., `mininum_deposit` and `minimum_deposit_percent` in `sale.order.line`) are interdependent, it is best to define separate `@api.onchange` methods for each field, rather than a single method with both fields in the decorator.
**Why:**  
- This ensures that changes to either field are handled independently and correctly.
- It avoids recursion or unexpected overwrites, as each method is only triggered by its respective field.
- It improves code clarity and maintainability.
**Example:**
```python
class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"
    mininum_deposit = fields.Monetary("Deposit Amount")
    minimum_deposit_percent = fields.Float("Deposit %", ...)
    @api.onchange("mininum_deposit")
    def onchange_mininum_deposit(self):
        # Logic to update minimum_deposit_percent based on mininum_deposit
    @api.onchange("minimum_deposit_percent")
    def onchange_minimum_deposit_percent(self):
        # Logic to update mininum_deposit based on minimum_deposit_percent
```
**Do NOT:**
```python
# Avoid this pattern:
@api.onchange("mininum_deposit", "minimum_deposit_percent")
def onchange_both(self):
    # This can cause confusion and unexpected results
```
**References:**  
- @Odoo Documentation: @api.onchange
- See `sale_order_line.py` in this project for a real-world example.
---
**Summary:**  
When two fields depend on each other, always use separate `@api.onchange` methods for each field in Odoo 18.
