---
description: Odoo 18 Controller Development Standards for API Endpoints, Portal Controllers, and Security
globs: **/controllers/**/*.py
alwaysApply: false
---
# Odoo 18 Controller Development Standards
## Context
This rule enforces Odoo 18 controller development standards for the ConfigMatrix system, ensuring proper API design, security, and performance for both internal and portal controllers. Based on proven portal development patterns and real-world implementations.
## Input
- Controller files in controllers/ directory
- Portal controllers
- Website controllers
- API endpoints
- JSON-RPC handlers
## Output
- Secure and performant controller implementations
- Proper error handling and validation
- RESTful API design
- Portal access control
## Core Standards
### 1. Portal Architecture Standards
#### Portal Controller Structure
```python
# ✅ CORRECT - Portal controller structure
from odoo import http
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
import logging
_logger = logging.getLogger(__name__)
class ConfigMatrixCustomerPortal(CustomerPortal):
    """Portal controller for customer-facing ConfigMatrix features"""
    def _prepare_home_portal_values(self, counters):
        """Extend portal home with custom counters"""
        values = super()._prepare_home_portal_values(counters)
        if 'config_matrix_count' in counters:
            values['config_matrix_count'] = self._get_config_matrix_count()
        return values
    def _get_config_matrix_count(self):
        """Get configuration count for current user"""
        return request.env['config.matrix.template'].search_count([
            ('active', '=', True)
        ])
```
#### Portal User Management
```python
# ✅ CORRECT - Portal user identification strategy
def _check_portal_access(self):
    """Check if current user has portal access"""
    try:
        # Check if user is portal user
        if not request.env.user.has_group('base.group_portal'):
            return False
        # Use existing partner categorization instead of custom boolean fields
        local_freight_type = request.env.ref(
            'modula_contact_extended.res_partner_type_local_freight', 
            raise_if_not_found=False
        )
        if not local_freight_type or request.env.user.partner_id.customer_type_id != local_freight_type:
            return False
        return True
    except Exception:
        return False
# ❌ AVOID - Custom boolean fields for user identification
# is_local_freight_vendor = fields.Boolean(string="Is Local Freight Vendor")
```
### 2. Controller Structure Standards
#### Basic Controller Structure
```python
# ✅ CORRECT - Proper controller structure
from odoo import http
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
class ConfigMatrixController(http.Controller):
    """Main controller for ConfigMatrix API endpoints"""
    @http.route('/config_matrix/api/template/<int:template_id>', 
                type='json', auth='user', methods=['GET'])
    def get_template(self, template_id, **kw):
        """Get template data with proper error handling"""
        try:
            template = request.env['config.matrix.template'].browse(template_id)
            if not template.exists():
                return {'error': 'Template not found'}, 404
            return {
                'id': template.id,
                'name': template.name,
                'sections': template.section_ids.read(['name', 'sequence']),
                'fields': template.field_ids.read(['name', 'field_type', 'technical_name'])
            }
        except Exception as e:
            return {'error': str(e)}, 500
```
#### Portal Controller Structure
```python
# ✅ CORRECT - Portal controller structure
class ConfigMatrixCustomerPortal(CustomerPortal):
    """Portal controller for customer-facing ConfigMatrix features"""
    def _prepare_home_portal_values(self, counters):
        """Extend portal home with custom counters"""
        values = super()._prepare_home_portal_values(counters)
        if 'config_matrix_count' in counters:
            values['config_matrix_count'] = self._get_config_matrix_count()
        return values
    def _get_config_matrix_count(self):
        """Get configuration count for current user"""
        return request.env['config.matrix.template'].search_count([
            ('active', '=', True)
        ])
```
### 3. Route Definition Standards
#### RESTful API Routes
```python
# ✅ CORRECT - RESTful API route design
@http.route('/config_matrix/api/templates', 
            type='json', auth='user', methods=['GET'])
def get_templates(self, **kw):
    """Get list of templates with pagination"""
    try:
        limit = int(kw.get('limit', 20))
        offset = int(kw.get('offset', 0))
        templates = request.env['config.matrix.template'].search([
            ('active', '=', True)
        ], limit=limit, offset=offset)
        return {
            'templates': templates.read(['name', 'code', 'state']),
            'total': templates.search_count([('active', '=', True)]),
            'limit': limit,
            'offset': offset
        }
    except Exception as e:
        return {'error': str(e)}, 500
@http.route('/config_matrix/api/template', 
            type='json', auth='user', methods=['POST'])
def create_template(self, **post):
    """Create new template"""
    try:
        template_data = {
            'name': post.get('name'),
            'code': post.get('code'),
            'product_template_id': int(post.get('product_template_id')),
            'description': post.get('description', '')
        }
        template = request.env['config.matrix.template'].create(template_data)
        return {'id': template.id, 'name': template.name}
    except Exception as e:
        return {'error': str(e)}, 400
```
#### Portal Routes
```python
# ✅ CORRECT - Portal route implementation
@http.route(['/my/config-matrices'], type='http', auth="user", website=True)
def portal_my_config_matrices(self, **kw):
    """List view - paginated configuration listing"""
    if not self._check_portal_access():
        return request.redirect('/my')
    try:
        ConfigMatrixTemplate = request.env['config.matrix.template']
        # Pagination
        page = int(kw.get('page', 1))
        limit = 20
        offset = (page - 1) * limit
        # Search domain
        domain = [('active', '=', True)]
        searchbar_sortings = {
            'name': {'label': 'Name', 'order': 'name'},
            'code': {'label': 'Code', 'order': 'code'},
            'date': {'label': 'Date', 'order': 'create_date desc'},
        }
        # Sorting
        sortby = kw.get('sortby', 'name')
        order = searchbar_sortings[sortby]['order']
        templates = ConfigMatrixTemplate.search(domain, order=order, limit=limit, offset=offset)
        total = ConfigMatrixTemplate.search_count(domain)
        pager = portal_pager(
            url="/my/config-matrices",
            total=total,
            page=page,
            step=limit
        )
        values = {
            'templates': templates,
            'pager': pager,
            'searchbar_sortings': searchbar_sortings,
            'sortby': sortby,
        }
        return request.render("config_matrix.portal_my_config_matrices", values)
    except Exception as e:
        return request.render("portal.portal_my_home", {'error': str(e)})
```
### 4. Security Standards
#### Access Control
```python
# ✅ CORRECT - Proper access control implementation
def _check_portal_access(self):
    """Check if current user has portal access"""
    try:
        # Check if user is portal user
        if not request.env.user.has_group('base.group_portal'):
            return False
        # Check specific access rights using existing partner categorization
        local_freight_type = request.env.ref(
            'modula_contact_extended.res_partner_type_local_freight', 
            raise_if_not_found=False
        )
        if not local_freight_type or request.env.user.partner_id.customer_type_id != local_freight_type:
            return False
        return True
    except Exception:
        return False
@http.route(['/my/config-matrices/<int:template_id>'], type='http', auth="user", website=True)
def portal_my_config_matrix(self, template_id, **kw):
    """Detail view - individual configuration page"""
    if not self._check_portal_access():
        return request.redirect('/my')
    try:
        template = request.env['config.matrix.template'].browse(template_id)
        if not template.exists():
            return request.redirect('/my/config-matrices')
        values = {
            'template': template,
            'sections': template.section_ids,
        }
        return request.render("config_matrix.portal_my_config_matrix", values)
    except Exception as e:
        return request.render("portal.portal_my_home", {'error': str(e)})
```
#### CSRF Protection
```python
# ✅ CORRECT - CSRF protection for POST routes
@http.route(['/my/config-matrices/<int:template_id>/save'], 
            type='http', auth="user", methods=['POST'], website=True, csrf=True)
def portal_save_configuration(self, template_id, **post):
    """Save configuration with CSRF protection"""
    if not self._check_portal_access():
        return request.redirect('/my')
    try:
        template = request.env['config.matrix.template'].browse(template_id)
        if not template.exists():
            return request.redirect('/my/config-matrices')
        # Process form data
        configuration_data = {}
        for key, value in post.items():
            if key.startswith('field_'):
                field_name = key.replace('field_', '')
                configuration_data[field_name] = value
        # Save configuration
        # Implementation here
        return request.redirect(f'/my/config-matrices/{template_id}?success=1')
    except Exception as e:
        return request.render("portal.portal_my_home", {'error': str(e)})
```
### 5. Error Handling Standards
#### Comprehensive Error Handling
```python
# ✅ CORRECT - Comprehensive error handling
@http.route('/config_matrix/api/template/<int:template_id>/validate', 
            type='json', auth='user', methods=['POST'])
def validate_template(self, template_id, **kw):
    """Validate template configuration"""
    try:
        # Input validation
        if not template_id:
            return {'error': 'Template ID is required'}, 400
        template = request.env['config.matrix.template'].browse(template_id)
        if not template.exists():
            return {'error': 'Template not found'}, 404
        # Business logic validation
        if template.state != 'draft':
            return {'error': 'Only draft templates can be validated'}, 400
        # Perform validation
        validation_result = template.action_validate()
        return {
            'success': True,
            'message': 'Template validated successfully',
            'template_id': template.id,
            'new_state': template.state
        }
    except ValidationError as e:
        return {'error': str(e)}, 400
    except UserError as e:
        return {'error': str(e)}, 400
    except Exception as e:
        # Log the error for debugging
        _logger.error(f"Error validating template {template_id}: {str(e)}")
        return {'error': 'Internal server error'}, 500
```
### 6. Performance Optimization Standards
#### Caching and Optimization
```python
# ✅ CORRECT - Performance optimized controller
@http.route('/config_matrix/api/template/<int:template_id>/data', 
            type='json', auth='user', methods=['GET'])
def get_template_data(self, template_id, **kw):
    """Get template data with caching"""
    try:
        template = request.env['config.matrix.template'].browse(template_id)
        if not template.exists():
            return {'error': 'Template not found'}, 404
        # Use read() for efficient data retrieval
        template_data = template.read([
            'name', 'code', 'description', 'state', 'version'
        ])[0]
        # Efficient related data loading
        sections_data = template.section_ids.read([
            'name', 'sequence', 'description'
        ])
        fields_data = template.field_ids.read([
            'name', 'technical_name', 'field_type', 'sequence'
        ])
        return {
            'template': template_data,
            'sections': sections_data,
            'fields': fields_data
        }
    except Exception as e:
        return {'error': str(e)}, 500
```
### 7. Input Validation Standards
#### Request Validation
```python
# ✅ CORRECT - Input validation
def _validate_template_data(self, data):
    """Validate template creation/update data"""
    errors = []
    # Required fields
    if not data.get('name'):
        errors.append('Template name is required')
    if not data.get('code'):
        errors.append('Template code is required')
    if not data.get('product_template_id'):
        errors.append('Product template is required')
    # Field format validation
    if data.get('code') and not data['code'].isalnum():
        errors.append('Template code must contain only alphanumeric characters')
    # Business rule validation
    if data.get('product_template_id'):
        product = request.env['product.template'].browse(int(data['product_template_id']))
        if not product.exists():
            errors.append('Invalid product template')
    return errors
@http.route('/config_matrix/api/template', 
            type='json', auth='user', methods=['POST'])
def create_template(self, **post):
    """Create new template with validation"""
    try:
        # Validate input data
        validation_errors = self._validate_template_data(post)
        if validation_errors:
            return {'errors': validation_errors}, 400
        # Create template
        template_data = {
            'name': post['name'],
            'code': post['code'],
            'product_template_id': int(post['product_template_id']),
            'description': post.get('description', '')
        }
        template = request.env['config.matrix.template'].create(template_data)
        return {
            'success': True,
            'template_id': template.id,
            'name': template.name
        }
    except Exception as e:
        return {'error': str(e)}, 500
```
### 8. Response Format Standards
#### Consistent Response Format
```python
# ✅ CORRECT - Consistent response format
def _format_success_response(self, data=None, message=None):
    """Format successful response"""
    response = {'success': True}
    if data:
        response['data'] = data
    if message:
        response['message'] = message
    return response
def _format_error_response(self, error, status_code=400):
    """Format error response"""
    return {
        'success': False,
        'error': str(error),
        'status_code': status_code
    }
@http.route('/config_matrix/api/template/<int:template_id>', 
            type='json', auth='user', methods=['GET'])
def get_template(self, template_id, **kw):
    """Get template with consistent response format"""
    try:
        template = request.env['config.matrix.template'].browse(template_id)
        if not template.exists():
            return self._format_error_response('Template not found', 404)
        template_data = template.read(['name', 'code', 'state', 'description'])[0]
        return self._format_success_response(template_data)
    except Exception as e:
        return self._format_error_response(str(e), 500)
```
### 9. Logging and Monitoring Standards
#### Proper Logging
```python
# ✅ CORRECT - Proper logging implementation
import logging
_logger = logging.getLogger(__name__)
@http.route('/config_matrix/api/template/<int:template_id>/action', 
            type='json', auth='user', methods=['POST'])
def perform_template_action(self, template_id, action_type, **kw):
    """Perform action on template with logging"""
    try:
        _logger.info(f"Performing action {action_type} on template {template_id}")
        template = request.env['config.matrix.template'].browse(template_id)
        if not template.exists():
            _logger.warning(f"Template {template_id} not found")
            return {'error': 'Template not found'}, 404
        # Perform action based on type
        if action_type == 'validate':
            result = template.action_validate()
        elif action_type == 'archive':
            result = template.action_archive()
        else:
            _logger.error(f"Unknown action type: {action_type}")
            return {'error': 'Unknown action type'}, 400
        _logger.info(f"Action {action_type} completed successfully for template {template_id}")
        return {'success': True, 'result': result}
    except Exception as e:
        _logger.error(f"Error performing action {action_type} on template {template_id}: {str(e)}")
        return {'error': str(e)}, 500
```
### 10. Portal Template Integration
#### Portal Template Rendering
```python
# ✅ CORRECT - Portal template rendering with proper error handling
@http.route(['/my/config-matrices'], type='http', auth="user", website=True)
def portal_my_config_matrices(self, **kw):
    """List view - paginated configuration listing"""
    if not self._check_portal_access():
        return request.redirect('/my')
    try:
        ConfigMatrixTemplate = request.env['config.matrix.template']
        # Pagination
        page = int(kw.get('page', 1))
        limit = 20
        offset = (page - 1) * limit
        # Search domain
        domain = [('active', '=', True)]
        templates = ConfigMatrixTemplate.search(domain, limit=limit, offset=offset)
        total = ConfigMatrixTemplate.search_count(domain)
        pager = portal_pager(
            url="/my/config-matrices",
            total=total,
            page=page,
            step=limit
        )
        values = {
            'templates': templates,
            'pager': pager,
        }
        return request.render("config_matrix.portal_my_config_matrices", values)
    except Exception as e:
        _logger.error(f"Error in portal config matrices: {str(e)}")
        return request.render("portal.portal_my_home", {'error': str(e)})
```
## Examples
### Complete Controller Example
```python
from odoo import http
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
from odoo.exceptions import UserError, ValidationError
import logging
_logger = logging.getLogger(__name__)
class ConfigMatrixController(http.Controller):
    """Main controller for ConfigMatrix API endpoints"""
    def _format_success_response(self, data=None, message=None):
        """Format successful response"""
        response = {'success': True}
        if data:
            response['data'] = data
        if message:
            response['message'] = message
        return response
    def _format_error_response(self, error, status_code=400):
        """Format error response"""
        return {
            'success': False,
            'error': str(error),
            'status_code': status_code
        }
    @http.route('/config_matrix/api/templates', 
                type='json', auth='user', methods=['GET'])
    def get_templates(self, **kw):
        """Get list of templates with pagination"""
        try:
            limit = int(kw.get('limit', 20))
            offset = int(kw.get('offset', 0))
            templates = request.env['config.matrix.template'].search([
                ('active', '=', True)
            ], limit=limit, offset=offset)
            return self._format_success_response({
                'templates': templates.read(['name', 'code', 'state']),
                'total': templates.search_count([('active', '=', True)]),
                'limit': limit,
                'offset': offset
            })
        except Exception as e:
            _logger.error(f"Error getting templates: {str(e)}")
            return self._format_error_response(str(e), 500)
    @http.route('/config_matrix/api/template/<int:template_id>', 
                type='json', auth='user', methods=['GET'])
    def get_template(self, template_id, **kw):
        """Get template details"""
        try:
            template = request.env['config.matrix.template'].browse(template_id)
            if not template.exists():
                return self._format_error_response('Template not found', 404)
            template_data = template.read([
                'name', 'code', 'description', 'state', 'version'
            ])[0]
            return self._format_success_response(template_data)
        except Exception as e:
            _logger.error(f"Error getting template {template_id}: {str(e)}")
            return self._format_error_response(str(e), 500)
class ConfigMatrixCustomerPortal(CustomerPortal):
    """Portal controller for customer-facing ConfigMatrix features"""
    def _check_portal_access(self):
        """Check if current user has portal access"""
        try:
            if not request.env.user.has_group('base.group_portal'):
                return False
            # Use existing partner categorization
            local_freight_type = request.env.ref(
                'modula_contact_extended.res_partner_type_local_freight', 
                raise_if_not_found=False
            )
            if not local_freight_type or request.env.user.partner_id.customer_type_id != local_freight_type:
                return False
            return True
        except Exception:
            return False
    def _prepare_home_portal_values(self, counters):
        """Extend portal home with custom counters"""
        values = super()._prepare_home_portal_values(counters)
        if 'config_matrix_count' in counters:
            values['config_matrix_count'] = self._get_config_matrix_count()
        return values
    def _get_config_matrix_count(self):
        """Get configuration count for current user"""
        return request.env['config.matrix.template'].search_count([
            ('active', '=', True)
        ])
    @http.route(['/my/config-matrices'], type='http', auth="user", website=True)
    def portal_my_config_matrices(self, **kw):
        """List view - paginated configuration listing"""
        if not self._check_portal_access():
            return request.redirect('/my')
        try:
            ConfigMatrixTemplate = request.env['config.matrix.template']
            # Pagination
            page = int(kw.get('page', 1))
            limit = 20
            offset = (page - 1) * limit
            # Search domain
            domain = [('active', '=', True)]
            templates = ConfigMatrixTemplate.search(domain, limit=limit, offset=offset)
            total = ConfigMatrixTemplate.search_count(domain)
            pager = portal_pager(
                url="/my/config-matrices",
                total=total,
                page=page,
                step=limit
            )
            values = {
                'templates': templates,
                'pager': pager,
            }
            return request.render("config_matrix.portal_my_config_matrices", values)
        except Exception as e:
            _logger.error(f"Error in portal config matrices: {str(e)}")
            return request.render("portal.portal_my_home", {'error': str(e)})
