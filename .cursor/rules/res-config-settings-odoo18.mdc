---
description: 
globs: 
alwaysApply: true
---
Structure Changes:
Odoo 18 uses a more modern and cleaner structure with:
<app> tag for grouping settings
<block> tag for section headers
<setting> tag for individual settings
This replaces the old div-based structure in Odoo 16XPath Changes:
Odoo 18: Target //form directly
Odoo 16: Target //div[hasclass('settings')]
Field Presentation:
Odoo 18: Use <setting> tag which handles layout automatically
Odoo 16: Manual layout with divs and classes
Best Practices for Odoo 18:
Group related settings under a single <app> tag
Use descriptive block titles
Provide clear help text in the help attribute
Follow Odoo's naming conventions for fields
Keep settings organized and logically grouped
Example Structure:
Apply to res-config-s...
Key Benefits of Odoo 18 Structure:
Cleaner and more maintainable code
Automatic layout handling
Better organization of settings
More consistent UI
Easier to extend and modify
Migration Considerations:
When migrating from Odoo 16 to 18, you'll need to:
Update the XPath expressions
Convert div-based structure to app/block/setting structure
Move help text to the help attribute
Ensure proper grouping of related settings
This new structure in Odoo 18 makes it easier to maintain and extend settings while providing a more consistent user interface. The <setting> tag automatically handles the layout, reducing the amount of manual HTML structure needed.
