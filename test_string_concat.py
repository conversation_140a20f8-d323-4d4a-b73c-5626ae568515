#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for string concatenation in calculated field formulas
"""

class MockConfigMatrixCalculatedField:
    """Mock class to test the string concatenation issue"""
    
    def _convert_js_to_python(self, js_formula):
        """Convert JavaScript formula syntax to Python syntax"""
        if not js_formula:
            return js_formula

        python_formula = js_formula

        # Convert JavaScript operators to Python
        python_formula = python_formula.replace('===', '==')
        python_formula = python_formula.replace('!==', '!=')
        python_formula = python_formula.replace('&&', ' and ')
        python_formula = python_formula.replace('||', ' or ')

        # Convert JavaScript Math.min/max to Python min/max
        python_formula = python_formula.replace('Math.min(', 'min(')
        python_formula = python_formula.replace('Math.max(', 'max(')
        python_formula = python_formula.replace('Math.', '')

        # Convert JavaScript logical OR with default value (|| 0) to Python equivalent
        python_formula = python_formula.replace(' or 0', ' or 0')

        # Convert ternary operator
        python_formula = self._convert_ternary_operators(python_formula)

        return python_formula

    def _convert_ternary_operators(self, formula):
        """Convert JavaScript ternary operators to Python if-else expressions"""
        
        def convert_nested_ternary_manual(expr):
            """Convert nested ternary operators manually for the specific pattern"""
            expr = expr.strip()
            
            # Find the first question mark
            first_question = expr.find('?')
            if first_question == -1:
                return expr
            
            # Find the colon after the first question mark
            first_colon = expr.find(':', first_question)
            if first_colon == -1:
                return expr
            
            # Check if there's a nested ternary in the false part
            false_part_start = first_colon + 1
            false_part = expr[false_part_start:].strip()
            
            # Look for nested ternary in the false part
            if false_part.startswith('(') and '?' in false_part and ':' in false_part:
                # Extract the parts
                condition1 = expr[:first_question].strip()
                true1 = expr[first_question + 1:first_colon].strip()
                
                # Remove the outer parentheses from the false part
                nested_expr = false_part[1:-1] if false_part.startswith('(') and false_part.endswith(')') else false_part
                
                # Find the nested ternary
                nested_question = nested_expr.find('?')
                nested_colon = nested_expr.find(':', nested_question)
                
                if nested_question != -1 and nested_colon != -1:
                    condition2 = nested_expr[:nested_question].strip()
                    true2 = nested_expr[nested_question + 1:nested_colon].strip()
                    false2 = nested_expr[nested_colon + 1:].strip()
                    
                    # Convert to Python if-else
                    python_expr = f"({true1} if ({condition1}) else ({true2} if ({condition2}) else {false2}))"
                    return python_expr
            
            # If no nested pattern found, try simple ternary
            condition = expr[:first_question].strip()
            true_part = expr[first_question + 1:first_colon].strip()
            false_part = expr[first_colon + 1:].strip()
            
            # Convert to Python if-else
            python_expr = f"({true_part} if ({condition}) else {false_part})"
            return python_expr
        
        # Only process if there are ternary operators
        if '?' in formula and ':' in formula:
            try:
                return convert_nested_ternary_manual(formula)
            except Exception as e:
                print(f"⚠️ Ternary conversion failed: {e}, using fallback")
                # Fallback to simple replacement
                formula = formula.replace(' ? ', ' if ')
                formula = formula.replace(' : ', ' else ')
                return formula
        
        return formula


def test_string_concatenation():
    """Test the string concatenation issue"""
    
    # Create mock instance
    calc_field = MockConfigMatrixCalculatedField()
    
    # Test case: String concatenation formula
    js_formula = "'min(' + _CALCULATED_manual_left_height + ', ' + _CALCULATED_manual_right_height + ') = ' + _CALCULATED_smallest_door_height"
    
    print("🔍 Testing string concatenation formula...")
    print(f"Original JS formula: {js_formula}")
    print()
    
    # Convert to Python
    python_formula = calc_field._convert_js_to_python(js_formula)
    print(f"Converted Python formula: {python_formula}")
    print()
    
    # Test syntax validity
    try:
        compiled = compile(python_formula, '<string>', 'eval')
        print("✅ Syntax check passed - formula compiles successfully")
    except SyntaxError as e:
        print(f"❌ Syntax error: {e}")
        return False
    
    # Test with sample values
    test_context = {
        '_CALCULATED_manual_left_height': 100,
        '_CALCULATED_manual_right_height': 150,
        '_CALCULATED_smallest_door_height': 100,
    }
    
    try:
        result = eval(compiled, test_context)
        print(f"✅ Runtime test passed - result: {result}")
        print(f"   Result type: {type(result)}")
        return True
        
    except TypeError as e:
        print(f"❌ TypeError: {e}")
        print("\n🔧 This is the expected error - string concatenation with numbers")
        print("   The issue is that we're trying to concatenate strings with numbers")
        print("   We need to convert numbers to strings explicitly")
        return False
    except Exception as e:
        print(f"❌ Runtime error: {e}")
        return False


def test_fixed_string_concatenation():
    """Test with fixed string concatenation using str() conversion"""
    
    print("\n" + "="*60)
    print("🔧 Testing FIXED string concatenation...")
    print("="*60)
    
    # Fixed formula with explicit string conversion
    fixed_formula = "str('min(') + str(_CALCULATED_manual_left_height) + str(', ') + str(_CALCULATED_manual_right_height) + str(') = ') + str(_CALCULATED_smallest_door_height)"
    
    print(f"Fixed formula: {fixed_formula}")
    print()
    
    # Test syntax validity
    try:
        compiled = compile(fixed_formula, '<string>', 'eval')
        print("✅ Syntax check passed - formula compiles successfully")
    except SyntaxError as e:
        print(f"❌ Syntax error: {e}")
        return False
    
    # Test with sample values
    test_context = {
        '_CALCULATED_manual_left_height': 100,
        '_CALCULATED_manual_right_height': 150,
        '_CALCULATED_smallest_door_height': 100,
    }
    
    try:
        result = eval(compiled, test_context)
        print(f"✅ Runtime test passed - result: {result}")
        print(f"   Result type: {type(result)}")
        return True
        
    except Exception as e:
        print(f"❌ Runtime error: {e}")
        return False


def test_alternative_approaches():
    """Test alternative approaches for string concatenation"""
    
    print("\n" + "="*60)
    print("🧪 Testing alternative approaches...")
    print("="*60)
    
    test_context = {
        '_CALCULATED_manual_left_height': 100,
        '_CALCULATED_manual_right_height': 150,
        '_CALCULATED_smallest_door_height': 100,
    }
    
    # Approach 1: Using str() function
    approach1 = "str('min(') + str(_CALCULATED_manual_left_height) + str(', ') + str(_CALCULATED_manual_right_height) + str(') = ') + str(_CALCULATED_smallest_door_height)"
    
    # Approach 2: Using f-string style (but with format)
    approach2 = "'min({}, {}) = {}'.format(_CALCULATED_manual_left_height, _CALCULATED_manual_right_height, _CALCULATED_smallest_door_height)"
    
    # Approach 3: Using % formatting
    approach3 = "'min(%s, %s) = %s' % (_CALCULATED_manual_left_height, _CALCULATED_manual_right_height, _CALCULATED_smallest_door_height)"
    
    approaches = [
        ("str() conversion", approach1),
        ("format() method", approach2),
        ("% formatting", approach3)
    ]
    
    for name, formula in approaches:
        print(f"\nTesting {name}:")
        print(f"Formula: {formula}")
        
        try:
            compiled = compile(formula, '<string>', 'eval')
            result = eval(compiled, test_context)
            print(f"✅ Result: {result}")
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    print("Testing string concatenation in calculated field formulas")
    print("="*60)
    
    # Test the original failing case
    test_string_concatenation()
    
    # Test the fixed version
    test_fixed_string_concatenation()
    
    # Test alternative approaches
    test_alternative_approaches() 