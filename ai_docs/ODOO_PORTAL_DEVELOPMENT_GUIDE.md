# Odoo Portal Development Guide

## Overview

This guide documents the experience and best practices for developing portal functionality in Odoo 18, based on the implementation of the `modula_delivery_courier` module's vendor delivery portal.

## Table of Contents

1. [Portal Architecture](#portal-architecture)
2. [Portal User Management](#portal-user-management)
3. [Portal Controller Development](#portal-controller-development)
4. [Portal Template Development](#portal-template-development)
5. [Security and Access Control](#security-and-access-control)
6. [Common Issues and Solutions](#common-issues-and-solutions)
7. [Best Practices](#best-practices)
8. [Testing Portal Functionality](#testing-portal-functionality)
9. [Portal Security Troubleshooting](#portal-security-troubleshooting)

## Portal Architecture

### Core Components

1. **Portal Controller** (`controllers/portal.py`)
   - Extends `CustomerPortal` class
   - Handles HTTP routes and request processing
   - Manages portal user access and permissions

2. **Portal Templates** (`views/portal_templates.xml`)
   - QWeb templates for portal pages
   - Inherits from `portal.portal_layout`
   - Uses portal-specific components and styling

3. **Model Extensions** (`models/`)
   - Extends existing models for portal functionality
   - Adds computed fields for portal access
   - Implements portal-specific business logic

4. **Security Configuration** (`security/`)
   - Access rights for portal users
   - Record rules for data filtering
   - Portal-specific security groups

### Portal User Types

- **Internal Users**: Full access to backend
- **Portal Users**: Limited access to specific portal pages
- **Public Users**: No access (redirected to login)

## Portal User Management

### User Identification Strategy

**Recommended Approach**: Use existing partner categorization instead of custom boolean fields.

```python
# Good: Use existing customer_type_id field
@api.depends('customer_type_id')
def _compute_is_local_freight_vendor(self):
    local_freight_type = self.env.ref('modula_contact_extended.res_partner_type_local_freight', raise_if_not_found=False)
    for partner in self:
        partner.is_local_freight_vendor = partner.customer_type_id == local_freight_type

# Avoid: Custom boolean fields
is_local_freight_vendor = fields.Boolean(string="Is Local Freight Vendor")
```

### Portal User Creation

**Use Odoo's Native Portal User Management**:

```python
# Let Odoo handle portal user creation automatically
# No need for custom creation methods

# In partner form view, just show the user_id field
<field name="user_id" readonly="not is_local_freight_vendor"/>
```

### Portal Access Verification

```python
def _check_portal_access(self):
    """Check if current user has portal access"""
    local_freight_type = request.env.ref('modula_contact_extended.res_partner_type_local_freight', raise_if_not_found=False)
    if not local_freight_type or request.env.user.partner_id.customer_type_id != local_freight_type:
        return False
    return True
```

## Portal Controller Development

### Controller Structure

```python
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager

class DeliveryCustomerPortal(CustomerPortal):
    
    def _prepare_home_portal_values(self, counters):
        """Add portal counters to home page"""
        values = super()._prepare_home_portal_values(counters)
        if 'vendor_delivery_count' in counters:
            values['vendor_delivery_count'] = self._get_vendor_delivery_count()
        return values
    
    def _get_vendor_delivery_count(self):
        """Get count for portal home page"""
        # Implementation here
        pass
```

### Route Definition

```python
@http.route(['/my/vendor-deliveries'], type='http', auth="user", website=True)
def portal_my_vendor_deliveries(self, page=1, sortby=None, filterby=None, search=None, search_in='all', **kw):
    """Portal page route"""
    # Access control
    if not self._check_portal_access():
        return request.redirect('/my')
    
    # Prepare values and render template
    values = self._prepare_vendor_deliveries_values(page, sortby, filterby, search, search_in, **kw)
    return request.render("modula_delivery_courier.portal_my_vendor_deliveries", values)
```

### Pagination Implementation

**Correct Pagination Pattern**:

```python
def _prepare_vendor_deliveries_values(self, page, sortby, filterby, search, search_in, **kw):
    # Get searchbar configurations first
    searchbar_sortings = self._vendor_delivery_get_searchbar_sortings()
    
    # Set default sortby if not provided
    if not sortby:
        sortby = 'date'  # Default to date sorting
    
    # Build domain
    domain = [('freight_carrier_id', '=', request.env.user.partner_id.id)]
    
    # Apply filters
    if search and search_in:
        domain = AND([domain, self._get_search_domain(search_in, search)])
    if filterby:
        domain = AND([domain, self._get_filter_domain(filterby)])
    
    # Get total count for pagination
    total = request.env['stock.picking'].search_count(domain)
    
    # Create pager
    pager = portal_pager(
        url="/my/vendor-deliveries",
        url_args={'sortby': sortby, 'search_in': search_in, 'search': search, 'filterby': filterby},
        total=total,
        page=page,
        step=self._items_per_page
    )
    
    # Get paginated records with sorting
    order = searchbar_sortings[sortby]['order']
    deliveries = request.env['stock.picking'].search(
        domain,
        order=order,
        limit=self._items_per_page,
        offset=pager['offset']
    )
    
    return {
        'deliveries': deliveries,
        'pager': pager,
        'searchbar_sortings': searchbar_sortings,
        'sortby': sortby,
        # ... other values
    }
```

**Avoid This Pattern**:
```python
# WRONG: Loading all records then slicing
deliveries = request.env['stock.picking'].search(domain)
total = len(deliveries)
deliveries = deliveries[(page - 1) * self._items_per_page:page * self._items_per_page]
```

### Search and Filter Implementation

```python
def _get_searchbar_sortings(self):
    """Define sorting options"""
    return {
        'name': {'label': request.env._('Name'), 'order': 'name'},
        'date': {'label': request.env._('Date'), 'order': 'scheduled_date desc'},
        'state': {'label': request.env._('Status'), 'order': 'state'},
    }

def _get_searchbar_filters(self):
    """Define filter options"""
    return {
        'all': {'label': request.env._('All'), 'domain': []},
        'draft': {'label': request.env._('Draft'), 'domain': [('state', '=', 'draft')]},
        'done': {'label': request.env._('Done'), 'domain': [('state', '=', 'done')]},
    }

def _get_searchbar_inputs(self):
    """Define search input options"""
    return {
        'all': {'input': 'all', 'label': request.env._('Search in All')},
        'name': {'input': 'name', 'label': request.env._('Search in Name')},
    }
```

## Portal Template Development

### Template Structure

```xml
<template id="portal_my_vendor_deliveries" name="My Vendor Deliveries">
    <t t-call="portal.portal_layout">
        <t t-set="breadcrumbs_searchbar" t-value="True"/>
        
        <t t-call="portal.portal_searchbar">
            <t t-set="title">Vendor Deliveries</t>
        </t>
        
        <!-- Content here -->
        
        <div t-if="pager" class="text-center">
            <t t-call="portal.pager"/>
        </div>
    </t>
</template>
```

### CSRF Token Implementation

**Critical**: All POST forms in portal templates must include CSRF tokens to prevent CSRF attacks.

```xml
<!-- CORRECT: Include CSRF token in all POST forms -->
<form t-attf-action="/my/vendor-deliveries/#{delivery['id']}/update" method="post">
    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
    <!-- form fields here -->
    <button type="submit" class="btn btn-primary">Submit</button>
</form>

<!-- WRONG: Missing CSRF token -->
<form t-attf-action="/my/vendor-deliveries/#{delivery['id']}/update" method="post">
    <!-- form fields here -->
    <button type="submit" class="btn btn-primary">Submit</button>
</form>
```

**Error Message**: If CSRF token is missing, you'll see:
```
No CSRF validation token provided for path '/my/vendor-deliveries/242/update'
```

### Field Rendering

**Correct Field Output**:
```xml
<!-- Use t-out for simple field output -->
<span t-out="delivery.name"/>
<span t-out="delivery.partner_id.name"/>

<!-- Use t-attf-class for conditional styling -->
<span t-attf-class="badge #{'badge-success' if delivery.state == 'done' else 'badge-warning'}">
    <t t-out="delivery.state"/>
</span>
```

**Avoid These Patterns**:
```xml
<!-- WRONG: t-field on t element -->
<t t-field="delivery.name"/>

<!-- WRONG: t-raw with t-field -->
<t t-raw="0" t-field="pager"/>

<!-- WRONG: Complex t-options in portal templates -->
<span t-field="delivery.state" 
      t-options="{'widget': 'badge', 'decoration-success': delivery.state == 'done'}"/>
```

### Table Structure

```xml
<t t-call="portal.portal_table">
    <thead>
        <tr>
            <th>Name</th>
            <th>Status</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        <t t-foreach="deliveries" t-as="delivery">
            <tr>
                <td>
                    <a t-attf-href="/my/vendor-deliveries/#{delivery.id}">
                        <span t-out="delivery.name"/>
                    </a>
                </td>
                <td>
                    <span t-out="delivery.state"/>
                </td>
                <td>
                    <a t-attf-href="/my/vendor-deliveries/#{delivery.id}" 
                       class="btn btn-sm btn-primary">View</a>
                </td>
            </tr>
        </t>
    </tbody>
</t>
```

### Portal Home Integration

**Complete Portal Home Button Implementation**:

```xml
<template id="portal_my_home_vendor_deliveries" inherit_id="modula_web.portal_my_home_extended" name="Portal My Home : vendor deliveries entries" priority="50">
    <xpath expr="//div[hasclass('o_portal_docs')]" position="before">
        <t t-set="portal_vendor_category_enable" t-value="True"/>
    </xpath>
    <div id="portal_vendor_category" position="inside">
        <t t-call="portal.portal_docs_entry">
            <t t-set="icon" t-value="'fa fa-truck'"/>
            <t t-set="title">Vendor Deliveries</t>
            <t t-set="url" t-value="'/my/vendor-deliveries'"/>
            <t t-set="text">Manage and track your delivery assignments</t>
            <t t-set="placeholder_count" t-value="'vendor_delivery_count'"/>
        </t>
    </div>
</template>
```

**Important**: If your project has a custom `modula_web` module that overrides the portal home template, you must inherit from the custom template instead of the standard one.

**Custom Icon Implementation**:
```xml
<!-- Use custom icon from your module -->
<t t-set="icon" t-value="'/modula_delivery_courier/static/src/img/delivery.png'"/>

<!-- Or use Font Awesome icon -->
<t t-set="icon" t-value="'fa fa-truck'"/>
```

**Controller Implementation**:
```python
def _prepare_home_portal_values(self, counters):
    values = super()._prepare_home_portal_values(counters)
    if 'vendor_delivery_count' in counters:
        values['vendor_delivery_count'] = self._get_vendor_delivery_count()
    return values

def _get_vendor_delivery_count(self):
    """Get delivery count for freight vendor"""
    local_freight_type = request.env.ref('modula_contact_extended.res_partner_type_local_freight', raise_if_not_found=False)
    if not local_freight_type or request.env.user.partner_id.customer_type_id != local_freight_type:
        return 0
    return request.env['stock.picking'].search_count([
        ('freight_carrier_id', '=', request.env.user.partner_id.id),
        ('picking_type_code', '=', 'outgoing')
    ])
```

**Key Elements**:
- **Icon**: Font Awesome truck icon (`fa fa-truck`)
- **Title**: "Vendor Deliveries"
- **URL**: `/my/vendor-deliveries`
- **Description**: "Manage and track your delivery assignments"
- **Count**: Shows number of deliveries assigned to the vendor
- **Conditional Display**: Only shows if `vendor_delivery_count > 0`

## Security and Access Control

### Access Rights

```xml
<!-- security/ir.model.access.csv -->
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_stock_picking_portal,stock.picking.portal,stock.model_stock_picking,base.group_portal,1,1,0,0
access_res_partner_portal,res.partner.portal,base.model_res_partner,base.group_portal,1,1,0,0
```

### Record Rules

```xml
<!-- security/ir_rule.xml -->
<record id="rule_stock_picking_portal" model="ir.rule">
    <field name="name">Portal: Only own deliveries</field>
    <field name="model_id" ref="stock.model_stock_picking"/>
    <field name="domain_force">[('freight_carrier_id', '=', user.partner_id.id)]</field>
    <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
</record>

<record id="rule_res_partner_portal" model="ir.rule">
    <field name="name">Portal: Only own partner record</field>
    <field name="model_id" ref="base.model_res_partner"/>
    <field name="domain_force">[('id', '=', user.partner_id.id)]</field>
    <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
</record>
```

### Controller Access Control

```python
def _check_portal_access(self):
    """Verify portal access rights"""
    # Check user type
    local_freight_type = request.env.ref('modula_contact_extended.res_partner_type_local_freight', raise_if_not_found=False)
    if not local_freight_type or request.env.user.partner_id.customer_type_id != local_freight_type:
        return False
    
    # Check if user has portal access
    if not request.env.user.has_group('base.group_portal'):
        return False
    
    return True

@http.route(['/my/vendor-deliveries'], type='http', auth="user", website=True)
def portal_my_vendor_deliveries(self, **kw):
    if not self._check_portal_access():
        return request.redirect('/my')
    # ... rest of implementation
```

## Common Issues and Solutions

### Portal Console Errors

#### 1. **Bootstrap Modal Console Errors**

**Error**: `bootstrap is not defined` or `$ is not defined`

**Root Cause**: Portal templates don't have full Bootstrap JS or jQuery loaded by default.

**Solutions**:

**Option A: Use jQuery Modal Methods (Recommended)**
```javascript
// Instead of Bootstrap modal API
$('#signatureModal').modal('show');
$('#signatureModal').modal('hide');

// Use jQuery methods
$('#signatureModal').show();
$('#signatureModal').hide();
```

**Option B: Check for Bootstrap Availability**
```javascript
// Check if Bootstrap is available before using
if (typeof bootstrap !== 'undefined') {
    const modal = new bootstrap.Modal(document.getElementById('signatureModal'));
    modal.show();
} else {
    // Fallback to jQuery
    $('#signatureModal').show();
}
```

**Option C: Load Bootstrap Assets**
```xml
<!-- In manifest.py -->
'portal.assets_frontend': [
    'web/static/lib/bootstrap/dist/js/bootstrap.bundle.min.js',
    'modula_delivery_courier/static/src/js/portal_signature.js',
],
```

#### 2. **OWL Lifecycle Console Errors**

**Error**: `owl.lifecycle is not defined` or `owl.App is not defined`

**Root Cause**: Portal context doesn't have full OWL environment with all services initialized.

**Solutions**:

**Option A: Use Direct HTML5 Canvas (Recommended)**
```javascript
// Instead of OWL components, use direct HTML5 canvas
class SignatureCanvas {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.isDrawing = false;
        this.init();
    }
    
    init() {
        this.canvas.addEventListener('mousedown', this.startDrawing.bind(this));
        this.canvas.addEventListener('mousemove', this.draw.bind(this));
        this.canvas.addEventListener('mouseup', this.stopDrawing.bind(this));
        this.canvas.addEventListener('mouseout', this.stopDrawing.bind(this));
        
        // Touch support
        this.canvas.addEventListener('touchstart', this.handleTouch.bind(this));
        this.canvas.addEventListener('touchmove', this.handleTouch.bind(this));
        this.canvas.addEventListener('touchend', this.stopDrawing.bind(this));
    }
    
    // ... implementation
}
```

**Option B: Check OWL Environment**
```javascript
// Check if OWL is available before using
if (typeof owl !== 'undefined' && owl.App) {
    // Use OWL components
    const app = new owl.App();
    app.mount(document.getElementById('owl-container'));
} else {
    // Fallback to direct implementation
    this.initDirectCanvas();
}
```

#### 3. **CSRF Token Console Errors**

**Error**: `No CSRF validation token provided for path '/my/vendor-deliveries/242/update'`

**Root Cause**: POST forms missing CSRF tokens.

**Solution**: Add CSRF token to all POST forms:

```xml
<!-- Add to all POST forms -->
<form t-attf-action="/my/vendor-deliveries/#{delivery['id']}/update" method="post">
    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
    <!-- form fields -->
</form>
```

**JavaScript Fetch with CSRF**:
```javascript
// Include CSRF token in fetch requests
const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

const response = await fetch('/my/vendor-deliveries/save-signature', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-CSRFToken': csrfToken,
    },
    body: formData
});
```

#### 4. **Aria Attributes Console Warnings**

**Error**: `aria-hidden` attribute warnings in Bootstrap modals

**Root Cause**: Bootstrap modal ARIA attributes not properly configured.

**Solution**: Update modal attributes:

```xml
<!-- Update modal attributes -->
<div class="modal fade" id="signatureModal" tabindex="-1" 
     aria-labelledby="signatureModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="signatureModalLabel">Capture Signature</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" 
                        aria-label="Close"></button>
            </div>
            <!-- modal body -->
        </div>
    </div>
</div>
```

#### 5. **JavaScript Import Errors**

**Error**: `import { attachComponent } from '@web/core/utils/components'` fails

**Root Cause**: Portal context doesn't have full OWL/Web framework imports.

**Solution**: Use direct DOM manipulation instead of OWL utilities:

```javascript
// Instead of OWL attachComponent
// import { attachComponent } from '@web/core/utils/components';

// Use direct DOM manipulation
function showSignatureDialog() {
    const modal = document.getElementById('signatureModal');
    if (modal) {
        $(modal).show();
        // Initialize canvas
        initSignatureCanvas();
    }
}

function hideSignatureDialog() {
    const modal = document.getElementById('signatureModal');
    if (modal) {
        $(modal).hide();
        // Cleanup
        cleanupSignatureCanvas();
    }
}
```

#### 6. **Component Destroy Errors**

**Error**: `signatureComponent.destroy is not a function`

**Root Cause**: Trying to call destroy on non-OWL components.

**Solution**: Implement proper cleanup for direct components:

```javascript
class SignatureManager {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.isDrawing = false;
    }
    
    init(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.setupEventListeners();
    }
    
    destroy() {
        // Remove event listeners
        if (this.canvas) {
            this.canvas.removeEventListener('mousedown', this.startDrawing);
            this.canvas.removeEventListener('mousemove', this.draw);
            this.canvas.removeEventListener('mouseup', this.stopDrawing);
            // ... remove other listeners
        }
        
        // Clear canvas
        if (this.ctx) {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        }
        
        // Reset state
        this.canvas = null;
        this.ctx = null;
        this.isDrawing = false;
    }
}
```

#### 7. **Service Not Found Errors**

**Error**: `Service 'ui' not found` or `Service 'notification' not found`

**Root Cause**: Portal context doesn't have full OWL services available.

**Solution**: Use alternative approaches for UI feedback:

```javascript
// Instead of OWL services
// this.env.services.ui.notification.add(...)

// Use direct DOM manipulation for notifications
function showNotification(message, type = 'info') {
    const notificationDiv = document.createElement('div');
    notificationDiv.className = `alert alert-${type} alert-dismissible fade show`;
    notificationDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert at top of page
    document.body.insertBefore(notificationDiv, document.body.firstChild);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notificationDiv.parentNode) {
            notificationDiv.parentNode.removeChild(notificationDiv);
        }
    }, 5000);
}
```

#### 8. **Signature Implementation Console Errors**

**Error**: `owl.lifecycle is not defined` when using OWL NameAndSignature component

**Root Cause**: Portal context doesn't have full OWL environment with lifecycle services.

**Solution**: Replace OWL components with direct HTML5 canvas implementation:

```javascript
// Complete HTML5 Canvas Signature Implementation
class PortalSignatureCanvas {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.isDrawing = false;
        this.lastX = 0;
        this.lastY = 0;
        this.init();
    }
    
    init() {
        // Set canvas size
        this.canvas.width = this.canvas.offsetWidth;
        this.canvas.height = this.canvas.offsetHeight;
        
        // Set drawing style
        this.ctx.strokeStyle = '#000';
        this.ctx.lineWidth = 2;
        this.ctx.lineCap = 'round';
        
        // Mouse events
        this.canvas.addEventListener('mousedown', this.startDrawing.bind(this));
        this.canvas.addEventListener('mousemove', this.draw.bind(this));
        this.canvas.addEventListener('mouseup', this.stopDrawing.bind(this));
        this.canvas.addEventListener('mouseout', this.stopDrawing.bind(this));
        
        // Touch events for mobile
        this.canvas.addEventListener('touchstart', this.handleTouch.bind(this));
        this.canvas.addEventListener('touchmove', this.handleTouch.bind(this));
        this.canvas.addEventListener('touchend', this.stopDrawing.bind(this));
    }
    
    startDrawing(e) {
        this.isDrawing = true;
        const rect = this.canvas.getBoundingClientRect();
        this.lastX = e.clientX - rect.left;
        this.lastY = e.clientY - rect.top;
    }
    
    draw(e) {
        if (!this.isDrawing) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        this.ctx.beginPath();
        this.ctx.moveTo(this.lastX, this.lastY);
        this.ctx.lineTo(x, y);
        this.ctx.stroke();
        
        this.lastX = x;
        this.lastY = y;
    }
    
    handleTouch(e) {
        e.preventDefault();
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' : 
                                         e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });
        this.canvas.dispatchEvent(mouseEvent);
    }
    
    stopDrawing() {
        this.isDrawing = false;
    }
    
    clear() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    getSignatureData() {
        return this.canvas.toDataURL('image/png');
    }
    
    destroy() {
        // Remove event listeners
        this.canvas.removeEventListener('mousedown', this.startDrawing);
        this.canvas.removeEventListener('mousemove', this.draw);
        this.canvas.removeEventListener('mouseup', this.stopDrawing);
        this.canvas.removeEventListener('mouseout', this.stopDrawing);
        this.canvas.removeEventListener('touchstart', this.handleTouch);
        this.canvas.removeEventListener('touchmove', this.handleTouch);
        this.canvas.removeEventListener('touchend', this.stopDrawing);
    }
}
```

**Template Implementation**:
```xml
<!-- Replace OWL component with direct canvas -->
<div class="modal-body">
    <div class="form-group">
        <label for="customer_name">Customer Name:</label>
        <input type="text" class="form-control" id="customer_name" name="customer_name" 
               placeholder="Enter customer name">
    </div>
    
    <div class="form-group">
        <label for="signature_canvas">Signature:</label>
        <canvas id="signature_canvas" class="border" style="width: 100%; height: 200px; cursor: crosshair;"></canvas>
        <div class="mt-2">
            <button type="button" class="btn btn-secondary btn-sm" onclick="clearSignature()">Clear</button>
        </div>
    </div>
    
    <div class="form-group">
        <label for="file_upload">Upload Photos:</label>
        <input type="file" class="form-control" id="file_upload" name="file_upload" 
               multiple accept="image/*">
    </div>
</div>
```

#### 9. **KeyError: 'name' in Portal Templates**

**Error**: `KeyError: 'name'` when `delivery.partner_id` is null

**Root Cause**: Template trying to access fields on null objects.

**Solution**: Add null-safe checks in controller and templates:

**Controller Implementation**:
```python
def _prepare_vendor_deliveries_values(self, page, sortby, filterby, search, search_in, **kw):
    # ... existing code ...
    
    # Prepare delivery data with partner information using sudo
    delivery_data = []
    for delivery in deliveries:
        # Read partner data with sudo to avoid access rights issues
        partner_data = {
            'id': None,
            'name': '',
            'street': '',
            'street2': '',
            'city': '',
            'state_name': '',
            'zip': '',
            'country_name': '',
        }
        if delivery.partner_id:
            partner = delivery.partner_id.sudo()
            partner_data = {
                'id': partner.id,
                'name': partner.name,
                'street': partner.street or '',
                'street2': partner.street2 or '',
                'city': partner.city or '',
                'state_name': partner.state_id.name if partner.state_id else '',
                'zip': partner.zip or '',
                'country_name': partner.country_id.name if partner.country_id else '',
            }
        
        delivery_data.append({
            'id': delivery.id,
            'name': delivery.name,
            'partner_data': partner_data,
            'scheduled_date': delivery.scheduled_date,
            'state': delivery.state,
            'state_label': dict(delivery._fields['state'].selection).get(delivery.state, delivery.state),
            'origin': delivery.origin or '',
        })
    
    return delivery_data
```

**Template Implementation**:
```xml
<!-- Safe template access -->
<td>
    <span t-out="delivery['partner_data']['name'] or ''"/>
</td>
<td>
    <span t-out="delivery['partner_data']['street'] or ''"/>
</td>
```

### Template Errors

**Error**: `t-field can not be used on a t element, provide an actual HTML node`

**Solution**: Use `t-out` instead of `t-field` for simple field output in portal templates.

```xml
<!-- WRONG -->
<t t-field="delivery.name"/>

<!-- CORRECT -->
<span t-out="delivery.name"/>
```

### Pagination Issues

**Error**: Slow pagination or memory issues

**Solution**: Use proper database-level pagination with `search_count()` and `offset`.

```python
# CORRECT
total = request.env['stock.picking'].search_count(domain)
deliveries = request.env['stock.picking'].search(
    domain,
    limit=self._items_per_page,
    offset=pager['offset']
)

# WRONG
deliveries = request.env['stock.picking'].search(domain)
deliveries = deliveries[(page - 1) * self._items_per_page:page * self._items_per_page]
```

### Portal User Creation Issues

**Issue**: Complex portal user creation logic

**Solution**: Use Odoo's native portal user management.

```python
# Let Odoo handle portal user creation
# Remove custom creation methods
# Use existing user_id field on res.partner
```

### CSRF Token Issues

**Error**: `No CSRF validation token provided for path '/my/vendor-deliveries/242/update'`

**Root Cause**: POST forms in portal templates are missing CSRF tokens.

**Solution**: Add CSRF token to all POST forms:

```xml
<!-- Add this line to all POST forms -->
<input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
```

**Complete Example**:
```xml
<form t-attf-action="/my/vendor-deliveries/#{delivery['id']}/update" method="post">
    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
    <div class="form-group">
        <label for="scheduled_date">Update Scheduled Date:</label>
        <input type="datetime-local" 
               class="form-control" 
               name="scheduled_date" 
               t-att-value="delivery['scheduled_date']"/>
    </div>
    <button type="submit" class="btn btn-primary">Update Delivery</button>
</form>
```

**Security Note**: CSRF tokens are automatically validated by Odoo for all POST requests. Never disable CSRF protection for portal forms.

### Template Inheritance Conflicts

**Issue**: Portal home button not appearing despite correct template inheritance

**Root Cause**: Custom modules (like `modula_web`) that completely replace the portal home template

**Solution**: Inherit from the custom template instead of the standard one:

```xml
<!-- WRONG - if modula_web exists -->
<template id="portal_my_home_vendor_deliveries" inherit_id="portal.portal_my_home">

<!-- CORRECT - inherit from custom template -->
<template id="portal_my_home_vendor_deliveries" inherit_id="modula_web.portal_my_home_extended">
```

**Check for Conflicts**:
1. Look for modules that override `portal.portal_my_home`
2. Use the custom template ID in your inheritance
3. Add the custom module as a dependency in your manifest

### Datetime Format Issues

**Error**: `ValueError("time data '2025-06-05T04:20:39' does not match format '%Y-%m-%d %H:%M:%S'")`

**Root Cause**: HTML datetime-local inputs send ISO format but Odoo expects different format.

**Solution**: Convert datetime formats in controller:

```python
# Convert form data to Odoo format
if 'scheduled_date' in post and post['scheduled_date']:
    from datetime import datetime
    try:
        iso_datetime = datetime.fromisoformat(post['scheduled_date'].replace('Z', '+00:00'))
        update_values['scheduled_date'] = iso_datetime.strftime('%Y-%m-%d %H:%M:%S')
    except ValueError:
        update_values['scheduled_date'] = post['scheduled_date']

# Format for template display
'scheduled_date': delivery.scheduled_date.strftime('%Y-%m-%dT%H:%M') if delivery.scheduled_date else ''
```

### Datetime Format Issues

**Error**: `ValueError("time data '2025-06-05T04:20:39' does not match format '%Y-%m-%d %H:%M:%S'")`

**Root Cause**: HTML datetime-local inputs send ISO format (`2025-06-05T04:20:39`) but Odoo expects different format.

**Solution**: Convert datetime formats in controller:

```python
# In controller - converting form data to Odoo format
if 'scheduled_date' in post and post['scheduled_date']:
    from datetime import datetime
    try:
        # Parse the ISO datetime string from the form
        iso_datetime = datetime.fromisoformat(post['scheduled_date'].replace('Z', '+00:00'))
        # Convert to Odoo datetime format
        update_values['scheduled_date'] = iso_datetime.strftime('%Y-%m-%d %H:%M:%S')
    except ValueError as e:
        # If parsing fails, try to use the original value
        update_values['scheduled_date'] = post['scheduled_date']

# In controller - formatting for template display
delivery_data = {
    'scheduled_date': delivery.scheduled_date.strftime('%Y-%m-%dT%H:%M') if delivery.scheduled_date else '',
    # ... other fields
}
```

**Template Implementation**:
```xml
<input type="datetime-local" 
       class="form-control" 
       name="scheduled_date" 
       t-att-value="delivery['scheduled_date']"/>
```

**Format Conversion Table**:
- **HTML datetime-local input**: `YYYY-MM-DDTHH:MM` (e.g., `2025-06-05T04:20`)
- **Odoo database format**: `YYYY-MM-DD HH:MM:SS` (e.g., `2025-06-05 04:20:00`)
- **ISO format**: `YYYY-MM-DDTHH:MM:SS` (e.g., `2025-06-05T04:20:39`)

### Access Control Issues

**Issue**: Portal users accessing unauthorized data

**Solution**: Implement proper record rules and controller-level checks.

```python
# Controller-level check
if delivery.freight_carrier_id != request.env.user.partner_id:
    return request.redirect('/my/vendor-deliveries')

# Record rule
[('freight_carrier_id', '=', user.partner_id.id)]
```

## Portal Security Troubleshooting

### Common Security Errors

#### 1. **AccessError: Contact (res.partner) doesn't have 'read' access**

**Error Message**:
```
AccessError: Uh-oh! Looks like you have stumbled upon some top-secret records.
Sorry, Freight test (id=26) doesn't have 'read' access to:
- Contact (res.partner)
```

**Root Cause**: Portal users need read and write access to their own partner record for portal functionality.

**Solution**:

1. **Update Access Rights** (`ir.model.access.csv`):
```csv
access_res_partner_vendor_portal,Res Partner Vendor Portal Access,base.model_res_partner,base.group_portal,1,1,0,0
```

2. **Update Record Rules** (`vendor_portal_rules.xml`):
```xml
<record id="vendor_partner_rule_portal" model="ir.rule">
    <field name="name">Vendor Partner: portal users can only see their own partner record</field>
    <field name="model_id" ref="base.model_res_partner"/>
    <field name="domain_force">[('id', '=', user.partner_id.id)]</field>
    <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
    <field name="perm_read" eval="True"/>
    <field name="perm_write" eval="True"/>
    <field name="perm_create" eval="False"/>
    <field name="perm_unlink" eval="False"/>
</record>
```

3. **Remove Duplicate Entries**: Ensure no duplicate access rights for the same model and group.

#### 2. **AccessError: Portal users cannot read other partners' data**

**Error Message**:
```
AccessError: Uh-oh! Looks like you have stumbled upon some top-secret records.
Sorry, Portal User doesn't have 'read' access to:
- Contact (res.partner) #123 (Customer XYZ)
```

**Root Cause**: Portal users need to display customer/partner information but don't have access rights to read other partners' data.

**Solution**: Use `sudo()` in controller to read partner data and pass safe dictionaries to templates.

**Controller Implementation**:
```python
def _prepare_vendor_deliveries_values(self, page, sortby, filterby, search, search_in, **kw):
    # ... existing code ...
    
    # Prepare delivery data with partner information using sudo
    delivery_data = []
    for delivery in deliveries:
        # Read partner data with sudo to avoid access rights issues
        partner_data = {}
        if delivery.partner_id:
            related_data = {}
            related_data = {
                'id': related_record.id,
                'name': related_record.name,
                # Add other needed fields with null checks
                'field1': related_record.field1 or '',
                'field2': related_record.field2 or '',
            }
        
        # Build safe data structure
        processed_record = {
            'id': record.id,
            'name': record.name,
            'state': record.state,
            'related_data': related_data,
            # Add computed fields
            'display_name': f"{record.name} - {record.state}",
        }
        
        processed_data.append(processed_record)
    
    return processed_data
```

**Template Implementation**:
```xml
<!-- Before: Direct model access (causes access errors) -->
<span t-out="delivery.partner_id.name"/>
<div t-out="delivery.partner_id.street"/>

<!-- After: Dictionary access (safe) -->
<span t-out="delivery['partner_data']['name']"/>
<div t-out="delivery['partner_data']['street']"/>
```

**Benefits of sudo() Approach**:
- ✅ **No Access Rights Issues**: Bypasses access rights for reading data
- ✅ **Better Performance**: Data is pre-fetched and cached
- ✅ **Template Safety**: No risk of access errors in templates
- ✅ **Cleaner Code**: Templates work with safe data structures
- ✅ **Maintainability**: All data processing centralized in controller

**Security Considerations**:
- Use `sudo()` only for reading data, not writing
- Maintain access control at controller level
- Limit data to what's needed for display
- Portal users can still only see their own deliveries

#### 3. **KeyError: None in Portal Searchbar**

**Error Message**:
```
KeyError: None
Template: portal.portal_searchbar
Node: <t t-esc="searchbar_sortings[sortby].get('label', 'Newest')"/>
```

**Root Cause**: `sortby` parameter is `None` when page loads for the first time.

**Solution**: Set default `sortby` value in controller:

```python
def _prepare_vendor_deliveries_values(self, page, sortby, filterby, search, search_in, **kw):
    # Get searchbar configurations
    searchbar_sortings = self._vendor_delivery_get_searchbar_sortings()
    
    # Set default sortby if not provided
    if not sortby:
        sortby = 'date'  # Default to date sorting
    
    # ... rest of implementation
```

#### 4. **Portal User Cannot Access Their Own Data**

**Root Cause**: Missing or incorrect record rules.

**Solution**: Ensure record rules allow access to user's own data:

```xml
<!-- Allow portal users to access their own partner record -->
<record id="rule_res_partner_portal" model="ir.rule">
    <field name="name">Portal: Only own partner record</field>
    <field name="model_id" ref="base.model_res_partner"/>
    <field name="domain_force">[('id', '=', user.partner_id.id)]</field>
    <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
</record>
```

### Advanced Portal Data Handling

#### **Pattern: Controller Data Processing**

For complex portal scenarios where portal users need to display related data they don't have direct access to:

```python
def _prepare_portal_data(self, records):
    """Process records into template-safe dictionaries"""
    processed_data = []
    
    for record in records:
        # Use sudo() for related data access
        related_data = {}
        if record.related_field_id:
            related_record = record.related_field_id.sudo()
            related_data = {
                'id': related_record.id,
                'name': related_record.name,
                # Add other needed fields with null checks
                'field1': related_record.field1 or '',
                'field2': related_record.field2 or '',
            }
        
        # Build safe data structure
        processed_record = {
            'id': record.id,
            'name': record.name,
            'state': record.state,
            'related_data': related_data,
            # Add computed fields
            'display_name': f"{record.name} - {record.state}",
        }
        
        processed_data.append(processed_record)
    
    return processed_data
```

#### **Pattern: Template-Safe Data Access**

```xml
<!-- Safe template patterns -->
<t t-foreach="processed_records" t-as="record">
    <div class="record-item">
        <h4 t-out="record['name']"/>
        <p t-out="record['related_data']['name']"/>
        
        <!-- Conditional display with safe access -->
        <t t-if="record['related_data']['field1']">
            <span t-out="record['related_data']['field1']"/>
        </t>
        
        <!-- Safe URL generation -->
        <a t-attf-href="/my/records/#{record['id']}">View</a>
    </div>
</t>
```

#### **Pattern: Error Handling in Controllers**

```python
def _safe_read_partner_data(self, partner_id):
    """Safely read partner data with error handling"""
    try:
        partner = self.env['res.partner'].sudo().browse(partner_id)
        if partner.exists():
            return {
                'id': partner.id,
                'name': partner.name,
                'street': partner.street or '',
                'city': partner.city or '',
                # Add other fields as needed
            }
        else:
            return {
                'id': partner_id,
                'name': 'Unknown Partner',
                'street': '',
                'city': '',
            }
    except Exception as e:
        # Log error and return safe default
        _logger.warning(f"Error reading partner {partner_id}: {e}")
        return {
            'id': partner_id,
            'name': 'Error Loading Partner',
            'street': '',
            'city': '',
        }
```

### Security Checklist

- [ ] Portal users have read/write access to their own partner record
- [ ] Record rules restrict access to only user's own data
- [ ] No duplicate access rights entries
- [ ] Controller-level access control implemented
- [ ] Proper domain filters in record rules
- [ ] Portal users cannot access other users' data
- [ ] Use `sudo()` only for reading related data, not writing
- [ ] Pre-process data in controllers before passing to templates
- [ ] Implement error handling for data access failures
- [ ] Validate all user inputs and sanitize data

### Debugging Security Issues

1. **Check Access Rights**:
```python
# In Odoo shell or debug mode
user = env['res.users'].browse(user_id)
user.has_group('base.group_portal')  # Should be True
user.partner_id.read(['name'])  # Should work
```

2. **Check Record Rules**:
```python
# Check if record rules are applied
env['ir.rule'].search([('groups', 'in', [env.ref('base.group_portal').id])])
```

3. **Test Portal Access**:
```python
# Test portal user access to their own partner
portal_user = env['res.users'].browse(portal_user_id)
partner = portal_user.partner_id
partner.read(['name', 'email'])  # Should work
```

4. **Test Related Data Access**:
```python
# Test access to related data with sudo
delivery = env['stock.picking'].browse(delivery_id)
partner = delivery.partner_id.sudo()  # Should work
partner.read(['name', 'street'])  # Should work
```

## Best Practices

### 1. Use Portal-Specific Components

- Use `portal.portal_layout` as base template
- Use `portal.portal_table` for data tables
- Use `portal.pager` for pagination
- Use `portal.portal_searchbar` for search functionality

### 2. Follow Odoo 18 Standards

- Use `t-out` instead of `t-field` for simple output
- Use `t-attf-class` for conditional styling
- Avoid complex `t-options` in portal templates
- Use proper portal template inheritance

### 3. Security First

- Always implement access control in controllers
- Use record rules for data filtering
- Verify user permissions before operations
- Sanitize user inputs
- **Include CSRF tokens in all POST forms**
- Never disable CSRF protection for portal forms

### 4. Data Format Handling

- **Convert datetime formats** between HTML inputs and Odoo database format
- **Handle timezone conversions** properly for datetime fields
- **Validate user inputs** before processing
- **Use proper error handling** for format conversion failures
- **Format data for templates** to match HTML input requirements

### 5. Performance Optimization

- Use database-level pagination
- Implement proper caching strategies
- Optimize database queries
- Use `search_count()` for totals

### 6. User Experience

- Provide clear navigation
- Use consistent styling
- Implement proper error handling
- Add loading states for long operations

### 7. Code Organization

- Separate business logic from presentation
- Use descriptive method names
- Implement proper error handling
- Add comprehensive documentation

## ✅ Successfully Implemented Portal Features

### **modula_delivery_courier Portal Implementation Status**

The following features have been successfully implemented and tested:

#### **1. Portal Home Integration** ✅
- **Portal Home Button**: Successfully added "Vendor Deliveries" button to `/my` homepage
- **Icon**: Custom delivery icon (`/modula_delivery_courier/static/src/img/delivery.png`)
- **Counter**: Shows real-time count of deliveries assigned to the vendor
- **Navigation**: Clicking button redirects to `/my/vendor-deliveries`
- **Conditional Display**: Only appears for users with local freight vendor type

#### **2. Portal Delivery Management** ✅
- **Delivery List**: Complete list view with pagination, search, and filtering
- **Delivery Details**: Individual delivery view with customer information
- **Update Functionality**: Ability to update scheduled dates and add notes
- **Validation**: Ability to validate deliveries
- **CSRF Protection**: All forms include proper CSRF tokens

#### **3. Security Implementation** ✅
- **Access Control**: Portal users can only see their assigned deliveries
- **Record Rules**: Proper domain filtering for data access
- **Controller Security**: User type validation and access checks
- **Data Sanitization**: Safe handling of user inputs and related data

#### **4. Technical Implementation** ✅
- **Template Inheritance**: Proper inheritance from custom portal template
- **JavaScript Integration**: Counter display and portal functionality
- **Asset Management**: Proper loading of custom JavaScript and images
- **Error Handling**: Comprehensive error handling for all operations

## Testing Portal Functionality

### Manual Testing Checklist

1. **Portal Home Integration** ✅
   - [x] Portal home button appears for local freight vendors
   - [x] Button shows correct icon and title
   - [x] Button displays delivery count correctly
   - [x] Clicking button redirects to `/my/vendor-deliveries`
   - [x] Button only appears for users with local freight vendor type

2. **Portal User Access**
   - [ ] Portal user can log in
   - [ ] Portal user sees only authorized data
   - [ ] Portal user cannot access unauthorized pages

3. **Navigation**
   - [ ] Portal home page shows correct counters
   - [ ] Navigation links work correctly
   - [ ] Breadcrumbs are properly displayed

4. **Data Display**
   - [ ] Lists show correct data
   - [ ] Pagination works correctly
   - [ ] Search and filters work
   - [ ] Sorting works correctly

5. **Actions**
   - [ ] Update operations work
   - [ ] Validation operations work
   - [ ] Error handling works correctly
   - [ ] Datetime format conversions work correctly
   - [ ] No datetime format errors in server logs

6. **Security**
   - [ ] Access control prevents unauthorized access
   - [ ] Record rules filter data correctly
   - [ ] CSRF protection works
   - [ ] All POST forms include CSRF tokens
   - [ ] No CSRF token errors in server logs

### Automated Testing

```python
def test_portal_access_control(self):
    """Test portal access control"""
    # Test unauthorized access
    with self.assertRaises(AccessError):
        self.env['stock.picking'].with_user(self.portal_user).search([])
    
    # Test authorized access
    deliveries = self.env['stock.picking'].with_user(self.portal_user).search([
        ('freight_carrier_id', '=', self.portal_user.partner_id.id)
    ])
    self.assertTrue(deliveries)
```

## Conclusion

Building portal functionality in Odoo 18 requires careful attention to:

1. **Template Structure**: Use proper portal components and avoid template errors
2. **Controller Logic**: Implement efficient pagination and proper access control
3. **Security**: Use record rules and controller-level checks
4. **User Experience**: Follow portal design patterns and provide clear navigation
5. **Performance**: Optimize database queries and use proper pagination

## Lessons Learned from modula_delivery_courier Implementation

### ✅ Success Factors
1. **Template Inheritance**: Properly inheriting from custom portal templates when they exist
2. **Security First**: Implementing CSRF protection and access control from the start
3. **Error Handling**: Comprehensive error handling for all user interactions
4. **Asset Management**: Proper loading of JavaScript and custom assets
5. **Documentation**: Maintaining comprehensive documentation throughout development

### 🔧 Key Technical Solutions
1. **Portal Home Integration**: Using JavaScript counters and proper template inheritance
2. **Datetime Handling**: Converting between HTML and Odoo datetime formats
3. **Data Access**: Using `sudo()` for safe reading of related data
4. **Form Security**: Including CSRF tokens in all POST forms
5. **Access Control**: Implementing both record rules and controller-level validation

### 📚 Best Practices Established
1. **Always check for template overrides** before implementing portal home buttons
2. **Use portal-specific components** (portal.portal_layout, portal.portal_table, etc.)
3. **Implement proper error handling** for all user interactions
4. **Test with different user types** to ensure proper access control
5. **Document security measures** and implementation details

By following these guidelines and learning from the `modula_delivery_courier` implementation, developers can create robust and user-friendly portal functionality in Odoo 18.

## References

- [Odoo Portal Documentation](https://www.odoo.com/documentation/18.0/developer/reference/addons/portal.html)
- [Odoo 18 Template Changes](https://www.odoo.com/documentation/18.0/developer/reference/addons/web.html)
- [Portal Security Best Practices](https://www.odoo.com/documentation/18.0/developer/reference/addons/security.html) 