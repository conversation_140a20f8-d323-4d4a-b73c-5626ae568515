# AI Prompt Templates for Odoo Development

## 📋 Overview

This document provides proven prompt templates for efficiently developing Odoo modules with AI assistance. These templates are based on the successful development of the Modula Delivery module.

---

## 🚀 Initial Project Setup Templates

### Template 1: Project Initiation
```
I need to develop an Odoo {VERSION} module for {BUSINESS_NEED}.

Business Requirements:
- {REQUIREMENT_1}
- {REQUIREMENT_2}
- {REQUIREMENT_3}

Technical Context:
- Odoo Version: {VERSION}
- Dependencies: {MODULE_LIST}
- Target Users: {USER_TYPES}
- Performance Requirements: {PERFORMANCE_SPECS}
- Integration Needs: {INTEGRATION_REQUIREMENTS}

Please analyze these requirements and provide:
1. Technical architecture recommendation
2. Data model design
3. Integration approach
4. Implementation phases
5. Risk assessment

Focus on Odoo {VERSION} best practices and standards.
```

### Template 2: Architecture Planning
```
Based on the requirements for {MODULE_NAME}, please provide detailed technical architecture:

1. **Data Models**:
   - Primary models with fields and relationships
   - Extended models and inheritance patterns
   - Computed fields and dependencies

2. **Integration Points**:
   - Existing Odoo modules to extend
   - API methods to implement/override
   - Workflow integration points

3. **File Structure**:
   - Recommended directory organization
   - Model, view, and controller files
   - Security and data files

4. **Key Methods**:
   - Core business logic methods
   - Integration methods
   - Validation and constraint methods

Follow Odoo {VERSION} standards and reference similar official modules.
```

---

## 🔧 Implementation Templates

### Template 3: Module Structure Creation
```
Create the complete Odoo {VERSION} module structure for {MODULE_NAME}:

**Requirements**:
- Module name: {MODULE_NAME}
- Display name: {DISPLAY_NAME}
- Category: {CATEGORY}
- Dependencies: {DEPENDENCIES}
- Version: {VERSION}

**Structure needed**:
- Proper __manifest__.py with all required fields
- Models directory with __init__.py
- Views directory with XML files
- Security permissions (ir.model.access.csv)
- Data files for initial setup
- Demo data for testing

**Standards**:
- Follow Odoo {VERSION} manifest format
- Use proper licensing headers
- Include comprehensive descriptions
- Set up proper file organization

Use the file creation tools to implement the complete structure.
```

### Template 4: Core Model Implementation
```
Implement the {MODEL_NAME} model for {MODULE_NAME}:

**Model Specifications**:
- Model name: {MODEL_NAME}
- Description: {MODEL_DESCRIPTION}
- Inheritance: {INHERITANCE_TYPE}

**Required Fields**:
{FIELD_LIST_WITH_TYPES_AND_DESCRIPTIONS}

**Business Logic**:
- {BUSINESS_RULE_1}
- {BUSINESS_RULE_2}
- {VALIDATION_RULE_1}

**Methods Needed**:
- {METHOD_1}: {METHOD_DESCRIPTION}
- {METHOD_2}: {METHOD_DESCRIPTION}

**Standards Requirements**:
- Follow Odoo {VERSION} coding standards
- Include comprehensive docstrings
- Add proper field validation
- Implement error handling
- Add logging for important operations

Reference official Odoo modules for similar functionality patterns.
```

### Template 5: Integration Implementation
```
Extend the existing {EXISTING_MODEL} to integrate with our {NEW_MODULE}:

**Integration Requirements**:
- Add fields: {FIELD_LIST}
- Override methods: {METHOD_LIST}
- Add relationships: {RELATIONSHIP_LIST}

**Functionality to implement**:
- {FUNCTIONALITY_1}
- {FUNCTIONALITY_2}
- {FUNCTIONALITY_3}

**Constraints**:
- Maintain backward compatibility
- Follow existing patterns in {EXISTING_MODEL}
- Ensure proper error handling
- Add comprehensive logging

**Standards**:
- Use proper inheritance patterns
- Follow Odoo {VERSION} best practices
- Reference official modules: {REFERENCE_MODULES}
- Include proper documentation

Analyze the existing {EXISTING_MODEL} implementation first to understand current patterns.
```

---

## 🎨 UI Development Templates

### Template 6: View Creation
```
Create comprehensive views for {MODEL_NAME}:

**View Requirements**:
- List view with columns: {COLUMN_LIST}
- Form view with sections: {SECTION_LIST}
- Search view with filters: {FILTER_LIST}
- Actions and menu items

**UI Specifications**:
- {UI_REQUIREMENT_1}
- {UI_REQUIREMENT_2}
- {UI_REQUIREMENT_3}

**User Experience**:
- Intuitive field grouping
- Proper field visibility rules
- Helpful placeholder text
- Validation feedback

**Standards**:
- Follow Odoo {VERSION} XML view guidelines
- Use proper view inheritance patterns
- Ensure mobile responsiveness
- Include proper security groups
- Add comprehensive help text

**CRITICAL XML Requirements**:
- Use correct external ID format: module_name.xml_id
- Verify all external IDs exist in referenced modules
- Ensure proper file order: views before actions before menus
- Validate all external references against actual Odoo modules

Reference the XML view guidelines in docs/Odoo{VERSION}/ for standards and error prevention.
```

### Template 7: Advanced UI Features
```
Enhance the {MODEL_NAME} views with advanced features:

**Advanced Features Needed**:
- {FEATURE_1}: {FEATURE_DESCRIPTION}
- {FEATURE_2}: {FEATURE_DESCRIPTION}
- {FEATURE_3}: {FEATURE_DESCRIPTION}

**Interactive Elements**:
- Smart buttons for {FUNCTIONALITY}
- Computed field displays
- Dynamic field visibility
- Validation indicators

**User Experience Enhancements**:
- Progress indicators
- Status badges
- Quick action buttons
- Contextual help

**Technical Requirements**:
- Use proper widget types
- Implement field dependencies
- Add client-side validation
- Ensure performance optimization

Follow established patterns from similar Odoo modules.
```

### Template 7.5: XML Error-Prevention Template
```
Create XML views for {MODEL_NAME} with STRICT error prevention:

**View Requirements**:
- List view with fields: {FIELD_LIST}
- Form view with sections: {SECTION_LIST}
- Search view with filters: {FILTER_LIST}
- Action and menu items

**CRITICAL XML Error Prevention**:
1. **External ID Validation**:
   - Use EXACT format: module_name.xml_id
   - Parent menu: {EXACT_PARENT_MENU_ID} (verify this exists)
   - Referenced views: {EXACT_VIEW_IDS} (verify these exist)
   - Security groups: {EXACT_GROUP_IDS} (verify these exist)

2. **File Order Requirements**:
   - Create in __manifest__.py data order: security, views, actions, menus
   - Within XML file order: views first, then actions, then menus
   - Ensure all referenced IDs are defined before use

3. **Dependency Verification**:
   - Confirm all referenced modules are in depends: {MODULE_DEPENDENCIES}
   - Validate external IDs exist in target modules
   - Check module installation order

**Validation Steps**:
- Verify XML syntax with: python3 -c "import xml.etree.ElementTree as ET; ET.parse('file.xml')"
- Test external ID references exist
- Confirm file order prevents dependency errors

**Common External IDs to Use**:
- Stock menu: stock.menu_stock_root
- Delivery views: delivery.view_delivery_carrier_form
- Security groups: stock.group_stock_manager, base.group_user

Reference docs/Odoo18/odoo_xml_view_guide_line.md for complete error prevention guide.
```

---

## 🧪 Testing Templates

### Template 8: Test Suite Creation
```
Create a comprehensive test suite for {MODULE_NAME}:

**Test Coverage Requirements**:
- Unit tests for all model methods
- Integration tests for {INTEGRATION_POINTS}
- UI workflow tests
- Edge case and error condition tests

**Specific Test Scenarios**:
- {TEST_SCENARIO_1}
- {TEST_SCENARIO_2}
- {TEST_SCENARIO_3}

**Test Data Requirements**:
- {TEST_DATA_1}
- {TEST_DATA_2}
- {TEST_DATA_3}

**Performance Tests**:
- Large dataset handling
- Concurrent user scenarios
- Memory usage validation

**Standards**:
- Follow Odoo testing patterns
- Use TransactionCase for database tests
- Include both positive and negative test cases
- Achieve >80% code coverage
- Add performance benchmarks

Reference existing Odoo test patterns for similar modules.
```

---

## 📊 Quality Assurance Templates

### Template 9: Standards Compliance Review
```
Review the {MODULE_NAME} implementation against Odoo {VERSION} standards:

**Review Areas**:
1. **Code Standards**:
   - Compare with official {REFERENCE_MODULE} module
   - Validate coding patterns and conventions
   - Check error handling and logging

2. **Architecture Compliance**:
   - Model design patterns
   - Integration approaches
   - API implementation

3. **Performance Standards**:
   - Database query optimization
   - Memory usage patterns
   - Response time requirements

4. **Security Compliance**:
   - Access control implementation
   - Data validation
   - Input sanitization

**Action Items**:
- Identify gaps and improvements needed
- Update code to match official standards
- Document any necessary deviations
- Validate all changes with tests

Use the codebase-retrieval tool to analyze official Odoo modules for reference.
```

### Template 10: Enhancement Implementation
```
Based on the standards review, enhance {MODULE_NAME} with:

**Priority Enhancements**:
- {ENHANCEMENT_1}: {JUSTIFICATION}
- {ENHANCEMENT_2}: {JUSTIFICATION}
- {ENHANCEMENT_3}: {JUSTIFICATION}

**Implementation Requirements**:
- Maintain backward compatibility
- Follow patterns from {REFERENCE_MODULE}
- Add comprehensive tests for new features
- Update documentation

**Quality Standards**:
- All enhancements must have corresponding tests
- Performance impact must be validated
- Security implications must be assessed
- Documentation must be updated

**Validation Criteria**:
- All existing tests continue to pass
- New functionality is thoroughly tested
- Performance benchmarks are maintained
- Code quality standards are met

Implement enhancements incrementally and validate each step.
```

---

## 📚 Documentation Templates

### Template 11: User Documentation
```
Create comprehensive user documentation for {MODULE_NAME}:

**Documentation Structure**:
1. **Overview**: Features and benefits
2. **Installation**: Setup and configuration
3. **Usage**: Step-by-step instructions
4. **Examples**: Real-world scenarios
5. **Troubleshooting**: Common issues and solutions

**Content Requirements**:
- Clear, non-technical language
- Screenshots and examples
- Best practices and tips
- Configuration options
- Integration guidance

**Target Audience**: {USER_TYPES}

**Format**: Markdown with proper structure and navigation

Make it accessible for users with varying technical expertise.
```

### Template 12: Technical Documentation
```
Create technical documentation for {MODULE_NAME}:

**Documentation Sections**:
1. **Architecture**: Design decisions and patterns
2. **API Reference**: Methods and parameters
3. **Database Schema**: Models and relationships
4. **Extension Points**: How to extend the module
5. **Performance**: Optimization and considerations

**Target Audience**: Developers and system administrators

**Content Requirements**:
- Code examples and snippets
- Architecture diagrams
- API documentation with parameters
- Extension guidelines
- Performance benchmarks

**Standards**:
- Use proper markdown formatting
- Include comprehensive examples
- Reference official Odoo documentation
- Provide clear extension guidelines

Focus on enabling future development and maintenance.
```

---

## 🎯 Handoff Templates

### Template 13: Phase Completion Handoff
```
Prepare handoff documentation for {MODULE_NAME} Phase {PHASE_NUMBER}:

**Completion Status**:
- ✅ {COMPLETED_ITEM_1}
- ✅ {COMPLETED_ITEM_2}
- ✅ {COMPLETED_ITEM_3}

**Current Capabilities**:
- {CAPABILITY_1}
- {CAPABILITY_2}
- {CAPABILITY_3}

**Next Phase Preparation**:
- Suggested enhancements: {ENHANCEMENT_LIST}
- Development guidelines: {GUIDELINE_LIST}
- Extension points: {EXTENSION_POINTS}

**Documentation Provided**:
- User manual
- Technical architecture
- API documentation
- Development guidelines

**Quality Metrics**:
- Test coverage: {COVERAGE_PERCENTAGE}
- Performance benchmarks: {BENCHMARKS}
- Standards compliance: {COMPLIANCE_STATUS}

Ensure the next developer has everything needed to continue efficiently.
```

---

## 💡 Pro Tips for Effective Prompting

### 1. Context is King
Always provide:
- Odoo version
- Module purpose
- Integration requirements
- Performance expectations

### 2. Be Specific
❌ "Make it better"
✅ "Add postcode_fixed_price field for fallback pricing when no postcode matches"

### 3. Reference Standards
Always mention:
- Official Odoo modules to reference
- Specific standards to follow
- Patterns to maintain

### 4. Request Validation
Always ask for:
- Standards compliance checking
- Error handling implementation
- Test coverage
- Documentation updates

### 5. Think Incrementally
Break complex requests into:
- Logical phases
- Testable components
- Reviewable chunks
- Documentable features

---

**Template Status**: ✅ **PROVEN EFFECTIVE**
**Success Rate**: 100% (Based on Modula Delivery implementation)
**Usage**: Copy, customize, and use for any Odoo development project
