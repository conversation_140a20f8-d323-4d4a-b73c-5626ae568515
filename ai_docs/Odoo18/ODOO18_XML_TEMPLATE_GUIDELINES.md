# Odoo 18 XML Template Guidelines for JavaScript Components

## 🎯 **Purpose**
Comprehensive guidelines for AI when working with Odoo 18 XML templates for JavaScript components, based on real development experience.

## 📋 **Core Principles**

### **1. Template-JavaScript Relationship**
Every JavaScript component that renders UI must have a corresponding XML template.

#### **Critical Rule:**
```javascript
// JavaScript file
MyComponent.template = "module_name.ComponentName";
```
```xml
<!-- XML file -->
<t t-name="module_name.ComponentName">
    <!-- Template content -->
</t>
```
**The template name MUST match exactly!**

### **2. File Organization**
```
static/src/
├── components/
│   ├── my_component.js
│   └── my_component.xml
├── views/
│   ├── form_view.js
│   └── form_view.xml
└── fields/
    ├── custom_field.js
    └── custom_field.xml
```

## 🔧 **XML Template Patterns**

### **1. Basic Template Structure**

#### **Standard Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="module_name.ComponentName">
        <div class="component-container">
            <!-- Component content -->
        </div>
    </t>
</templates>
```

#### **Key Elements:**
- `<?xml version="1.0" encoding="UTF-8"?>` - XML declaration
- `<templates xml:space="preserve">` - Template container
- `<t t-name="...">` - Template definition
- Proper indentation and structure

### **2. Dialog Templates**

#### **Dialog Component Template:**
```xml
<templates xml:space="preserve">
    <t t-name="module_name.DialogComponent">
        <Dialog title="props.title" size="props.size or 'lg'">
            <div class="modal-body">
                <div class="row" t-if="props.message">
                    <div class="col-12">
                        <p t-esc="props.message"/>
                    </div>
                </div>
                
                <!-- Custom content -->
                <div class="row">
                    <div class="col-12">
                        <!-- Your dialog content here -->
                    </div>
                </div>
            </div>
            
            <t t-set-slot="footer">
                <button class="btn btn-primary" t-on-click="onConfirm">
                    <i class="fa fa-check"/> Confirm
                </button>
                <button class="btn btn-secondary" t-on-click="onCancel">
                    <i class="fa fa-times"/> Cancel
                </button>
            </t>
        </Dialog>
    </t>
</templates>
```

#### **JavaScript Reference:**
```javascript
import { Dialog } from "@web/core/dialog/dialog";

export class DialogComponent extends Component {
    // Component logic
}

DialogComponent.template = "module_name.DialogComponent";
DialogComponent.components = { Dialog };
```

### **3. Field Extension Templates**

#### **Field Inheritance Pattern:**
```xml
<templates xml:space="preserve">
    <t t-name="module_name.CustomFloatField" t-inherit="web.FloatField" t-inherit-mode="extension">
        <xpath expr="//input" position="after">
            <button t-if="needsApproval" 
                    class="btn btn-sm btn-warning ms-2"
                    t-on-click="onApproveClick">
                <i class="fa fa-check"/> Approve
            </button>
        </xpath>
    </t>
</templates>
```

#### **Key Inheritance Attributes:**
- `t-inherit="web.FloatField"` - Base template to inherit from
- `t-inherit-mode="extension"` - Inheritance mode
- `<xpath>` - XPath selector for modification point
- `position="after"` - Where to add content (before, after, inside, replace)

### **4. List and Selection Templates**

#### **Employee Selection Template:**
```xml
<templates xml:space="preserve">
    <t t-name="module_name.EmployeeSelection">
        <Dialog title="'Select Employee'" size="'lg'">
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="list-group">
                            <t t-foreach="props.employees" t-as="employee" t-key="employee.id">
                                <button class="list-group-item list-group-item-action d-flex align-items-center"
                                        t-on-click="() => this.selectEmployee(employee)">
                                    <img t-if="employee.avatar" 
                                         t-att-src="employee.avatar" 
                                         class="rounded-circle me-3" 
                                         style="width: 40px; height: 40px;"/>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1" t-esc="employee.name"/>
                                        <small class="text-muted" t-esc="employee.department"/>
                                    </div>
                                    <i t-if="employee.isSelected" class="fa fa-check text-success"/>
                                </button>
                            </t>
                        </div>
                    </div>
                </div>
            </div>
            
            <t t-set-slot="footer">
                <button class="btn btn-secondary" t-on-click="onCancel">
                    Cancel
                </button>
            </t>
        </Dialog>
    </t>
</templates>
```

### **5. PIN Input Template**

#### **PIN Validation Template:**
```xml
<templates xml:space="preserve">
    <t t-name="module_name.PinPopup">
        <Dialog title="'PIN Validation'" size="'md'">
            <div class="modal-body">
                <div class="row">
                    <div class="col-12 text-center">
                        <div class="employee-info mb-3">
                            <img t-if="props.employee.avatar" 
                                 t-att-src="props.employee.avatar" 
                                 class="rounded-circle mb-2" 
                                 style="width: 60px; height: 60px;"/>
                            <h5 t-esc="props.employee.name"/>
                        </div>
                        
                        <div class="pin-input">
                            <input type="password" 
                                   class="form-control form-control-lg text-center" 
                                   placeholder="Enter PIN"
                                   t-model="state.pin"
                                   t-on-keyup="onKeyUp"
                                   maxlength="6"/>
                        </div>
                        
                        <div t-if="state.error" class="alert alert-danger mt-3">
                            <i class="fa fa-exclamation-triangle"/> 
                            <span t-esc="state.error"/>
                        </div>
                    </div>
                </div>
            </div>
            
            <t t-set-slot="footer">
                <button class="btn btn-primary" 
                        t-on-click="onValidate"
                        t-att-disabled="!state.pin">
                    <i class="fa fa-unlock"/> Validate
                </button>
                <button class="btn btn-secondary" t-on-click="onCancel">
                    Cancel
                </button>
            </t>
        </Dialog>
    </t>
</templates>
```

## 🎨 **Template Directives**

### **1. Conditional Rendering**

#### **Basic Conditions:**
```xml
<!-- Simple condition -->
<div t-if="condition">Content shown when true</div>

<!-- Condition with alternative -->
<div t-if="user.isAdmin">Admin content</div>
<div t-else="">Regular user content</div>

<!-- Multiple conditions -->
<div t-if="status === 'draft'">Draft</div>
<div t-elif="status === 'confirmed'">Confirmed</div>
<div t-else="">Other status</div>
```

### **2. Loops and Iteration**

#### **List Iteration:**
```xml
<!-- Basic loop -->
<t t-foreach="items" t-as="item" t-key="item.id">
    <div t-esc="item.name"/>
</t>

<!-- Loop with index -->
<t t-foreach="items" t-as="item" t-key="item_index">
    <div>
        <span t-esc="item_index + 1"/>. 
        <span t-esc="item.name"/>
    </div>
</t>

<!-- Empty state -->
<div t-if="!items.length" class="text-muted">
    No items found
</div>
```

### **3. Event Handling**

#### **Click Events:**
```xml
<!-- Simple click -->
<button t-on-click="onButtonClick">Click me</button>

<!-- Click with parameters -->
<button t-on-click="() => this.selectItem(item)">Select</button>

<!-- Keyboard events -->
<input t-on-keyup="onKeyUp" t-on-keydown="onKeyDown"/>

<!-- Form events -->
<form t-on-submit.prevent="onSubmit">
    <!-- Form content -->
</form>
```

### **4. Data Binding**

#### **Model Binding:**
```xml
<!-- Two-way binding -->
<input t-model="state.inputValue"/>

<!-- Attribute binding -->
<img t-att-src="imageUrl" t-att-alt="imageAlt"/>

<!-- Class binding -->
<div t-att-class="{'active': isActive, 'disabled': isDisabled}">
    Content
</div>

<!-- Style binding -->
<div t-att-style="'color: ' + textColor + '; font-size: ' + fontSize + 'px;'">
    Styled content
</div>
```

## 🚨 **Common Issues and Solutions**

### **1. Template Reference Mismatch**

#### **❌ Problem:**
```javascript
// JavaScript
MyComponent.template = "module_name.ComponentName";
```
```xml
<!-- XML -->
<t t-name="different_module.DifferentName">
```

#### **✅ Solution:**
```javascript
// JavaScript
MyComponent.template = "module_name.ComponentName";
```
```xml
<!-- XML -->
<t t-name="module_name.ComponentName">
```

### **2. Missing Component Imports**

#### **❌ Problem:**
```xml
<Dialog title="'My Dialog'">
    <!-- Content -->
</Dialog>
```
```javascript
// Missing Dialog import
export class MyComponent extends Component {}
```

#### **✅ Solution:**
```javascript
import { Dialog } from "@web/core/dialog/dialog";

export class MyComponent extends Component {}
MyComponent.components = { Dialog };
```

### **3. Incorrect XPath in Inheritance**

#### **❌ Problem:**
```xml
<xpath expr="//wrong-selector" position="after">
    <!-- Content -->
</xpath>
```

#### **✅ Solution:**
```xml
<!-- Check the base template structure first -->
<xpath expr="//input" position="after">
    <!-- Content -->
</xpath>
```

### **4. Event Handler Issues**

#### **❌ Problem:**
```xml
<!-- Incorrect event binding -->
<button t-on-click="nonExistentMethod">Click</button>
```

#### **✅ Solution:**
```xml
<!-- Correct event binding -->
<button t-on-click="onButtonClick">Click</button>
```
```javascript
// Ensure method exists in component
onButtonClick() {
    // Method implementation
}
```

## 📊 **Best Practices**

### **1. Template Organization**

#### **File Structure:**
```
static/src/
├── employee_selection/
│   ├── popup.js
│   ├── popup.xml          ← Template for popup.js
│   ├── pin_popup.js
│   └── pin_popup.xml      ← Template for pin_popup.js
```

#### **Naming Convention:**
- **JavaScript file**: `component_name.js`
- **XML file**: `component_name.xml`
- **Template name**: `module_name.ComponentName`

### **2. Template Content**

#### **Semantic HTML:**
```xml
<templates xml:space="preserve">
    <t t-name="module_name.ComponentName">
        <div class="component-wrapper">
            <header class="component-header">
                <h3 t-esc="props.title"/>
            </header>
            
            <main class="component-body">
                <!-- Main content -->
            </main>
            
            <footer class="component-footer" t-if="hasFooter">
                <!-- Footer content -->
            </footer>
        </div>
    </t>
</templates>
```

#### **Bootstrap Classes:**
```xml
<!-- Use Bootstrap 5 classes -->
<div class="container-fluid">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Title</h5>
                </div>
                <div class="card-body">
                    <!-- Content -->
                </div>
            </div>
        </div>
    </div>
</div>
```

### **3. Accessibility**

#### **ARIA Attributes:**
```xml
<button class="btn btn-primary" 
        t-on-click="onConfirm"
        aria-label="Confirm action"
        t-att-aria-disabled="isDisabled">
    Confirm
</button>

<input type="text" 
       class="form-control"
       t-model="state.value"
       aria-describedby="helpText"
       t-att-aria-invalid="hasError"/>
<small id="helpText" class="form-text text-muted">
    Help text here
</small>
```

## 🎯 **AI Development Checklist**

### **Before Creating XML Templates:**
- [ ] **Identify component type** (dialog, field, view, etc.)
- [ ] **Check base templates** for inheritance patterns
- [ ] **Plan template structure** and required elements
- [ ] **Consider responsive design** and Bootstrap classes

### **During Template Creation:**
- [ ] **Match template name** exactly with JavaScript reference
- [ ] **Include required XML declaration** and templates wrapper
- [ ] **Use semantic HTML** structure
- [ ] **Add proper event handlers** for interactive elements
- [ ] **Include conditional rendering** for dynamic content

### **After Template Creation:**
- [ ] **Verify template reference** matches JavaScript
- [ ] **Test responsive behavior** on different screen sizes
- [ ] **Validate accessibility** features
- [ ] **Check browser console** for template errors
- [ ] **Test all interactive elements**

## 🚀 **Conclusion**

Proper XML template structure is crucial for Odoo 18 JavaScript components. Following these guidelines ensures:

- **Correct rendering** of components
- **Proper integration** with Odoo's UI framework
- **Consistent user experience** across the application
- **Maintainable code** structure

Always ensure template names match JavaScript references exactly, and follow Odoo's established patterns for inheritance and component structure.
