[main]
host = https://www.transifex.com

[o:odoo:p:odoo-18:r:theme_anelusia]
file_filter            = theme_anelusia/i18n/<lang>.po
source_file            = theme_anelusia/i18n/theme_anelusia.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_anelusia
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_artists]
file_filter            = theme_artists/i18n/<lang>.po
source_file            = theme_artists/i18n/theme_artists.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_artists
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_avantgarde]
file_filter            = theme_avantgarde/i18n/<lang>.po
source_file            = theme_avantgarde/i18n/theme_avantgarde.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_avantgarde
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_aviato]
file_filter            = theme_aviato/i18n/<lang>.po
source_file            = theme_aviato/i18n/theme_aviato.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_aviato
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_beauty]
file_filter            = theme_beauty/i18n/<lang>.po
source_file            = theme_beauty/i18n/theme_beauty.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_beauty
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_bewise]
file_filter            = theme_bewise/i18n/<lang>.po
source_file            = theme_bewise/i18n/theme_bewise.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_bewise
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_bistro]
file_filter            = theme_bistro/i18n/<lang>.po
source_file            = theme_bistro/i18n/theme_bistro.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_bistro
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_bookstore]
file_filter            = theme_bookstore/i18n/<lang>.po
source_file            = theme_bookstore/i18n/theme_bookstore.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_bookstore
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_buzzy]
file_filter            = theme_buzzy/i18n/<lang>.po
source_file            = theme_buzzy/i18n/theme_buzzy.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_buzzy
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_clean]
file_filter            = theme_clean/i18n/<lang>.po
source_file            = theme_clean/i18n/theme_clean.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_clean
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_cobalt]
file_filter            = theme_cobalt/i18n/<lang>.po
source_file            = theme_cobalt/i18n/theme_cobalt.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_cobalt
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_common]
file_filter            = theme_common/i18n/<lang>.po
source_file            = theme_common/i18n/theme_common.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_common
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_enark]
file_filter            = theme_enark/i18n/<lang>.po
source_file            = theme_enark/i18n/theme_enark.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_enark
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_graphene]
file_filter            = theme_graphene/i18n/<lang>.po
source_file            = theme_graphene/i18n/theme_graphene.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_graphene
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_kea]
file_filter            = theme_kea/i18n/<lang>.po
source_file            = theme_kea/i18n/theme_kea.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_kea
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_kiddo]
file_filter            = theme_kiddo/i18n/<lang>.po
source_file            = theme_kiddo/i18n/theme_kiddo.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_kiddo
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_loftspace]
file_filter            = theme_loftspace/i18n/<lang>.po
source_file            = theme_loftspace/i18n/theme_loftspace.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_loftspace
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_monglia]
file_filter            = theme_monglia/i18n/<lang>.po
source_file            = theme_monglia/i18n/theme_monglia.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_monglia
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_nano]
file_filter            = theme_nano/i18n/<lang>.po
source_file            = theme_nano/i18n/theme_nano.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_nano
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_notes]
file_filter            = theme_notes/i18n/<lang>.po
source_file            = theme_notes/i18n/theme_notes.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_notes
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_odoo_experts]
file_filter            = theme_odoo_experts/i18n/<lang>.po
source_file            = theme_odoo_experts/i18n/theme_odoo_experts.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_odoo_experts
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_orchid]
file_filter            = theme_orchid/i18n/<lang>.po
source_file            = theme_orchid/i18n/theme_orchid.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_orchid
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_paptic]
file_filter            = theme_paptic/i18n/<lang>.po
source_file            = theme_paptic/i18n/theme_paptic.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_paptic
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_real_estate]
file_filter            = theme_real_estate/i18n/<lang>.po
source_file            = theme_real_estate/i18n/theme_real_estate.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_real_estate
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_treehouse]
file_filter            = theme_treehouse/i18n/<lang>.po
source_file            = theme_treehouse/i18n/theme_treehouse.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_treehouse
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_vehicle]
file_filter            = theme_vehicle/i18n/<lang>.po
source_file            = theme_vehicle/i18n/theme_vehicle.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_vehicle
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_yes]
file_filter            = theme_yes/i18n/<lang>.po
source_file            = theme_yes/i18n/theme_yes.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_yes
replace_edited_strings = false
keep_translations      = false

[o:odoo:p:odoo-18:r:theme_zap]
file_filter            = theme_zap/i18n/<lang>.po
source_file            = theme_zap/i18n/theme_zap.pot
type                   = PO
minimum_perc           = 0
resource_name          = theme_zap
replace_edited_strings = false
keep_translations      = false
