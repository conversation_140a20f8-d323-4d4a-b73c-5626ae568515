#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for the fixed string concatenation in calculated field formulas
"""

class MockConfigMatrixCalculatedField:
    """Mock class to test the fixed string concatenation"""
    
    def _convert_js_to_python(self, js_formula):
        """Convert JavaScript formula syntax to Python syntax"""
        if not js_formula:
            return js_formula

        python_formula = js_formula

        # Convert JavaScript operators to Python
        python_formula = python_formula.replace('===', '==')
        python_formula = python_formula.replace('!==', '!=')
        python_formula = python_formula.replace('&&', ' and ')
        python_formula = python_formula.replace('||', ' or ')

        # Convert JavaScript Math.min/max to Python min/max
        python_formula = python_formula.replace('Math.min(', 'min(')
        python_formula = python_formula.replace('Math.max(', 'max(')
        python_formula = python_formula.replace('Math.', '')

        # Convert JavaScript logical OR with default value (|| 0) to Python equivalent
        python_formula = python_formula.replace(' or 0', ' or 0')

        # Convert JavaScript string concatenation to Python
        python_formula = self._convert_string_concatenation(python_formula)

        # Convert ternary operator
        python_formula = self._convert_ternary_operators(python_formula)

        return python_formula

    def _convert_string_concatenation(self, formula):
        """Convert JavaScript string concatenation to Python string formatting"""
        import re
        
        # More comprehensive approach: find all variables that are being concatenated
        # and wrap them with str()
        
        # First, let's find all variable names in the formula
        variable_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b'
        variables = re.findall(variable_pattern, formula)
        
        # For each variable, check if it's being concatenated with a string
        for variable in variables:
            # Skip if it's already wrapped with str()
            if f'str({variable})' in formula:
                continue
                
            # Check if this variable is being concatenated with a string
            # Pattern: variable + 'string' or 'string' + variable
            concat_pattern = rf'\b{variable}\b\s*\+\s*[\'"][^\'"]*[\'"]|[\'"][^\'"]*[\'"]\s*\+\s*\b{variable}\b'
            
            if re.search(concat_pattern, formula):
                # Replace the variable with str(variable)
                formula = re.sub(rf'\b{variable}\b', f'str({variable})', formula)
        
        return formula

    def _convert_ternary_operators(self, formula):
        """Convert JavaScript ternary operators to Python if-else expressions"""
        
        def convert_nested_ternary_manual(expr):
            """Convert nested ternary operators manually for the specific pattern"""
            expr = expr.strip()
            
            # Find the first question mark
            first_question = expr.find('?')
            if first_question == -1:
                return expr
            
            # Find the colon after the first question mark
            first_colon = expr.find(':', first_question)
            if first_colon == -1:
                return expr
            
            # Check if there's a nested ternary in the false part
            false_part_start = first_colon + 1
            false_part = expr[false_part_start:].strip()
            
            # Look for nested ternary in the false part
            if false_part.startswith('(') and '?' in false_part and ':' in false_part:
                # Extract the parts
                condition1 = expr[:first_question].strip()
                true1 = expr[first_question + 1:first_colon].strip()
                
                # Remove the outer parentheses from the false part
                nested_expr = false_part[1:-1] if false_part.startswith('(') and false_part.endswith(')') else false_part
                
                # Find the nested ternary
                nested_question = nested_expr.find('?')
                nested_colon = nested_expr.find(':', nested_question)
                
                if nested_question != -1 and nested_colon != -1:
                    condition2 = nested_expr[:nested_question].strip()
                    true2 = nested_expr[nested_question + 1:nested_colon].strip()
                    false2 = nested_expr[nested_colon + 1:].strip()
                    
                    # Convert to Python if-else
                    python_expr = f"({true1} if ({condition1}) else ({true2} if ({condition2}) else {false2}))"
                    return python_expr
            
            # If no nested pattern found, try simple ternary
            condition = expr[:first_question].strip()
            true_part = expr[first_question + 1:first_colon].strip()
            false_part = expr[first_colon + 1:].strip()
            
            # Convert to Python if-else
            python_expr = f"({true_part} if ({condition}) else {false_part})"
            return python_expr
        
        # Only process if there are ternary operators
        if '?' in formula and ':' in formula:
            try:
                return convert_nested_ternary_manual(formula)
            except Exception as e:
                print(f"⚠️ Ternary conversion failed: {e}, using fallback")
                # Fallback to simple replacement
                formula = formula.replace(' ? ', ' if ')
                formula = formula.replace(' : ', ' else ')
                return formula
        
        return formula


def test_string_concatenation_cases():
    """Test various string concatenation cases"""
    
    calc_field = MockConfigMatrixCalculatedField()
    
    # Test cases
    test_cases = [
        # Original failing case
        {
            'name': 'Original failing case',
            'js_formula': "'min(' + _CALCULATED_manual_left_height + ', ' + _CALCULATED_manual_right_height + ') = ' + _CALCULATED_smallest_door_height",
            'expected': "min(100, 150) = 100"
        },
        # Simple string + variable
        {
            'name': 'Simple string + variable',
            'js_formula': "'Hello ' + user_name",
            'expected': "Hello John"
        },
        # Variable + string
        {
            'name': 'Variable + string',
            'js_formula': "count + ' items'",
            'expected': "5 items"
        },
        # Variable + string + variable
        {
            'name': 'Variable + string + variable',
            'js_formula': "width + ' x ' + height",
            'expected': "100 x 200"
        },
        # Complex concatenation
        {
            'name': 'Complex concatenation',
            'js_formula': "'Result: ' + min_value + ' to ' + max_value + ' (total: ' + total + ')'",
            'expected': "Result: 10 to 50 (total: 60)"
        }
    ]
    
    print("🧪 Testing string concatenation conversion...")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print(f"JS Formula: {test_case['js_formula']}")
        
        # Convert to Python
        python_formula = calc_field._convert_js_to_python(test_case['js_formula'])
        print(f"Python Formula: {python_formula}")
        
        # Test context
        test_context = {
            '_CALCULATED_manual_left_height': 100,
            '_CALCULATED_manual_right_height': 150,
            '_CALCULATED_smallest_door_height': 100,
            'user_name': 'John',
            'count': 5,
            'width': 100,
            'height': 200,
            'min_value': 10,
            'max_value': 50,
            'total': 60
        }
        
        try:
            # Test syntax
            compiled = compile(python_formula, '<string>', 'eval')
            print("✅ Syntax check passed")
            
            # Test execution
            result = eval(compiled, test_context)
            print(f"✅ Result: {result}")
            
            # Check if result matches expected
            if str(result) == test_case['expected']:
                print("✅ Expected result matched!")
            else:
                print(f"⚠️ Expected: {test_case['expected']}, Got: {result}")
                
        except Exception as e:
            print(f"❌ Error: {e}")


def test_mixed_formulas():
    """Test formulas that combine string concatenation with other operations"""
    
    calc_field = MockConfigMatrixCalculatedField()
    
    print("\n" + "=" * 60)
    print("🧪 Testing mixed formulas (concatenation + other operations)...")
    print("=" * 60)
    
    # Test case with ternary operator and string concatenation
    mixed_formula = "_CALCULATED_height_calculation_method === 'manual' ? 'Manual: ' + _CALCULATED_manual_left_height : 'Auto: ' + _CALCULATED_smallest_door_height"
    
    print(f"Mixed JS Formula: {mixed_formula}")
    
    # Convert to Python
    python_formula = calc_field._convert_js_to_python(mixed_formula)
    print(f"Mixed Python Formula: {python_formula}")
    
    # Test context
    test_context = {
        '_CALCULATED_height_calculation_method': 'manual',
        '_CALCULATED_manual_left_height': 100,
        '_CALCULATED_smallest_door_height': 150
    }
    
    try:
        # Test syntax
        compiled = compile(python_formula, '<string>', 'eval')
        print("✅ Syntax check passed")
        
        # Test execution
        result = eval(compiled, test_context)
        print(f"✅ Result: {result}")
        
        # Test with different calculation method
        test_context['_CALCULATED_height_calculation_method'] = 'auto'
        result2 = eval(compiled, test_context)
        print(f"✅ Result (auto): {result2}")
        
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    print("Testing fixed string concatenation in calculated field formulas")
    print("=" * 60)
    
    test_string_concatenation_cases()
    test_mixed_formulas()
    
    print("\n🎉 All tests completed!") 