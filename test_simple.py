#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple test script for ternary operator parsing
"""

def convert_ternary_right_to_left(expr):
    """Convert ternary operators from right to left"""
    print(f"🔄 Converting: {expr}")
    
    # Find the rightmost question mark
    rightmost_question = expr.rfind('?')
    if rightmost_question == -1:
        print("   No question mark found")
        return expr
    
    print(f"   Rightmost question mark at position {rightmost_question}")
    
    # Find the colon that matches this question mark
    paren_depth = 0
    colon_pos = -1
    
    for i in range(rightmost_question + 1, len(expr)):
        char = expr[i]
        if char == '(':
            paren_depth += 1
        elif char == ')':
            paren_depth -= 1
        elif char == ':' and paren_depth == 0:
            colon_pos = i
            break
    
    if colon_pos == -1:
        print("   No matching colon found")
        return expr
    
    print(f"   Matching colon at position {colon_pos}")
    
    # Extract the parts
    condition = expr[:rightmost_question].strip()
    true_part = expr[rightmost_question + 1:colon_pos].strip()
    false_part = expr[colon_pos + 1:].strip()
    
    print(f"   Condition: {condition}")
    print(f"   True part: {true_part}")
    print(f"   False part: {false_part}")
    
    # Convert to Python if-else
    python_expr = f"({true_part} if ({condition}) else {false_part})"
    print(f"   ✅ Converted to: {python_expr}")
    
    return python_expr

def test_cases():
    """Test various ternary expressions"""
    
    test_cases = [
        "a ? b : c",
        "a ? b : (c ? d : e)",
        "x == 1 ? 'one' : (x == 2 ? 'two' : 'other')",
        "_CALCULATED_height_calculation_method == 'manual' ? max(_CALCULATED_manual_left_height, _CALCULATED_manual_right_height) : (_CALCULATED_height_calculation_method == 'even' ? (parseFloat(bx_dbl_hinge_make_each_door_height_mm_even)  or  0) : max(parseFloat(bx_dbl_hinge_make_left_door_height_mm_uneven)  or  0, parseFloat(bx_dbl_hinge_make_right_door_height_mm_uneven)  or  0))"
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"Test Case {i}: {test_case}")
        print(f"{'='*60}")
        
        result = convert_ternary_right_to_left(test_case)
        print(f"Final result: {result}")
        
        # Test if it compiles
        try:
            compiled = compile(result, '<string>', 'eval')
            print("✅ Compiles successfully")
        except SyntaxError as e:
            print(f"❌ Syntax error: {e}")

if __name__ == "__main__":
    test_cases() 